package com.yhl.scp.mds.product.impl;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.baseResource.infrastructure.dao.PhysicalResource2Dao;
import com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.routing.infrastructure.po.StandardStepPO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.mold.service.MoldToolingService;
import com.yhl.scp.mds.mold.vo.MoldToolingVO;
import com.yhl.scp.mds.newproduct.convertor.NewProductStockPointConvertor;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.product.convertor.ProductCandidateResourceTimeConvertor;
import com.yhl.scp.mds.product.domain.entity.ProductCandidateResourceTimeDO;
import com.yhl.scp.mds.product.domain.service.ProductCandidateResourceTimeDomainService;
import com.yhl.scp.mds.product.dto.ProductCandidateResourceTimeDTO;
import com.yhl.scp.mds.product.dto.ProductCandidateResourceTimeExportDTO;
import com.yhl.scp.mds.product.infrastructure.dao.ProductCandidateResourceTimeDao;
import com.yhl.scp.mds.product.infrastructure.po.ProductCandidateResourceTimePO;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mds.resource.infrastructure.dao.PhysicalResourceDao;
import com.yhl.scp.mds.resource.infrastructure.dao.StandardResourceDao;
import com.yhl.scp.mds.routing.convertor.ProductCandidateResourceConvertor;
import com.yhl.scp.mds.routing.infrastructure.dao.*;
import com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO;
import com.yhl.scp.mds.routing.service.NewProductCandidateResourceService;
import com.yhl.scp.mds.routing.service.NewRoutingStepResourceService;
import com.yhl.scp.mds.stock.infrastructure.dao.NewStockPointDao;
import com.yhl.scp.mds.stock.infrastructure.po.NewStockPointPO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import feign.FeignException;
import feign.RetryableException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ProductCandidateResourceTimeServiceImpl</code>
 * <p>
 * 产品资源生产关系时段优先级表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 10:03:10
 */
@Slf4j
@Service
public class ProductCandidateResourceTimeServiceImpl extends AbstractService implements ProductCandidateResourceTimeService {

    @Resource
    private ProductCandidateResourceTimeDao productCandidateResourceTimeDao;

    @Resource
    private ProductCandidateResourceTimeDomainService productCandidateResourceTimeDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;


    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    private NewProductCandidateResourceDao newProductCandidateResourceDao;

    @Resource
    private StandardStepDao standardStepDao;

    @Resource
    private StandardResourceDao standardResourceDao;

    @Resource
    private PhysicalResource2Dao physicalResource2Dao;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewProductStockPointDao newProductStockPointDao;

    @Resource
    private NewStockPointDao newStockPointDao;

    @Resource
    private NewRoutingStepResourceService newRoutingStepResourceService;

    @Resource
    private NewProductCandidateResourceService productCandidateResourceService;

    @Resource
    private NewRoutingDao newRoutingDao;

    @Resource
    private NewRoutingStepDao newRoutingStepDao;
    @Resource
    private NewRoutingStepResourceDao newRoutingStepResourceDao;

    @Resource
    private NewProductStockPointService newProductStockPointService;
    @Resource
    private PhysicalResourceDao physicalResourceDao;

    @Resource
    private MoldToolingService moldToolingService;

    @Resource
    private NewStockPointService newStockPointService;

    @Override
    public BaseResponse<Void> doCreate(ProductCandidateResourceTimeDTO productCandidateResourceTimeDTO) {
        // 0.数据转换
        ProductCandidateResourceTimeDO productCandidateResourceTimeDO = ProductCandidateResourceTimeConvertor.INSTANCE.dto2Do(productCandidateResourceTimeDTO);
        ProductCandidateResourceTimePO productCandidateResourceTimePO = ProductCandidateResourceTimeConvertor.INSTANCE.dto2Po(productCandidateResourceTimeDTO);
        // 1.数据校验
        productCandidateResourceTimeDomainService.validation(productCandidateResourceTimeDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productCandidateResourceTimePO);
        productCandidateResourceTimeDao.insertWithPrimaryKey(productCandidateResourceTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductCandidateResourceTimeDTO productCandidateResourceTimeDTO) {
        // 0.数据转换
        ProductCandidateResourceTimeDO productCandidateResourceTimeDO = ProductCandidateResourceTimeConvertor.INSTANCE.dto2Do(productCandidateResourceTimeDTO);
        // 1.数据校验
        productCandidateResourceTimeDomainService.validation(productCandidateResourceTimeDO);
        ProductCandidateResourceTimePO productCandidateResourceTimePO = new ProductCandidateResourceTimePO();
        productCandidateResourceTimePO.setPriority(productCandidateResourceTimeDTO.getPriority());
        productCandidateResourceTimePO.setEffectiveTime(productCandidateResourceTimeDTO.getEffectiveTime());
        productCandidateResourceTimePO.setVersionValue(productCandidateResourceTimeDTO.getVersionValue());
        // 2.数据持久化
        BasePOUtils.updateFiller(productCandidateResourceTimePO);
        productCandidateResourceTimeDao.updateSelective(productCandidateResourceTimePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductCandidateResourceTimeDTO> list) {
        List<ProductCandidateResourceTimePO> newList = ProductCandidateResourceTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productCandidateResourceTimeDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductCandidateResourceTimeDTO> list) {
        List<ProductCandidateResourceTimePO> newList = ProductCandidateResourceTimeConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        productCandidateResourceTimeDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productCandidateResourceTimeDao.deleteBatch(idList);
        }
        return productCandidateResourceTimeDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductCandidateResourceTimeVO selectByPrimaryKey(String id) {
        ProductCandidateResourceTimePO po = productCandidateResourceTimeDao.selectByPrimaryKey(id);
        return ProductCandidateResourceTimeConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_product_candidate_resource_time")
    public List<ProductCandidateResourceTimeVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mds_product_candidate_resource_time")
    public List<ProductCandidateResourceTimeVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductCandidateResourceTimeVO> dataList = productCandidateResourceTimeDao.selectByCondition(sortParam, queryCriteriaParam);
        ProductCandidateResourceTimeServiceImpl target = SpringBeanUtils.getBean(ProductCandidateResourceTimeServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectByParams(Map<String, Object> params) {
        List<ProductCandidateResourceTimePO> list = productCandidateResourceTimeDao.selectByParams(params);
        return ProductCandidateResourceTimeConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void export(HttpServletResponse response) {
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productCandidateResourceTimeDao.selectVOByParams(new HashMap<>());
        List<ProductCandidateResourceTimeExportDTO> productCandidateResourceTimeExportDTOS = ProductCandidateResourceTimeConvertor.INSTANCE.vo2ExportDtos(productCandidateResourceTimeVOS);
        try {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("产品资源生产关系.xlsx", "UTF-8"));
            EasyExcel.write(response.getOutputStream(), ProductCandidateResourceTimeExportDTO.class)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .sheet("产品资源生产关系")
                    .doWrite(productCandidateResourceTimeExportDTOS);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    @Override
    @BusinessMonitorLog(businessCode = "产品产线关系调整", moduleCode = "MPS", businessFrequency = "MONTH")
    public void updateData(ProductCandidateResourceTimeVO vo) {
        if (vo == null) {
            throw new BusinessException("参数为空");
        }
        if (StringUtils.isEmpty(vo.getId())) {
            throw new BusinessException("参数id为空");
        }
        if (StringUtils.isEmpty(vo.getCandidateResourceId())) {
            throw new BusinessException("参数candidateResourceId为空");
        }
        if (StringUtils.isEmpty(vo.getProductCode())) {
            throw new BusinessException("参数productCode为空");
        }
        if (StringUtils.isEmpty(vo.getOperationCode())) {
            throw new BusinessException("参数operationCode为空");
        }
        if (vo.getPriority() == null) {
            throw new BusinessException("参数priority为空");
        }

        //更新除了要更新优先级，还要重新计算其他资源的优先级
        List<ProductCandidateResourceTimeVO> updateList = new ArrayList<>();
        Map<String, Object> params = new HashMap<>();
        params.put("productCode", vo.getProductCode());
        params.put("operationCode", vo.getOperationCode());
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productCandidateResourceTimeDao.selectVOByParams(params);
        if (CollectionUtils.isNotEmpty(productCandidateResourceTimeVOS)) {
            //找到原数据
            ProductCandidateResourceTimeVO resourceTimeVO = productCandidateResourceTimeVOS.stream().filter(t -> vo.getId().equals(t.getId())).findFirst().orElse(null);
            if (resourceTimeVO == null) {
                return;
            }
            List<ProductCandidateResourceTimeVO> dataList = new ArrayList<>();
            //计算优先级变化量
            int i = vo.getPriority() - resourceTimeVO.getPriority();
            if (i > 0) { //优先级从1变3，i=2， 优先级变小了，原来优先级是2，3的变为1，2
                int j = 0;
                //将原数据排除，按优先级从小到大的顺序排序
                dataList = productCandidateResourceTimeVOS.stream()
                        .filter(t -> !vo.getId().equals(t.getId())
                                && t.getPriority() > resourceTimeVO.getPriority())
                        .sorted(Comparator.comparing(ProductCandidateResourceTimeVO::getPriority))
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(dataList)) {
                    while (i > j) {
                        dataList.get(j).setPriority(dataList.get(j).getPriority() - 1);
                        updateList.add(dataList.get(j));
                        j++;
                    }
                }
            } else if (i < 0) {//优先级从3变1， 优先级变大了，原来优先级是1，2的变为2，3

                //将原数据排除，按优先级从小到大的顺序排序
                dataList = productCandidateResourceTimeVOS.stream()
                        .filter(t -> !vo.getId().equals(t.getId())
                                && t.getPriority() < resourceTimeVO.getPriority())
                        .sorted(Comparator.comparing(ProductCandidateResourceTimeVO::getPriority).reversed())
                        .collect(Collectors.toList());
                int j = 0;
                if (CollectionUtils.isNotEmpty(dataList)) {
                    while (Math.abs(i) > j) {
                        dataList.get(j).setPriority(dataList.get(j).getPriority() + 1);
                        updateList.add(dataList.get(j));
                        j++;
                    }
                }
            }
            resourceTimeVO.setPriority(vo.getPriority());
            updateList.add(resourceTimeVO);
        }

        if (CollectionUtils.isNotEmpty(updateList)) {
            List<ProductCandidateResourceTimePO> list = new ArrayList<>();
            for (ProductCandidateResourceTimeVO productCandidateResourceTimeVO : updateList) {
                ProductCandidateResourceTimePO po = new ProductCandidateResourceTimePO();
                BeanUtils.copyProperties(productCandidateResourceTimeVO, po);
                list.add(po);
            }
            BasePOUtils.updateBatchFiller(list);
            productCandidateResourceTimeDao.updateBatch(list);
        }

    }

    @Override
    public BaseResponse<Void> syncRoutingStepResourceBase(List<MesMoldChangeTime> list) {
        String scenario = SystemHolder.getScenario();
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success();
        }
        List<com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO> insertDTOs = new ArrayList<>();
        List<com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO> updateDTOs = new ArrayList<>();
        // HashMap<String, Object> map = MapUtil.newHashMap(3);

        HashMap<String, Object> mapPhysicalResourceCode = MapUtil.newHashMap(3);
        //
        HashMap<String, Object> mapNewStockPoint = MapUtil.newHashMap(3);
        List<NewStockPointPO> newStockPointPOS =
                newStockPointDao.selectByParams(mapNewStockPoint);
        Map<String, NewStockPointPO> newStockPointPOSMap = CollectionUtils.isEmpty(newStockPointPOS) ?
                MapUtil.newHashMap() :
                newStockPointPOS.stream().collect(
                        Collectors.toMap(NewStockPointPO::getStockPointCode, Function.identity(), (v1, v2) -> v1));
        List<NewProductStockPointPO> productStockPointPOS =
                newProductStockPointDao.selectByProductCodeStockPointCode(list);
        Map<String, NewProductStockPointPO> productStockPointPOSMap = CollectionUtils.isEmpty(productStockPointPOS) ?
                MapUtil.newHashMap() :
                productStockPointPOS.stream().collect(Collectors.toMap(t -> t.getProductCode() + "_" + t.getStockPointCode(),
                        Function.identity(), (v1, v2) -> v1));
        List<StandardStepPO> standardStepPOS = standardStepDao.selectByParams(MapUtil.newHashMap(3));
        Map<String, StandardStepPO> standardStepPOSMap = CollectionUtils.isEmpty(standardStepPOS) ?
                MapUtil.newHashMap() :
                standardStepPOS.stream().collect(Collectors.toMap(t -> t.getStockPointCode() + "_" + t.getStandardStepCode(),
                        Function.identity(), (v1, v2) -> v1));
        List<String> physicalResourceCodes =
                list.stream().map(MesMoldChangeTime::getProdLineCode).distinct().collect(Collectors.toList());
        mapPhysicalResourceCode.put("physicalResourceCodes", physicalResourceCodes);
        List<PhysicalResourcePO> physicalResourcePOS = physicalResource2Dao.selectByParams(mapPhysicalResourceCode);
        Map<String, PhysicalResourcePO> physicalResourcePOSMap = CollectionUtils.isEmpty(physicalResourcePOS) ?
                MapUtil.newHashMap() :
                physicalResourcePOS.stream().collect(Collectors.toMap(PhysicalResourcePO::getPhysicalResourceCode, Function.identity(), (v1, v2) -> v1));


        List<ProductCandidateResourcePO> oldPos =
                newProductCandidateResourceDao.selectByProductIdAndPhysicalResourceId(list);

        Map<String, ProductCandidateResourcePO> oldPosMap = CollectionUtils.isEmpty(oldPos) ?
                MapUtil.newHashMap() :
                oldPos.stream().collect(Collectors.toMap(t -> t.getProductId() + "_" + t.getPhysicalResourceId()+"_"+t.getStandardStepId(),
                        Function.identity(), (v1, v2) -> v1));
        //维护需要转换的候选资源id
        List<String> routingStepResourceIds = new ArrayList<>();
        //TODO 产品资源关系整合到生产节拍的表中，暂时预留代码
        // Date date = new Date();
        // Calendar calendar = Calendar.getInstance();
        // calendar.setTime(date);
        //
        // // 设置日期为1号
        // calendar.set(Calendar.DAY_OF_MONTH, 1);
        //
        // // 去掉时分秒
        // calendar.set(Calendar.HOUR_OF_DAY, 0);
        // calendar.set(Calendar.MINUTE, 0);
        // calendar.set(Calendar.SECOND, 0);
        // calendar.set(Calendar.MILLISECOND, 0);
        //
        // Date firstDayOfMonth = calendar.getTime();
        // List<ProductCandidateResourceTimeDTO> insertTimeDto = new ArrayList<>();
        // List<ProductCandidateResourceTimeDTO> updateTimeDto = new ArrayList<>();

        // List<NewRoutingPO> newRoutingPOS = newRoutingDao.selectStockPointIdRoutingCode(list);
        // Map<String, NewRoutingPO> newRoutingPOSMap = CollectionUtils.isEmpty(newRoutingPOS) ?
        //         MapUtil.newHashMap() :
        //         newRoutingPOS.stream().collect(Collectors.toMap(t -> t.getStockPointId() + "_" + t.getRoutingCode() ,
        //                 Function.identity(), (v1, v2) -> v1));
        // List<NewRoutingStepPO> newRoutingStepPOS = newRoutingStepDao.selectRoutingIdSequenceNo(list);
        // Map<String, NewRoutingStepPO>newRoutingStepPOSMap = CollectionUtils.isEmpty(newRoutingStepPOS) ?
        //         MapUtil.newHashMap() :
        //         newRoutingStepPOS.stream().collect(Collectors.toMap(t -> t.getRoutingId() + "_" + t.getSequenceNo() ,
        //                 Function.identity(), (v1, v2) -> v1));
        //  List<NewRoutingStepResourcePO> newRoutingStepResourcePOS =
        //          newRoutingStepResourceDao.selectByParams( MapUtil.newHashMap());
        // Map<String, NewRoutingStepResourcePO>newRoutingStepResourcePOSMap = CollectionUtils.isEmpty(newRoutingStepResourcePOS) ?
        //         MapUtil.newHashMap() :
        //         newRoutingStepResourcePOS.stream().collect(Collectors.toMap(t -> t.getRoutingId() + "_" + t.getRoutingStepId() + "_" + t.getStandardResourceId() + "_" + t.getPhysicalResourceId() ,
        //                 Function.identity(), (v1, v2) -> v1));
        // Set<String> candidateResourceIds =
        //         newRoutingStepResourcePOS.stream().map(NewRoutingStepResourcePO::getId).collect(Collectors.toSet());
        // HashMap<String, Object> mapProductCandidateResourceTime = MapUtil.newHashMap(3);
        // mapProductCandidateResourceTime.put("candidateResourceIds", candidateResourceIds);
        // List<ProductCandidateResourceTimePO> oldTimePos =
        //         productCandidateResourceTimeDao.selectByParams(mapProductCandidateResourceTime);
        // Map<String, ProductCandidateResourceTimePO> oldTimePosMap = CollectionUtils.isEmpty(oldTimePos) ?
        //         MapUtil.newHashMap() :
        //         oldTimePos.stream().collect(Collectors.toMap(ProductCandidateResourceTimePO::getCandidateResourceId, Function.identity(),
        //                 (v1, v2) -> v1));
        for (MesMoldChangeTime mesMoldChangeTime : list) {
            com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO dto = new com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO();


            String standardStepCodeId = mesMoldChangeTime.getPlantCode() + "_" + mesMoldChangeTime.getSequenceCode();
            if (!standardStepPOSMap.containsKey(standardStepCodeId)) {
                log.info("standardStepPOSMap:   stockPointCode " + mesMoldChangeTime.getPlantCode() + "standardStepCode" + mesMoldChangeTime.getSequenceCode() + "不存在");
                continue;
            }
            String standardStepId = standardStepPOSMap.get(standardStepCodeId).getId();
            if (StringUtils.isEmpty(mesMoldChangeTime.getProdLineCode())) {
                log.info("getProdLineCode:" + mesMoldChangeTime.getKid() + "不存在");
                continue;
            }

            if (!physicalResourcePOSMap.containsKey(mesMoldChangeTime.getProdLineCode())) {
                log.info("physicalResourcePOSMap:" + mesMoldChangeTime.getProdLineCode() + "不存在");
                continue;
            }
            String enabled = "N".equals(mesMoldChangeTime.getEnableFlag()) ? YesOrNoEnum.NO.getCode() :
                    YesOrNoEnum.YES.getCode();
            PhysicalResourcePO physicalResourcePO = physicalResourcePOSMap.get(mesMoldChangeTime.getProdLineCode());
            String physicalResourceId = physicalResourcePO.getId();
            String standardResourceId = physicalResourcePO.getStandardResourceId();

            // String newRoutingId = mesMoldChangeTime.getPlantCode() + "_" + mesMoldChangeTime.getItemCode();
            // if(newRoutingPOSMap.containsKey(newRoutingId)){
            //     NewRoutingPO newRoutingPO = newRoutingPOSMap.get(newRoutingId);
            //     String routingId = newRoutingPO.getId();
            //     String newRoutingStepId = routingId + "_" + mesMoldChangeTime.getSequenceCode();
            //     if (!newRoutingStepPOSMap.containsKey(newRoutingStepId)) {
            //         log.info(" newRoutingStepPOSMap:   stockPointCode " + mesMoldChangeTime.getPlantCode()+"standardStepCode"+mesMoldChangeTime.getSequenceCode() + "不存在");
            //         continue;
            //     }
            //     NewRoutingStepPO newRoutingStepPO = newRoutingStepPOSMap.get(newRoutingStepId);
            //     String routingStepId = newRoutingStepPO.getId();
            //     String routingStepResourceId =
            //             routingId + "_" + routingStepId+"_" + standardResourceId+"_" + physicalResourceId;
            //     log.info("routingStepResourceId: " + routingStepResourceId);
            //     if(newRoutingStepResourcePOSMap.containsKey(routingStepResourceId)){
            //         ProductCandidateResourceTimeDTO timeDTO =  new ProductCandidateResourceTimeDTO();
            //         NewRoutingStepResourcePO newRoutingStepResourcePO = newRoutingStepResourcePOSMap.get(routingStepResourceId);
            //         log.info("newRoutingStepResourcePO: " + newRoutingStepResourcePO.toString());
            //         String newRoutingStepResourceId = newRoutingStepResourcePO.getId();
            //         if (oldTimePosMap.containsKey(newRoutingStepResourceId)) {
            //             ProductCandidateResourceTimePO oldTimePo = oldTimePosMap.get(newRoutingStepResourceId);
            //             org.springframework.beans.BeanUtils.copyProperties(oldTimePo, timeDTO);
            //             if (mesMoldChangeTime.getPriorityNum() != null) {
            //                 timeDTO.setPriority(mesMoldChangeTime.getPriorityNum());
            //             }
            //             timeDTO.setEffectiveTime(firstDayOfMonth);
            //             timeDTO.setEnabled(enabled);
            //             updateTimeDto.add(timeDTO);
            //         }else {
            //             timeDTO.setCandidateResourceId(newRoutingStepResourceId);
            //             if (mesMoldChangeTime.getPriorityNum() != null) {
            //                 timeDTO.setPriority(mesMoldChangeTime.getPriorityNum());
            //             }
            //             timeDTO.setEffectiveTime(firstDayOfMonth);
            //             timeDTO.setEnabled(enabled);
            //             insertTimeDto.add(timeDTO);
            //         }
            //     }
            // }

            if (!newStockPointPOSMap.containsKey(mesMoldChangeTime.getPlantCode())) {
                log.info("newStockPointPOSMap:" + mesMoldChangeTime.getPlantCode() + "不存在");
                continue;
            }
            NewStockPointPO newStockPointPO = newStockPointPOSMap.get(mesMoldChangeTime.getPlantCode());
            String newStockPointId = newStockPointPO.getOrganizeId();
            String productStockPointCodeId = mesMoldChangeTime.getItemCode() + "_" + mesMoldChangeTime.getPlantCode();
            if (!productStockPointPOSMap.containsKey(productStockPointCodeId)) {
                log.info("productStockPointPOSMap:    itemCode" + mesMoldChangeTime.getItemCode() + "plantCode:" + mesMoldChangeTime.getPlantCode() + "不存在");
                continue;
            }
            String productStockPointId = productStockPointPOSMap.get(productStockPointCodeId).getId();

            String id = productStockPointId + "_" + physicalResourceId+"_"+standardStepId;
            if (oldPosMap.containsKey(id)) {
                ProductCandidateResourcePO oldPo = oldPosMap.get(id);
                org.springframework.beans.BeanUtils.copyProperties(oldPo, dto);
                generatePo(mesMoldChangeTime, dto, newStockPointId, productStockPointId, standardStepId, physicalResourceId, standardResourceId, enabled);
                updateDTOs.add(dto);
            } else {
                generatePo(mesMoldChangeTime, dto, newStockPointId, productStockPointId, standardStepId, physicalResourceId, standardResourceId, enabled);
                dto.setId(UUIDUtil.getUUID());
                dto.setSetupUnitBatchSize(new BigDecimal(1));
                dto.setProductionUnitBatchSize(new BigDecimal(1));
                dto.setCleanupUnitBatchSize(new BigDecimal(1));
                insertDTOs.add(dto);
            }
            routingStepResourceIds.add(dto.getId());
        }
        log.info(" insertDTOs: " + insertDTOs.size() + "  :updateDTOs:" + updateDTOs.size());
        if (CollectionUtils.isNotEmpty(insertDTOs)) {
            List<ProductCandidateResourcePO> newList = ProductCandidateResourceConvertor.INSTANCE.dto2Pos(insertDTOs);
            BasePOUtils.insertBatchFiller(newList);
            newProductCandidateResourceDao.insertBatchWithPrimaryKey(newList);
        }
        if (CollectionUtils.isNotEmpty(updateDTOs)) {
            productCandidateResourceService.doUpdateBatch(updateDTOs);
        }
        // if (CollectionUtils.isNotEmpty(insertTimeDto)) {
        //     insertTimeDto = insertTimeDto.stream().collect(Collectors.collectingAndThen(
        //             Collectors.toMap(
        //                     ProductCandidateResourceTimeDTO::getCandidateResourceId,
        //                     dto -> dto,
        //                     (existing, replacement) -> existing // 保留第一个出现的元素
        //             ),
        //             timeDTOMap -> new ArrayList<>(timeDTOMap.values())
        //     ));
        //     log.info(" 唯一值 insertTimeDto: " + insertTimeDto.size());
        //    doCreateBatch(insertTimeDto);;
        // }
        // if (CollectionUtils.isNotEmpty(updateTimeDto)) {
        //   doUpdateBatch(updateTimeDto);
        // }
        try {
            if (CollectionUtils.isNotEmpty(routingStepResourceIds)) {
                List<NewStockPointVO> stockPointVOList = newStockPointService.selectAll();
                //物品候选资源转产品工艺路径候选资源
                newRoutingStepResourceService.doTransitionRoutingStepResource(routingStepResourceIds, null ,null,
                        stockPointVOList, scenario);
            }
            Executor executor = Executors.newFixedThreadPool(5); // 根据需求调整线程数
            CompletableFuture.runAsync(() -> {
                try {
                    afterCandidateResourceOnProduct();
                } catch (Exception e) {
                    log.error("处理物品模具数量限制推移失败", e);
                }
            }, executor);
        } catch (Exception e) {
            log.info("物品候选资源转产品工艺路径候选资源报错:"+e.getMessage());
            // 先保障同步正常
            return BaseResponse.success("同步成功");
        }
        return BaseResponse.success("同步成功");
    }

    /**
     * 后处理物品模具数量限制
     */
    @Override
    public void afterCandidateResourceOnProduct() {
        List<ProductCandidateResourcePO> productCandidateResourcePOS = newProductCandidateResourceDao.selectByParams(new HashMap<>());
        List<String> productIds = productCandidateResourcePOS.stream().map(ProductCandidateResourcePO::getProductId).distinct().collect(Collectors.toList());
        Map<String, List<ProductCandidateResourcePO>> productCandidateResourceMap = productCandidateResourcePOS.stream().collect(Collectors.groupingBy(ProductCandidateResourcePO::getProductId));
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByParams(ImmutableMap.of("ids", productIds));
        // 物理资源
        List<MoldToolingVO> moldToolingVOS = moldToolingService.selectAll();
        Map<String, MoldToolingVO> moldToolingVOMap = StreamUtils.mapByColumn(moldToolingVOS, MoldToolingVO::getId);
        List<NewProductStockPointPO> updateQuantityList = new ArrayList<>();
        for (NewProductStockPointPO newProductStockPointPO : newProductStockPointPOS) {
            if (!productCandidateResourceMap.containsKey(newProductStockPointPO.getId())) {
                continue;
            }
            Set<String> quantity = new HashSet<>();
            List<ProductCandidateResourcePO> productCandidateResource = productCandidateResourceMap.get(newProductStockPointPO.getId())
                    .stream().filter(p -> StrUtil.isEmpty(p.getStandardStepId())).collect(Collectors.toList());
            for (ProductCandidateResourcePO productCandidateResourcePO : productCandidateResource) {
                String id = productCandidateResourcePO.getPhysicalResourceId();
                if (!moldToolingVOMap.containsKey(id)) {
                    continue;
                }
                MoldToolingVO moldToolingVO = moldToolingVOMap.get(id);
                String resourceType = moldToolingVO.getResourceCategory();
                if (StrUtil.isNotEmpty(resourceType) && resourceType.equals("TOOL")) {
                    quantity.add(moldToolingVO.getPhysicalResourceCode());
                }
            }
            newProductStockPointPO.setMoldQuantityLimit(quantity.size());
            updateQuantityList.add(newProductStockPointPO);
        }
        List<NewProductStockPointDTO> newProductStockPointDTOS = NewProductStockPointConvertor.INSTANCE.po2Dtos(updateQuantityList);
        if (CollectionUtils.isNotEmpty(newProductStockPointDTOS)) {
            log.info("修改物品模具数量限制数量行数：{}", newProductStockPointDTOS.size());
            Lists.partition(newProductStockPointDTOS, 1000).forEach(list -> newProductStockPointService.doUpdateBatch(list));
        }
    }

    private void generatePo(MesMoldChangeTime mesMoldChangeTime, com.yhl.scp.mds.routing.dto.ProductCandidateResourceDTO dto,
                            String newStockPointId, String productStockPointId, String standardStepId, String physicalResourceId, String standardResourceId, String enabled) {
        dto.setStockPointId(newStockPointId);
        dto.setProductId(productStockPointId);
        dto.setStandardStepId(standardStepId);
        dto.setStandardResourceId(standardResourceId);
        dto.setPhysicalResourceId(physicalResourceId);
        dto.setKid(mesMoldChangeTime.getKid());
        if (StringUtils.isEmpty(mesMoldChangeTime.getBeat()) || "0".equals(mesMoldChangeTime.getBeat())) {
            dto.setUnitProductionTime(BigDecimal.ONE);
        } else {
            try {
                dto.setUnitProductionTime(new BigDecimal(mesMoldChangeTime.getBeat().trim()));
            } catch (NumberFormatException e) {
                // 处理转换异常
                System.err.println("Invalid number format: " + mesMoldChangeTime.getBeat());
                //   dto.setUnitProductionTime(BigDecimal.ZERO); // 或者设置其他默认值
            }
        }
        if (mesMoldChangeTime.getPriorityNum() != null) {
            dto.setPriority(mesMoldChangeTime.getPriorityNum());
        }
        dto.setEnabled(enabled);
    }

    @Override
    public BaseResponse<Void> syncMoldChangeTime(String tenantId) {
        Map<String, Object> params = MapUtil.newHashMap(3);
        try{
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.NEW_PRODUCT_CANDIDATE_RESOURCE.getCode(), params);
        } catch (RetryableException e) {
           log.info("RetryableException: " + e.getMessage());
           log.info("Status: " + e.status());
           log.info("Content: " + e.contentUTF8());
            return BaseResponse.error("同步失败: " + e.getMessage());
        } catch (FeignException e) {
           log.info("FeignException: " + e.getMessage());
           log.info("Status: " + e.status());
           log.info("Content: " + e.contentUTF8());
            return BaseResponse.error("同步失败: " + e.getMessage());
        } catch (Exception e) {
           log.info("其他异常: " + e.getMessage());
            e.printStackTrace();
            return BaseResponse.error("同步失败: " + e.getMessage());
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PRODUCT_CANDIDATE_RESOURCE_TIME.getCode();
    }

    @Override
    public List<ProductCandidateResourceTimeVO> invocation(List<ProductCandidateResourceTimeVO> dataList, Map<String, Object> params, String invocation) {

        return dataList;
    }

    @Override
    public List<ProductCandidateResourceTimeVO> selectByProductCode(List<String> productCodeList) {
        return productCandidateResourceTimeDao.selectByProductCode(productCodeList);
    }
}
