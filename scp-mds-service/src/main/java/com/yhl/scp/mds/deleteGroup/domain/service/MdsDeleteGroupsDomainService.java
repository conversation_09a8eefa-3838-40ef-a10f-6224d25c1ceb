package com.yhl.scp.mds.deleteGroup.domain.service;

import com.yhl.scp.mds.deleteGroup.domain.entity.MdsDeleteGroupsDO;
import com.yhl.scp.mds.deleteGroup.infrastructure.dao.MdsDeleteGroupsDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <code>MdsDeleteGroupsDomainService</code>
 * <p>
 * ERP删除组接口中间表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 20:24:53
 */
@Service
public class MdsDeleteGroupsDomainService {

    @Resource
    private MdsDeleteGroupsDao mdsDeleteGroupsDao;

    /**
     * 数据校验
     *
     * @param mdsDeleteGroupsDO 领域对象
     */
    public void validation(MdsDeleteGroupsDO mdsDeleteGroupsDO) {
        checkNotNull(mdsDeleteGroupsDO);
        checkUniqueCode(mdsDeleteGroupsDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param mdsDeleteGroupsDO 领域对象
     */
    private void checkNotNull(MdsDeleteGroupsDO mdsDeleteGroupsDO) {
//        if (StringUtils.isBlank(mdsDeleteGroupsDO.getMdsDeleteGroupsCode())) {
//            throw new BusinessException("ERP删除组接口中间表代码，不能为空");
//        }
//        if (StringUtils.isBlank(mdsDeleteGroupsDO.getMdsDeleteGroupsName())) {
//            throw new BusinessException("ERP删除组接口中间表名称，不能为空");
//        }
    }

    /**
     * 唯一性校验
     *
     * @param mdsDeleteGroupsDO 领域对象
     */
    private void checkUniqueCode(MdsDeleteGroupsDO mdsDeleteGroupsDO) {
//        Map<String, Object> params = new HashMap<>(4);
//        params.put("mdsDeleteGroupsCode", mdsDeleteGroupsDO.getMdsDeleteGroupsCode());
//        if (StringUtils.isBlank(mdsDeleteGroupsDO.getId())) {
//            List<MdsDeleteGroupsPO> list = mdsDeleteGroupsDao.selectByParams(params);
//            if (CollectionUtils.isNotEmpty(list)) {
//                throw new BusinessException("新增失败，ERP删除组接口中间表代码已存在：" + mdsDeleteGroupsDO.getMdsDeleteGroupsCode());
//            }
//        } else {
//            MdsDeleteGroupsPO old = mdsDeleteGroupsDao.selectByPrimaryKey(mdsDeleteGroupsDO.getId());
//            if (!mdsDeleteGroupsDO.getMdsDeleteGroupsCode().equals(old.getMdsDeleteGroupsCode())) {
//                List<MdsDeleteGroupsPO> list = mdsDeleteGroupsDao.selectByParams(params);
//                if (CollectionUtils.isNotEmpty(list)) {
//                    throw new BusinessException("修改失败，ERP删除组接口中间表代码已存在：" + mdsDeleteGroupsDO.getMdsDeleteGroupsCode());
//                }
//            }
//        }
    }

}
