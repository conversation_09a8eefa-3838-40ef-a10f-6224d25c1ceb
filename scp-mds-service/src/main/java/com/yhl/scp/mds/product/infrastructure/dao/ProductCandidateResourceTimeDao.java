package com.yhl.scp.mds.product.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.product.infrastructure.po.ProductCandidateResourceTimePO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import org.apache.ibatis.annotations.Param;

import javax.xml.crypto.Data;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ProductCandidateResourceTimeDao</code>
 * <p>
 * 产品资源生产关系时段优先级表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 10:03:09
 */
public interface ProductCandidateResourceTimeDao extends BaseDao<ProductCandidateResourceTimePO, ProductCandidateResourceTimeVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link ProductCandidateResourceTimeVO}
     */
    List<ProductCandidateResourceTimeVO> selectVOByParams(@Param("params") Map<String, Object> params);

    List<ProductCandidateResourceTimeVO> selectByCandidateResourceId(@Param("candidateResourceIdList") List<String> candidateResourceIdList);


    /**
     * 根据候选资源id删除数据
     *
     * @param candidateResourceIdList 候选资源id集合
     */
    void deleteByCandidateResourceId(@Param("candidateResourceIdList") List<String> candidateResourceIdList);

    List<StandardResourceVO> selectResourceAndOperation();

    List<ProductCandidateResourceTimeVO> selectListByOperationCodeAndEffectiveTime(@Param("operationCode") String operationCode, @Param("effectiveTime") String effectiveTime);

    void deleteByStartAndEndTime(@Param("startTime")Date startTime, @Param("endTime")Date endTime);

    List<ProductCandidateResourceTimeVO> selectByProductCode(@Param("productCodeList") List<String> productCodeList);
}
