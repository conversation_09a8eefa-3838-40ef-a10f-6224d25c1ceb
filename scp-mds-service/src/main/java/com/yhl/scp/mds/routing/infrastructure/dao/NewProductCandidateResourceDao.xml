<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.routing.infrastructure.dao.NewProductCandidateResourceDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO">
        <!--@Table mds_rou_product_candidate_resource-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="product_type" jdbcType="VARCHAR" property="productType"/>
        <result column="product_series_id" jdbcType="VARCHAR" property="productSeriesId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="kid" jdbcType="VARCHAR" property="kid"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="setup_duration" jdbcType="INTEGER" property="setupDuration"/>
        <result column="fixed_work_hours" jdbcType="INTEGER" property="fixedWorkHours"/>
        <result column="units_per_hour" jdbcType="VARCHAR" property="unitsPerHour"/>
        <result column="unit_production_time" jdbcType="VARCHAR" property="unitProductionTime"/>
        <result column="cleanup_duration" jdbcType="INTEGER" property="cleanupDuration"/>
        <result column="alt_tool_code" jdbcType="VARCHAR" property="altToolCode"/>
        <result column="match_code" jdbcType="VARCHAR" property="matchCode"/>
        <result column="max_lot_size" jdbcType="INTEGER" property="maxLotSize"/>
        <result column="min_lot_size" jdbcType="INTEGER" property="minLotSize"/>
        <result column="setup_unit_batch_size" jdbcType="VARCHAR" property="setupUnitBatchSize"/>
        <result column="production_unit_batch_size" jdbcType="VARCHAR" property="productionUnitBatchSize"/>
        <result column="cleanup_unit_batch_size" jdbcType="VARCHAR" property="cleanupUnitBatchSize"/>
        <result column="max_setup_suspend_duration" jdbcType="INTEGER" property="maxSetupSuspendDuration"/>
        <result column="max_production_suspend_duration" jdbcType="INTEGER" property="maxProductionSuspendDuration"/>
        <result column="max_cleanup_suspend_duration" jdbcType="INTEGER" property="maxCleanupSuspendDuration"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO">
    	<result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
		<result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
		<result column="series_code" jdbcType="VARCHAR" property="seriesCode"/>
		<result column="series_name" jdbcType="VARCHAR" property="seriesName"/>
		<result column="product_code" jdbcType="VARCHAR" property="productCode"/>
		<result column="product_name" jdbcType="VARCHAR" property="productName"/>
		<result column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
		<result column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
		<result column="standard_resource_code" jdbcType="VARCHAR" property="standardResourceCode"/>
		<result column="standard_resource_name" jdbcType="VARCHAR" property="standardResourceName"/>
		<result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
		<result column="physical_resource_name" jdbcType="VARCHAR" property="physicalResourceName"/>
        <result column="mold_quantity_limit" jdbcType="DECIMAL" property="moldQuantityLimit"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,
		stock_point_id,
		product_type,
		product_series_id,
		product_id,
		standard_step_id,
		standard_resource_id,
		physical_resource_id,
		kid,
		priority,
		setup_duration,
		fixed_work_hours,
		units_per_hour,
		unit_production_time,
		cleanup_duration,
		alt_tool_code,
		match_code,
		max_lot_size,
		min_lot_size,
		setup_unit_batch_size,
		production_unit_batch_size,
		cleanup_unit_batch_size,
		max_setup_suspend_duration,
		max_production_suspend_duration,
		max_cleanup_suspend_duration,
		effective_time,
		expiry_time,
		remark,
		enabled,
		creator,
		create_time,
		modifier,
		modify_time
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List" />,
        stock_point_code,
        stock_point_name,
        series_code,
        series_name,
        product_code,
        product_name,
        standard_step_code,
        standard_step_name,
        standard_resource_code,
        standard_resource_name,
        physical_resource_code,
        physical_resource_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointIds != null and params.stockPointIds.size > 0">
                and stock_point_id in
                <foreach collection="params.stockPointIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productType != null and params.productType != ''">
                and product_type = #{params.productType,jdbcType=VARCHAR}
            </if>
            <if test="params.productTypes != null and params.productTypes.size > 0">
                and product_type in
                <foreach collection="params.productTypes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productSeriesId != null and params.productSeriesId != ''">
                and product_series_id = #{params.productSeriesId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productIds != null and params.productIds.size > 0">
                and product_id in
                <foreach collection="params.productIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and standard_step_id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardStepIds != null and params.standardStepIds.size > 0">
                and standard_step_id in
                <foreach collection="params.standardStepIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.standardResourceId != null and params.standardResourceId != ''">
                and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceIds != null and params.standardResourceIds.size > 0">
                and standard_resource_id in
                <foreach collection="params.standardResourceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
                and physical_resource_id = #{params.physicalResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceIds != null and params.physicalResourceIds.size > 0">
                and physical_resource_id in
                <foreach collection="params.physicalResourceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.kid != null and params.kid != ''">
                and kid = #{params.kid,jdbcType=VARCHAR}
            </if>
            <if test="params.kids != null and params.kids != ''">
                and kid in
                <foreach collection="params.kids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.setupDuration != null">
                and setup_duration = #{params.setupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.fixedWorkHours != null">
                and fixed_work_hours = #{params.fixedWorkHours,jdbcType=INTEGER}
            </if>
            <if test="params.unitsPerHour != null">
                and units_per_hour = #{params.unitsPerHour,jdbcType=VARCHAR}
            </if>
            <if test="params.unitProductionTime != null">
                and unit_production_time = #{params.unitProductionTime,jdbcType=VARCHAR}
            </if>
            <if test="params.cleanupDuration != null">
                and cleanup_duration = #{params.cleanupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.altToolCode != null and params.altToolCode != ''">
                and alt_tool_code = #{params.altToolCode,jdbcType=VARCHAR}
            </if>
            <if test="params.matchCode != null and params.matchCode != ''">
                and match_code = #{params.matchCode,jdbcType=VARCHAR}
            </if>
            <if test="params.maxLotSize != null">
                and max_lot_size = #{params.maxLotSize,jdbcType=INTEGER}
            </if>
            <if test="params.minLotSize != null">
                and min_lot_size = #{params.minLotSize,jdbcType=INTEGER}
            </if>
            <if test="params.setupUnitBatchSize != null">
                and setup_unit_batch_size = #{params.setupUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.productionUnitBatchSize != null">
                and production_unit_batch_size = #{params.productionUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.cleanupUnitBatchSize != null">
                and cleanup_unit_batch_size = #{params.cleanupUnitBatchSize,jdbcType=VARCHAR}
            </if>
            <if test="params.maxSetupSuspendDuration != null">
                and max_setup_suspend_duration = #{params.maxSetupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxProductionSuspendDuration != null">
                and max_production_suspend_duration = #{params.maxProductionSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxCleanupSuspendDuration != null">
                and max_cleanup_suspend_duration = #{params.maxCleanupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_candidate_resource
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_candidate_resource
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_candidate_resource
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_rou_product_candidate_resource
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mds_rou_product_candidate_resource
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectMoldQuantityLimit" resultMap="VOResultMap">
        SELECT
            mrpcr.stock_point_id,
            mrpcr.product_id,
            mrpcr.standard_step_code,
            count( 1 ) as mold_quantity_limit
        FROM
            v_mds_rou_product_candidate_resource mrpcr,
            mds_res_physical_resource mrpr
        WHERE
            mrpcr.physical_resource_id = mrpr.id
          AND mrpr.resource_category = 'TOOL'
        GROUP BY
            mrpcr.stock_point_id,
            mrpcr.product_id,
            mrpcr.standard_step_code
    </select>
    <select id="selectByProductIdAndPhysicalResourceId" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from v_mds_rou_product_candidate_resource
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                stock_point_code = #{item.plantCode,jdbcType=VARCHAR} and product_code = #{item.itemCode,jdbcType=VARCHAR}
                and physical_resource_code = #{item.prodLineCode,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectProductOrganizeProductId" resultType="java.lang.String">
        SELECT DISTINCT
            psp.id
        FROM
            mds_product_stock_point psp
                LEFT JOIN mds_stock_point sp ON psp.stock_point_code = sp.stock_point_code
        WHERE
            sp.id = #{stockPointId,jdbcType=VARCHAR}
          AND product_code = (SELECT product_code FROM mds_product_stock_point
                                                  WHERE id = #{productId,jdbcType=VARCHAR})
        LIMIT 1
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_rou_product_candidate_resource(
        id,
        stock_point_id,
        product_type,
        product_series_id,
        product_id,
        standard_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        priority,
        setup_duration,
        fixed_work_hours,
        units_per_hour,
        unit_production_time,
        cleanup_duration,
        alt_tool_code,
        match_code,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_setup_suspend_duration,
        max_production_suspend_duration,
        max_cleanup_suspend_duration,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{productSeriesId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{standardStepId,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{setupDuration,jdbcType=INTEGER},
        #{fixedWorkHours,jdbcType=INTEGER},
        #{unitsPerHour,jdbcType=VARCHAR},
        #{unitProductionTime,jdbcType=VARCHAR},
        #{cleanupDuration,jdbcType=INTEGER},
        #{altToolCode,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{maxLotSize,jdbcType=INTEGER},
        #{minLotSize,jdbcType=INTEGER},
        #{setupUnitBatchSize,jdbcType=VARCHAR},
        #{productionUnitBatchSize,jdbcType=VARCHAR},
        #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO">
        insert into mds_rou_product_candidate_resource(
        id,
        stock_point_id,
        product_type,
        product_series_id,
        product_id,
        standard_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        priority,
        setup_duration,
        fixed_work_hours,
        units_per_hour,
        unit_production_time,
        cleanup_duration,
        alt_tool_code,
        match_code,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_setup_suspend_duration,
        max_production_suspend_duration,
        max_cleanup_suspend_duration,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{productType,jdbcType=VARCHAR},
        #{productSeriesId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{standardStepId,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{kid,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{setupDuration,jdbcType=INTEGER},
        #{fixedWorkHours,jdbcType=INTEGER},
        #{unitsPerHour,jdbcType=VARCHAR},
        #{unitProductionTime,jdbcType=VARCHAR},
        #{cleanupDuration,jdbcType=INTEGER},
        #{altToolCode,jdbcType=VARCHAR},
        #{matchCode,jdbcType=VARCHAR},
        #{maxLotSize,jdbcType=INTEGER},
        #{minLotSize,jdbcType=INTEGER},
        #{setupUnitBatchSize,jdbcType=VARCHAR},
        #{productionUnitBatchSize,jdbcType=VARCHAR},
        #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_rou_product_candidate_resource(
        id,
        stock_point_id,
        product_type,
        product_series_id,
        product_id,
        standard_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        priority,
        setup_duration,
        fixed_work_hours,
        units_per_hour,
        unit_production_time,
        cleanup_duration,
        alt_tool_code,
        match_code,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_setup_suspend_duration,
        max_production_suspend_duration,
        max_cleanup_suspend_duration,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productType,jdbcType=VARCHAR},
        #{entity.productSeriesId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.fixedWorkHours,jdbcType=INTEGER},
        #{entity.unitsPerHour,jdbcType=VARCHAR},
        #{entity.unitProductionTime,jdbcType=VARCHAR},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.altToolCode,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.maxLotSize,jdbcType=INTEGER},
        #{entity.minLotSize,jdbcType=INTEGER},
        #{entity.setupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.productionUnitBatchSize,jdbcType=VARCHAR},
        #{entity.cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_rou_product_candidate_resource(
        id,
        stock_point_id,
        product_type,
        product_series_id,
        product_id,
        standard_step_id,
        standard_resource_id,
        physical_resource_id,
        kid,
        priority,
        setup_duration,
        fixed_work_hours,
        units_per_hour,
        unit_production_time,
        cleanup_duration,
        alt_tool_code,
        match_code,
        max_lot_size,
        min_lot_size,
        setup_unit_batch_size,
        production_unit_batch_size,
        cleanup_unit_batch_size,
        max_setup_suspend_duration,
        max_production_suspend_duration,
        max_cleanup_suspend_duration,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.productType,jdbcType=VARCHAR},
        #{entity.productSeriesId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceId,jdbcType=VARCHAR},
        #{entity.kid,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.fixedWorkHours,jdbcType=INTEGER},
        #{entity.unitsPerHour,jdbcType=VARCHAR},
        #{entity.unitProductionTime,jdbcType=VARCHAR},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.altToolCode,jdbcType=VARCHAR},
        #{entity.matchCode,jdbcType=VARCHAR},
        #{entity.maxLotSize,jdbcType=INTEGER},
        #{entity.minLotSize,jdbcType=INTEGER},
        #{entity.setupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.productionUnitBatchSize,jdbcType=VARCHAR},
        #{entity.cleanupUnitBatchSize,jdbcType=VARCHAR},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO">
        update mds_rou_product_candidate_resource set
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        product_type = #{productType,jdbcType=VARCHAR},
        product_series_id = #{productSeriesId,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        standard_step_id = #{standardStepId,jdbcType=VARCHAR},
        standard_resource_id = #{standardResourceId,jdbcType=VARCHAR},
        physical_resource_id = #{physicalResourceId,jdbcType=VARCHAR},
        kid = #{kid,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=INTEGER},
        setup_duration = #{setupDuration,jdbcType=INTEGER},
        fixed_work_hours = #{fixedWorkHours,jdbcType=INTEGER},
        units_per_hour = #{unitsPerHour,jdbcType=VARCHAR},
        unit_production_time = #{unitProductionTime,jdbcType=VARCHAR},
        cleanup_duration = #{cleanupDuration,jdbcType=INTEGER},
        alt_tool_code = #{altToolCode,jdbcType=VARCHAR},
        match_code = #{matchCode,jdbcType=VARCHAR},
        max_lot_size = #{maxLotSize,jdbcType=INTEGER},
        min_lot_size = #{minLotSize,jdbcType=INTEGER},
        setup_unit_batch_size = #{setupUnitBatchSize,jdbcType=VARCHAR},
        production_unit_batch_size = #{productionUnitBatchSize,jdbcType=VARCHAR},
        cleanup_unit_batch_size = #{cleanupUnitBatchSize,jdbcType=VARCHAR},
        max_setup_suspend_duration = #{maxSetupSuspendDuration,jdbcType=INTEGER},
        max_production_suspend_duration = #{maxProductionSuspendDuration,jdbcType=INTEGER},
        max_cleanup_suspend_duration = #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO">
        update mds_rou_product_candidate_resource
        <set>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.productSeriesId != null and item.productSeriesId != ''">
                product_series_id = #{item.productSeriesId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.fixedWorkHours != null">
                fixed_work_hours = #{item.fixedWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.unitsPerHour != null">
                units_per_hour = #{item.unitsPerHour,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionTime != null">
                unit_production_time = #{item.unitProductionTime,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.altToolCode != null and item.altToolCode != ''">
                alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.minLotSize != null">
                min_lot_size = #{item.minLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.setupUnitBatchSize != null">
                setup_unit_batch_size = #{item.setupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.productionUnitBatchSize != null">
                production_unit_batch_size = #{item.productionUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupUnitBatchSize != null">
                cleanup_unit_batch_size = #{item.cleanupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_rou_product_candidate_resource
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_series_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSeriesId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.kid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="setup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="fixed_work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.fixedWorkHours,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="units_per_hour = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitsPerHour,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="unit_production_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.unitProductionTime,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cleanup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="alt_tool_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.altToolCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="match_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.matchCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxLotSize,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minLotSize,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="setup_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cleanup_unit_batch_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupUnitBatchSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_setup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxSetupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_production_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxProductionSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_cleanup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxCleanupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_rou_product_candidate_resource 
        <set>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productType != null and item.productType != ''">
                product_type = #{item.productType,jdbcType=VARCHAR},
            </if>
            <if test="item.productSeriesId != null and item.productSeriesId != ''">
                product_series_id = #{item.productSeriesId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.kid != null and item.kid != ''">
                kid = #{item.kid,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.fixedWorkHours != null">
                fixed_work_hours = #{item.fixedWorkHours,jdbcType=INTEGER},
            </if>
            <if test="item.unitsPerHour != null">
                units_per_hour = #{item.unitsPerHour,jdbcType=VARCHAR},
            </if>
            <if test="item.unitProductionTime != null">
                unit_production_time = #{item.unitProductionTime,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.altToolCode != null and item.altToolCode != ''">
                alt_tool_code = #{item.altToolCode,jdbcType=VARCHAR},
            </if>
            <if test="item.matchCode != null and item.matchCode != ''">
                match_code = #{item.matchCode,jdbcType=VARCHAR},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.minLotSize != null">
                min_lot_size = #{item.minLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.setupUnitBatchSize != null">
                setup_unit_batch_size = #{item.setupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.productionUnitBatchSize != null">
                production_unit_batch_size = #{item.productionUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupUnitBatchSize != null">
                cleanup_unit_batch_size = #{item.cleanupUnitBatchSize,jdbcType=VARCHAR},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_rou_product_candidate_resource where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_rou_product_candidate_resource where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    
    <update id="doLogicDelete" parameterType="java.util.List">
        update mds_rou_product_candidate_resource 
        set enabled = 'NO'
        where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </update>
</mapper>
