<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.baseResource.infrastructure.dao.PhysicalResource2Dao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO">
        <!--@Table mds_res_physical_resource-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="standard_resource_id" jdbcType="VARCHAR" property="standardResourceId"/>
        <result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
        <result column="physical_resource_name" jdbcType="VARCHAR" property="physicalResourceName"/>
        <result column="resource_category" jdbcType="VARCHAR" property="resourceCategory"/>
        <result column="resource_type" jdbcType="VARCHAR" property="resourceType"/>
        <result column="resource_classification" jdbcType="VARCHAR" property="resourceClassification"/>
        <result column="subtask_type" jdbcType="VARCHAR" property="subtaskType"/>
        <result column="assign_quantity_type" jdbcType="VARCHAR" property="assignQuantityType"/>
        <result column="display_index" jdbcType="INTEGER" property="displayIndex"/>
        <result column="bottleneck" jdbcType="VARCHAR" property="bottleneck"/>
        <result column="infinite_capacity" jdbcType="VARCHAR" property="infiniteCapacity"/>
        <result column="sequence_code" jdbcType="VARCHAR" property="sequenceCode"/>
        <result column="variable_work_hours" jdbcType="VARCHAR" property="variableWorkHours"/>
        <result column="resource_quantity_coefficient" jdbcType="VARCHAR" property="resourceQuantityCoefficient"/>
        <result column="counting_unit_id" jdbcType="VARCHAR" property="countingUnitId"/>
        <result column="production_efficiency" jdbcType="VARCHAR" property="productionEfficiency"/>
        <result column="setup_efficiency" jdbcType="VARCHAR" property="setupEfficiency"/>
        <result column="cleanup_efficiency" jdbcType="VARCHAR" property="cleanupEfficiency"/>
        <result column="setup_duration" jdbcType="INTEGER" property="setupDuration"/>
        <result column="cleanup_duration" jdbcType="INTEGER" property="cleanupDuration"/>
        <result column="buffer_time_before" jdbcType="INTEGER" property="bufferTimeBefore"/>
        <result column="buffer_time_after" jdbcType="INTEGER" property="bufferTimeAfter"/>
        <result column="max_production_suspend_duration" jdbcType="INTEGER" property="maxProductionSuspendDuration"/>
        <result column="max_setup_suspend_duration" jdbcType="INTEGER" property="maxSetupSuspendDuration"/>
        <result column="max_cleanup_suspend_duration" jdbcType="INTEGER" property="maxCleanupSuspendDuration"/>
        <result column="production_line" jdbcType="VARCHAR" property="productionLine"/>
        <result column="strict_production_line_constraints" jdbcType="VARCHAR" property="strictProductionLineConstraints"/>
        <result column="no_buffer_action_type" jdbcType="VARCHAR" property="noBufferActionType"/>
        <result column="no_buffer_action_duration" jdbcType="INTEGER" property="noBufferActionDuration"/>
        <result column="lot_size" jdbcType="INTEGER" property="lotSize"/>
        <result column="max_lot_size" jdbcType="INTEGER" property="maxLotSize"/>
        <result column="production_date_last_num_change_unit" jdbcType="VARCHAR" property="productionDateLastNumChangeUnit"/>
        <result column="production_time_last_num_change_unit" jdbcType="VARCHAR" property="productionTimeLastNumChangeUnit"/>
        <result column="production_duration_logic" jdbcType="VARCHAR" property="productionDurationLogic"/>
        <result column="setup_and_cleanup_duration_logic" jdbcType="VARCHAR" property="setupAndCleanupDurationLogic"/>
        <result column="dynamic_setup_and_cleanup_duration_logic" jdbcType="VARCHAR" property="dynamicSetupAndCleanupDurationLogic"/>
        <result column="changeover_duration_logic" jdbcType="VARCHAR" property="changeoverDurationLogic"/>
        <result column="dynamic_changeover_duration_logic" jdbcType="VARCHAR" property="dynamicChangeoverDurationLogic"/>
        <result column="effective_time" jdbcType="TIMESTAMP" property="effectiveTime"/>
        <result column="expiry_time" jdbcType="TIMESTAMP" property="expiryTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.baseResource.vo.PhysicalResourceVO">
        <!-- TODO -->
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
    </resultMap>
    <sql id="Base_Column_List">
id,standard_resource_id,physical_resource_code,physical_resource_name,resource_category,resource_type,resource_classification,subtask_type,assign_quantity_type,display_index,bottleneck,infinite_capacity,variable_work_hours,resource_quantity_coefficient,counting_unit_id,production_efficiency,setup_efficiency,cleanup_efficiency,setup_duration,cleanup_duration,sequence_code,buffer_time_before,buffer_time_after,max_production_suspend_duration,max_setup_suspend_duration,max_cleanup_suspend_duration,production_line,strict_production_line_constraints,no_buffer_action_type,no_buffer_action_duration,lot_size,max_lot_size,production_date_last_num_change_unit,production_time_last_num_change_unit,production_duration_logic,setup_and_cleanup_duration_logic,dynamic_setup_and_cleanup_duration_logic,changeover_duration_logic,dynamic_changeover_duration_logic,effective_time,expiry_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceId != null and params.standardResourceId != ''">
                and standard_resource_id = #{params.standardResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceIdNull != null and params.standardResourceIdNull != ''">
                and standard_resource_id is null
            </if>
            <if test="params.physicalResourceCode != null and params.physicalResourceCode != ''">
                and physical_resource_code = #{params.physicalResourceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceCodes != null and params.physicalResourceCodes != ''">
                and physical_resource_code in
                <foreach collection="params.physicalResourceCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.standardResourceIds != null and params.standardResourceIds != ''">
                and standard_resource_id in
                <foreach collection="params.standardResourceIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.physicalResourceCodes != null and params.physicalResourceCodes != ''">
                and physical_resource_code in
                <foreach collection="params.physicalResourceCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.physicalResourceName != null and params.physicalResourceName != ''">
                and physical_resource_name = #{params.physicalResourceName,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceCategory != null and params.resourceCategory != ''">
                and resource_category = #{params.resourceCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceType != null and params.resourceType != ''">
                and resource_type = #{params.resourceType,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceClassification != null and params.resourceClassification != ''">
                and resource_classification = #{params.resourceClassification,jdbcType=VARCHAR}
            </if>
            <if test="params.subtaskType != null and params.subtaskType != ''">
                and subtask_type = #{params.subtaskType,jdbcType=VARCHAR}
            </if>
            <if test="params.assignQuantityType != null and params.assignQuantityType != ''">
                and assign_quantity_type = #{params.assignQuantityType,jdbcType=VARCHAR}
            </if>
            <if test="params.displayIndex != null">
                and display_index = #{params.displayIndex,jdbcType=INTEGER}
            </if>
            <if test="params.bottleneck != null and params.bottleneck != ''">
                and bottleneck = #{params.bottleneck,jdbcType=VARCHAR}
            </if>
            <if test="params.infiniteCapacity != null and params.infiniteCapacity != ''">
                and infinite_capacity = #{params.infiniteCapacity,jdbcType=VARCHAR}
            </if>
            <if test="params.sequenceCode != null and params.sequenceCode != ''">
                and sequence_code = #{params.sequenceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.variableWorkHours != null and params.variableWorkHours != ''">
                and variable_work_hours = #{params.variableWorkHours,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceQuantityCoefficient != null and params.resourceQuantityCoefficient != ''">
                and resource_quantity_coefficient = #{params.resourceQuantityCoefficient,jdbcType=VARCHAR}
            </if>
            <if test="params.countingUnitId != null and params.countingUnitId != ''">
                and counting_unit_id = #{params.countingUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.productionEfficiency != null">
                and production_efficiency = #{params.productionEfficiency,jdbcType=VARCHAR}
            </if>
            <if test="params.setupEfficiency != null">
                and setup_efficiency = #{params.setupEfficiency,jdbcType=VARCHAR}
            </if>
            <if test="params.cleanupEfficiency != null">
                and cleanup_efficiency = #{params.cleanupEfficiency,jdbcType=VARCHAR}
            </if>
            <if test="params.setupDuration != null">
                and setup_duration = #{params.setupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.cleanupDuration != null">
                and cleanup_duration = #{params.cleanupDuration,jdbcType=INTEGER}
            </if>
            <if test="params.bufferTimeBefore != null">
                and buffer_time_before = #{params.bufferTimeBefore,jdbcType=INTEGER}
            </if>
            <if test="params.bufferTimeAfter != null">
                and buffer_time_after = #{params.bufferTimeAfter,jdbcType=INTEGER}
            </if>
            <if test="params.maxProductionSuspendDuration != null">
                and max_production_suspend_duration = #{params.maxProductionSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxSetupSuspendDuration != null">
                and max_setup_suspend_duration = #{params.maxSetupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.maxCleanupSuspendDuration != null">
                and max_cleanup_suspend_duration = #{params.maxCleanupSuspendDuration,jdbcType=INTEGER}
            </if>
            <if test="params.productionLine != null and params.productionLine != ''">
                and production_line = #{params.productionLine,jdbcType=VARCHAR}
            </if>
            <if test="params.strictProductionLineConstraints != null and params.strictProductionLineConstraints != ''">
                and strict_production_line_constraints = #{params.strictProductionLineConstraints,jdbcType=VARCHAR}
            </if>
            <if test="params.noBufferActionType != null and params.noBufferActionType != ''">
                and no_buffer_action_type = #{params.noBufferActionType,jdbcType=VARCHAR}
            </if>
            <if test="params.noBufferActionDuration != null">
                and no_buffer_action_duration = #{params.noBufferActionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.lotSize != null">
                and lot_size = #{params.lotSize,jdbcType=INTEGER}
            </if>
            <if test="params.maxLotSize != null">
                and max_lot_size = #{params.maxLotSize,jdbcType=INTEGER}
            </if>
            <if test="params.productionDateLastNumChangeUnit != null and params.productionDateLastNumChangeUnit != ''">
                and production_date_last_num_change_unit = #{params.productionDateLastNumChangeUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.productionTimeLastNumChangeUnit != null and params.productionTimeLastNumChangeUnit != ''">
                and production_time_last_num_change_unit = #{params.productionTimeLastNumChangeUnit,jdbcType=VARCHAR}
            </if>
            <if test="params.productionDurationLogic != null and params.productionDurationLogic != ''">
                and production_duration_logic = #{params.productionDurationLogic,jdbcType=VARCHAR}
            </if>
            <if test="params.setupAndCleanupDurationLogic != null and params.setupAndCleanupDurationLogic != ''">
                and setup_and_cleanup_duration_logic = #{params.setupAndCleanupDurationLogic,jdbcType=VARCHAR}
            </if>
            <if test="params.dynamicSetupAndCleanupDurationLogic != null and params.dynamicSetupAndCleanupDurationLogic != ''">
                and dynamic_setup_and_cleanup_duration_logic = #{params.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR}
            </if>
            <if test="params.changeoverDurationLogic != null and params.changeoverDurationLogic != ''">
                and changeover_duration_logic = #{params.changeoverDurationLogic,jdbcType=VARCHAR}
            </if>
            <if test="params.dynamicChangeoverDurationLogic != null and params.dynamicChangeoverDurationLogic != ''">
                and dynamic_changeover_duration_logic = #{params.dynamicChangeoverDurationLogic,jdbcType=VARCHAR}
            </if>
            <if test="params.effectiveTime != null">
                and effective_time = #{params.effectiveTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.expiryTime != null">
                and expiry_time = #{params.expiryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mds_res_physical_resource
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mds_res_physical_resource(
        id,
        standard_resource_id,
        physical_resource_code,
        physical_resource_name,
        resource_category,
        resource_type,
        resource_classification,
        subtask_type,
        assign_quantity_type,
        display_index,
        bottleneck,
        infinite_capacity,
        sequence_code,
        variable_work_hours,
        resource_quantity_coefficient,
        counting_unit_id,
        production_efficiency,
        setup_efficiency,
        cleanup_efficiency,
        setup_duration,
        cleanup_duration,
        buffer_time_before,
        buffer_time_after,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        production_line,
        strict_production_line_constraints,
        no_buffer_action_type,
        no_buffer_action_duration,
        lot_size,
        max_lot_size,
        production_date_last_num_change_unit,
        production_time_last_num_change_unit,
        production_duration_logic,
        setup_and_cleanup_duration_logic,
        dynamic_setup_and_cleanup_duration_logic,
        changeover_duration_logic,
        dynamic_changeover_duration_logic,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceCode,jdbcType=VARCHAR},
        #{physicalResourceName,jdbcType=VARCHAR},
        #{resourceCategory,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR},
        #{resourceClassification,jdbcType=VARCHAR},
        #{subtaskType,jdbcType=VARCHAR},
        #{assignQuantityType,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{bottleneck,jdbcType=VARCHAR},
        #{infiniteCapacity,jdbcType=VARCHAR},
        #{sequenceCode,jdbcType=VARCHAR},
        #{variableWorkHours,jdbcType=VARCHAR},
        #{resourceQuantityCoefficient,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{productionEfficiency,jdbcType=VARCHAR},
        #{setupEfficiency,jdbcType=VARCHAR},
        #{cleanupEfficiency,jdbcType=VARCHAR},
        #{setupDuration,jdbcType=INTEGER},
        #{cleanupDuration,jdbcType=INTEGER},
        #{bufferTimeBefore,jdbcType=INTEGER},
        #{bufferTimeAfter,jdbcType=INTEGER},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{productionLine,jdbcType=VARCHAR},
        #{strictProductionLineConstraints,jdbcType=VARCHAR},
        #{noBufferActionType,jdbcType=VARCHAR},
        #{noBufferActionDuration,jdbcType=INTEGER},
        #{lotSize,jdbcType=INTEGER},
        #{maxLotSize,jdbcType=INTEGER},
        #{productionDateLastNumChangeUnit,jdbcType=VARCHAR},
        #{productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
        #{productionDurationLogic,jdbcType=VARCHAR},
        #{setupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{changeoverDurationLogic,jdbcType=VARCHAR},
        #{dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO">
        insert into mds_res_physical_resource(
        id,
        standard_resource_id,
        physical_resource_code,
        physical_resource_name,
        resource_category,
        resource_type,
        resource_classification,
        subtask_type,
        assign_quantity_type,
        display_index,
        bottleneck,
        infinite_capacity,
        sequence_code,
        variable_work_hours,
        resource_quantity_coefficient,
        counting_unit_id,
        production_efficiency,
        setup_efficiency,
        cleanup_efficiency,
        setup_duration,
        cleanup_duration,
        buffer_time_before,
        buffer_time_after,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        production_line,
        strict_production_line_constraints,
        no_buffer_action_type,
        no_buffer_action_duration,
        lot_size,
        max_lot_size,
        production_date_last_num_change_unit,
        production_time_last_num_change_unit,
        production_duration_logic,
        setup_and_cleanup_duration_logic,
        dynamic_setup_and_cleanup_duration_logic,
        changeover_duration_logic,
        dynamic_changeover_duration_logic,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{standardResourceId,jdbcType=VARCHAR},
        #{physicalResourceCode,jdbcType=VARCHAR},
        #{physicalResourceName,jdbcType=VARCHAR},
        #{resourceCategory,jdbcType=VARCHAR},
        #{resourceType,jdbcType=VARCHAR},
        #{resourceClassification,jdbcType=VARCHAR},
        #{subtaskType,jdbcType=VARCHAR},
        #{assignQuantityType,jdbcType=VARCHAR},
        #{displayIndex,jdbcType=INTEGER},
        #{bottleneck,jdbcType=VARCHAR},
        #{infiniteCapacity,jdbcType=VARCHAR},
        #{sequenceCode,jdbcType=VARCHAR},
        #{variableWorkHours,jdbcType=VARCHAR},
        #{resourceQuantityCoefficient,jdbcType=VARCHAR},
        #{countingUnitId,jdbcType=VARCHAR},
        #{productionEfficiency,jdbcType=VARCHAR},
        #{setupEfficiency,jdbcType=VARCHAR},
        #{cleanupEfficiency,jdbcType=VARCHAR},
        #{setupDuration,jdbcType=INTEGER},
        #{cleanupDuration,jdbcType=INTEGER},
        #{bufferTimeBefore,jdbcType=INTEGER},
        #{bufferTimeAfter,jdbcType=INTEGER},
        #{maxProductionSuspendDuration,jdbcType=INTEGER},
        #{maxSetupSuspendDuration,jdbcType=INTEGER},
        #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{productionLine,jdbcType=VARCHAR},
        #{strictProductionLineConstraints,jdbcType=VARCHAR},
        #{noBufferActionType,jdbcType=VARCHAR},
        #{noBufferActionDuration,jdbcType=INTEGER},
        #{lotSize,jdbcType=INTEGER},
        #{maxLotSize,jdbcType=INTEGER},
        #{productionDateLastNumChangeUnit,jdbcType=VARCHAR},
        #{productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
        #{productionDurationLogic,jdbcType=VARCHAR},
        #{setupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{changeoverDurationLogic,jdbcType=VARCHAR},
        #{dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
        #{effectiveTime,jdbcType=TIMESTAMP},
        #{expiryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_res_physical_resource(
        id,
        standard_resource_id,
        physical_resource_code,
        physical_resource_name,
        resource_category,
        resource_type,
        resource_classification,
        subtask_type,
        assign_quantity_type,
        display_index,
        bottleneck,
        infinite_capacity,
        sequence_code,
        variable_work_hours,
        resource_quantity_coefficient,
        counting_unit_id,
        production_efficiency,
        setup_efficiency,
        cleanup_efficiency,
        setup_duration,
        cleanup_duration,
        buffer_time_before,
        buffer_time_after,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        production_line,
        strict_production_line_constraints,
        no_buffer_action_type,
        no_buffer_action_duration,
        lot_size,
        max_lot_size,
        production_date_last_num_change_unit,
        production_time_last_num_change_unit,
        production_duration_logic,
        setup_and_cleanup_duration_logic,
        dynamic_setup_and_cleanup_duration_logic,
        changeover_duration_logic,
        dynamic_changeover_duration_logic,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceCode,jdbcType=VARCHAR},
        #{entity.physicalResourceName,jdbcType=VARCHAR},
        #{entity.resourceCategory,jdbcType=VARCHAR},
        #{entity.resourceType,jdbcType=VARCHAR},
        #{entity.resourceClassification,jdbcType=VARCHAR},
        #{entity.subtaskType,jdbcType=VARCHAR},
        #{entity.assignQuantityType,jdbcType=VARCHAR},
        #{entity.displayIndex,jdbcType=INTEGER},
        #{entity.bottleneck,jdbcType=VARCHAR},
        #{entity.infiniteCapacity,jdbcType=VARCHAR},
        #{entity.sequenceCode,jdbcType=VARCHAR},
        #{entity.variableWorkHours,jdbcType=VARCHAR},
        #{entity.resourceQuantityCoefficient,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.productionEfficiency,jdbcType=VARCHAR},
        #{entity.setupEfficiency,jdbcType=VARCHAR},
        #{entity.cleanupEfficiency,jdbcType=VARCHAR},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.bufferTimeBefore,jdbcType=INTEGER},
        #{entity.bufferTimeAfter,jdbcType=INTEGER},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.productionLine,jdbcType=VARCHAR},
        #{entity.strictProductionLineConstraints,jdbcType=VARCHAR},
        #{entity.noBufferActionType,jdbcType=VARCHAR},
        #{entity.noBufferActionDuration,jdbcType=INTEGER},
        #{entity.lotSize,jdbcType=INTEGER},
        #{entity.maxLotSize,jdbcType=INTEGER},
        #{entity.productionDateLastNumChangeUnit,jdbcType=VARCHAR},
        #{entity.productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
        #{entity.productionDurationLogic,jdbcType=VARCHAR},
        #{entity.setupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{entity.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{entity.changeoverDurationLogic,jdbcType=VARCHAR},
        #{entity.dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_res_physical_resource(
        id,
        standard_resource_id,
        physical_resource_code,
        physical_resource_name,
        resource_category,
        resource_type,
        resource_classification,
        subtask_type,
        assign_quantity_type,
        display_index,
        bottleneck,
        infinite_capacity,
        sequence_code,
        variable_work_hours,
        resource_quantity_coefficient,
        counting_unit_id,
        production_efficiency,
        setup_efficiency,
        cleanup_efficiency,
        setup_duration,
        cleanup_duration,
        buffer_time_before,
        buffer_time_after,
        max_production_suspend_duration,
        max_setup_suspend_duration,
        max_cleanup_suspend_duration,
        production_line,
        strict_production_line_constraints,
        no_buffer_action_type,
        no_buffer_action_duration,
        lot_size,
        max_lot_size,
        production_date_last_num_change_unit,
        production_time_last_num_change_unit,
        production_duration_logic,
        setup_and_cleanup_duration_logic,
        dynamic_setup_and_cleanup_duration_logic,
        changeover_duration_logic,
        dynamic_changeover_duration_logic,
        effective_time,
        expiry_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.standardResourceId,jdbcType=VARCHAR},
        #{entity.physicalResourceCode,jdbcType=VARCHAR},
        #{entity.physicalResourceName,jdbcType=VARCHAR},
        #{entity.resourceCategory,jdbcType=VARCHAR},
        #{entity.resourceType,jdbcType=VARCHAR},
        #{entity.resourceClassification,jdbcType=VARCHAR},
        #{entity.subtaskType,jdbcType=VARCHAR},
        #{entity.assignQuantityType,jdbcType=VARCHAR},
        #{entity.displayIndex,jdbcType=INTEGER},
        #{entity.bottleneck,jdbcType=VARCHAR},
        #{entity.infiniteCapacity,jdbcType=VARCHAR},
        #{entity.sequenceCode,jdbcType=VARCHAR},
        #{entity.variableWorkHours,jdbcType=VARCHAR},
        #{entity.resourceQuantityCoefficient,jdbcType=VARCHAR},
        #{entity.countingUnitId,jdbcType=VARCHAR},
        #{entity.productionEfficiency,jdbcType=VARCHAR},
        #{entity.setupEfficiency,jdbcType=VARCHAR},
        #{entity.cleanupEfficiency,jdbcType=VARCHAR},
        #{entity.setupDuration,jdbcType=INTEGER},
        #{entity.cleanupDuration,jdbcType=INTEGER},
        #{entity.bufferTimeBefore,jdbcType=INTEGER},
        #{entity.bufferTimeAfter,jdbcType=INTEGER},
        #{entity.maxProductionSuspendDuration,jdbcType=INTEGER},
        #{entity.maxSetupSuspendDuration,jdbcType=INTEGER},
        #{entity.maxCleanupSuspendDuration,jdbcType=INTEGER},
        #{entity.productionLine,jdbcType=VARCHAR},
        #{entity.strictProductionLineConstraints,jdbcType=VARCHAR},
        #{entity.noBufferActionType,jdbcType=VARCHAR},
        #{entity.noBufferActionDuration,jdbcType=INTEGER},
        #{entity.lotSize,jdbcType=INTEGER},
        #{entity.maxLotSize,jdbcType=INTEGER},
        #{entity.productionDateLastNumChangeUnit,jdbcType=VARCHAR},
        #{entity.productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
        #{entity.productionDurationLogic,jdbcType=VARCHAR},
        #{entity.setupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{entity.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
        #{entity.changeoverDurationLogic,jdbcType=VARCHAR},
        #{entity.dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
        #{entity.effectiveTime,jdbcType=TIMESTAMP},
        #{entity.expiryTime,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO">
        update mds_res_physical_resource set
        standard_resource_id = #{standardResourceId,jdbcType=VARCHAR},
        physical_resource_code = #{physicalResourceCode,jdbcType=VARCHAR},
        physical_resource_name = #{physicalResourceName,jdbcType=VARCHAR},
        resource_category = #{resourceCategory,jdbcType=VARCHAR},
        resource_type = #{resourceType,jdbcType=VARCHAR},
        resource_classification = #{resourceClassification,jdbcType=VARCHAR},
        subtask_type = #{subtaskType,jdbcType=VARCHAR},
        assign_quantity_type = #{assignQuantityType,jdbcType=VARCHAR},
        display_index = #{displayIndex,jdbcType=INTEGER},
        bottleneck = #{bottleneck,jdbcType=VARCHAR},
        infinite_capacity = #{infiniteCapacity,jdbcType=VARCHAR},
        sequence_code = #{sequenceCode,jdbcType=VARCHAR},
        variable_work_hours = #{variableWorkHours,jdbcType=VARCHAR},
        resource_quantity_coefficient = #{resourceQuantityCoefficient,jdbcType=VARCHAR},
        counting_unit_id = #{countingUnitId,jdbcType=VARCHAR},
        production_efficiency = #{productionEfficiency,jdbcType=VARCHAR},
        setup_efficiency = #{setupEfficiency,jdbcType=VARCHAR},
        cleanup_efficiency = #{cleanupEfficiency,jdbcType=VARCHAR},
        setup_duration = #{setupDuration,jdbcType=INTEGER},
        cleanup_duration = #{cleanupDuration,jdbcType=INTEGER},
        buffer_time_before = #{bufferTimeBefore,jdbcType=INTEGER},
        buffer_time_after = #{bufferTimeAfter,jdbcType=INTEGER},
        max_production_suspend_duration = #{maxProductionSuspendDuration,jdbcType=INTEGER},
        max_setup_suspend_duration = #{maxSetupSuspendDuration,jdbcType=INTEGER},
        max_cleanup_suspend_duration = #{maxCleanupSuspendDuration,jdbcType=INTEGER},
        production_line = #{productionLine,jdbcType=VARCHAR},
        strict_production_line_constraints = #{strictProductionLineConstraints,jdbcType=VARCHAR},
        no_buffer_action_type = #{noBufferActionType,jdbcType=VARCHAR},
        no_buffer_action_duration = #{noBufferActionDuration,jdbcType=INTEGER},
        lot_size = #{lotSize,jdbcType=INTEGER},
        max_lot_size = #{maxLotSize,jdbcType=INTEGER},
        production_date_last_num_change_unit = #{productionDateLastNumChangeUnit,jdbcType=VARCHAR},
        production_time_last_num_change_unit = #{productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
        production_duration_logic = #{productionDurationLogic,jdbcType=VARCHAR},
        setup_and_cleanup_duration_logic = #{setupAndCleanupDurationLogic,jdbcType=VARCHAR},
        dynamic_setup_and_cleanup_duration_logic = #{dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
        changeover_duration_logic = #{changeoverDurationLogic,jdbcType=VARCHAR},
        dynamic_changeover_duration_logic = #{dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
        effective_time = #{effectiveTime,jdbcType=TIMESTAMP},
        expiry_time = #{expiryTime,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mds.baseResource.infrastructure.po.PhysicalResourcePO">
        update mds_res_physical_resource
        <set>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceCode != null and item.physicalResourceCode != ''">
                physical_resource_code = #{item.physicalResourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceName != null and item.physicalResourceName != ''">
                physical_resource_name = #{item.physicalResourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCategory != null and item.resourceCategory != ''">
                resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceType != null and item.resourceType != ''">
                resource_type = #{item.resourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceClassification != null and item.resourceClassification != ''">
                resource_classification = #{item.resourceClassification,jdbcType=VARCHAR},
            </if>
            <if test="item.subtaskType != null and item.subtaskType != ''">
                subtask_type = #{item.subtaskType,jdbcType=VARCHAR},
            </if>
            <if test="item.assignQuantityType != null and item.assignQuantityType != ''">
                assign_quantity_type = #{item.assignQuantityType,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.bottleneck != null and item.bottleneck != ''">
                bottleneck = #{item.bottleneck,jdbcType=VARCHAR},
            </if>
            <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceCode != null and item.sequenceCode != ''">
                sequence_code = #{item.sequenceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.variableWorkHours != null and item.variableWorkHours != ''">
                variable_work_hours = #{item.variableWorkHours,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceQuantityCoefficient != null and item.resourceQuantityCoefficient != ''">
                resource_quantity_coefficient = #{item.resourceQuantityCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.productionEfficiency != null">
                production_efficiency = #{item.productionEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.setupEfficiency != null">
                setup_efficiency = #{item.setupEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupEfficiency != null">
                cleanup_efficiency = #{item.cleanupEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.bufferTimeBefore != null">
                buffer_time_before = #{item.bufferTimeBefore,jdbcType=INTEGER},
            </if>
            <if test="item.bufferTimeAfter != null">
                buffer_time_after = #{item.bufferTimeAfter,jdbcType=INTEGER},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.productionLine != null and item.productionLine != ''">
                production_line = #{item.productionLine,jdbcType=VARCHAR},
            </if>
            <if test="item.strictProductionLineConstraints != null and item.strictProductionLineConstraints != ''">
                strict_production_line_constraints = #{item.strictProductionLineConstraints,jdbcType=VARCHAR},
            </if>
            <if test="item.noBufferActionType != null and item.noBufferActionType != ''">
                no_buffer_action_type = #{item.noBufferActionType,jdbcType=VARCHAR},
            </if>
            <if test="item.noBufferActionDuration != null">
                no_buffer_action_duration = #{item.noBufferActionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=INTEGER},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.productionDateLastNumChangeUnit != null and item.productionDateLastNumChangeUnit != ''">
                production_date_last_num_change_unit = #{item.productionDateLastNumChangeUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.productionTimeLastNumChangeUnit != null and item.productionTimeLastNumChangeUnit != ''">
                production_time_last_num_change_unit = #{item.productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.productionDurationLogic != null and item.productionDurationLogic != ''">
                production_duration_logic = #{item.productionDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.setupAndCleanupDurationLogic != null and item.setupAndCleanupDurationLogic != ''">
                setup_and_cleanup_duration_logic = #{item.setupAndCleanupDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.dynamicSetupAndCleanupDurationLogic != null and item.dynamicSetupAndCleanupDurationLogic != ''">
                dynamic_setup_and_cleanup_duration_logic = #{item.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.changeoverDurationLogic != null and item.changeoverDurationLogic != ''">
                changeover_duration_logic = #{item.changeoverDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.dynamicChangeoverDurationLogic != null and item.dynamicChangeoverDurationLogic != ''">
                dynamic_changeover_duration_logic = #{item.dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mds_res_physical_resource
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="standard_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_classification = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceClassification,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="subtask_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subtaskType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="assign_quantity_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.assignQuantityType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="display_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.displayIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="bottleneck = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bottleneck,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="infinite_capacity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.infiniteCapacity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sequence_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.sequenceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="variable_work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.variableWorkHours,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_quantity_coefficient = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceQuantityCoefficient,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="counting_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.countingUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_efficiency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionEfficiency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="setup_efficiency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupEfficiency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cleanup_efficiency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupEfficiency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="setup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="cleanup_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cleanupDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="buffer_time_before = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bufferTimeBefore,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="buffer_time_after = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.bufferTimeAfter,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_production_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxProductionSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_setup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxSetupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_cleanup_suspend_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxCleanupSuspendDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_line = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionLine,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="strict_production_line_constraints = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.strictProductionLineConstraints,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="no_buffer_action_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.noBufferActionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="no_buffer_action_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.noBufferActionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotSize,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="max_lot_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.maxLotSize,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_date_last_num_change_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionDateLastNumChangeUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_time_last_num_change_unit = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionTimeLastNumChangeUnit,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_duration_logic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionDurationLogic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="setup_and_cleanup_duration_logic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.setupAndCleanupDurationLogic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dynamic_setup_and_cleanup_duration_logic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="changeover_duration_logic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.changeoverDurationLogic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="dynamic_changeover_duration_logic = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dynamicChangeoverDurationLogic,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="effective_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.effectiveTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="expiry_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.expiryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mds_res_physical_resource 
        <set>
            <if test="item.standardResourceId != null and item.standardResourceId != ''">
                standard_resource_id = #{item.standardResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceCode != null and item.physicalResourceCode != ''">
                physical_resource_code = #{item.physicalResourceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceName != null and item.physicalResourceName != ''">
                physical_resource_name = #{item.physicalResourceName,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceCategory != null and item.resourceCategory != ''">
                resource_category = #{item.resourceCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceType != null and item.resourceType != ''">
                resource_type = #{item.resourceType,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceClassification != null and item.resourceClassification != ''">
                resource_classification = #{item.resourceClassification,jdbcType=VARCHAR},
            </if>
            <if test="item.subtaskType != null and item.subtaskType != ''">
                subtask_type = #{item.subtaskType,jdbcType=VARCHAR},
            </if>
            <if test="item.assignQuantityType != null and item.assignQuantityType != ''">
                assign_quantity_type = #{item.assignQuantityType,jdbcType=VARCHAR},
            </if>
            <if test="item.displayIndex != null">
                display_index = #{item.displayIndex,jdbcType=INTEGER},
            </if>
            <if test="item.bottleneck != null and item.bottleneck != ''">
                bottleneck = #{item.bottleneck,jdbcType=VARCHAR},
            </if>
            <if test="item.infiniteCapacity != null and item.infiniteCapacity != ''">
                infinite_capacity = #{item.infiniteCapacity,jdbcType=VARCHAR},
            </if>
            <if test="item.sequenceCode != null and item.sequenceCode != ''">
                sequence_code = #{item.sequenceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.variableWorkHours != null and item.variableWorkHours != ''">
                variable_work_hours = #{item.variableWorkHours,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceQuantityCoefficient != null and item.resourceQuantityCoefficient != ''">
                resource_quantity_coefficient = #{item.resourceQuantityCoefficient,jdbcType=VARCHAR},
            </if>
            <if test="item.countingUnitId != null and item.countingUnitId != ''">
                counting_unit_id = #{item.countingUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.productionEfficiency != null">
                production_efficiency = #{item.productionEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.setupEfficiency != null">
                setup_efficiency = #{item.setupEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.cleanupEfficiency != null">
                cleanup_efficiency = #{item.cleanupEfficiency,jdbcType=VARCHAR},
            </if>
            <if test="item.setupDuration != null">
                setup_duration = #{item.setupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.cleanupDuration != null">
                cleanup_duration = #{item.cleanupDuration,jdbcType=INTEGER},
            </if>
            <if test="item.bufferTimeBefore != null">
                buffer_time_before = #{item.bufferTimeBefore,jdbcType=INTEGER},
            </if>
            <if test="item.bufferTimeAfter != null">
                buffer_time_after = #{item.bufferTimeAfter,jdbcType=INTEGER},
            </if>
            <if test="item.maxProductionSuspendDuration != null">
                max_production_suspend_duration = #{item.maxProductionSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxSetupSuspendDuration != null">
                max_setup_suspend_duration = #{item.maxSetupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.maxCleanupSuspendDuration != null">
                max_cleanup_suspend_duration = #{item.maxCleanupSuspendDuration,jdbcType=INTEGER},
            </if>
            <if test="item.productionLine != null and item.productionLine != ''">
                production_line = #{item.productionLine,jdbcType=VARCHAR},
            </if>
            <if test="item.strictProductionLineConstraints != null and item.strictProductionLineConstraints != ''">
                strict_production_line_constraints = #{item.strictProductionLineConstraints,jdbcType=VARCHAR},
            </if>
            <if test="item.noBufferActionType != null and item.noBufferActionType != ''">
                no_buffer_action_type = #{item.noBufferActionType,jdbcType=VARCHAR},
            </if>
            <if test="item.noBufferActionDuration != null">
                no_buffer_action_duration = #{item.noBufferActionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.lotSize != null">
                lot_size = #{item.lotSize,jdbcType=INTEGER},
            </if>
            <if test="item.maxLotSize != null">
                max_lot_size = #{item.maxLotSize,jdbcType=INTEGER},
            </if>
            <if test="item.productionDateLastNumChangeUnit != null and item.productionDateLastNumChangeUnit != ''">
                production_date_last_num_change_unit = #{item.productionDateLastNumChangeUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.productionTimeLastNumChangeUnit != null and item.productionTimeLastNumChangeUnit != ''">
                production_time_last_num_change_unit = #{item.productionTimeLastNumChangeUnit,jdbcType=VARCHAR},
            </if>
            <if test="item.productionDurationLogic != null and item.productionDurationLogic != ''">
                production_duration_logic = #{item.productionDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.setupAndCleanupDurationLogic != null and item.setupAndCleanupDurationLogic != ''">
                setup_and_cleanup_duration_logic = #{item.setupAndCleanupDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.dynamicSetupAndCleanupDurationLogic != null and item.dynamicSetupAndCleanupDurationLogic != ''">
                dynamic_setup_and_cleanup_duration_logic = #{item.dynamicSetupAndCleanupDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.changeoverDurationLogic != null and item.changeoverDurationLogic != ''">
                changeover_duration_logic = #{item.changeoverDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.dynamicChangeoverDurationLogic != null and item.dynamicChangeoverDurationLogic != ''">
                dynamic_changeover_duration_logic = #{item.dynamicChangeoverDurationLogic,jdbcType=VARCHAR},
            </if>
            <if test="item.effectiveTime != null">
                effective_time = #{item.effectiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.expiryTime != null">
                expiry_time = #{item.expiryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mds_res_physical_resource where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_res_physical_resource where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <select id="selectById" resultMap="VOResultMap">
        select
        <include refid="Base_Column_List" />
        from mds_res_physical_resource
        where id in
        <foreach collection="idList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <select id="selectAllResourceSequence" resultMap="VOResultMap">
        SELECT
			t2.organization_id,
			t3.stock_point_code,
			t1.sequence_code,
			t1.standard_resource_id,
			t1.id,
			t1.physical_resource_code,
			t1.physical_resource_name 
		FROM
			mds_res_physical_resource t1,
			mds_res_standard_resource t2,
			mds_stock_point t3 
		WHERE
			t1.standard_resource_id = t2.id 
			AND t3.organize_id = t2.organization_id 
			AND t1.enabled = 'YES' 
			AND t2.enabled = 'YES'
    </select>
</mapper>
