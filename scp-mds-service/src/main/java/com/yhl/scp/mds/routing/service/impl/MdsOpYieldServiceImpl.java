package com.yhl.scp.mds.routing.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesOpYield;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.dto.StandardStepDTO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.newproduct.infrastructure.dao.NewProductStockPointDao;
import com.yhl.scp.mds.newproduct.infrastructure.po.NewProductStockPointPO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.routing.convertor.MdsOpYieldConvertor;
import com.yhl.scp.mds.routing.convertor.ProductRoutingStepConvertor;
import com.yhl.scp.mds.routing.convertor.StandardStepConvertor;
import com.yhl.scp.mds.routing.domain.entity.MdsOpYieldDO;
import com.yhl.scp.mds.routing.domain.service.MdsOpYieldDomainService;
import com.yhl.scp.mds.routing.dto.MdsOpYieldDTO;
import com.yhl.scp.mds.routing.dto.ProductRoutingStepDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.MdsOpYieldDao;
import com.yhl.scp.mds.routing.infrastructure.po.MdsOpYieldPO;
import com.yhl.scp.mds.routing.service.MdsOpYieldService;
import com.yhl.scp.mds.routing.service.ProductRoutingService;
import com.yhl.scp.mds.routing.service.ProductRoutingStepService;
import com.yhl.scp.mds.routing.service.StandardStepService;
import com.yhl.scp.mds.routing.vo.MdsOpYieldVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingStepVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MdsOpYieldServiceImpl</code>
 * <p>
 * mes工序成品率接口同步中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 11:44:32
 */
@Slf4j
@Service
public class MdsOpYieldServiceImpl extends AbstractService implements MdsOpYieldService {

    @Resource
    private MdsOpYieldDao mdsOpYieldDao;

    @Resource
    private MdsOpYieldDomainService mdsOpYieldDomainService;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewProductStockPointService newProductStockPointService;
    @Resource
    private NewProductStockPointDao newProductStockPointDao;
    @Resource
    private NewStockPointService newStockPointService;
    @Resource
    private ProductRoutingStepService productRoutingStepService;
    @Resource
    private ProductRoutingService productRoutingService;
    @Resource
    private StandardStepService standardStepService;
    @Override
    public BaseResponse<Void> doCreate(MdsOpYieldDTO mdsOpYieldDTO) {
        // 0.数据转换
        MdsOpYieldDO mdsOpYieldDO = MdsOpYieldConvertor.INSTANCE.dto2Do(mdsOpYieldDTO);
        MdsOpYieldPO mdsOpYieldPO = MdsOpYieldConvertor.INSTANCE.dto2Po(mdsOpYieldDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsOpYieldDomainService.validation(mdsOpYieldDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mdsOpYieldPO);
        mdsOpYieldDao.insert(mdsOpYieldPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MdsOpYieldDTO mdsOpYieldDTO) {
        // 0.数据转换
        MdsOpYieldDO mdsOpYieldDO = MdsOpYieldConvertor.INSTANCE.dto2Do(mdsOpYieldDTO);
        MdsOpYieldPO mdsOpYieldPO = MdsOpYieldConvertor.INSTANCE.dto2Po(mdsOpYieldDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsOpYieldDomainService.validation(mdsOpYieldDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mdsOpYieldPO);
        mdsOpYieldDao.update(mdsOpYieldPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MdsOpYieldDTO> list) {
        List<MdsOpYieldPO> newList = MdsOpYieldConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsOpYieldDao.insertBatch(newList);
    }
    @Override
    public void doCreateBatchWithPartition(List<MdsOpYieldDTO> list) {
        List<MdsOpYieldPO> newList = MdsOpYieldConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> mdsOpYieldDao.insertBatch(poList), null);
    }

    @Override
    public void doUpdateBatch(List<MdsOpYieldDTO> list) {
        List<MdsOpYieldPO> newList = MdsOpYieldConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsOpYieldDao.updateBatch(newList);
    }
    @Override
    public void doUpdateBatchWithPartition(List<MdsOpYieldDTO> list) {
        List<MdsOpYieldPO> newList = MdsOpYieldConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> mdsOpYieldDao.updateBatch(poList), null);
    }
    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsOpYieldDao.deleteBatch(idList);
        }
        return mdsOpYieldDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MdsOpYieldVO selectByPrimaryKey(String id) {
        MdsOpYieldPO po = mdsOpYieldDao.selectByPrimaryKey(id);
        return MdsOpYieldConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MDS_OP_YIELD")
    public List<MdsOpYieldVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MDS_OP_YIELD")
    public List<MdsOpYieldVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MdsOpYieldVO> dataList = mdsOpYieldDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsOpYieldServiceImpl target = SpringBeanUtils.getBean(MdsOpYieldServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MdsOpYieldVO> selectByParams(Map<String, Object> params) {
        List<MdsOpYieldPO> list = mdsOpYieldDao.selectByParams(params);
        return MdsOpYieldConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MdsOpYieldVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<MdsOpYieldVO> selectByKids(List<String> kids) {
        return MdsOpYieldConvertor.INSTANCE.po2Vos(mdsOpYieldDao.selectByKids(kids));
    }

    @Override
    public BaseResponse<Void> sync(List<MesOpYield> o) {

        if (CollectionUtils.isEmpty(o)) {
            log.error("MES工序成品率数据为空");
            return BaseResponse.success();
        }
        log.info("接收到MES工序成品率接口数据大小:{}",o.size());
        List<String> kids = o.stream().map(MesOpYield::getKid).distinct().collect(Collectors.toList());
        List<String> productCodes = o.stream().map(MesOpYield::getItemCode).distinct().collect(Collectors.toList());
        List<NewProductStockPointPO> newProductStockPointPOS = newProductStockPointDao.selectByProductCode(productCodes);

        Map<String, NewProductStockPointPO> newProductStockPointPOMap =
                CollectionUtils.isEmpty(newProductStockPointPOS) ?
                        MapUtil.newHashMap() :
                        newProductStockPointPOS.stream().collect(
                                Collectors.toMap(t -> t.getStockPointCode() + "|" + t.getProductCode(),
                                        Function.identity(), (v1, v2) -> v1));
        Map params = MapUtil.builder().put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> stockPoints = newStockPointService.selectByParams(params);
        Map<String, NewStockPointVO> stockMap = stockPoints.stream().collect(Collectors.
                toMap(NewStockPointVO::getStockPointCode, Function.identity(), (v1, v2) -> v1));

        List<MdsOpYieldDTO> insertMdsOpYieldDTOs = Lists.newArrayList();
        List<MdsOpYieldDTO> updateMdsOpYieldDTOs = Lists.newArrayList();
        List<MdsOpYieldPO> oldMdsOpYieldVOs = mdsOpYieldDao.selectByKids(kids);
        Map<String, MdsOpYieldPO> oldMdsOpYieldVOMap = oldMdsOpYieldVOs.stream().collect(Collectors.toMap(MdsOpYieldPO::getKid, Function.identity(), (v1, v2) -> v1));
        for(MesOpYield mesOpYield:o){
            String productDataKey = mesOpYield.getPlantCode() + "|" + mesOpYield.getItemCode();
            String stockPointCode = mesOpYield.getPlantCode();

            String dataKey = mesOpYield.getKid();
            MdsOpYieldDTO mdsOpYieldDTO = new MdsOpYieldDTO();

            if(!newProductStockPointPOMap.containsKey(productDataKey)){
                log.error("没有找到对应的物料信息，物料编码：{}，库存点：{}", mesOpYield.getItemCode(), mesOpYield.getPlantCode());
                continue;
            }
            if (!stockMap.containsKey(stockPointCode)) {
                log.error("没有找到对应的库存点，库存点：{}", stockPointCode);
                continue;
            }
            String productId = newProductStockPointPOMap.get(productDataKey).getId();
            String stockId = stockMap.get(stockPointCode).getId();
            if(oldMdsOpYieldVOMap.containsKey(dataKey)) {
                log.info("已有数据，正在处理数据，dataKey:{}",dataKey);

                MdsOpYieldPO mdsOpYieldPO = oldMdsOpYieldVOMap.get(dataKey);
                mdsOpYieldDTO.setRemark( mdsOpYieldPO.getRemark() );
                mdsOpYieldDTO.setId( mdsOpYieldPO.getId() );
                mdsOpYieldDTO.setProductCode( mdsOpYieldPO.getProductCode() );
                mdsOpYieldDTO.setVersionValue( mdsOpYieldPO.getVersionValue() );

                mdsOpYieldDTO.setYearMm(mesOpYield.getYearMm());
                mdsOpYieldDTO.setKid(mesOpYield.getKid());
                mdsOpYieldDTO.setLastUpdateDate(mesOpYield.getLastUpdateDate());
                mdsOpYieldDTO.setProductCode(mesOpYield.getItemCode());
                mdsOpYieldDTO.setStockPointCode(mesOpYield.getPlantCode());
                mdsOpYieldDTO.setPlanArea(mesOpYield.getBusinessArea());
                mdsOpYieldDTO.setEnabled(YesOrNoEnum.YES.getCode());
                mdsOpYieldDTO.setOpProcess(mesOpYield.getOpProcess());
                mdsOpYieldDTO.setProductSpec(mesOpYield.getItemDesc());
                mdsOpYieldDTO.setProductId(productId);
                mdsOpYieldDTO.setOpYield(mesOpYield.getOpYield());
                mdsOpYieldDTO.setStockPointId(stockId);
                updateMdsOpYieldDTOs.add(mdsOpYieldDTO);
            }else{
                log.info("没有找到对应的数据，正在处理新增数据，dataKey:{}",dataKey);
                mdsOpYieldDTO.setYearMm(mesOpYield.getYearMm());
                mdsOpYieldDTO.setKid(mesOpYield.getKid());
                mdsOpYieldDTO.setLastUpdateDate(mesOpYield.getLastUpdateDate());
                mdsOpYieldDTO.setProductCode(mesOpYield.getItemCode());
                mdsOpYieldDTO.setStockPointCode(mesOpYield.getPlantCode());
                mdsOpYieldDTO.setPlanArea(mesOpYield.getBusinessArea());
                mdsOpYieldDTO.setEnabled(YesOrNoEnum.YES.getCode());
                mdsOpYieldDTO.setOpProcess(mesOpYield.getOpProcess());
                mdsOpYieldDTO.setProductSpec(mesOpYield.getItemDesc());
                mdsOpYieldDTO.setOpYield(mesOpYield.getOpYield());
                mdsOpYieldDTO.setProductId(productId);
                mdsOpYieldDTO.setStockPointId(stockId);
                insertMdsOpYieldDTOs.add(mdsOpYieldDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(insertMdsOpYieldDTOs)){
            this.doCreateBatchWithPartition(insertMdsOpYieldDTOs);
            log.info("MES工序成品率数据新增数据条数：{}", insertMdsOpYieldDTOs.size());
        }
        if(CollectionUtils.isNotEmpty(updateMdsOpYieldDTOs)){
            this.doUpdateBatchWithPartition(updateMdsOpYieldDTOs);
            log.info("MES工序成品率数据更新数据条数：{}", updateMdsOpYieldDTOs.size());
        }
        return BaseResponse.success("同步成功");
    }

    public void doMatchRoutingData(List<MdsOpYieldDTO> insertMdsOpYieldDTOs, List<MdsOpYieldDTO> updateMdsOpYieldDTOs) {
        List<MdsOpYieldDTO> allMdsOpYieldDTOS = Lists.newArrayList();
        allMdsOpYieldDTOS.addAll(insertMdsOpYieldDTOs);
        allMdsOpYieldDTOS.addAll(updateMdsOpYieldDTOs);

        if(CollectionUtils.isNotEmpty(allMdsOpYieldDTOS)){
            Map<String, MdsOpYieldDTO> latestYearMMMap = allMdsOpYieldDTOS.stream()
                    .collect(Collectors.groupingBy(
                            dto -> dto.getProductId() + "|" + dto.getOpProcess(),
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(MdsOpYieldDTO::getYearMm)),
                                    opt -> opt.orElse(null)
                            )
                    ));
            List<MdsOpYieldDTO> filteredMdsOpYieldDTOS = new ArrayList<>(latestYearMMMap.values());

            List<String> productIds = filteredMdsOpYieldDTOS.stream().map(MdsOpYieldDTO::getProductId).distinct().collect(Collectors.toList());
            List<ProductRoutingVO> productRoutingVOS = productRoutingService.selectByProductIds(productIds);
            List<String> routingIds = productRoutingVOS.stream().map(ProductRoutingVO::getId).collect(Collectors.toList());
            List<ProductRoutingStepVO> existRoutingStepList = productRoutingStepService.selectByRoutingIds(routingIds);

            Map<String, ProductRoutingVO> existRoutingVOMap =
                    CollectionUtils.isEmpty(productRoutingVOS) ?MapUtil.newHashMap() :productRoutingVOS.stream().collect(
                            Collectors.toMap(t->t.getProductId()+"|"+t.getStockPointId(), Function.identity(), (v1, v2) -> v1));

            Map<String, List<ProductRoutingStepVO>> existRoutingStepVOMap =
                    CollectionUtils.isEmpty(existRoutingStepList) ?MapUtil.newHashMap() :existRoutingStepList.stream().collect(
                            Collectors.groupingBy(ProductRoutingStepVO::getRoutingId));

            List<ProductRoutingStepDTO> updateRoutingStepDTOs = Lists.newArrayList();

            for(MdsOpYieldDTO mdsOpYieldDTO:allMdsOpYieldDTOS){

                String routingDataKey = mdsOpYieldDTO.getProductId()+"|"+mdsOpYieldDTO.getStockPointId();

                ProductRoutingVO productRoutingVO = existRoutingVOMap.get(routingDataKey);
                if(Objects.isNull(productRoutingVO)){
                    log.error("没有找到对应的工艺信息，物料id：{}，物料编码:{},库存点id：{},库存点代码:{}",
                            mdsOpYieldDTO.getProductId(),mdsOpYieldDTO.getProductCode(), mdsOpYieldDTO.getStockPointId()
                            ,mdsOpYieldDTO.getStockPointCode());
                    continue;
                }
                List<ProductRoutingStepVO> routingStepVO = existRoutingStepVOMap.get(productRoutingVO.getId());
                if(CollectionUtils.isEmpty(routingStepVO)){
                    log.info("RoutingId:{}:没有找到对应的产品工艺路径步骤信息，物料编码：{}，库存点：{}", productRoutingVO.getId(),
                            mdsOpYieldDTO.getProductId(), mdsOpYieldDTO.getStockPointCode());
                    continue;
                }

                String operationSequenceNum = mdsOpYieldDTO.getOpProcess();
                String yield = mdsOpYieldDTO.getOpYield();
                routingStepVO.stream().filter(t->Objects.equals(String.valueOf(t.getSequenceNo()), operationSequenceNum)).findFirst()
                        .ifPresent(t->{
                            ProductRoutingStepDTO productRoutingStepDTO= ProductRoutingStepConvertor.INSTANCE.vo2Dto(t);
                            try{
                                productRoutingStepDTO.setYield(convertPercentageToDecimal(yield));
                                updateRoutingStepDTOs.add(productRoutingStepDTO);
                            }catch (Exception e){
                                e.printStackTrace();
                                log.error("库存点：{}，产品编码：{}，操作序号：{},百分比字符串:{},不能转换为数字",mdsOpYieldDTO.getStockPointCode(), mdsOpYieldDTO.getProductCode(), operationSequenceNum,yield);
                            }
                        });
            }

            if(CollectionUtils.isNotEmpty(updateRoutingStepDTOs)){
                productRoutingStepService.doUpdateBatchWithPartition(updateRoutingStepDTOs);
                log.info("更新产品工艺路径步骤数据条数：{}", updateRoutingStepDTOs.size());
            }
        }
    }

    @Override
    public BaseResponse<Void> handleSyncOpYield(Scenario scenario) {

        Map<String, Object> apiParamMap = new HashMap<>(2);
        apiParamMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
        apiParamMap.put("dataBaseName", scenario.getDataBaseName());
        newDcpFeign.callExternalApi(scenario.getTenantId(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.OP_YIELD.getCode(), apiParamMap);

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleSyncStandStepOpYield(String tenantId) {
        List<StandardStepVO> standardStepVOS = standardStepService.selectAll();
        if (CollectionUtils.isEmpty(standardStepVOS)) {
            return BaseResponse.error("标准工序表为空");
        }
        Map<String, StandardStepVO> stepVOMap = standardStepVOS.stream().collect(Collectors.toMap(t ->
                t.getStockPointCode() + "|" + t.getStandardStepCode(), t -> t, (v1, v2) -> v1));
        List<MdsOpYieldVO> mdsOpYieldVOS = mdsOpYieldDao.selectLatestOpYield();
        if (CollectionUtils.isEmpty(mdsOpYieldVOS)) {
            return BaseResponse.error("工序成品率为空");
        }
        List<StandardStepDTO> updateStepDTOS = new ArrayList<>();
        for (MdsOpYieldVO mdsOpYieldVO : mdsOpYieldVOS) {
            String key = mdsOpYieldVO.getStockPointCode() + "|" + mdsOpYieldVO.getOpProcess();
            if(stepVOMap.containsKey(key)) {
                log.info("工序成品率key：{}，成品率：{}",key,mdsOpYieldVO.getOpYield());
                StandardStepVO standardStepVO = stepVOMap.get(key);
                StandardStepDTO standardStepDTO = StandardStepConvertor.INSTANCE.vo2Dto(standardStepVO);
                standardStepDTO.setYield(new BigDecimal(mdsOpYieldVO.getOpYield()));
                updateStepDTOS.add(standardStepDTO);
            }
        }
        if(CollectionUtils.isNotEmpty(updateStepDTOS)) {
            standardStepService.doUpdateBatch(updateStepDTOS);
            log.info("工序成品率更新数量{}",updateStepDTOS.size());
        }
        return BaseResponse.success("匹配成功");
    }

    @Override
    public BaseResponse<Void> handleSyncRoutingStepOpYield(String tenantId) {
        List<MdsOpYieldVO> mdsOpYieldVOS = mdsOpYieldDao.selectLatestOpYieldForRouting();
        if (CollectionUtils.isEmpty(mdsOpYieldVOS)) {
            return BaseResponse.error("工序成品率为空");
        }

        List<String> productIds = mdsOpYieldVOS.stream().map(MdsOpYieldVO::getProductId).distinct().collect(Collectors.toList());
        List<ProductRoutingVO> productRoutingVOS = productRoutingService.selectByProductIds(productIds);
        List<String> routingIds = productRoutingVOS.stream().map(ProductRoutingVO::getId).collect(Collectors.toList());
        List<ProductRoutingStepVO> existRoutingStepList = productRoutingStepService.selectByRoutingIds(routingIds);
        Map<String, ProductRoutingVO> existRoutingVOMap =
                CollectionUtils.isEmpty(productRoutingVOS) ?MapUtil.newHashMap() :productRoutingVOS.stream().collect(
                        Collectors.toMap(t->t.getProductId()+"|"+t.getStockPointId(), Function.identity(), (v1, v2) -> v1));
        Map<String, List<ProductRoutingStepVO>> existRoutingStepVOMap =
                CollectionUtils.isEmpty(existRoutingStepList) ?MapUtil.newHashMap() :existRoutingStepList.stream().collect(
                        Collectors.groupingBy(ProductRoutingStepVO::getRoutingId));
        List<ProductRoutingStepDTO> updateRoutingStepDTOs = Lists.newArrayList();
        for(MdsOpYieldVO mdsOpYieldDTO:mdsOpYieldVOS){

            String routingDataKey = mdsOpYieldDTO.getProductId()+"|"+mdsOpYieldDTO.getStockPointId();

            ProductRoutingVO productRoutingVO = existRoutingVOMap.get(routingDataKey);
            if(Objects.isNull(productRoutingVO)){
                log.error("没有找到对应的工艺信息，物料id：{}，物料编码:{},库存点id：{},库存点代码:{}",
                        mdsOpYieldDTO.getProductId(),mdsOpYieldDTO.getProductCode(), mdsOpYieldDTO.getStockPointId()
                        ,mdsOpYieldDTO.getStockPointCode());
                continue;
            }
            List<ProductRoutingStepVO> routingStepVO = existRoutingStepVOMap.get(productRoutingVO.getId());
            if(CollectionUtils.isEmpty(routingStepVO)){
                log.info("RoutingId:{}:没有找到对应的产品工艺路径步骤信息，物料编码：{}，库存点：{}", productRoutingVO.getId(),
                        mdsOpYieldDTO.getProductId(), mdsOpYieldDTO.getStockPointCode());
                continue;
            }

            String operationSequenceNum = mdsOpYieldDTO.getOpProcess();
            String yield = mdsOpYieldDTO.getOpYield();
            routingStepVO.stream().filter(t->Objects.equals(String.valueOf(t.getSequenceNo()), operationSequenceNum)).findFirst()
                    .ifPresent(t->{
                        ProductRoutingStepDTO productRoutingStepDTO =ProductRoutingStepConvertor.INSTANCE.vo2Dto(t);
                        productRoutingStepDTO.setYield(new BigDecimal(yield));
                        updateRoutingStepDTOs.add(productRoutingStepDTO);

                    });
        }
        if(CollectionUtils.isNotEmpty(updateRoutingStepDTOs)){
            productRoutingStepService.doUpdateBatchWithPartition(updateRoutingStepDTOs);
            log.info("更新产品工艺路径步骤数据条数：{}", updateRoutingStepDTOs.size());
        }
        return BaseResponse.success("匹配成功");
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MdsOpYieldVO> invocation(List<MdsOpYieldVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }
    /**
     * 将百分比字符串转换为准确的小数
     *
     * @param percentageStr 百分比字符串，例如 "85.7143%"
     * @return 转换后的数字（准确的小数），例如 0.857143
     * @throws IllegalArgumentException 如果输入字符串格式无效
     */
    public static BigDecimal convertPercentageToDecimal(String percentageStr) {
        if (percentageStr == null || !percentageStr.endsWith("%")) {
            throw new IllegalArgumentException("输入的百分比字符串格式无效");
        }

        String numberStr = percentageStr.substring(0, percentageStr.length() - 1).trim();

        try {
            BigDecimal percentage = new BigDecimal(numberStr);

            // 限制最大值 98%
            if (percentage.compareTo(BigDecimal.valueOf(100)) >= 0) {
                return BigDecimal.valueOf(0.98);
            }
            if (percentage.compareTo(BigDecimal.ZERO) <= 0 ) {
                log.error("percentage{}百分比值小于0",numberStr);
                return BigDecimal.valueOf(0.98);
            }
            return percentage.divide(BigDecimal.valueOf(100), 10, RoundingMode.HALF_UP) // 先转换
                    .stripTrailingZeros()
                    .setScale(4, RoundingMode.HALF_UP);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("无法解析百分比值: " + percentageStr, e);
        }
    }

}
