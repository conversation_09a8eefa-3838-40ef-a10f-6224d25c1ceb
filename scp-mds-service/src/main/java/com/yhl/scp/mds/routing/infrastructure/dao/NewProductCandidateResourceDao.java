package com.yhl.scp.mds.routing.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMoldChangeTime;
import com.yhl.scp.mds.routing.infrastructure.po.ProductCandidateResourcePO;
import com.yhl.scp.mds.routing.vo.ProductCandidateResourceVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>ProductCandidateResourceDao</code>
 * <p>
 * 物品候选资源DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-18 11:45:18
 */
public interface NewProductCandidateResourceDao extends BaseDao<ProductCandidateResourcePO, ProductCandidateResourceVO> {

    /**
     * 组合查询`
     *
     * @param params 查询条件
     * @return list {@link ProductCandidateResourceVO}
     */
    List<ProductCandidateResourceVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 查询模具限制数量
     * @return
     */
    List<ProductCandidateResourceVO> selectMoldQuantityLimit();

    List<ProductCandidateResourcePO> selectByProductIdAndPhysicalResourceId(@Param("list") List<MesMoldChangeTime> mesMoldChangeTimeList);

    String selectProductOrganizeProductId(@Param("productId") String productId, @Param("stockPointId") String stockPointId);

	void doLogicDelete(@Param("ids") List<String> ids);

}
