<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mds.supplier.infrastructure.dao.SupplierDataDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO">
        <!--@Table mds_sup_supplier-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="supplier_delivery_date" jdbcType="INTEGER" property="supplierDeliveryDate"/>
        <result column="plan_display_cycle" jdbcType="INTEGER" property="planDisplayCycle"/>
        <result column="plan_display_cycle_month" jdbcType="INTEGER" property="planDisplayCycleMonth"/>
        <result column="supplier_abbreviation" jdbcType="VARCHAR" property="supplierAbbreviation"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="head_expiry_time" jdbcType="TIMESTAMP" property="headExpiryTime"/>
        <result column="receiving_month_day" jdbcType="VARCHAR" property="receivingMonthDay"/>
        <result column="receiving_week_day" jdbcType="VARCHAR" property="receivingWeekDay"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mds.extension.supplier.vo.SupplierVO">
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,organization_id,supplier_code,supplier_name,remark,enabled,creator,create_time,modifier,modify_time,
        supplier_delivery_date,plan_display_cycle,plan_display_cycle_month,supplier_abbreviation,supplier_id,head_expiry_time,receiving_month_day, receiving_week_day
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List"/>,organization_code,organization_name
    </sql>
    <sql id="Base_Where_Condition">
        <if test="params.id != null and params.id != ''">
            and id = #{params.id,jdbcType=VARCHAR}
        </if>
        <if test="params.organizationId != null and params.organizationId != ''">
            and organization_id = #{params.organizationId,jdbcType=VARCHAR}
        </if>
        <if test="params.supplierCode != null and params.supplierCode != ''">
            and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
        </if>
        <if test="params.supplierName != null and params.supplierName != ''">
            and supplier_name = #{params.supplierName,jdbcType=VARCHAR}
        </if>
        <if test="params.remark != null and params.remark != ''">
            and remark = #{params.remark,jdbcType=VARCHAR}
        </if>
        <if test="params.enabled != null and params.enabled != ''">
            and enabled = #{params.enabled,jdbcType=VARCHAR}
        </if>
        <if test="params.creator != null and params.creator != ''">
            and creator = #{params.creator,jdbcType=VARCHAR}
        </if>
        <if test="params.createTime != null">
            and create_time = #{params.createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.modifier != null and params.modifier != ''">
            and modifier = #{params.modifier,jdbcType=VARCHAR}
        </if>
        <if test="params.modifyTime != null">
            and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.ids != null and params.ids.size > 0">
            and id in
            <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.supplierCodes != null and params.supplierCodes.size > 0">
            and supplier_code in
            <foreach collection="params.supplierCodes" item="item" index="index" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="params.supplierDeliveryDate != null">
            and supplier_delivery_date = #{params.supplierDeliveryDate,jdbcType=INTEGER}
        </if>
        <if test="params.planDisplayCycle != null">
            and plan_display_cycle = #{params.planDisplayCycle,jdbcType=INTEGER}
        </if>
        <if test="params.planDisplayCycleMonth != null">
            and plan_display_cycle_month = #{params.planDisplayCycleMonth,jdbcType=INTEGER}
        </if>
        <if test="params.supplierAbbreviation != null">
            and supplier_abbreviation = #{params.supplierAbbreviation,jdbcType=VARCHAR}
        </if>
        <if test="params.supplierId != null">
            and supplier_id = #{params.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="params.headExpiryTime != null">
            and head_expiry_time = #{params.headExpiryTime,jdbcType=TIMESTAMP}
        </if>
        <if test="params.expiryTime != null and params.expiryTime != ''">
            and DATE(head_expiry_time) >= DATE(#{params.expiryTime,jdbcType=TIMESTAMP})
        </if>
        <if test="params.receivingMonthDay != null">
            and receiving_month_day = #{params.receivingMonthDay,jdbcType=VARCHAR}
        </if>
        <if test="params.receivingWeekDay != null">
            and receiving_week_day = #{params.receivingWeekDay,jdbcType=VARCHAR}
        </if>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mds_sup_supplier
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier
        <where>
            <include refid="Base_Where_Condition"/>
        </where>
    </select>
    <select id="selectBySupplierIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mds_sup_supplier
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                organization_id = #{item.orgId,jdbcType=VARCHAR} and supplier_id = #{item.supplierId,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectSupplierLike" resultMap="BaseResultMap">
        select
        supplier_name,supplier_code
        from mds_sup_supplier where 1 = 1
        <if test="supplierName != null and supplierName != ''">
            and supplier_name like #{supplierName,jdbcType=VARCHAR}
        </if>
    </select>
    <!-- 新增 -->
    <sql id="insertColumns">
        id,
        organization_id,
        supplier_code,
        supplier_name,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supplier_delivery_date,
        plan_display_cycle,
                  plan_display_cycle_month,
        supplier_abbreviation,
        supplier_id,
        head_expiry_time,
        receiving_month_day,
        receiving_week_day
    </sql>
    <sql id="insertValues">
        #{id,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{supplierDeliveryDate,jdbcType=INTEGER},
        #{planDisplayCycle,jdbcType=INTEGER},
        #{planDisplayCycleMonth,jdbcType=INTEGER},
        #{supplierAbbreviation,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{receivingMonthDay,jdbcType=VARCHAR},
        #{receivingWeekDay,jdbcType=VARCHAR}
    </sql>
    <insert id="insert" parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO">
        insert into mds_sup_supplier(<include refid="insertColumns"/>)
        values (<include refid="insertValues"/>)
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO">
        insert into mds_sup_supplier(
        id,
        organization_id,
        supplier_code,
        supplier_name,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supplier_delivery_date,
        plan_display_cycle,
        plan_display_cycle_month,
        supplier_abbreviation,
        supplier_id,
        head_expiry_time,
        receiving_month_day,
        receiving_week_day)
        values (
        #{id,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{supplierName,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{supplierDeliveryDate,jdbcType=INTEGER},
        #{planDisplayCycle,jdbcType=INTEGER},
        #{planDisplayCycleMonth,jdbcType=INTEGER},
        #{supplierAbbreviation,jdbcType=VARCHAR},
        #{supplierId,jdbcType=VARCHAR},
        #{headExpiryTime,jdbcType=TIMESTAMP},
        #{receivingMonthDay,jdbcType=VARCHAR},
        #{receivingWeekDay,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <sql id="insertBatchValues">
        #{entity.id,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.supplierCode,jdbcType=VARCHAR},
        #{entity.supplierName,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.supplierDeliveryDate,jdbcType=INTEGER},
        #{entity.planDisplayCycle,jdbcType=INTEGER},
        #{entity.planDisplayCycleMonth,jdbcType=INTEGER},
        #{entity.supplierAbbreviation,jdbcType=VARCHAR},
        #{entity.supplierId,jdbcType=VARCHAR},
        #{entity.headExpiryTime,jdbcType=TIMESTAMP},
        #{entity.receivingMonthDay,jdbcType=VARCHAR},
        #{entity.receivingWeekDay,jdbcType=VARCHAR}
    </sql>
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mds_sup_supplier(<include refid="insertColumns"/>)
        values
        <foreach collection="list" item="entity" separator=",">
            (<include refid="insertBatchValues"/>)
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mds_sup_supplier(
        id,
        organization_id,
        supplier_code,
        supplier_name,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        supplier_delivery_date,
        plan_display_cycle,
        plan_display_cycle_month,
        supplier_abbreviation,
        supplier_id,
        head_expiry_time,
        receiving_month_day,
        receiving_week_day)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.organizationId,jdbcType=VARCHAR},
            #{entity.supplierCode,jdbcType=VARCHAR},
            #{entity.supplierName,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.supplierDeliveryDate,jdbcType=INTEGER},
            #{entity.planDisplayCycle,jdbcType=INTEGER},
            #{entity.planDisplayCycleMonth,jdbcType=INTEGER},
            #{entity.supplierAbbreviation,jdbcType=VARCHAR},
            #{entity.supplierId,jdbcType=VARCHAR},
            #{entity.headExpiryTime,jdbcType=TIMESTAMP},
            #{entity.receivingMonthDay,jdbcType=VARCHAR},
            #{entity.receivingWeekDay,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <sql id="updateColumns">
        organization_id = #{organizationId,jdbcType=VARCHAR},
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
        supplier_name = #{supplierName,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        supplier_delivery_date = #{supplierDeliveryDate,jdbcType=INTEGER},
        plan_display_cycle = #{planDisplayCycle,jdbcType=INTEGER},
        plan_display_cycle_month = #{planDisplayCycleMonth,jdbcType=INTEGER},
        supplier_abbreviation = #{supplierAbbreviation,jdbcType=VARCHAR},
        supplier_id = #{supplierId,jdbcType=VARCHAR},
        head_expiry_time = #{headExpiryTime,jdbcType=TIMESTAMP},
        receiving_month_day = #{receivingMonthDay,jdbcType=VARCHAR},
        receiving_week_day = #{receivingWeekDay,jdbcType=VARCHAR}
    </sql>
    <update id="update" parameterType="com.yhl.scp.mds.extension.supplier.infrastructure.po.SupplierPO">
        update mds_sup_supplier set
        <include refid="updateColumns"/>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <sql id="updateSelectiveColumns">
        <if test="item.organizationId != null and item.organizationId != ''">
            organization_id = #{item.organizationId,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierCode != null and item.supplierCode != ''">
            supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierName != null and item.supplierName != ''">
            supplier_name = #{item.supplierName,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null and item.remark != ''">
            remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.enabled != null and item.enabled != ''">
            enabled = #{item.enabled,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null and item.creator != ''">
            creator = #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
            create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifier != null and item.modifier != ''">
            modifier = #{item.modifier,jdbcType=VARCHAR},
        </if>
        <if test="item.modifyTime != null">
            modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.supplierDeliveryDate != null">
            supplier_delivery_date = #{item.supplierDeliveryDate,jdbcType=INTEGER}
        </if>
        <if test="item.planDisplayCycle != null">
            and plan_display_cycle = #{item.planDisplayCycle,jdbcType=INTEGER}
        </if>
        <if test="item.planDisplayCycleMonth != null">
            and plan_display_cycle_month = #{item.planDisplayCycleMonth,jdbcType=INTEGER}
        </if>
        <if test="item.supplierAbbreviation != null">
            and supplier_abbreviation = #{item.supplierAbbreviation,jdbcType=VARCHAR}
        </if>
        <if test="item.supplierId != null and item.supplierId != ''">
            and supplier_id = #{item.supplierId,jdbcType=VARCHAR}
        </if>
        <if test="item.headExpiryTime != null">
            and head_expiry_time = #{item.headExpiryTime,jdbcType=TIMESTAMP}
        </if>
        <if test="item.receivingMonthDay != null and item.receivingMonthDay != ''">
            and receiving_month_day = #{item.receivingMonthDay,jdbcType=VARCHAR}
        </if>
        <if test="item.receivingWeekDay != null and item.receivingWeekDay != ''">
            and receiving_week_day = #{item.receivingWeekDay,jdbcType=VARCHAR}
        </if>
    </sql>
    <!-- 批量修改 -->
    <sql id="updateBatchColumns">
        <trim prefix="organization_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationId,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="supplier_code = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="supplier_name = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierName,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="remark = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="enabled = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="modifier = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="modify_time = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
            </foreach>
        </trim>
        <trim prefix="supplier_id = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierId,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="head_expiry_time = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.headExpiryTime,jdbcType=TIMESTAMP}
            </foreach>
        </trim>
        <trim prefix="receiving_month_day = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.receivingMonthDay,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="receiving_week_day = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.receivingWeekDay,jdbcType=VARCHAR}
            </foreach>
        </trim>
        <trim prefix="plan_display_cycle_month = case" suffix="end,">
            <foreach collection="list" index="index" item="item">
                when id = #{item.id,jdbcType=VARCHAR} then #{item.planDisplayCycleMonth,jdbcType=INTEGER}
            </foreach>
        </trim>
    </sql>
    <update id="updateBatch" parameterType="java.util.List">
        update mds_sup_supplier
        <trim prefix="set" suffixOverrides=",">
            <include refid="updateBatchColumns"/>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>    <!-- 批量选择修改 -->
    <sql id="updateBatchSelectiveColumns">
        <if test="item.organizationId != null and item.organizationId != ''">
            organization_id = #{item.organizationId,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierCode != null and item.supplierCode != ''">
            supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierName != null and item.supplierName != ''">
            supplier_name = #{item.supplierName,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierAbbreviation != null and item.supplierAbbreviation != ''">
            supplier_abbreviation = #{item.supplierAbbreviation,jdbcType=VARCHAR},
        </if>
        <if test="item.remark != null and item.remark != ''">
            remark = #{item.remark,jdbcType=VARCHAR},
        </if>
        <if test="item.enabled != null and item.enabled != ''">
            enabled = #{item.enabled,jdbcType=VARCHAR},
        </if>
        <if test="item.creator != null and item.creator != ''">
            creator = #{item.creator,jdbcType=VARCHAR},
        </if>
        <if test="item.createTime != null">
            create_time = #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.modifier != null and item.modifier != ''">
            modifier = #{item.modifier,jdbcType=VARCHAR},
        </if>
        <if test="item.modifyTime != null">
            modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.supplierId != null and item.supplierId != ''">
            supplier_id = #{item.supplierId,jdbcType=VARCHAR},
        </if>
        <if test="item.headExpiryTime != null">
            head_expiry_time = #{item.headExpiryTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.receivingMonthDay != null and item.receivingMonthDay != ''">
            receiving_month_day = #{item.receivingMonthDay,jdbcType=VARCHAR},
        </if>
        <if test="item.receivingWeekDay != null and item.receivingWeekDay != ''">
            receiving_week_day = #{item.receivingWeekDay,jdbcType=VARCHAR},
        </if>
        <if test="item.supplierDeliveryDate != null">
            supplier_delivery_date = #{item.supplierDeliveryDate},
        </if>
        <if test="item.planDisplayCycle != null">
            plan_display_cycle = #{item.planDisplayCycle},
        </if>
        <if test="item.planDisplayCycleMonth != null">
            plan_display_cycle_month = #{item.planDisplayCycleMonth},
        </if>
    </sql>
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mds_sup_supplier
            <set>
                <include refid="updateBatchSelectiveColumns"/>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mds_sup_supplier
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mds_sup_supplier where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectSeaTransportation" resultMap="VOResultMap">
        SELECT
        	DISTINCT a.supplier_code, a.supplier_name
		FROM
			mds_sup_supplier a
<!--			LEFT JOIN mds_sup_supplier_address b ON a.id = b.supplier_id -->
		WHERE
		a.enabled = 'YES'
<!--		AND b.address like concat('%', '运费', '%')-->
		order by a.supplier_code
    </select>
</mapper>
