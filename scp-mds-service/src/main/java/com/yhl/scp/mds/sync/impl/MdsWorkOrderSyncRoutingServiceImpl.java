package com.yhl.scp.mds.sync.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mds.basic.resource.convertor.PhysicalResourceConvertor;
import com.yhl.scp.mds.basic.routing.infrastructure.po.RoutingStepResourceBasicPO;
import com.yhl.scp.mds.extension.resource.domain.entity.PhysicalResourceDO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.domain.entity.*;
import com.yhl.scp.mds.extension.routing.infrastructure.po.*;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.resource.service.PhysicalResourceService;
import com.yhl.scp.mds.routing.convertor.*;
import com.yhl.scp.mds.routing.infrastructure.dao.*;
import com.yhl.scp.mds.routing.service.StandardStepService;
import com.yhl.scp.mds.sync.service.MdsWorkOrderSyncRoutingService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <code>MdsWorkOrderSyncRoutingServiceImpl</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-11 10:42:29
 */
@Slf4j
@Service
public class MdsWorkOrderSyncRoutingServiceImpl implements MdsWorkOrderSyncRoutingService {

    @Resource
    private RoutingDao routingDao;
    @Resource
    private RoutingStepDao routingStepDao;
    @Resource
    private RoutingStepInputDao routingStepInputDao;
    @Resource
    private RoutingStepOutputDao routingStepOutputDao;
    @Resource
    private RoutingStepResourceDao routingStepResourceDao;
    @Resource
    private PhysicalResourceService physicalResourceService;
    @Resource
    private StandardStepService standardStepService;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 构建RoutingDO对象，物品相关信息去掉了
     *
     * @param params
     * @return
     */
    @Override
    public List<RoutingDO> getRoutingDOByParams(Map<String, Object> params) {
        List<RoutingDO> routingDOList = new ArrayList<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        List<RoutingPO> routingPOS = routingDao.selectByParams(params);
        if (CollectionUtils.isEmpty(routingPOS)) {
            return routingDOList;
        }
        List<RoutingDO> routingDOS = RoutingConvertor.INSTANCE.po2Dos(routingPOS);
        routingDOList.addAll(routingDOS);
        List<String> routingIds = routingPOS.stream().map(RoutingPO::getId).collect(Collectors.toList());
        //查询路径步骤
        List<RoutingStepPO> routingSteps = routingStepDao.selectByParams(ImmutableMap.of("routingIds", routingIds, "enabled", YesOrNoEnum.YES.getCode()));
        if (CollectionUtils.isEmpty(routingSteps)) {
            routingSteps = new ArrayList<>();
        }
        List<String> routingStepIds = routingSteps.stream().map(RoutingStepPO::getId).collect(Collectors.toList());
        Map<String, List<RoutingStepPO>> routingIdToStepMaps = routingSteps.stream().collect(Collectors.groupingBy(RoutingStepPO::getRoutingId));
        //查询路径步骤输入
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());
        queryParams.put("routingStepIds", routingStepIds);
        List<RoutingStepInputPO> routingStepInputPOS = routingStepInputDao.selectByParams(queryParams);
        //查询路径步骤输出
        List<RoutingStepOutputPO> routingStepOutputPOS = routingStepOutputDao.selectByParams(queryParams);
        //查询路径步骤候选资源
        List<RoutingStepResourcePO> routingStepResourcePOS = routingStepResourceDao.selectByParams(queryParams);
        List<RoutingStepInputDO> routingStepInputDOS = RoutingStepInputConvertor.INSTANCE.po2Dos(routingStepInputPOS)
                .stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                input -> input.getRoutingStepId() + "-" + input.getInputProductId(),
                                input -> input,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));
        List<RoutingStepOutputDO> routingStepOutputDOS = RoutingStepOutputConvertor.INSTANCE.po2Dos(routingStepOutputPOS)
                .stream()
                .collect(Collectors.collectingAndThen(
                        Collectors.toMap(
                                input -> input.getRoutingStepId() + "-" + input.getOutputProductId(),
                                input -> input,
                                (existing, replacement) -> existing
                        ),
                        map -> new ArrayList<>(map.values())
                ));

        List<RoutingStepResourceDO> routingStepResourceDOS = RoutingStepResourceConvertor.INSTANCE.po2Dos(routingStepResourcePOS);
        Map<String, List<RoutingStepInputDO>> stepId2InputsMaps = routingStepInputDOS.stream().collect(Collectors.groupingBy(RoutingStepInputDO::getRoutingStepId));
        Map<String, List<RoutingStepOutputDO>> stepId2OutputsMaps = routingStepOutputDOS.stream().collect(Collectors.groupingBy(RoutingStepOutputDO::getRoutingStepId));
        Map<String, List<RoutingStepResourceDO>> stepId2ResourcesMaps = routingStepResourceDOS.stream().collect(Collectors.groupingBy(RoutingStepResourceDO::getRoutingStepId));
        //查询相关资源
        List<String> specialCandidateStandardResourceIds = routingStepResourcePOS.stream().filter(k -> k.getPhysicalResourceId().equals("*")).map(RoutingStepResourceBasicPO::getStandardResourceId).collect(Collectors.toList());
        List<String> normalPhysicalResourceIds = routingStepResourcePOS.stream().map(RoutingStepResourceBasicPO::getPhysicalResourceId).filter(physicalResourceId -> !physicalResourceId.equals("*")).distinct().collect(Collectors.toList());
        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectByIds(normalPhysicalResourceIds);
        Map<String, PhysicalResourceVO> physicalResourceIdMaps = physicalResourceVOS.stream().collect(Collectors.toMap(BaseVO::getId, v -> v));

        // 添加缓存逻辑
        List<PhysicalResourceVO> specialPhysicalResourceVOS;
        String cacheKey = "specialPhysicalResourceVOS:" + String.join(",", specialCandidateStandardResourceIds);
        String cachedData = redisTemplate.opsForValue().get(cacheKey);
        if (cachedData != null) {
            specialPhysicalResourceVOS = JSON.parseArray(cachedData, PhysicalResourceVO.class);
        } else {
            specialPhysicalResourceVOS = physicalResourceService.selectByStandResourceIds(specialCandidateStandardResourceIds);
            redisTemplate.opsForValue().set(cacheKey, JSON.toJSONString(specialPhysicalResourceVOS), 1, TimeUnit.HOURS);
        }

        Map<String, List<PhysicalResourceVO>> standardResourceId2PhysicalResourceMaps = specialPhysicalResourceVOS.stream().collect(Collectors.groupingBy(PhysicalResourceVO::getStandardResourceId));
        //相关标准工艺
        List<String> standardStepIds = routingSteps.stream().map(RoutingStepPO::getStandardStepId).distinct().collect(Collectors.toList());
        List<StandardStepVO> standardStepVOS = standardStepService.selectByIds(standardStepIds);
        Map<String, StandardStepVO> standardStepMaps = standardStepVOS.stream().collect(Collectors.toMap(StandardStepVO::getId, v -> v));
        routingDOS.parallelStream().forEach(routingDO -> {
            List<RoutingStepPO> routingStepPOS = routingIdToStepMaps.get(routingDO.getId());
            if (CollectionUtils.isNotEmpty(routingStepPOS)) {
                List<RoutingStepDO> routingStepDOS = RoutingStepConvertor.INSTANCE.po2Dos(routingStepPOS);
                for (RoutingStepDO routingStepDO : routingStepDOS) {
                    List<RoutingStepInputDO> inputs = stepId2InputsMaps.getOrDefault(routingStepDO.getId(), new ArrayList<>());
                    List<RoutingStepOutputDO> outputs = stepId2OutputsMaps.getOrDefault(routingStepDO.getId(), new ArrayList<>());
                    List<RoutingStepResourceDO> resources = stepId2ResourcesMaps.getOrDefault(routingStepDO.getId(), new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(resources)) {
                        for (RoutingStepResourceDO resource : resources) {
                            String physicalResourceId = resource.getPhysicalResourceId();
                            if ("*".equals(physicalResourceId)) {
                                String standardResourceId = resource.getStandardResourceId();
                                List<PhysicalResourceVO> physicalResourceVOSOfThis = standardResourceId2PhysicalResourceMaps.get(standardResourceId);
                                if (CollectionUtils.isNotEmpty(physicalResourceVOSOfThis)) {
                                    List<PhysicalResourceDO> physicalResourceDOS = PhysicalResourceConvertor.INSTANCE.vo2dos(physicalResourceVOSOfThis);
                                    resource.setPhysicalResourceDOS(physicalResourceDOS);
                                }
                            } else {
                                PhysicalResourceVO physicalResourceVO = physicalResourceIdMaps.get(physicalResourceId);
                                if (physicalResourceVO != null) {
                                    PhysicalResourceDO physicalResourceDO = PhysicalResourceConvertor.INSTANCE.vo2do(physicalResourceVO);
                                    resource.setPhysicalResourceDOS(Collections.singletonList(physicalResourceDO));
                                }
                            }
                        }
                    }
                    routingStepDO.setRoutingStepInputDOList(inputs);
                    routingStepDO.setRoutingStepOutputDOList(outputs);
                    routingStepDO.setRoutingStepResourceDOList(resources);
                    StandardStepVO standardStepVO = standardStepMaps.get(routingStepDO.getStandardStepId());
                    routingStepDO.setStandardStepDO(StandardStepConvertor.INSTANCE.vo2do(standardStepVO));
                }
                routingDO.setRoutingStepDOList(routingStepDOS);
            }
        });
        return routingDOList;
    }
}
