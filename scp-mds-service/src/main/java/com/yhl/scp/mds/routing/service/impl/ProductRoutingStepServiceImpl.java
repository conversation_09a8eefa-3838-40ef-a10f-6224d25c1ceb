package com.yhl.scp.mds.routing.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.routing.convertor.ProductRoutingStepConvertor;
import com.yhl.scp.mds.routing.domain.entity.ProductRoutingStepDO;
import com.yhl.scp.mds.routing.domain.service.ProductRoutingStepDomainService;
import com.yhl.scp.mds.routing.dto.ProductRoutingStepDTO;
import com.yhl.scp.mds.routing.infrastructure.dao.NewRoutingStepDao;
import com.yhl.scp.mds.routing.infrastructure.dao.ProductRoutingStepDao;
import com.yhl.scp.mds.routing.infrastructure.po.NewRoutingStepPO;
import com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO;
import com.yhl.scp.mds.routing.service.ProductRoutingStepService;
import com.yhl.scp.mds.routing.vo.ProductRoutingStepVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>ProductRoutingStepServiceImpl</code>
 * <p>
 * 物品工艺路径步骤应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:51:57
 */
@Slf4j
@Service
public class ProductRoutingStepServiceImpl extends AbstractService implements ProductRoutingStepService {

    @Resource
    private ProductRoutingStepDao productRoutingStepDao;

    @Resource
    private ProductRoutingStepDomainService productRoutingStepDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewRoutingStepDao newRoutingStepDao;

    @Override
    public BaseResponse<Void> doCreate(ProductRoutingStepDTO productRoutingStepDTO) {
        // 0.数据转换
        ProductRoutingStepDO productRoutingStepDO = ProductRoutingStepConvertor.INSTANCE.dto2Do(productRoutingStepDTO);
        ProductRoutingStepPO productRoutingStepPO = ProductRoutingStepConvertor.INSTANCE.dto2Po(productRoutingStepDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        productRoutingStepDomainService.validation(productRoutingStepDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(productRoutingStepPO);
        productRoutingStepDao.insert(productRoutingStepPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ProductRoutingStepDTO productRoutingStepDTO) {
        // 0.数据转换
        ProductRoutingStepDO productRoutingStepDO = ProductRoutingStepConvertor.INSTANCE.dto2Do(productRoutingStepDTO);
        ProductRoutingStepPO productRoutingStepPO = ProductRoutingStepConvertor.INSTANCE.dto2Po(productRoutingStepDTO);
        // 1.数据校验
        productRoutingStepDomainService.validation(productRoutingStepDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(productRoutingStepPO);
        productRoutingStepDao.updateSelective(productRoutingStepPO);
        //修改对应产品工艺路径的成品率
        NewRoutingStepPO updateStep = new NewRoutingStepPO();
        updateStep.setId(productRoutingStepDTO.getId());
        updateStep.setYield(productRoutingStepDTO.getYield());
        BasePOUtils.updateFiller(updateStep);
        newRoutingStepDao.updateSelective(updateStep);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ProductRoutingStepDTO> list) {
        List<ProductRoutingStepPO> newList = ProductRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        productRoutingStepDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ProductRoutingStepDTO> list) {
        // 获取当前线程的堆栈跟踪
//        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//        log.info("productRoutingStepService doUpdateBatch method called with {} records", list != null ? list.size() : 0);
//
//        if (stackTrace.length > 2) {  // 索引0是getStackTrace，索引1是当前方法
//            StackTraceElement caller = stackTrace[2];
//            log.info("Direct caller: {}.{} ({}:{})",
//                    caller.getClassName(),
//                    caller.getMethodName(),
//                    caller.getFileName(),
//                    caller.getLineNumber());
//        }
//
//        // 打印完整调用链
//        log.info("Complete call stack trace:");
//        for (int i = 1; i < stackTrace.length; i++) {
//            String indentation = StringUtils.repeat("  ", i-1);
//
//            log.info("{}at {}.{} ({}:{})",
//                    indentation,
//                    stackTrace[i].getClassName(),
//                    stackTrace[i].getMethodName(),
//                    stackTrace[i].getFileName(),
//                    stackTrace[i].getLineNumber());
//        }
        List<ProductRoutingStepPO> newList = ProductRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        log.info("Executing batch update product_routing_step in database...");
        productRoutingStepDao.updateBatch(newList);
        log.info("Batch update product_routing_step completed successfully");
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return productRoutingStepDao.deleteBatch(idList);
        }
        return productRoutingStepDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ProductRoutingStepVO selectByPrimaryKey(String id) {
        ProductRoutingStepPO po = productRoutingStepDao.selectByPrimaryKey(id);
        return ProductRoutingStepConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "PRODUCT_ROUTING_STEP")
    public List<ProductRoutingStepVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "PRODUCT_ROUTING_STEP")
    public List<ProductRoutingStepVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ProductRoutingStepVO> dataList = productRoutingStepDao.selectByCondition(sortParam, queryCriteriaParam);
        ProductRoutingStepServiceImpl target = SpringBeanUtils.getBean(ProductRoutingStepServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ProductRoutingStepVO> selectByParams(Map<String, Object> params) {
        List<ProductRoutingStepPO> list = productRoutingStepDao.selectByParams(params);
        return ProductRoutingStepConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ProductRoutingStepVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<ProductRoutingStepVO> selectByOperationSequenceIds(List<String> operationSequenceIds) {
        if (CollectionUtils.isNotEmpty(operationSequenceIds)) {
            return ProductRoutingStepConvertor.INSTANCE.po2Vos(productRoutingStepDao.selectByOperationSequenceIds(operationSequenceIds));
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public List<ProductRoutingStepVO> selectByRoutingIds(List<String> routingIds) {
        if (CollectionUtils.isNotEmpty(routingIds)) {
            return productRoutingStepDao.selectByRoutingIds(routingIds);
        } else {
            return new ArrayList<>();
        }
    }

    @Override
    public void doCreateBatchWithPartition(List<ProductRoutingStepDTO> list) {
        List<ProductRoutingStepPO> newList = ProductRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> productRoutingStepDao.insertBatch(poList), null);
    }

    @Override
    public void doUpdateBatchWithPartition(List<ProductRoutingStepDTO> list) {
        List<ProductRoutingStepPO> newList = ProductRoutingStepConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        // 获取当前线程的堆栈跟踪
//        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
//        log.info("productRoutingStepService doUpdateBatchWithPartition method called with {} records", list != null ? list.size() : 0);

//        if (stackTrace.length > 2) {  // 索引0是getStackTrace，索引1是当前方法
//            StackTraceElement caller = stackTrace[2];
//            log.info("Direct caller: {}.{} ({}:{})",
//                    caller.getClassName(),
//                    caller.getMethodName(),
//                    caller.getFileName(),
//                    caller.getLineNumber());
//        }

        // 打印完整调用链
//        log.info("Complete call stack trace:");
//        for (int i = 1; i < stackTrace.length; i++) {
//            String indentation = StringUtils.repeat("  ", i-1);
//
//            log.info("{}at {}.{} ({}:{})",
//                    indentation,
//                    stackTrace[i].getClassName(),
//                    stackTrace[i].getMethodName(),
//                    stackTrace[i].getFileName(),
//                    stackTrace[i].getLineNumber());
//        }
        log.info("Executing bulkUpdateOrCreate product_routing_step in database...");

        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> productRoutingStepDao.updateBatch(poList), null);
        log.info("Executing bulkUpdateOrCreate product_routing_step in database...");

    }

    @Override
    public void doLogicDeleteBatch(List<RemoveVersionDTO> deleteProductRoutingStepVersionList) {
        productRoutingStepDao.doLogicDeleteBatch(deleteProductRoutingStepVersionList);
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<ProductRoutingStepVO> invocation(List<ProductRoutingStepVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
