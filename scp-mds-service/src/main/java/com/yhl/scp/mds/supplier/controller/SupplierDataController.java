package com.yhl.scp.mds.supplier.controller;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.baseResource.service.FyStandardResourceService;
import com.yhl.scp.mds.extension.supplier.dto.SupplierDTO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.supplier.service.SupplierDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>SupplierController</code>
 * <p>
 * 供应商控制器
 * </p>
 *supplierControllersupplierController
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-09 16:19:48
 */
@Slf4j
@Api(tags = "供应商控制器")
@RestController
@RequestMapping("supplierData")
public class SupplierDataController extends BaseController {

    @Resource
    private SupplierDataService supplierService;


    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<SupplierVO>> page() {
        List<SupplierVO> supplierList = supplierService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<SupplierVO> pageInfo = new PageInfo<>(supplierList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody SupplierDTO supplierDTO) {
        return supplierService.doCreate(supplierDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody SupplierDTO supplierDTO) {
        return supplierService.doUpdate(supplierDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        supplierService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<SupplierVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, supplierService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "下拉查询")
    @GetMapping(value = "dropdown")
    @SuppressWarnings("unchecked")
    public BaseResponse<List<LabelValue<String>>> dropdown() {
        List<SupplierVO> list = supplierService.selectAll();
        list.sort(Comparator.comparing(SupplierVO::getSupplierCode));
        List<LabelValue<String>> data = list.stream()
                .map(item -> new LabelValue<>(item.getSupplierName() + "(" + item.getSupplierCode() + ")", item.getId())).collect(Collectors.toList());
        return BaseResponse.success(BaseResponse.OP_SUCCESS, data);
    }

    @ApiOperation(value = "下拉查询（value为供应商编码）")
    @GetMapping(value = "dropDownSupplierCode")
    public BaseResponse<List<LabelValue<String>>> dropdown02() {
        List<SupplierVO> list = supplierService.selectAll();
        list.sort(Comparator.comparing(SupplierVO::getSupplierCode));
        List<LabelValue<String>> data = list.stream()
                .map(item -> new LabelValue<>(item.getSupplierName() + "(" + item.getSupplierCode() + ")", item.getSupplierCode())).collect(Collectors.toList());
        return BaseResponse.success(BaseResponse.OP_SUCCESS, data);
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        supplierService.export(response);
    }


    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncSupplier() {
        return supplierService.syncSupplier(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "承运商下拉")
    @GetMapping(value = "supplyDropdown")
    public List<LabelValue<String>> supplyDropdown() {
        return supplierService.supplyDropdown();
    }

    @ApiOperation(value = "供应商下拉（模糊搜索）")
    @GetMapping(value = "selectSupplierLike")
    public BaseResponse<List<LabelValue<String>>> selectSupplierLike(@RequestParam(value = "supplierName",
            required = false) String supplierName) {
        List<SupplierVO> list = supplierService.selectSupplierLike(supplierName)
                .stream()
                .filter(data -> data != null && StringUtils.isNotEmpty(data.getSupplierName()))
                .distinct()
                .collect(Collectors.toList());

        List<LabelValue<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(x -> {
                LabelValue<String> labelValue = new LabelValue<>();
                labelValue.setLabel(x.getSupplierName());
                labelValue.setValue(x.getSupplierCode());
                result.add(labelValue);
            });
        }
        return BaseResponse.success(result);
    }

}