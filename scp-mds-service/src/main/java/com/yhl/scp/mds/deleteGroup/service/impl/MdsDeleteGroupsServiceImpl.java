package com.yhl.scp.mds.deleteGroup.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpDeleteGroup;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.bom.service.MdsProductBomService;
import com.yhl.scp.mds.bom.service.MdsProductBomVersionService;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.deleteGroup.convertor.MdsDeleteGroupsConvertor;
import com.yhl.scp.mds.deleteGroup.domain.entity.MdsDeleteGroupsDO;
import com.yhl.scp.mds.deleteGroup.domain.service.MdsDeleteGroupsDomainService;
import com.yhl.scp.mds.deleteGroup.dto.MdsDeleteGroupsDTO;
import com.yhl.scp.mds.deleteGroup.infrastructure.dao.MdsDeleteGroupsDao;
import com.yhl.scp.mds.deleteGroup.infrastructure.po.MdsDeleteGroupsPO;
import com.yhl.scp.mds.deleteGroup.service.MdsDeleteGroupsService;
import com.yhl.scp.mds.deleteGroup.vo.MdsDeleteGroupsVO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointBasicDTO;
import com.yhl.scp.mds.newproduct.dto.NewProductStockPointDTO;
import com.yhl.scp.mds.newproduct.service.NewProductStockPointService;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.routing.service.NewRoutingService;
import com.yhl.scp.mds.routing.service.ProductBomVersionService;
import com.yhl.scp.mds.routing.service.ProductRoutingService;
import com.yhl.scp.mds.routing.service.ProductRoutingStepService;
import com.yhl.scp.mds.routing.vo.ProductRoutingStepVO;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;
import com.yhl.scp.mds.stock.service.NewStockPointService;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MdsDeleteGroupsServiceImpl</code>
 * <p>
 * ERP删除组接口中间表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 20:24:50
 */
@Slf4j
@Service
public class MdsDeleteGroupsServiceImpl extends AbstractService implements MdsDeleteGroupsService {

    @Resource
    private MdsDeleteGroupsDao mdsDeleteGroupsDao;

    @Resource
    private MdsDeleteGroupsDomainService mdsDeleteGroupsDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewStockPointService newStockPointService;

    @Resource
    private NewProductStockPointService newProductStockPointService;

    @Resource
    private ProductRoutingService productRoutingService;

    @Resource
    private ProductRoutingStepService productRoutingStepService;
    @Resource
    private MdsProductBomVersionService mdsProductBomVersionService;
    @Resource
    private MdsProductBomService mdsProductBomService;

    @Resource
    private NewRoutingService newRoutingService;


    @Override
    public BaseResponse<Void> doCreate(MdsDeleteGroupsDTO mdsDeleteGroupsDTO) {
        // 0.数据转换
        MdsDeleteGroupsDO mdsDeleteGroupsDO = MdsDeleteGroupsConvertor.INSTANCE.dto2Do(mdsDeleteGroupsDTO);
        MdsDeleteGroupsPO mdsDeleteGroupsPO = MdsDeleteGroupsConvertor.INSTANCE.dto2Po(mdsDeleteGroupsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsDeleteGroupsDomainService.validation(mdsDeleteGroupsDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mdsDeleteGroupsPO);
        mdsDeleteGroupsDao.insert(mdsDeleteGroupsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MdsDeleteGroupsDTO mdsDeleteGroupsDTO) {
        // 0.数据转换
        MdsDeleteGroupsDO mdsDeleteGroupsDO = MdsDeleteGroupsConvertor.INSTANCE.dto2Do(mdsDeleteGroupsDTO);
        MdsDeleteGroupsPO mdsDeleteGroupsPO = MdsDeleteGroupsConvertor.INSTANCE.dto2Po(mdsDeleteGroupsDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mdsDeleteGroupsDomainService.validation(mdsDeleteGroupsDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mdsDeleteGroupsPO);
        mdsDeleteGroupsDao.update(mdsDeleteGroupsPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MdsDeleteGroupsDTO> list) {
        List<MdsDeleteGroupsPO> newList = MdsDeleteGroupsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mdsDeleteGroupsDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MdsDeleteGroupsDTO> list) {
        List<MdsDeleteGroupsPO> newList = MdsDeleteGroupsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mdsDeleteGroupsDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mdsDeleteGroupsDao.deleteBatch(idList);
        }
        return mdsDeleteGroupsDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MdsDeleteGroupsVO selectByPrimaryKey(String id) {
        MdsDeleteGroupsPO po = mdsDeleteGroupsDao.selectByPrimaryKey(id);
        return MdsDeleteGroupsConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MDS_DELETE_GROUPS")
    public List<MdsDeleteGroupsVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MDS_DELETE_GROUPS")
    public List<MdsDeleteGroupsVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MdsDeleteGroupsVO> dataList = mdsDeleteGroupsDao.selectByCondition(sortParam, queryCriteriaParam);
        MdsDeleteGroupsServiceImpl target = SpringBeanUtils.getBean(MdsDeleteGroupsServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MdsDeleteGroupsVO> selectByParams(Map<String, Object> params) {
        List<MdsDeleteGroupsPO> list = mdsDeleteGroupsDao.selectByParams(params);
        return MdsDeleteGroupsConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MdsDeleteGroupsVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void doCreateBatchWithPartition(List<MdsDeleteGroupsDTO> list) {
        List<MdsDeleteGroupsPO> newList = MdsDeleteGroupsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> mdsDeleteGroupsDao.insertBatch(poList), null);
    }

    @Override
    public void doUpdateBatchWithPartition(List<MdsDeleteGroupsDTO> list) {
        List<MdsDeleteGroupsPO> newList = MdsDeleteGroupsConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        BulkOperationUtils.bulkUpdateOrCreate(newList, (poList) -> mdsDeleteGroupsDao.updateBatch(poList), null);
    }

    @Override
    public List<MdsDeleteGroupsVO> selectByDeleteGroupNames(List<String> deleteGroupNames) {
        return MdsDeleteGroupsConvertor.INSTANCE.po2Vos(mdsDeleteGroupsDao.selectByDeleteGroupNames(deleteGroupNames));

    }

    @Override
    public BaseResponse<Void> sync(List<ErpDeleteGroup> o) {

        if (CollectionUtils.isEmpty(o)) {
            log.error("ERP删除组数据为空");
            return BaseResponse.error("ERP删除组数据为空");
        }
        List<MdsDeleteGroupsDTO> insertMdsDeleteGroupsDTOS = Lists.newArrayList();
        List<NewProductStockPointDTO> deleteProductList = Lists.newArrayList();
        List<String> deleteBomVersionList = Lists.newArrayList();
        List<String> deleteProductRoutingList = Lists.newArrayList();
        List<String> deleteBomLineList = Lists.newArrayList();
        List<String> deleteProductRoutingStepList = Lists.newArrayList();

        for (ErpDeleteGroup erpDeleteGroup : o) {
            String deleteEntity = erpDeleteGroup.getDeleteType();
            MdsDeleteGroupsDTO mdsDeleteGroupsDTO = new MdsDeleteGroupsDTO();
            // 新增
            mdsDeleteGroupsDTO.setStockPointCode(erpDeleteGroup.getOrganizationCode());
            mdsDeleteGroupsDTO.setStockPointName(erpDeleteGroup.getOrganizationName());
            mdsDeleteGroupsDTO.setDeleteGroupName(erpDeleteGroup.getDeleteGroupName());
            mdsDeleteGroupsDTO.setDeleteType(erpDeleteGroup.getDeleteType());
            mdsDeleteGroupsDTO.setItemConcatSegments(erpDeleteGroup.getItemConcatSegments());
            mdsDeleteGroupsDTO.setItemDescription(erpDeleteGroup.getItemDescription());
            mdsDeleteGroupsDTO.setDeleteEntity(erpDeleteGroup.getDeleteEntity());
            mdsDeleteGroupsDTO.setBillSequenceId(erpDeleteGroup.getBillSequenceId());
            mdsDeleteGroupsDTO.setComponentSequenceId(erpDeleteGroup.getComponentSequenceId());
            mdsDeleteGroupsDTO.setRoutingSequenceId(erpDeleteGroup.getRoutingSequenceId());
            mdsDeleteGroupsDTO.setOperationSequenceId(erpDeleteGroup.getOperationSequenceId());
            mdsDeleteGroupsDTO.setOperationSeqNum(erpDeleteGroup.getOperationSeqNum());
            mdsDeleteGroupsDTO.setOperationDepartmentCode(erpDeleteGroup.getOperationDepartmentCode());
            mdsDeleteGroupsDTO.setDescription(erpDeleteGroup.getDescription());
            mdsDeleteGroupsDTO.setDeleteStatus(erpDeleteGroup.getDeleteStatus());
            mdsDeleteGroupsDTO.setComponentConcatSegments(erpDeleteGroup.getComponentConcatSegments());
            mdsDeleteGroupsDTO.setItemNum(erpDeleteGroup.getItemNum());
            mdsDeleteGroupsDTO.setEffectivityDate(erpDeleteGroup.getEffectivityDate());
            mdsDeleteGroupsDTO.setDeleteDate(erpDeleteGroup.getDeleteDate());
            mdsDeleteGroupsDTO.setLastUpdateDate(erpDeleteGroup.getLastUpdateDate());
            mdsDeleteGroupsDTO.setEnabled(YesOrNoEnum.YES.getCode());
            insertMdsDeleteGroupsDTOS.add(mdsDeleteGroupsDTO);

            if("1".equals(deleteEntity)){
                NewProductStockPointDTO newProductStockPointDTO = new NewProductStockPointDTO();
                newProductStockPointDTO.setStockPointCode(erpDeleteGroup.getOrganizationCode());
                newProductStockPointDTO.setProductCode(erpDeleteGroup.getItemConcatSegments());
                deleteProductList.add(newProductStockPointDTO);
            }else if("2".equals(deleteEntity)){
                deleteBomVersionList.add(erpDeleteGroup.getBillSequenceId());
            }else if("3".equals(deleteEntity)){
                deleteProductRoutingList.add(erpDeleteGroup.getRoutingSequenceId());
            }else if("4".equals(deleteEntity)){
                deleteBomLineList.add(erpDeleteGroup.getComponentSequenceId());
            }else if("5".equals(deleteEntity)){
                deleteProductRoutingStepList.add(erpDeleteGroup.getOperationSequenceId());
            }

        }
        if (CollectionUtils.isNotEmpty(insertMdsDeleteGroupsDTOS)) {
            doCreateBatchWithPartition(insertMdsDeleteGroupsDTOS);
            log.info("新增删除组数据条数：{}", insertMdsDeleteGroupsDTOS.size());
        }
        if (CollectionUtils.isNotEmpty(deleteProductList)) {
            List<String> productCodes = deleteProductList.stream().map(NewProductStockPointBasicDTO::getProductCode)
                    .distinct().collect(Collectors.toList());
            List<NewProductStockPointVO> newProductStockPointVOS = newProductStockPointService.selectByProductCode(productCodes);
            Map<String, NewProductStockPointVO> newProductStockPointVOMap =
                    CollectionUtils.isEmpty(newProductStockPointVOS) ?
                            MapUtil.newHashMap() :
                            newProductStockPointVOS.stream().filter(t->Objects.equals(t.getEnabled(),YesOrNoEnum.YES.getCode())).collect(
                                    Collectors.toMap(t -> t.getProductCode() + "|" + t.getStockPointCode(),
                                            Function.identity(), (v1, v2) -> v1));
            List<RemoveVersionDTO> deleteProductVersionList = Lists.newArrayList();
            for(NewProductStockPointBasicDTO newProductStockPointBasicDTO:deleteProductList){
                String productKey = newProductStockPointBasicDTO.getProductCode()+"|"+newProductStockPointBasicDTO.getStockPointCode();
                NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMap.get(productKey);
                if(Objects.isNull(newProductStockPointVO)){
                    log.error("没找到对应的有效物料，productKey：{}", productKey);
                    continue;
                }
                RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                removeVersionDTO.setId(newProductStockPointBasicDTO.getProductCode());
                removeVersionDTO.setVersionValue(newProductStockPointVO.getVersionValue());
                deleteProductVersionList.add(removeVersionDTO);
            }
            if(CollectionUtils.isNotEmpty(deleteProductVersionList)) {
                newProductStockPointService.doLogicDeleteBatch(deleteProductVersionList);
            }
            log.info("删除物料数据条数：{}", deleteProductVersionList.size());
        }
        if (CollectionUtils.isNotEmpty(deleteBomVersionList)) {
            //todo 删除bom版本数据
            List<ProductBomVersionVO> productBomVersionVOS = mdsProductBomVersionService.selectByBillSequenceIds(deleteBomVersionList);
            List<RemoveVersionDTO> deleteProductBomVersionList = Lists.newArrayList();
            for(ProductBomVersionVO productBomVersionVO:productBomVersionVOS){
                RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                removeVersionDTO.setId(productBomVersionVO.getId());
                removeVersionDTO.setVersionValue(productBomVersionVO.getVersionValue());
                deleteProductBomVersionList.add(removeVersionDTO);
            }
            if(CollectionUtils.isNotEmpty(deleteProductBomVersionList)){
                mdsProductBomVersionService.doLogicDeleteBatch(deleteProductBomVersionList);
                log.info("删除BOM版本数据条数：{}", deleteProductBomVersionList.size());
                List<String> deletedBomVersionIds = deleteProductBomVersionList.stream().map(RemoveVersionDTO::getId).collect(Collectors.toList());
                List<ProductBomVO> productBomVOS = mdsProductBomService.selectByBillBomVersionIds(deletedBomVersionIds);
                if(CollectionUtils.isNotEmpty(productBomVOS)){
                    List<RemoveVersionDTO> deleteProductBomList = Lists.newArrayList();
                    for(ProductBomVO productBomVO:productBomVOS){
                        RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                        removeVersionDTO.setId(productBomVO.getId());
                        removeVersionDTO.setVersionValue(productBomVO.getVersionValue());
                        deleteProductBomList.add(removeVersionDTO);
                    }
                    if(CollectionUtils.isNotEmpty(deleteProductBomList)) {
                        mdsProductBomService.doLogicDeleteBatch(deleteProductBomList);
                    }
                    log.info("删除BOM数据条数：{}", deleteProductBomList.size());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(deleteProductRoutingList)) {
            List<ProductRoutingVO> productRoutingVOS = productRoutingService.selectByRoutingSequenceIds(deleteProductRoutingList);
            List<RemoveVersionDTO> deleteProductRoutingVersionList = Lists.newArrayList();
            for(ProductRoutingVO productRoutingVO:productRoutingVOS){
                RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                removeVersionDTO.setId(productRoutingVO.getId());
                removeVersionDTO.setVersionValue(productRoutingVO.getVersionValue());
                deleteProductRoutingVersionList.add(removeVersionDTO);
            }
            if(CollectionUtils.isNotEmpty(deleteProductRoutingVersionList)){
                productRoutingService.doLogicDeleteBatch(deleteProductRoutingVersionList);
                log.info("删除工艺路径数据条数：{}", deleteProductRoutingVersionList.size());
                List<String> deletedRoutingIds = deleteProductRoutingVersionList.stream().map(RemoveVersionDTO::getId).collect(Collectors.toList());
                List<ProductRoutingStepVO> productRoutingStepVOS = productRoutingStepService.selectByRoutingIds(deletedRoutingIds);
                if(CollectionUtils.isNotEmpty(productRoutingStepVOS)){
                    List<RemoveVersionDTO> deleteStepVersionList = Lists.newArrayList();
                    for(ProductRoutingStepVO productRoutingStepVO:productRoutingStepVOS){
                        RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                        removeVersionDTO.setId(productRoutingStepVO.getId());
                        removeVersionDTO.setVersionValue(productRoutingStepVO.getVersionValue());
                        deleteStepVersionList.add(removeVersionDTO);
                    }
                    if(CollectionUtils.isNotEmpty(deleteStepVersionList)) {
                        productRoutingStepService.doLogicDeleteBatch(deleteStepVersionList);
                    }
                    log.info("删除工艺路径头时，删除工艺路径步骤数据条数：{}", deleteStepVersionList.size());
                }
            }
        }
        if (CollectionUtils.isNotEmpty(deleteBomLineList)) {
            //todo 删除bom行数据
            List<ProductBomVO> productBomVOS = mdsProductBomService.selectByComponentSequenceIds(deleteBomLineList);
            List<RemoveVersionDTO> deleteProductBomList = Lists.newArrayList();
            for(ProductBomVO productBomVO:productBomVOS){
                RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                removeVersionDTO.setId(productBomVO.getId());
                removeVersionDTO.setVersionValue(productBomVO.getVersionValue());
                deleteProductBomList.add(removeVersionDTO);
            }
            if(CollectionUtils.isNotEmpty(deleteProductBomList)){
                mdsProductBomService.doLogicDeleteBatch(deleteProductBomList);
                log.info("删除BOM行数据条数：{}", deleteProductBomList.size());
            }
        }
        if (CollectionUtils.isNotEmpty(deleteProductRoutingStepList)) {
            List<ProductRoutingStepVO> productRoutingStepVOS = productRoutingStepService.selectByOperationSequenceIds(deleteProductRoutingStepList);
            List<RemoveVersionDTO> deleteProductRoutingStepVersionList = Lists.newArrayList();

            for(ProductRoutingStepVO productRoutingStepVO:productRoutingStepVOS){
                RemoveVersionDTO removeVersionDTO = new RemoveVersionDTO();
                removeVersionDTO.setId(productRoutingStepVO.getId());
                removeVersionDTO.setVersionValue(productRoutingStepVO.getVersionValue());
                log.info("removeVersionDTO id:{},versionValue:{}",productRoutingStepVO.getId(),productRoutingStepVO.getVersionValue());

                deleteProductRoutingStepVersionList.add(removeVersionDTO);
            }
            log.info("deleteProductRoutingStepVersionList.size:{}",deleteProductRoutingStepVersionList.size());
            if(CollectionUtils.isNotEmpty(deleteProductRoutingStepVersionList)) {
                productRoutingStepService.doLogicDeleteBatch(deleteProductRoutingStepVersionList);
            }
            log.info("根据接口返回，删除工艺路径步骤数据条数：{}", deleteProductRoutingStepVersionList.size());
        }

        //同步删除对应的工艺路径，工艺路径步骤，输入输出物品，候选资源
        newRoutingService.doDeleteRoutingDate(deleteProductRoutingList, deleteBomVersionList,
        		deleteBomLineList, deleteProductRoutingStepList);

        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> syncDeleteGroups(String tenantId) {
        Map params = MapUtil.builder().put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> newStockPointVOS = newStockPointService.selectByParams(params);
        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        List<String> orgCodes =  newStockPointVOS.stream().
                filter(t-> StringUtils.isNotEmpty(t.getInterfaceFlag())).
                filter(t->t.getInterfaceFlag().contains(ApiCategoryEnum.DELETE_GROUP.getCode())).
                map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        for (String stockPoint : orgCodes) {
            // 调用远程的删除组信息
            Map<String, Object> newStockPoingMap = new HashMap<>(2);
            newStockPoingMap.put("stockPointCode", stockPoint);
            newStockPoingMap.put("triggerType", DcpConstants.TASKS_MANUAL_TRIGGER);
            newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.DELETE_GROUP.getCode(), newStockPoingMap);
        }
        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MdsDeleteGroupsVO> invocation(List<MdsDeleteGroupsVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
