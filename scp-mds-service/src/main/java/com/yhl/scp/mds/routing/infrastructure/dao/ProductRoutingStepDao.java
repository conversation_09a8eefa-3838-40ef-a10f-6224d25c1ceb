package com.yhl.scp.mds.routing.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.routing.infrastructure.po.ProductRoutingStepPO;
import com.yhl.scp.mds.routing.vo.ProductRoutingStepVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>ProductRoutingStepDao</code>
 * <p>
 * 物品工艺路径步骤DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:51:56
 */
public interface ProductRoutingStepDao extends BaseDao<ProductRoutingStepPO, ProductRoutingStepVO> {

    List<ProductRoutingStepPO> selectByOperationSequenceIds(@Param("operationSequenceIds") List<String> operationSequenceIds);

    List<ProductRoutingStepVO> selectByRoutingIds(@Param("routingIds") List<String> routingIds);

    void doLogicDeleteBatch(@Param("deleteProductRoutingStepVersionList") List<RemoveVersionDTO> deleteProductRoutingStepVersionList);
}
