package com.yhl.scp.mds.newproduct.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class NewProductStockPointBasicPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -47664391953463615L;

    /**
     * 库存点编码
     */
    private String stockPointCode;
    /**
     * 物品代码
     */
    private String productCode;
    /**
     * 物品名称
     */
    private String productName;
    /**
     * 物品类型
     */
    private String productType;
    /**
     * 物品分类
     */
    private String productClassify;
    /**
     * 分类说明
     */
    private String classifyDesc;
    /**
     * 车型编码
     */
    private String vehicleModelCode;
    /**
     * 供应类型
     */
    private String supplyType;
    /**
     * 计量单位
     */
    private String measurementUnit;
    /**
     * 销售类型
     */
    private String saleType;
    /**
     * 装车位置
     */
    private String loadingPosition;
    /**
     * 装车位置小类
     */
    private String loadingPositionSub;
    /**
     * 车型类型
     */
    private String vehicleModelType;
    /**
     * 业务特性
     */
    private String businessSpecial;
    /**
     * 核心工序
     */
    private String coreProcess;
    /**
     * 产品特性
     */
    private String productSpecial;
    /**
     * 长
     */
    private BigDecimal productLength;
    /**
     * 宽
     */
    private BigDecimal productWidth;
    /**
     * 厚度
     */
    private BigDecimal productThickness;
    /**
     * 颜色
     */
    private String productColor;
    /**
     * 面积M2
     */
    private BigDecimal productArea;
    /**
     * 重量
     */
    private BigDecimal productWeight;
    /**
     * 重量单位
     */
    private String weightUnit;
    /**
     * 每卷/每箱数量
     */
    private BigDecimal productQuantity;
    /**
     * 保质期
     */
    private Integer expireDate;
    /**
     * 特性说明
     */
    private String specialDesc;
    /**
     * 最小起订量
     */
    private BigDecimal minOrderQuantity;
    /**
     * 运输周期
     */
    private BigDecimal transportCycle;
    /**
     * 采购周期预加工
     */
    private String purchaseProcessPre;
    /**
     * 采购周期加工中
     */
    private String purchaseProcessIng;
    /**
     * 采购周期加工后
     */
    private String purchaseProcessAfter;
    /**
     * 物料采购锁定期
     */
    private BigDecimal purchaseLockPeriod;
    /**
     * 物料计划员
     */
    private String productPlanUser;
    /**
     * 人员
     */
    private String productUser;
    /**
     * 订单计划员
     */
    private String orderPlanner;
    /**
     * 生产计划员
     */
    private String productionPlanner;
    /**
     * 材料计划员
     */
    private String materialPlanner;

    /**
     * 采购计划员
     */
    private String purchasePlanner;
    /**
     * SOP
     */
    private Date productSop;
    /**
     * EOP
     */
    private Date productEop;
    /**
     * 计划员
     */
    private String plannerCode;
    /**
     * 采购计划类别
     */
    private String poCategory;
    /**
     * 组织ID
     */
    private String organizationId;
    /**
     * 物料ID
     */
    private String inventoryItemId;
    /**
     * 物料状态
     */
    private String inventoryItemStatusCode;
    /**
     * 物料大类
     */
    private String productCategory;
    /**
     * 最近更新时间
     */
    private Date lastUpdateDate;
    /**
     * edi标志
     */
    private String ediFlag;
    /**
     * 物料标识
     */
    private String itemFlag;
    /**
     * 编码类型标识
     */
    private String isbj;
    /**
     * 材料状态
     */
    private String materialDemandStatus;

    /**
     * 模具数量限制
     */
    private Integer moldQuantityLimit;
    
    /**
     * 自提类型：自提，非自提
     */
    private String pickUpType;
    
    /**
     * 理货单模式，MES，GRP
     */
    private String tallyOrderMode;
    
    /**
     * 是否整箱
     */
    private String fullBoxFlag;
    /***
     * 标准成本
     */
    private String itemCost;

    public String getPurchasePlanner() {
        return purchasePlanner;
    }

    public void setPurchasePlanner(String purchasePlanner) {
        this.purchasePlanner = purchasePlanner;
    }

    public Integer getMoldQuantityLimit() {
        return moldQuantityLimit;
    }

    public void setMoldQuantityLimit(Integer moldQuantityLimit) {
        this.moldQuantityLimit = moldQuantityLimit;
    }

    public String getStockPointCode() {
        return stockPointCode;
    }

    public void setStockPointCode(String stockPointCode) {
        this.stockPointCode = stockPointCode;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public String getProductClassify() {
        return productClassify;
    }

    public void setProductClassify(String productClassify) {
        this.productClassify = productClassify;
    }

    public String getClassifyDesc() {
        return classifyDesc;
    }

    public void setClassifyDesc(String classifyDesc) {
        this.classifyDesc = classifyDesc;
    }

    public String getVehicleModelCode() {
        return vehicleModelCode;
    }

    public void setVehicleModelCode(String vehicleModelCode) {
        this.vehicleModelCode = vehicleModelCode;
    }

    public String getSupplyType() {
        return supplyType;
    }

    public void setSupplyType(String supplyType) {
        this.supplyType = supplyType;
    }

    public String getMeasurementUnit() {
        return measurementUnit;
    }

    public void setMeasurementUnit(String measurementUnit) {
        this.measurementUnit = measurementUnit;
    }

    public String getSaleType() {
        return saleType;
    }

    public void setSaleType(String saleType) {
        this.saleType = saleType;
    }

    public String getLoadingPosition() {
        return loadingPosition;
    }

    public void setLoadingPosition(String loadingPosition) {
        this.loadingPosition = loadingPosition;
    }

    public String getLoadingPositionSub() {
        return loadingPositionSub;
    }

    public void setLoadingPositionSub(String loadingPositionSub) {
        this.loadingPositionSub = loadingPositionSub;
    }

    public String getVehicleModelType() {
        return vehicleModelType;
    }

    public void setVehicleModelType(String vehicleModelType) {
        this.vehicleModelType = vehicleModelType;
    }

    public String getBusinessSpecial() {
        return businessSpecial;
    }

    public void setBusinessSpecial(String businessSpecial) {
        this.businessSpecial = businessSpecial;
    }

    public String getProductSpecial() {
        return productSpecial;
    }

    public void setProductSpecial(String productSpecial) {
        this.productSpecial = productSpecial;
    }

    public BigDecimal getProductLength() {
        return productLength;
    }

    public void setProductLength(BigDecimal productLength) {
        this.productLength = productLength;
    }

    public BigDecimal getProductWidth() {
        return productWidth;
    }

    public void setProductWidth(BigDecimal productWidth) {
        this.productWidth = productWidth;
    }

    public BigDecimal getProductThickness() {
        return productThickness;
    }

    public void setProductThickness(BigDecimal productThickness) {
        this.productThickness = productThickness;
    }

    public String getProductColor() {
        return productColor;
    }

    public void setProductColor(String productColor) {
        this.productColor = productColor;
    }

    public BigDecimal getProductArea() {
        return productArea;
    }

    public void setProductArea(BigDecimal productArea) {
        this.productArea = productArea;
    }

    public BigDecimal getProductWeight() {
        return productWeight;
    }

    public void setProductWeight(BigDecimal productWeight) {
        this.productWeight = productWeight;
    }

    public String getWeightUnit() {
        return weightUnit;
    }

    public void setWeightUnit(String weightUnit) {
        this.weightUnit = weightUnit;
    }

    public BigDecimal getProductQuantity() {
        return productQuantity;
    }

    public void setProductQuantity(BigDecimal productQuantity) {
        this.productQuantity = productQuantity;
    }

    public Integer getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(Integer expireDate) {
        this.expireDate = expireDate;
    }

    public String getSpecialDesc() {
        return specialDesc;
    }

    public void setSpecialDesc(String specialDesc) {
        this.specialDesc = specialDesc;
    }

    public BigDecimal getMinOrderQuantity() {
        return minOrderQuantity;
    }

    public void setMinOrderQuantity(BigDecimal minOrderQuantity) {
        this.minOrderQuantity = minOrderQuantity;
    }

    public BigDecimal getTransportCycle() {
        return transportCycle;
    }

    public void setTransportCycle(BigDecimal transportCycle) {
        this.transportCycle = transportCycle;
    }

    public String getPurchaseProcessPre() {
        return purchaseProcessPre;
    }

    public void setPurchaseProcessPre(String purchaseProcessPre) {
        this.purchaseProcessPre = purchaseProcessPre;
    }

    public String getPurchaseProcessIng() {
        return purchaseProcessIng;
    }

    public void setPurchaseProcessIng(String purchaseProcessIng) {
        this.purchaseProcessIng = purchaseProcessIng;
    }

    public String getPurchaseProcessAfter() {
        return purchaseProcessAfter;
    }

    public void setPurchaseProcessAfter(String purchaseProcessAfter) {
        this.purchaseProcessAfter = purchaseProcessAfter;
    }

    public BigDecimal getPurchaseLockPeriod() {
        return purchaseLockPeriod;
    }

    public void setPurchaseLockPeriod(BigDecimal purchaseLockPeriod) {
        this.purchaseLockPeriod = purchaseLockPeriod;
    }

    public String getProductPlanUser() {
        return productPlanUser;
    }

    public void setProductPlanUser(String productPlanUser) {
        this.productPlanUser = productPlanUser;
    }

    public String getProductUser() {
        return productUser;
    }

    public void setProductUser(String productUser) {
        this.productUser = productUser;
    }

    public String getOrderPlanner() {
        return orderPlanner;
    }

    public void setOrderPlanner(String orderPlanner) {
        this.orderPlanner = orderPlanner;
    }

    public String getProductionPlanner() {
        return productionPlanner;
    }

    public void setProductionPlanner(String productionPlanner) {
        this.productionPlanner = productionPlanner;
    }

    public String getMaterialPlanner() {
        return materialPlanner;
    }

    public void setMaterialPlanner(String materialPlanner) {
        this.materialPlanner = materialPlanner;
    }

    public Date getProductSop() {
        return productSop;
    }

    public void setProductSop(Date productSop) {
        this.productSop = productSop;
    }

    public Date getProductEop() {
        return productEop;
    }

    public void setProductEop(Date productEop) {
        this.productEop = productEop;
    }

    public String getCoreProcess() {
        return coreProcess;
    }

    public void setCoreProcess(String coreProcess) {
        this.coreProcess = coreProcess;
    }

    public String getPlannerCode() {
        return plannerCode;
    }

    public void setPlannerCode(String plannerCode) {
        this.plannerCode = plannerCode;
    }

    public String getPoCategory() {
        return poCategory;
    }

    public void setPoCategory(String poCategory) {
        this.poCategory = poCategory;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getInventoryItemId() {
        return inventoryItemId;
    }

    public void setInventoryItemId(String inventoryItemId) {
        this.inventoryItemId = inventoryItemId;
    }

    public String getInventoryItemStatusCode() {
        return inventoryItemStatusCode;
    }

    public void setInventoryItemStatusCode(String inventoryItemStatusCode) {
        this.inventoryItemStatusCode = inventoryItemStatusCode;
    }

    public String getProductCategory() {
        return productCategory;
    }

    public void setProductCategory(String productCategory) {
        this.productCategory = productCategory;
    }

    public Date getLastUpdateDate() {
        return lastUpdateDate;
    }

    public void setLastUpdateDate(Date lastUpdateDate) {
        this.lastUpdateDate = lastUpdateDate;
    }

    public String getMaterialDemandStatus() {
        return materialDemandStatus;
    }

    public void setMaterialDemandStatus(String materialDemandStatus) {
        this.materialDemandStatus = materialDemandStatus;
    }

    public String getIsbj() {
        return isbj;
    }

    public void setIsbj(String isbj) {
        this.isbj = isbj;
    }

    public String getItemFlag() {
        return itemFlag;
    }

    public void setItemFlag(String itemFlag) {
        this.itemFlag = itemFlag;
    }

    public String getEdiFlag() {
        return ediFlag;
    }

    public void setEdiFlag(String ediFlag) {
        this.ediFlag = ediFlag;
    }

	public String getPickUpType() {
		return pickUpType;
	}

	public void setPickUpType(String pickUpType) {
		this.pickUpType = pickUpType;
	}

	public String getTallyOrderMode() {
		return tallyOrderMode;
	}

	public void setTallyOrderMode(String tallyOrderMode) {
		this.tallyOrderMode = tallyOrderMode;
	}

	public String getFullBoxFlag() {
		return fullBoxFlag;
	}

	public void setFullBoxFlag(String fullBoxFlag) {
		this.fullBoxFlag = fullBoxFlag;
	}

    public String getItemCost() {
        return itemCost;
    }

    public void setItemCost(String itemCost) {
        this.itemCost = itemCost;
    }
}
