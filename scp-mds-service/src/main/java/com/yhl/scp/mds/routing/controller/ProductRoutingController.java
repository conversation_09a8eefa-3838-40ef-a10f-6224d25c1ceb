package com.yhl.scp.mds.routing.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.product.service.ProductCandidateResourceTimeService;
import com.yhl.scp.mds.routing.dto.ProductRoutingDTO;
import com.yhl.scp.mds.routing.service.ProductRoutingService;
import com.yhl.scp.mds.routing.vo.ProductRoutingVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>ProductRoutingController</code>
 * <p>
 * 物品工艺路径控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:51:45
 */
@Slf4j
@Api(tags = "物品工艺路径控制器")
@RestController
@RequestMapping("productRouting")
public class ProductRoutingController extends BaseController {

    @Resource
    private ProductRoutingService productRoutingService;
    
    @Resource
    protected RedisUtil redisUtil;

    @Resource
    private ProductCandidateResourceTimeService productCandidateResourceTimeService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<ProductRoutingVO>> page() {
        List<ProductRoutingVO> productRoutingList = productRoutingService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<ProductRoutingVO> pageInfo = new PageInfo<>(productRoutingList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ProductRoutingDTO productRoutingDTO) {
        return productRoutingService.doCreate(productRoutingDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ProductRoutingDTO productRoutingDTO) {
        return productRoutingService.doUpdate(productRoutingDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        productRoutingService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<ProductRoutingVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productRoutingService.selectByPrimaryKey(id));
    }
    
    @ApiOperation(value = "工艺路径数据同步全量")
    @GetMapping(value = "syncAllRouting")
    public BaseResponse<Void> syncAllRouting() {
    	String syncKey = "MDS_ROUTING_INFO_ALL_SYNC";
    	// 校验锁是否被占用
        if (redisUtil.hasKey(syncKey)) {
        	return BaseResponse.error("数据正在同步!");
        }
        try {
        	//设置过期时间20分钟
        	redisUtil.set(syncKey, SystemHolder.getUserId(), 1200);
        	log.info("工艺路径数据全量同步开始");
        	productRoutingService.syncAllRouting();
        	log.info("工艺路径数据全量同步完成");
		} catch (Exception e) {
            e.printStackTrace();
			log.error("工艺路径数据全量同步失败：" + e);
			return BaseResponse.error("工艺路径数据全量同步失败:" + e);
		}finally {
			redisUtil.delete(syncKey);
		}
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    @ApiOperation(value = "同步")
    @GetMapping(value = "syncResource")
    public BaseResponse<Void> syncMoldChangeTime() {
        return productCandidateResourceTimeService.syncMoldChangeTime(SystemHolder.getTenantCode());
    }
}
