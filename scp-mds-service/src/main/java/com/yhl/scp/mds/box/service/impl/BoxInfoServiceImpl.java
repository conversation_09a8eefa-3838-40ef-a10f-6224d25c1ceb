package com.yhl.scp.mds.box.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesBoxInfo;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.box.convertor.BoxInfoConvertor;
import com.yhl.scp.mds.box.domain.entity.BoxInfoDO;
import com.yhl.scp.mds.box.domain.service.BoxInfoDomainService;
import com.yhl.scp.mds.box.dto.BoxInfoDTO;
import com.yhl.scp.mds.box.infrastructure.dao.BoxInfoDao;
import com.yhl.scp.mds.box.infrastructure.po.BoxInfoPO;
import com.yhl.scp.mds.box.service.BoxInfoService;
import com.yhl.scp.mds.box.vo.BoxInfoVO;
import com.yhl.scp.mds.common.dto.RemoveVersionDTO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>BoxInfoServiceImpl</code>
 * <p>
 * 箱体信息应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:36
 */
@Slf4j
@Service
public class BoxInfoServiceImpl extends AbstractService implements BoxInfoService {

    @Resource
    private BoxInfoDao boxInfoDao;

    @Resource
    private BoxInfoDomainService boxInfoDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse<Void> doCreate(BoxInfoDTO boxInfoDTO) {
        // 0.数据转换
        BoxInfoDO boxInfoDO = BoxInfoConvertor.INSTANCE.dto2Do(boxInfoDTO);
        BoxInfoPO boxInfoPO = BoxInfoConvertor.INSTANCE.dto2Po(boxInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        boxInfoDomainService.validation(boxInfoDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(boxInfoPO);
        boxInfoDao.insertWithPrimaryKey(boxInfoPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(BoxInfoDTO boxInfoDTO) {
        // 0.数据转换
        BoxInfoDO boxInfoDO = BoxInfoConvertor.INSTANCE.dto2Do(boxInfoDTO);
        BoxInfoPO boxInfoPO = BoxInfoConvertor.INSTANCE.dto2Po(boxInfoDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        boxInfoDomainService.validation(boxInfoDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(boxInfoPO);
        int update = boxInfoDao.update(boxInfoPO);
        return update > 0 ? BaseResponse.success() : BaseResponse.error("更新失败");
    }

    @Override
    public void doCreateBatch(List<BoxInfoDTO> list) {
        List<BoxInfoPO> newList = BoxInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        boxInfoDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<BoxInfoDTO> list) {
        List<BoxInfoPO> newList = BoxInfoConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        boxInfoDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return boxInfoDao.deleteBatch(idList);
        }
        return boxInfoDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public BoxInfoVO selectByPrimaryKey(String id) {
        BoxInfoPO po = boxInfoDao.selectByPrimaryKey(id);
        return BoxInfoConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mds_box_info")
    public List<BoxInfoVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override

    @Expression(value = "v_mds_box_info")
    public List<BoxInfoVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<BoxInfoVO> dataList = boxInfoDao.selectByCondition(sortParam, queryCriteriaParam);
        BoxInfoServiceImpl target = springBeanUtils.getBean(BoxInfoServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<BoxInfoVO> selectByParams(Map<String, Object> params) {
        List<BoxInfoPO> list = boxInfoDao.selectByParams(params);
        return BoxInfoConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<BoxInfoVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.BOX_INFO.getCode();
    }

    @Override
    public List<BoxInfoVO> invocation(List<BoxInfoVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int deleteBatchVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        boxInfoDomainService.checkDelete(versionDTOList);
        return boxInfoDao.deleteBatchVersion(versionDTOList);
    }

    /**
     * 获取箱体类型
     *
     * @return
     */
    @Override
    public List<LabelValue<String>> listBoxTypeDropDown() {
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> param = MapUtil.newHashMap();
        param.put("enabled", YesOrNoEnum.YES.getCode());
        List<BoxInfoVO> boxInfoVOS = this.selectByParams(param);
        if (CollectionUtils.isEmpty(boxInfoVOS)) {
            return result;
        }
        boxInfoVOS.stream()
                .filter(x -> StringUtils.isNotBlank(x.getBoxType()))
                .map(BoxInfoVO::getBoxType).distinct()
                .forEach(x -> result.add(new LabelValue<>(x, x)));
        return result;
    }

    /**
     * 根据箱体类型获取箱体编码下拉
     *
     * @param boxType
     * @return
     */
    @Override
    public List<LabelValue<String>> listBoxCodeDropDown(String boxType) {
        List<LabelValue<String>> result = Lists.newArrayList();
        Map<String, Object> param = MapUtil.newHashMap();
        param.put("boxType", boxType);
        param.put("enabled", YesOrNoEnum.YES.getCode());
        List<BoxInfoVO> boxInfoVOS = this.selectByParams(param);
        if (CollectionUtils.isEmpty(boxInfoVOS)) {
            return result;
        }
        boxInfoVOS.stream()
                .forEach(x -> {
                    LabelValue<String> labelValue = new LabelValue<>();
                    labelValue.setLabel(x.getBoxCode());
                    labelValue.setValue(x.getId());
                    result.add(labelValue);
                });
        return result;
    }

    @Override
    public List<BoxInfoVO> selectByPrimaryKeys(List<String> ids) {
        List<BoxInfoPO> boxInfoPOS = boxInfoDao.selectByPrimaryKeys(ids);
        return BoxInfoConvertor.INSTANCE.po2Vos(boxInfoPOS);
    }

    @Override
    public BaseResponse<Void> syncStockPoints(String tenantId,String organizeId) {
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("reqCode", "FY_PKN_CTN_TYPE_FOR_BPIM");
        params.put("ebsOuId", organizeId);
        newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.BOX_INFO.getCode(), params);
        return BaseResponse.success("同步成功");
    }

    @Override
    public BaseResponse<Void> handleFoundationBoxInfo(List<MesBoxInfo> o, String ebsOuId) {
        if (CollectionUtils.isEmpty(o)) {
            return BaseResponse.success();
        }
        List<BoxInfoDTO> insertDtoS = new ArrayList<>();
        List<BoxInfoDTO> updateDtoS = new ArrayList<>();
        Set<String> kids =
                o.stream().map(MesBoxInfo::getKid).collect(Collectors.toSet());
        HashMap<String, Object> map = MapUtil.newHashMap(3);
        map.put("kids", kids);
        List<BoxInfoPO> oldPos = boxInfoDao.selectByParams(map);
        Map<String, BoxInfoPO> oldPosMap = oldPos.stream().collect(
                Collectors.toMap(BoxInfoPO::getKid, Function.identity(), (v1, v2) -> v1));

        for (MesBoxInfo mesBoxInfo : o) {
            if (!ebsOuId.equals(mesBoxInfo.getSalePlantId())) {
                continue;
            }
            BoxInfoDTO dto =new BoxInfoDTO();
            if (oldPosMap.containsKey(mesBoxInfo.getKid())) {
                BoxInfoPO oldPo =  oldPosMap.get(mesBoxInfo.getKid());
                BeanUtils.copyProperties(oldPo, dto);
                generateDto(mesBoxInfo,  dto);
                updateDtoS.add(dto);
            } else {
                generateDto(mesBoxInfo,  dto);
                insertDtoS.add(dto);
            }

        }
        if (CollectionUtils.isNotEmpty(insertDtoS)) {
            doCreateBatch(insertDtoS);
        }
        if (CollectionUtils.isNotEmpty(updateDtoS)) {
            doUpdateBatch(updateDtoS);
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public List<BoxInfoVO> boxCodeDropDown() {
        Map<String, Object> param = MapUtil.newHashMap();
        param.put("enabled", YesOrNoEnum.YES.getCode());
        List<BoxInfoVO> boxInfoVOS = this.selectByParams(param);
        if (CollectionUtils.isNotEmpty(boxInfoVOS)) {
            return boxInfoVOS;
        }
        return Collections.emptyList();
    }

    private void generateDto(MesBoxInfo mesBoxInfo, BoxInfoDTO dto) {
        String enabled = "Y".equals(mesBoxInfo.getEnableFlag()) ? YesOrNoEnum.YES.getCode() :
                YesOrNoEnum.NO.getCode();
        dto.setKid(mesBoxInfo.getKid());
        dto.setLastUpdateTime(mesBoxInfo.getLastUpdateDate());
        dto.setBoxCode(mesBoxInfo.getTypeCode());
        dto.setBoxType(mesBoxInfo.getBroadType());
        dto.setStockPointId(mesBoxInfo.getSalePlantId());
        dto.setPlanArea(mesBoxInfo.getScheduleRegionCode());
        if (!StringUtils.isEmpty(mesBoxInfo.getHeight()) && mesBoxInfo.getHeight().matches("\\d+")) {
            dto.setBoxHeight(new BigDecimal(mesBoxInfo.getHeight().trim()));
        }
        if (!StringUtils.isEmpty(mesBoxInfo.getLength()) && mesBoxInfo.getLength().matches("\\d+")) {
            dto.setBoxLength(new BigDecimal(mesBoxInfo.getLength().trim()));
        }
        if (!StringUtils.isEmpty(mesBoxInfo.getNumPsPerBox()) && mesBoxInfo.getNumPsPerBox().matches("\\d+")) {
            dto.setPiecePerBox(Integer.valueOf(mesBoxInfo.getNumPsPerBox().trim()));
        }
        if (!StringUtils.isEmpty(mesBoxInfo.getBoxQty()) && mesBoxInfo.getBoxQty().matches("\\d+")) {
            dto.setBoxQuantity(Integer.valueOf(mesBoxInfo.getBoxQty().trim()));
        }
        if (!StringUtils.isEmpty(mesBoxInfo.getWidth()) && mesBoxInfo.getWidth().matches("\\d+")) {
            dto.setBoxWidth(new BigDecimal(mesBoxInfo.getWidth().trim()));
        }
        dto.setEnabled(enabled);
    }
}
