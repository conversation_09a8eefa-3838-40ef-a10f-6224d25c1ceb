package com.yhl.scp.dfp.consistence.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <code>MonitorResultUpdate</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-21 21:28:34
 */
@Data
public class MonitorResultUpdate {
    @ApiModelProperty(value = "年月")
    private String month;
    @ApiModelProperty(value = "更新集合")
    private List<ExecutionMonitorResultVO> list;


}
