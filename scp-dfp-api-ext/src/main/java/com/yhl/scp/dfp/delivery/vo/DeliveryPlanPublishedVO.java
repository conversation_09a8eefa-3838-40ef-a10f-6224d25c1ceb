package com.yhl.scp.dfp.delivery.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>DeliveryPlanPublishedVO</code>
 * <p>
 * 发货计划发布表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-17 15:59:51
 */
@ApiModel(value = "发货计划发布表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DeliveryPlanPublishedVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 443845270375143123L;

    /**
     * 发货计划版本id
     */
    @ApiModelProperty(value = "发货计划版本id")
    @FieldInterpretation(value = "发货计划版本id")
    private String deliveryVersionId;
    /**
     * 发货计划发布唯一ID
     */
    @ApiModelProperty(value = "发货计划发布唯一ID")
    @FieldInterpretation(value = "发货计划发布唯一ID")
    private Integer KID;
    /**
     * 日需求版本id
     */
    @ApiModelProperty(value = "日需求版本id")
    @FieldInterpretation(value = "日需求版本id")
    private String demandVersionId;
    /**
     * 主表id
     */
    @ApiModelProperty(value = "主表id")
    @FieldInterpretation(value = "主表id")
    private String deliveryPlanDataId;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    @FieldInterpretation(value = "需求类型")
    private String demandCategory;
    /**
     * 主机厂编码
     */
    @ApiModelProperty(value = "主机厂编码")
    @FieldInterpretation(value = "主机厂编码")
    private String oemCode;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 发货日期
     */
    @ApiModelProperty(value = "发货日期")
    @FieldInterpretation(value = "发货日期")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private Integer demandQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private Integer boxQuantity;
    /**
     * 发版人
     */
    @ApiModelProperty(value = "发版人")
    @FieldInterpretation(value = "发版人")
    private String publisher;
    /**
     * 发版时间
     */
    @ApiModelProperty(value = "发版时间")
    @FieldInterpretation(value = "发版时间")
    private Date publishTime;
    /**
     * 主机厂名称
     */
    @ApiModelProperty(value = "主机厂名称")
    @FieldInterpretation(value = "主机厂名称")
    private String oemName;

    @Override
    public void clean() {

    }

}
