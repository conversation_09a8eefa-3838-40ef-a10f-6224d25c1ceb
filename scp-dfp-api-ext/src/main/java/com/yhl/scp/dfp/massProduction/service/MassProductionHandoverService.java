package com.yhl.scp.dfp.massProduction.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.massProduction.dto.MassProductionHandoverDTO;
import com.yhl.scp.dfp.massProduction.vo.MassProductionHandoverVO;

import java.util.List;

/**
 * <code>MassProductionHandoverService</code>
 * <p>
 * 量产移交信息主表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 16:22:46
 */
public interface MassProductionHandoverService extends BaseService<MassProductionHandoverDTO, MassProductionHandoverVO> {

    /**
     * 查询所有
     *
     * @return list {@link MassProductionHandoverVO}
     */
    List<MassProductionHandoverVO> selectAll();

	MassProductionHandoverVO selectDetail(String id);

	void doApproval(String id);

    BaseResponse<Void> syncCreateProductionHandover(String ids, String oaId);

	void doUpdateApprovalStatus(String id);

    BaseResponse<Void> syncStatus(String tenantId, List<String> appId);

    BaseResponse<Void> syncProductionHandoverStatus(List<MassProductionHandoverDTO> dtoS);
}
