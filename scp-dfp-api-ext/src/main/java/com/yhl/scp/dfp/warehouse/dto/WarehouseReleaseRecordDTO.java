package com.yhl.scp.dfp.warehouse.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>WarehouseReleaseRecordDTO</code>
 * <p>
 * 仓库发货记录DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-31 10:17:12
 */
@ApiModel(value = "仓库发货记录DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WarehouseReleaseRecordDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 408362929737415761L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 工厂
     */
    @ApiModelProperty(value = "工厂")
    @JSONField(name = "plantcode")
    private String plantCode;
    /**
     * 是否已接收
     */
    @ApiModelProperty(value = "是否已接收")
    @JSONField(name = "isreceive")
    private String isReceive;
    /**
     * 发货清单号
     */
    @ApiModelProperty(value = "发货清单号")
    @JSONField(name = "listNum")
    private String listNum;
    /**
     * 发货计划单号
     */
    @ApiModelProperty(value = "发货计划单号")
    @JSONField(name = "shipmentNum")
    private String reqNum;
    /**
     * 条码号
     */
    @ApiModelProperty(value = "条码号")
    @JSONField(name = "caseBarcode")
    private String barNum;
    /**
     * 箱号
     */
    @ApiModelProperty(value = "箱号")
    private String boxNum;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @JSONField(name = "itemCode")
    private String itemCode;
    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @JSONField(name = "itemDescription")
    private String descriptions;
    /**
     * 发货数量
     */
    @ApiModelProperty(value = "发货数量")
    @JSONField(name = "actualQuantity")
    private BigDecimal sumQty;
    /**
     * 柜号
     */
    @ApiModelProperty(value = "柜号")
    @JSONField(name = "containerNum")
    private String containerNum;
    /**
     * 提单号
     */
    @ApiModelProperty(value = "提单号")
    @JSONField(name = "blNum")
    private String billOfLadingNum;
    /**
     * 船公司
     */
    @ApiModelProperty(value = "船公司")
    @JSONField(name = "shippingHouse")
    private String shipCompany;
    /**
     * 进仓工厂
     */
    @ApiModelProperty(value = "进仓工厂")
    @JSONField(name = "instockSource")
    private String instockSource;
    /**
     * 来源仓库
     */
    @ApiModelProperty(value = "来源仓库")
    @JSONField(name = "attribute1")
    private String attribute1;
    /**
     * 来源货位
     */
    @ApiModelProperty(value = "来源货位")
    @JSONField(name = "attribute2")
    private String attribute2;
    /**
     * 目标仓库
     */
    @ApiModelProperty(value = "目标仓库")
    @JSONField(name = "shipmentWarehouseCode")
    private String shipmentWarehouseCode;
    /**
     * 目标货位
     */
    @ApiModelProperty(value = "目标货位")
    @JSONField(name = "shipmentLocatorCode")
    private String shipmentLocatorCode;
    /**
     * 计划单号
     */
    @ApiModelProperty(value = "计划单号")
    @JSONField(name = "plnHeaderNum")
    private String req;
    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    @JSONField(name = "plnLineNum")
    private String lineNum;
    /**
     * 物流器具小类
     */
    @ApiModelProperty(value = "物流器具小类")
    @JSONField(name = "typeCode")
    private String typeCoode;
    /**
     * 发货时间
     */
    @ApiModelProperty(value = "发货时间")
    @JSONField(name = "shippingDatetime")
    private Date creationDate;
    /**
     * 发货人
     */
    @ApiModelProperty(value = "发货人")
    @JSONField(name = "createdBy ")
    private String consigner;
    /**
     * 入仓时间
     */
    @ApiModelProperty(value = "入仓时间")
    @JSONField(name = "attribute12")
    private Date inWarehouseTime;
    /**
     * 单片面积
     */
    @ApiModelProperty(value = "单片面积")
    @JSONField(name = "singleAcreage")
    private BigDecimal acreage;
    /**
     * 总面积
     */
    @ApiModelProperty(value = "总面积")
    @JSONField(name = "totalAcreage")
    private BigDecimal acreageSum;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * mes数据ID
     */
    @ApiModelProperty(value = "mes数据ID")
    @JSONField(name = "kid")
    private String kid;
    /**
     * 批次
     */
    @ApiModelProperty(value = "批次")
    @JSONField(name = "lotNumber")
    private String lotNumber;

    /**
     * 最终修改时间  只接收不传到数据库
     */
    @ApiModelProperty(value = "最终修改时间")
    private Date lastUpdateDate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码")
    private String customerNumber;
    /**
     * 地址ID
     */
    @ApiModelProperty(value = "地址ID")
    private String ebsSiteId;
    /**
     * 客户零件号
     */
    @ApiModelProperty(value = "客户零件号")
    private String custPart;
    /**
     * 客户po号
     */
    @ApiModelProperty(value = "客户po号")
    private String custPo;
    /**
     * 来源
     */
    @ApiModelProperty(value = "来源")
    private String sourceType;
    /**
     * 预计到港时间
     */
    @ApiModelProperty(value = "预计到港时间")
    private Date estimatedArrivePortTime;
    /**
     * 实际到港时间
     */
    @ApiModelProperty(value = "实际到港时间")
    private Date actualArrivePortTime;
    /**
     * 预计完成时间
     */
    @ApiModelProperty(value = "预计完成时间")
    private Date estimatedCompletionTime;
    /**
     * 实际完成时间
     */
    @ApiModelProperty(value = "实际完成时间")
    private Date actualCompletionTime;
    /**
     * 车牌号
     */
    @ApiModelProperty(value = "车牌号")
    @JSONField(name = "plateNum")
    private String carNum;

}
