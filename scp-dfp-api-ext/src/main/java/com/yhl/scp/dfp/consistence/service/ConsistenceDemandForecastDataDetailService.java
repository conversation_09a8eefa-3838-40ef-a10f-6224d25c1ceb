package com.yhl.scp.dfp.consistence.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastCheckDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDetailDTO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>ConsistenceDemandForecastDataDetailService</code>
 * <p>
 * 一致性业务预测数据明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 19:56:55
 */
public interface ConsistenceDemandForecastDataDetailService extends BaseService<ConsistenceDemandForecastDataDetailDTO, ConsistenceDemandForecastDataDetailVO> {

    List<ConsistenceDemandForecastDataDetailVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link ConsistenceDemandForecastDataDetailVO}
     */
    List<ConsistenceDemandForecastDataDetailVO> selectAll();

    /**
     * 批量删除
     *
     * @param versionDTOList
     */
    int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList);

    List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastBystockPointId(String id);

    List<ConsistenceDemandForecastDataDetailVO> selectByConsistenceDemandForecastDataId();

    List<ConsistenceDemandForecastDataDetailVO> getLatestPublishedVersionData(List<String> oemCodeScope, List<String> productScope);

    List<ConsistenceDemandForecastDataDetailVO> selectForecastQuantitySumByOemCodesAndMonths(Map<String, Object> params);

    List<ConsistenceDemandForecastDataDetailVO> selectByParentIdsAndMonth(List<String> ids, Date month);
	void doBatchUapdatForecastQuantity(List<ConsistenceDemandForecastCheckDTO> dtoList);

}
