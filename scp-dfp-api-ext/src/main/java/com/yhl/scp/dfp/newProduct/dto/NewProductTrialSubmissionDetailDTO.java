package com.yhl.scp.dfp.newProduct.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>NewProductTrialSubmissionDetailDTO</code>
 * <p>
 * 新品试制提报详情DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-29 16:53:13
 */
@ApiModel(value = "新品试制提报详情DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NewProductTrialSubmissionDetailDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = -88023164280714676L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 主表ID
     */
    @ApiModelProperty(value = "主表ID")
    private String submissionId;
    /**
     * 需求时间：日期
     */
    @ApiModelProperty(value = "需求时间：日期")
    private Date demandTime;
    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    private BigDecimal demandQuantity;
    /**
     * 投料数量
     */
    @ApiModelProperty(value = "投料数量")
    private BigDecimal feedingQuantity;
    /**
     * 投料单位
     */
    @ApiModelProperty(value = "投料单位")
    private String feedingUnit;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 零件
     */
    @ApiModelProperty(value = "零件")
    private String partCode;
    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称")
    private String productName;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    private String loadingPosition;
    /**
     * 制造订单id
     */
    @ApiModelProperty(value = "制造订单id")
    private String workOrderId;
    /**
     * 预处理
     */
    @ApiModelProperty(value = "预处理")
    private Integer pretreatment;
    /**
     * 成型
     */
    @ApiModelProperty(value = "成型")
    private Integer shaping;
    /**
     * 合片
     */
    @ApiModelProperty(value = "合片")
    private Integer lamination;
    /**
     * 总成
     */
    @ApiModelProperty(value = "总成")
    private Integer assembly;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

}
