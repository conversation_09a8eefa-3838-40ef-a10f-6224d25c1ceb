package com.yhl.scp.dfp.stock.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogDTO;
import com.yhl.scp.dfp.stock.vo.FdpInventoryBatchDetailLogVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>FdpInventoryBatchDetailLogService</code>
 * <p>
 * 应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:56:27
 */
public interface FdpInventoryBatchDetailLogService extends BaseService<FdpInventoryBatchDetailLogDTO, FdpInventoryBatchDetailLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link FdpInventoryBatchDetailLogVO}
     */
    List<FdpInventoryBatchDetailLogVO> selectAll();

    int doDeleteAllByOrgIds(String code, List<String> orgIds);

    int doDeleteAllByOrgId(String sourceType,String originalOrgId);
    void downloadOriginalData(String stockPointCode, HttpServletResponse response);

}
