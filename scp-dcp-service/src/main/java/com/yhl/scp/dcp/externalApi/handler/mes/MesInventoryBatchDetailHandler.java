package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <code>ShanghaiMesInventoryBatchDetailHandler</code>
 * <p>
 *MES库存批次明细同步
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-15 11:23:52
 */
@Component
@Slf4j
public class MesInventoryBatchDetailHandler extends SyncDataHandler<List<MesInventoryBatchDetail>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private ExtApiLogService extApiLogService;
    @Resource
    private NewMdsFeign mdsFeign;
    @Override
    protected List<MesInventoryBatchDetail> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES获取外部库存批次明细数据为空");
            return null;
        }
        List<MesInventoryBatchDetail> MesInventoryBatchDetail = JSONArray.parseArray(body, MesInventoryBatchDetail.class);
        return MesInventoryBatchDetail;
    }


    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesInventoryBatchDetail> mesInventoryBatchDetails) {
        long handleStartTime = System.currentTimeMillis();

        if (CollectionUtils.isEmpty(mesInventoryBatchDetails)) {
            log.error("库存批次明细为空");
            return null;
        }
        String plantIds = (String) params.get("plantId");
        Object scenarioObj = params.get("scenario");
        Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
        String dataBaseName = (String) scenarioMap.get("dataBaseName");
        List<String> orgIds = Arrays.asList(plantIds.split(","));
        log.info("库存点{},MES库存批次现有量数据大小:{}", plantIds, mesInventoryBatchDetails.size());

        long dfpCallStartTime = System.currentTimeMillis();
        dfpFeign.handleInventoryBatchDetail(dataBaseName, mesInventoryBatchDetails, orgIds);
        long dfpCallEndTime = System.currentTimeMillis();

        // 计算总处理时间和DFP调用时间（转换为秒）
        long handleEndTime = System.currentTimeMillis();
        double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;
        double dfpCallTimeSeconds = (dfpCallEndTime - dfpCallStartTime) / 1000.0;

        log.info("库存点{}的数据处理完成，总处理耗时：{}秒，其中DFP服务调用耗时：{}秒",
                plantIds, totalHandleTimeSeconds, dfpCallTimeSeconds);

        return null;
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES库存批次:{},{}", apiConfigVO, params);
        }
        //开始时间
        long methodStartTime = System.currentTimeMillis();

        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);

        ExecutorService executor = Executors.newFixedThreadPool(6);

        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
//            String systemNumber = apiConfigVO.getSystemNumber();
//            String flowNumber = this.sequenceService.getSuffix(systemNumber, getCommand(), 6);
//            String url = apiUri + "/" + systemNumber + "/" + flowNumber + apiParams;
            String url = apiUri +  apiParams;

            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);

            String reqCode = MesApiReqCodeEnum.FY_PKN_AND_SGL_FOR_BPIM.getCode();
            Object plantId = params.get("plantId");
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize()) ? 10000 : apiConfigVO.getOffsetSize();

            // 记录第一页请求的开始时间
            long firstPageStartTime = System.currentTimeMillis();

            // 先请求第一页拿总数
            Map<String, Object> firstParam = new HashMap<>();
            firstParam.put("reqCode", reqCode);
            firstParam.put("plantId", plantId);
            firstParam.put("currentPage", 1);
            firstParam.put("pageSize", pageSize);

            ExtApiLogDTO firstLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), JSONObject.toJSONString(firstParam));
            HttpEntity<String> httpEntity = new HttpEntity<>(JSONObject.toJSONString(firstParam), httpHeaders);
            ResponseEntity<String> firstResponse = restTemplate.postForEntity(url, httpEntity, String.class);
            Assert.isTrue(HttpStatus.OK.value() == firstResponse.getStatusCodeValue(), "MES请求失败");

            MesResponse firstParsed = JSON.parseObject(firstResponse.getBody(), MesResponse.class);
            MesResponseData firstData = firstParsed.getData();
            List<Object> result = new CopyOnWriteArrayList<>(firstData.getMessage());

            // 计算第一页请求耗时（秒）
            long firstPageEndTime = System.currentTimeMillis();
            double firstPageTimeSeconds = (firstPageEndTime - firstPageStartTime) / 1000.0;
            log.info("第1页请求完成，数据量：{}，耗时：{}秒", firstData.getMessage().size(), firstPageTimeSeconds);

            int totalRecords = firstData.getTotal();
            int totalPages = (int) Math.ceil((double) totalRecords / pageSize);
            extApiLogService.updateResponse(firstLog, null, firstData.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);

            List<CompletableFuture<Void>> futures = new ArrayList<>();
            for (int i = 2; i <= totalPages; i++) {
                int pageIndex = i;
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    // 记录单页请求开始时间
                    long pageStartTime = System.currentTimeMillis();

                    Map<String, Object> pageParam = new HashMap<>();
                    pageParam.put("reqCode", reqCode);
                    pageParam.put("plantId", plantId);
                    pageParam.put("currentPage", pageIndex);
                    pageParam.put("pageSize", pageSize);

                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog, httpHeaders.toString(), JSONObject.toJSONString(pageParam));
                    HttpEntity<String> pageEntity = new HttpEntity<>(JSONObject.toJSONString(pageParam), httpHeaders);
                    ResponseEntity<String> pageResp = restTemplate.postForEntity(url, pageEntity, String.class);

                    if (HttpStatus.OK.value() != pageResp.getStatusCodeValue()) {
                        extApiLogService.updateResponse(subLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
                        throw new BusinessException("分页请求失败：第" + pageIndex + "页");
                    }

                    MesResponse pageResult = JSON.parseObject(pageResp.getBody(), MesResponse.class);
                    List<Object> pageList = pageResult.getData().getMessage();
                    result.addAll(pageList);

                    // 计算单个页请求耗时（秒）
                    long pageEndTime = System.currentTimeMillis();
                    double pageTimeSeconds = (pageEndTime - pageStartTime) / 1000.0;
                    extApiLogService.updateResponse(subLog, null, pageList.size(), DcpConstants.TASKS_STATUS_SUCCESS);

                    log.info("第{}页请求完成，数据量：{}，耗时：{}秒", pageIndex, pageList.size(), pageTimeSeconds);
                }, executor);
                futures.add(future);
            }

            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 计算整个方法执行的总耗时（秒）
            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;
            log.info("库存点 {}, 总数据量：{}，总耗时：{}秒", plantId, result.size(), totalTimeSeconds);
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_SUCCESS);
            return JSONObject.toJSONString(result);

        } catch (Exception e) {
            // 计算异常情况下的耗时（秒）
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - methodStartTime) / 1000.0;
            log.error("调用MES并发接口异常，已执行耗时：{}秒，错误：{}", errorTimeSeconds, e.getLocalizedMessage());
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        } finally {
            executor.shutdown();
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.MES_INVENTORY_BATCH_DETAIL.getCode());

    }
}
