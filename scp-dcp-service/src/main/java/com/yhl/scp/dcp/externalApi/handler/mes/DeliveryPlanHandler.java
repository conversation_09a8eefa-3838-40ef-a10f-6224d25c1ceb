package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesDeliveryPlan;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesDeliveryPlanDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesDeliveryPlanDetailItem;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanHandler</code>
 * <p>
 * 发货计划给到MES
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-25 15:09:33
 */
@Component
@Slf4j
public class DeliveryPlanHandler extends SyncDataHandler<String> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected String convertData(String body) {
        return body;
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                String logId) {
        return logId;
    }


    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> apiParams) {
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, apiParams, null, null, null);
        if (log.isInfoEnabled()) {
            log.info("开始同步发货计划数据给MES，数据需求:{},{}", apiConfigVO, apiParams);
        }

        try {
            // 获取 token
            String mesToken = authHandler.handle(MapUtil.newHashMap());

            // 提取批量数据列表
            String dataString = JSON.toJSONString(apiParams.get("dataList"));
            List<JSONObject> dataList = JSON.parseArray(dataString, JSONObject.class);
            if (dataList == null || dataList.isEmpty()) {
                throw new BusinessException("批量数据不能为空");
            }
            Map<String, String> kidItemCodeMap = dataList.stream().collect(Collectors.toMap(x -> (String) x.get("kid"),
                    x -> (String) x.get("itemCode"), (v1, v2) -> v2));

            // // API URI 和流水号的获取
            String apiUri = apiConfigVO.getApiUri();
            // String systemNumber = apiConfigVO.getSystemNumber();
            // String flowNumber = this.sequenceService.getSuffix(systemNumber, getCommand(), 6);

            // 构建完整的 URL
            String url = apiUri ;
            if (apiParams.containsKey("apiParams")) {
                 url += "?DelNoVersion=" + apiParams.get("apiParams");
            }

            // 将整个dataList转换为JSON字符串
            String bodyStr = dataString;
            if (log.isInfoEnabled()) {
                log.info("apiUri={},url={}", apiUri, url);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> httpEntity = new HttpEntity<>(bodyStr, httpHeaders);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();

            if (HttpStatus.OK.value() != statusCodeValue) {
                log.error("同步发货计划给MES失败,HTTP状态码:{}", statusCodeValue);
            }

            String responseBody = responseEntity.getBody();
            MesDeliveryPlan mesDeliveryPlan = JSONObject.parseObject(responseBody, MesDeliveryPlan.class);

            if (Objects.nonNull(mesDeliveryPlan) && mesDeliveryPlan.getCode() == 0) {
                List<MesDeliveryPlanDetail> dataListResp = mesDeliveryPlan.getData();
                List<MesDeliveryPlanDetailItem> flatList = dataListResp.stream().map(MesDeliveryPlanDetail::getRsList)
                        .flatMap(Collection::stream).collect(Collectors.toList());
                int errorCount = 0;
                List<MesDeliveryPlanDetailItem> errorList = new ArrayList<>();
                for (MesDeliveryPlanDetailItem item : flatList) {
                    if (!"200".equals(item.getResponseCode())) {
                        errorCount += 1;
                        errorList.add(item);
                    }
                }
                mainLog.setApplyCount(dataList.size() - errorCount);

                if (CollectionUtils.isNotEmpty(errorList)) {
                    errorList.forEach(x -> x.setItemCode(kidItemCodeMap.get(x.getKid())));
                    String errorMsg = errorList.stream()
                            .collect(Collectors.groupingBy(
                                    MesDeliveryPlanDetailItem::getItemCode,
                                    TreeMap::new,
                                    Collectors.mapping(
                                            MesDeliveryPlanDetailItem::getMsg,
                                            Collectors.collectingAndThen(
                                                    Collectors.toSet(),
                                                    set -> set.stream()
                                                            .sorted()
                                                            .collect(Collectors.joining("; "))
                                            )
                                    )
                            )).entrySet().stream().map(x -> x.getKey() + ": " + x.getValue())
                            .collect(Collectors.joining("<br/>"));
                    mainLog.setRemark(errorMsg);
                }
                extApiLogService.updateResponse(mainLog, null, dataList.size(), DcpConstants.TASKS_STATUS_SUCCESS);
                log.info("请求同步发货计划数据给MES完成, 返回数据:{}", responseBody);
            } else {
                log.error("数据报错，{}", JSON.toJSONString(mesDeliveryPlan));
            }
            return mainLog.getId();
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException(e.getMessage());
        }
    }


    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.DELIVERY_PLAN_MES.getCode());
    }

}
