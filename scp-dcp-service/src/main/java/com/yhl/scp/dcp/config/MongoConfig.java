package com.yhl.scp.dcp.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;

/**
 * <code>MongoConfig</code>
 * <p>
 * MongoDB配置类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Configuration
@EnableMongoRepositories(basePackages = "com.yhl.scp.dcp.apiLog.dao")
public class MongoConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.host:***********}")
    private String host;

    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    @Value("${spring.data.mongodb.username:bpimadm}")
    private String username;

    @Value("${spring.data.mongodb.password:WOVQ.dL9z+2oHBW}")
    private String password;

    @Value("${spring.data.mongodb.database:mongosit}")
    private String database;

    @Override
    protected String getDatabaseName() {
        return database;
    }

    @Override
    protected String getMongoClientUri() {
        return String.format("mongodb://%s:%s@%s:%d/%s", username, password, host, port, database);
    }

    @Bean
    @Override
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }
}
