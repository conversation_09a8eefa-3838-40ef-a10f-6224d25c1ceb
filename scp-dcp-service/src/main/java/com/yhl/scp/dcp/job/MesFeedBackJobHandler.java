package com.yhl.scp.dcp.job;

import cn.hutool.core.map.MapUtil;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.service.ApiConfigService;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.externalApi.ExternalApiHandler;
import com.yhl.scp.dcp.externalApi.handler.Handler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MesFeedBackJobHandler</code>
 * <p>
 * MesFeedBackJobHandler
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17 14:06:23
 */
//@Component
@Slf4j
public class MesFeedBackJobHandler {

    @Resource
    private ExternalApiHandler externalApiHandler;

    @Resource
    private ApiConfigService apiConfigService;

    @XxlJob("mesFeedBackJobHandler")
    public ReturnT<String> mesFeedBackJobHandler() {
        try {
            Map<String, Object> params = MapUtil.newHashMap();
            params.put("apiCategory", ApiCategoryEnum.MES_FEED_BACK.getCode());
            params.put("enabled", YesOrNoEnum.YES.getCode());
            List<ApiConfigVO> apiConfigVOS = apiConfigService.selectVOByParams(params);
            if (CollectionUtils.isEmpty(apiConfigVOS)) {
                XxlJobHelper.log("不存在需要同步的生产报工反馈接口配置");
                return ReturnT.SUCCESS;
            }
            ApiConfigVO apiConfigVO = apiConfigVOS.get(0);
            String command = String.join(Handler.CMD_DELIMITER, apiConfigVO.getTenantCode(), apiConfigVO.getApiSource(), apiConfigVO.getApiCategory());
            externalApiHandler.handle(command, new HashMap<>());
        } catch (Exception ex) {
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }

}
