package com.yhl.scp.dcp.apiLog.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiLogPO</code>
 * <p>
 * 接口日志表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 13:43:12
 */
public class ExtApiLogPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 735051244301236521L;

    /**
     * 父日志id
     */
    private String parentId;
    /**
     * 配置ID
     */
    private String configId;
    /**
     * 批次号
     */
    private String batchNo;
    /**
     * 子批次号
     */
    private String subBatchNo;
    /**
     * 请求方式
     */
    private String requestType;
    /**
     * 触发方式
     */
    private String triggerType;
    /**
     * 请求头
     */
    private String requestHeaders;
    /**
     * 请求参数
     */
    private String requestParams;
    /**
     * 响应头
     */
    private String responseHeaders;
    /**
     * 响应状态
     */
    private String responseStatus;
    /**
     * 请求时间
     */
    private Date requestTime;
    /**
     * 响应时间
     */
    private Date responseTime;
    /**
     * 解析状态
     */
    private String resolveStatus;
    /**
     * 解析数量
     */
    private Integer resolveCount;
    /**
     * 应用数量
     */
    private Integer applyCount;
    /**
     * 运行状态
     */
    private String status;
    /**
     * 请求流水号
     */
    private String serialNum;
    private Date lastUpdateTime;

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getConfigId() {
        return configId;
    }

    public void setConfigId(String configId) {
        this.configId = configId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getSubBatchNo() {
        return subBatchNo;
    }

    public void setSubBatchNo(String subBatchNo) {
        this.subBatchNo = subBatchNo;
    }

    public String getRequestType() {
        return requestType;
    }

    public void setRequestType(String requestType) {
        this.requestType = requestType;
    }

    public String getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(String triggerType) {
        this.triggerType = triggerType;
    }

    public String getRequestHeaders() {
        return requestHeaders;
    }

    public void setRequestHeaders(String requestHeaders) {
        this.requestHeaders = requestHeaders;
    }

    public String getRequestParams() {
        return requestParams;
    }

    public void setRequestParams(String requestParams) {
        this.requestParams = requestParams;
    }

    public String getResponseHeaders() {
        return responseHeaders;
    }

    public void setResponseHeaders(String responseHeaders) {
        this.responseHeaders = responseHeaders;
    }

    public String getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(String responseStatus) {
        this.responseStatus = responseStatus;
    }

    public Date getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(Date requestTime) {
        this.requestTime = requestTime;
    }

    public Date getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Date responseTime) {
        this.responseTime = responseTime;
    }

    public String getResolveStatus() {
        return resolveStatus;
    }

    public void setResolveStatus(String resolveStatus) {
        this.resolveStatus = resolveStatus;
    }

    public Integer getResolveCount() {
        return resolveCount;
    }

    public void setResolveCount(Integer resolveCount) {
        this.resolveCount = resolveCount;
    }

    public Integer getApplyCount() {
        return applyCount;
    }

    public void setApplyCount(Integer applyCount) {
        this.applyCount = applyCount;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getSerialNum() {
        return serialNum;
    }

    public void setSerialNum(String serialNum) {
        this.serialNum = serialNum;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

}
