package com.yhl.scp.dcp.apiLog.mq;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.dcp.common.constants.ExtApiLogMqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <code>ExtApiLogMessageProducer</code>
 * <p>
 * 接口日志报文消息生产者
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
@Slf4j
@Component
public class ExtApiLogMessageProducer {

    @Resource
    private RabbitTemplate rabbitTemplate;

    /**
     * 发送请求报文消息
     *
     * @param logId       日志ID
     * @param requestBody 请求体
     */
    public void sendRequestMessage(String logId, String requestBody) {
        try {
            ExtApiLogMessageMQ message = ExtApiLogMessageMQ.builder()
                    .logId(logId)
                    .messageType("REQUEST")
                    .messageData(requestBody)
                    .build();

            rabbitTemplate.convertAndSend(
                    ExtApiLogMqConstants.BPIM_EVENT_EXCHANGE,
                    ExtApiLogMqConstants.EXT_API_LOG_MESSAGE_ROUTING_KEY,
                    JSONObject.toJSONString(message)
            );

            log.debug("发送请求报文消息成功，logId: {}", logId);
        } catch (Exception e) {
            log.error("发送请求报文消息失败，logId: {}, error: {}", logId, e.getMessage());
        }
    }

    /**
     * 发送响应报文消息
     *
     * @param logId        日志ID
     * @param responseBody 响应体
     */
    public void sendResponseMessage(String logId, String responseBody) {
        try {
            ExtApiLogMessageMQ message = ExtApiLogMessageMQ.builder()
                    .logId(logId)
                    .messageType("RESPONSE")
                    .messageData(responseBody)
                    .build();

            rabbitTemplate.convertAndSend(
                    ExtApiLogMqConstants.BPIM_EVENT_EXCHANGE,
                    ExtApiLogMqConstants.EXT_API_LOG_MESSAGE_ROUTING_KEY,
                    JSONObject.toJSONString(message)
            );

            log.debug("发送响应报文消息成功，logId: {}", logId);
        } catch (Exception e) {
            log.error("发送响应报文消息失败，logId: {}, error: {}", logId, e.getMessage());
        }
    }

    /**
     * 消息实体类
     */
    @lombok.Data
    @lombok.Builder
    @lombok.NoArgsConstructor
    @lombok.AllArgsConstructor
    public static class ExtApiLogMessageMQ {
        private String logId;
        private String messageType;
        private String messageData;
    }
}
