package com.yhl.scp.dcp.externalApi.handler.erp;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpInventoryBatchDetail;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <code>FYSLInventoryBatchDetail</code>
 * <p>
 * ERP库存批次现有量接口
 * 同步方式：全量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-05 11:34:36
 */
@Component
@Slf4j
public class ErpInventoryBatchDetailHandler extends SyncDataHandler<List<ErpInventoryBatchDetail>> {

    @Resource
    private AuthHandler authHandler;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<ErpInventoryBatchDetail> convertData(String body) {

        if (StringUtils.isBlank(body)) {
            log.error("ERP同步库存批次现有量为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, ErpInventoryBatchDetail.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpInventoryBatchDetail> erpInventoryBatchDetails) {
        // 记录数据处理开始时间
        long handleStartTime = System.currentTimeMillis();

        this.saveSyncCtrlSyncTime(apiConfigVO, params);

        if (CollectionUtils.isEmpty(erpInventoryBatchDetails)) {
            log.error("ERP库存批次现有量数据为空");
            return null;
        }

        try {
            // 记录服务调用的开始时间
            long serviceCallStartTime = System.currentTimeMillis();

            Object scenarioObj = params.get("scenario");
            Map<String, Object> scenarioMap = (Map<String, Object>) scenarioObj;
            String dataBaseName = (String) scenarioMap.get("dataBaseName");
            String orgId = (String) params.get("orgId");
            log.info("OrgId:{},ERP库存批次现有量数据大小:{}",orgId,erpInventoryBatchDetails.size());

            dfpFeign.handleErpInventoryBatchDetail(dataBaseName, erpInventoryBatchDetails,orgId);
            this.saveSyncCtrl(apiConfigVO, params, erpInventoryBatchDetails);

            // 记录服务调用结束时间
            long serviceCallEndTime = System.currentTimeMillis();

            // 计算总处理时间和服务调用时间
            long handleEndTime = System.currentTimeMillis();
            double totalHandleTimeSeconds = (handleEndTime - handleStartTime) / 1000.0;
            double serviceCallTimeSeconds = (serviceCallEndTime - serviceCallStartTime) / 1000.0;

            log.info("ERP库存批次数据处理完成，数据量：{}，总处理耗时：{}秒，其中服务调用耗时：{}秒",
                    erpInventoryBatchDetails.size(), totalHandleTimeSeconds, serviceCallTimeSeconds);

            return "处理成功";
        } catch (Exception e) {
            // 计算异常情况下的耗时
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - handleStartTime) / 1000.0;
            log.error("调用ERP库存批次接口异常，已执行耗时：{}秒，错误：{}", errorTimeSeconds, e.getLocalizedMessage());
            return "处理ERP库存批次现有量数据时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步ERP库存批次:{},{}", apiConfigVO, params);
        }
        // 记录整个方法的开始时间
        long methodStartTime = System.currentTimeMillis();

        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);

        try {

            String erpToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();

            String url = apiUri
                    + "?token=" + erpToken;
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            String orgId = (String) params.get("orgId");
            String currentUrl = url + "&orgId=" + params.get("orgId");


            if (log.isInfoEnabled()) {
                log.info("erpToken={},apiUri={},orgId={},url={}", erpToken, apiUri, orgId, url);
            }
            ResponseEntity<String> responseEntity = restTemplate.getForEntity(currentUrl, String.class);
            int statusCodeValue = responseEntity.getStatusCodeValue();
            if (HttpStatus.OK.value() != statusCodeValue) {
                extApiLogService.updateResponse(mainLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                throw new BusinessException(StrUtil.format("同步ERP库存批次现有量请求失败,HTTP状态码:{}!", statusCodeValue));
            }
            extApiLogService.updateResponse(mainLog, responseEntity, 1, DcpConstants.TASKS_STATUS_SUCCESS);
            String body = responseEntity.getBody();

            log.info("库存点{},同步ERP库存批次现有量完成,返回数据:{}!",orgId, body);
            ErpResponse erpResponse = JSONObject.parseObject(body, ErpResponse.class);

            // 计算整个方法的总耗时
            long methodEndTime = System.currentTimeMillis();
            double totalTimeSeconds = (methodEndTime - methodStartTime) / 1000.0;
            log.info("ERP库存批次数据获取完成，总耗时：{}秒", totalTimeSeconds);

            return JSONObject.toJSONString(erpResponse.getData());

        } catch (Exception e) {
            // 计算异常情况下的耗时
            long errorTime = System.currentTimeMillis();
            double errorTimeSeconds = (errorTime - methodStartTime) / 1000.0;
            log.error("调用ERP库存批次接口异常，已执行耗时：{}秒，错误：{}", errorTimeSeconds, e.getLocalizedMessage());
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw new BusinessException("ERP库存批次调用异常", e.getLocalizedMessage());
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.ERP.getCode(),
                ApiCategoryEnum.ERP_INVENTORY_BATCH_DETAIL.getCode());
    }


    @Override
    protected String getSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return super.getSyncRefValue(apiConfigVO, params);
    }

    /**
     * 获取同步分组值
     *
     * @param apiConfigVO
     * @param params
     * @return
     */
    @Override
    protected String getSyncGroupValue(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        return (String) params.get("stockPointCode");
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params, List<ErpInventoryBatchDetail> erpInventoryBatchDetails) {
        return super.computeMaxSyncRefValue(apiConfigVO, params, erpInventoryBatchDetails);
    }
}
