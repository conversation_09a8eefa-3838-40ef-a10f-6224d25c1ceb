<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dcp.apiLog.infrastructure.dao.ExtApiLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO">
        <!--@Table ext_api_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="config_id" jdbcType="VARCHAR" property="configId"/>
        <result column="batch_no" jdbcType="VARCHAR" property="batchNo"/>
        <result column="sub_batch_no" jdbcType="VARCHAR" property="subBatchNo"/>
        <result column="request_type" jdbcType="VARCHAR" property="requestType"/>
        <result column="trigger_Type" jdbcType="VARCHAR" property="triggerType"/>
        <result column="request_headers" jdbcType="VARCHAR" property="requestHeaders"/>
        <result column="request_params" jdbcType="VARCHAR" property="requestParams"/>
        <result column="request_body" jdbcType="VARCHAR" property="requestBody"/>
        <result column="response_headers" jdbcType="VARCHAR" property="responseHeaders"/>
        <result column="response_body" jdbcType="VARCHAR" property="responseBody"/>
        <result column="response_status" jdbcType="VARCHAR" property="responseStatus"/>
        <result column="request_time" jdbcType="TIMESTAMP" property="requestTime"/>
        <result column="response_time" jdbcType="TIMESTAMP" property="responseTime"/>
        <result column="resolve_status" jdbcType="VARCHAR" property="resolveStatus"/>
        <result column="resolve_count" jdbcType="INTEGER" property="resolveCount"/>
        <result column="apply_count" jdbcType="INTEGER" property="applyCount"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="serial_num" jdbcType="VARCHAR" property="serialNum"/>
        <result column="last_update_time" jdbcType="TIMESTAMP" property="lastUpdateTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO">
        <!-- TODO -->
        <result column="api_source" jdbcType="VARCHAR" property="apiSource"/>
        <result column="api_category" jdbcType="VARCHAR" property="spiCategory"/>
        <result column="api_name" jdbcType="VARCHAR" property="apiName"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,parent_id,config_id,batch_no,sub_batch_no,request_type,trigger_type,request_headers,request_params,response_headers,response_status,request_time,response_time,resolve_status,resolve_count,apply_count,status,serial_num,last_update_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,api_source,api_category,api_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.configId != null and params.configId != ''">
                and config_id = #{params.configId,jdbcType=VARCHAR}
            </if>
            <if test="params.batchNo != null and params.batchNo != ''">
                and batch_no = #{params.batchNo,jdbcType=VARCHAR}
            </if>
            <if test="params.subBatchNo != null and params.subBatchNo != ''">
                and sub_batch_no = #{params.subBatchNo,jdbcType=VARCHAR}
            </if>
            <if test="params.requestType != null and params.requestType != ''">
                and request_type = #{params.requestType,jdbcType=VARCHAR}
            </if>
            <if test="params.triggerType != null and params.triggerType != ''">
                and trigger_type = #{params.triggerType,jdbcType=VARCHAR}
            </if>
            <if test="params.requestHeaders != null and params.requestHeaders != ''">
                and request_headers = #{params.requestHeaders,jdbcType=VARCHAR}
            </if>
            <if test="params.requestParams != null and params.requestParams != ''">
                and request_params = #{params.requestParams,jdbcType=VARCHAR}
            </if>
            <if test="params.requestBody != null and params.requestBody != ''">
                and request_body = #{params.requestBody,jdbcType=VARCHAR}
            </if>
            <if test="params.responseHeaders != null and params.responseHeaders != ''">
                and response_headers = #{params.responseHeaders,jdbcType=VARCHAR}
            </if>
            <if test="params.responseBody != null and params.responseBody != ''">
                and response_body = #{params.responseBody,jdbcType=VARCHAR}
            </if>
            <if test="params.responseStatus != null and params.responseStatus != ''">
                and response_status = #{params.responseStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.requestTime != null">
                and request_time = #{params.requestTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.responseTime != null">
                and response_time = #{params.responseTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.resolveStatus != null and params.resolveStatus != ''">
                and resolve_status = #{params.resolveStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.resolveCount != null">
                and resolve_count = #{params.resolveCount,jdbcType=INTEGER}
            </if>
            <if test="params.applyCount != null">
                and apply_count = #{params.applyCount,jdbcType=INTEGER}
            </if>
            <if test="params.status != null and params.status != ''">
                and status = #{params.status,jdbcType=VARCHAR}
            </if>
            <if test="params.serialNum != null and params.serialNum != ''">
                and serial_num = #{params.serialNum,jdbcType=VARCHAR}
            </if>
            <if test="params.lastUpdateTime != null">
                and last_update_time = #{params.lastUpdateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ext_api_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ext_api_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_ext_api_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ext_api_log
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into ext_api_log(
        id,
        parent_id,
        config_id,
        batch_no,
        sub_batch_no,
        request_type,
        trigger_type,
        request_headers,
        request_params,
        request_body,
        response_headers,
        response_body,
        response_status,
        request_time,
        response_time,
        resolve_status,
        resolve_count,
        apply_count,
        status,
        serial_num,
        last_update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{configId,jdbcType=VARCHAR},
        #{batchNo,jdbcType=VARCHAR},
        #{subBatchNo,jdbcType=VARCHAR},
        #{requestType,jdbcType=VARCHAR},
        #{triggerType,jdbcType=VARCHAR},
        #{requestHeaders,jdbcType=VARCHAR},
        #{requestParams,jdbcType=VARCHAR},
        #{requestBody,jdbcType=VARCHAR},
        #{responseHeaders,jdbcType=VARCHAR},
        #{responseBody,jdbcType=VARCHAR},
        #{responseStatus,jdbcType=VARCHAR},
        #{requestTime,jdbcType=TIMESTAMP},
        #{responseTime,jdbcType=TIMESTAMP},
        #{resolveStatus,jdbcType=VARCHAR},
        #{resolveCount,jdbcType=INTEGER},
        #{applyCount,jdbcType=INTEGER},
        #{status,jdbcType=VARCHAR},
        #{serialNum,jdbcType=VARCHAR},
        #{lastUpdateTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO">
        insert into ext_api_log(id,
                                parent_id,
                                config_id,
                                batch_no,
                                sub_batch_no,
                                request_type,
                                trigger_type,
                                request_headers,
                                request_params,
                                request_body,
                                response_headers,
                                response_body,
                                response_status,
                                request_time,
                                response_time,
                                resolve_status,
                                resolve_count,
                                apply_count,
                                status,
                                serial_num,
                                last_update_time,
                                remark,
                                enabled,
                                creator,
                                create_time,
                                modifier,
                                modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{parentId,jdbcType=VARCHAR},
                #{configId,jdbcType=VARCHAR},
                #{batchNo,jdbcType=VARCHAR},
                #{subBatchNo,jdbcType=VARCHAR},
                #{requestType,jdbcType=VARCHAR},
                #{triggerType,jdbcType=VARCHAR},
                #{requestHeaders,jdbcType=VARCHAR},
                #{requestParams,jdbcType=VARCHAR},
                #{requestBody,jdbcType=VARCHAR},
                #{responseHeaders,jdbcType=VARCHAR},
                #{responseBody,jdbcType=VARCHAR},
                #{responseStatus,jdbcType=VARCHAR},
                #{requestTime,jdbcType=TIMESTAMP},
                #{responseTime,jdbcType=TIMESTAMP},
                #{resolveStatus,jdbcType=VARCHAR},
                #{resolveCount,jdbcType=INTEGER},
                #{applyCount,jdbcType=INTEGER},
                #{status,jdbcType=VARCHAR},
                #{serialNum,jdbcType=VARCHAR},
                #{lastUpdateTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into ext_api_log(
        id,
        parent_id,
        config_id,
        batch_no,
        sub_batch_no,
        request_type,
        trigger_type,
        request_headers,
        request_params,
        request_body,
        response_headers,
        response_body,
        response_status,
        request_time,
        response_time,
        resolve_status,
        resolve_count,
        apply_count,
        status,
        serial_num,
        last_update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.configId,jdbcType=VARCHAR},
            #{entity.batchNo,jdbcType=VARCHAR},
            #{entity.subBatchNo,jdbcType=VARCHAR},
            #{entity.requestType,jdbcType=VARCHAR},
            #{entity.triggerType,jdbcType=VARCHAR},
            #{entity.requestHeaders,jdbcType=VARCHAR},
            #{entity.requestParams,jdbcType=VARCHAR},
            #{entity.requestBody,jdbcType=VARCHAR},
            #{entity.responseHeaders,jdbcType=VARCHAR},
            #{entity.responseBody,jdbcType=VARCHAR},
            #{entity.responseStatus,jdbcType=VARCHAR},
            #{entity.requestTime,jdbcType=TIMESTAMP},
            #{entity.responseTime,jdbcType=TIMESTAMP},
            #{entity.resolveStatus,jdbcType=VARCHAR},
            #{entity.resolveCount,jdbcType=INTEGER},
            #{entity.applyCount,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.serialNum,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into ext_api_log(
        id,
        parent_id,
        config_id,
        batch_no,
        sub_batch_no,
        request_type,
        trigger_type,
        request_headers,
        request_params,
        request_body,
        response_headers,
        response_body,
        response_status,
        request_time,
        response_time,
        resolve_status,
        resolve_count,
        apply_count,
        status,
        serial_num,
        last_update_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.parentId,jdbcType=VARCHAR},
            #{entity.configId,jdbcType=VARCHAR},
            #{entity.batchNo,jdbcType=VARCHAR},
            #{entity.subBatchNo,jdbcType=VARCHAR},
            #{entity.requestType,jdbcType=VARCHAR},
            #{entity.triggerType,jdbcType=VARCHAR},
            #{entity.requestHeaders,jdbcType=VARCHAR},
            #{entity.requestParams,jdbcType=VARCHAR},
            #{entity.requestBody,jdbcType=VARCHAR},
            #{entity.responseHeaders,jdbcType=VARCHAR},
            #{entity.responseBody,jdbcType=VARCHAR},
            #{entity.responseStatus,jdbcType=VARCHAR},
            #{entity.requestTime,jdbcType=TIMESTAMP},
            #{entity.responseTime,jdbcType=TIMESTAMP},
            #{entity.resolveStatus,jdbcType=VARCHAR},
            #{entity.resolveCount,jdbcType=INTEGER},
            #{entity.applyCount,jdbcType=INTEGER},
            #{entity.status,jdbcType=VARCHAR},
            #{entity.serialNum,jdbcType=VARCHAR},
            #{entity.lastUpdateTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO">
        update ext_api_log
        set parent_id        = #{parentId,jdbcType=VARCHAR},
            config_id        = #{configId,jdbcType=VARCHAR},
            batch_no         = #{batchNo,jdbcType=VARCHAR},
            sub_batch_no     = #{subBatchNo,jdbcType=VARCHAR},
            request_type     = #{requestType,jdbcType=VARCHAR},
            trigger_type     = #{triggerType,jdbcType=VARCHAR},
            request_headers  = #{requestHeaders,jdbcType=VARCHAR},
            request_params   = #{requestParams,jdbcType=VARCHAR},
            request_body     = #{requestBody,jdbcType=VARCHAR},
            response_headers = #{responseHeaders,jdbcType=VARCHAR},
            response_body    = #{responseBody,jdbcType=VARCHAR},
            response_status  = #{responseStatus,jdbcType=VARCHAR},
            request_time     = #{requestTime,jdbcType=TIMESTAMP},
            response_time    = #{responseTime,jdbcType=TIMESTAMP},
            resolve_status   = #{resolveStatus,jdbcType=VARCHAR},
            resolve_count    = #{resolveCount,jdbcType=INTEGER},
            apply_count      = #{applyCount,jdbcType=INTEGER},
            status           = #{status,jdbcType=VARCHAR},
            serial_num       = #{serialNum,jdbcType=VARCHAR},
            last_update_time = #{lastUpdateTime,jdbcType=TIMESTAMP},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dcp.apiLog.infrastructure.po.ExtApiLogPO">
        update ext_api_log
        <set>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.configId != null and item.configId != ''">
                config_id = #{item.configId,jdbcType=VARCHAR},
            </if>
            <if test="item.batchNo != null and item.batchNo != ''">
                batch_no = #{item.batchNo,jdbcType=VARCHAR},
            </if>
            <if test="item.subBatchNo != null and item.subBatchNo != ''">
                sub_batch_no = #{item.subBatchNo,jdbcType=VARCHAR},
            </if>
            <if test="item.requestType != null and item.requestType != ''">
                request_type = #{item.requestType,jdbcType=VARCHAR},
            </if>
            <if test="item.triggerType != null and item.triggerType != ''">
                trigger_type = #{item.triggerType,jdbcType=VARCHAR},
            </if>
            <if test="item.requestHeaders != null and item.requestHeaders != ''">
                request_headers = #{item.requestHeaders,jdbcType=VARCHAR},
            </if>
            <if test="item.requestParams != null and item.requestParams != ''">
                request_params = #{item.requestParams,jdbcType=VARCHAR},
            </if>
            <if test="item.requestBody != null and item.requestBody != ''">
                request_body = #{item.requestBody,jdbcType=VARCHAR},
            </if>
            <if test="item.responseHeaders != null and item.responseHeaders != ''">
                response_headers = #{item.responseHeaders,jdbcType=VARCHAR},
            </if>
            <if test="item.responseBody != null and item.responseBody != ''">
                response_body = #{item.responseBody,jdbcType=VARCHAR},
            </if>
            <if test="item.responseStatus != null and item.responseStatus != ''">
                response_status = #{item.responseStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.requestTime != null">
                request_time = #{item.requestTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.responseTime != null">
                response_time = #{item.responseTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.resolveStatus != null and item.resolveStatus != ''">
                resolve_status = #{item.resolveStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.resolveCount != null">
                resolve_count = #{item.resolveCount,jdbcType=INTEGER},
            </if>
            <if test="item.applyCount != null">
                apply_count = #{item.applyCount,jdbcType=INTEGER},
            </if>
            <if test="item.status != null and item.status != ''">
                status = #{item.status,jdbcType=VARCHAR},
            </if>
            <if test="item.serialNum != null and item.serialNum != ''">
                serial_num = #{item.serialNum,jdbcType=VARCHAR},
            </if>
            <if test="item.lastUpdateTime != null">
                last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update ext_api_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="config_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.configId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="batch_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.batchNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sub_batch_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.subBatchNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="trigger_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.triggerType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_headers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestHeaders,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_params = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestParams,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_body = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestBody,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="response_headers = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.responseHeaders,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="response_body = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.responseBody,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="response_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.responseStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="response_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.responseTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="resolve_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resolveStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resolve_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resolveCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="apply_count = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.applyCount,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.status,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="serial_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.serialNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update ext_api_log
            <set>
                <if test="item.parentId != null and item.parentId != ''">
                    parent_id = #{item.parentId,jdbcType=VARCHAR},
                </if>
                <if test="item.configId != null and item.configId != ''">
                    config_id = #{item.configId,jdbcType=VARCHAR},
                </if>
                <if test="item.batchNo != null and item.batchNo != ''">
                    batch_no = #{item.batchNo,jdbcType=VARCHAR},
                </if>
                <if test="item.subBatchNo != null and item.subBatchNo != ''">
                    sub_batch_no = #{item.subBatchNo,jdbcType=VARCHAR},
                </if>
                <if test="item.requestType != null and item.requestType != ''">
                    request_type = #{item.requestType,jdbcType=VARCHAR},
                </if>
                <if test="item.triggerType != null and item.triggerType != ''">
                    trigger_type = #{item.triggerType,jdbcType=VARCHAR},
                </if>
                <if test="item.requestHeaders != null and item.requestHeaders != ''">
                    request_headers = #{item.requestHeaders,jdbcType=VARCHAR},
                </if>
                <if test="item.requestParams != null and item.requestParams != ''">
                    request_params = #{item.requestParams,jdbcType=VARCHAR},
                </if>
                <if test="item.requestBody != null and item.requestBody != ''">
                    request_body = #{item.requestBody,jdbcType=VARCHAR},
                </if>
                <if test="item.responseHeaders != null and item.responseHeaders != ''">
                    response_headers = #{item.responseHeaders,jdbcType=VARCHAR},
                </if>
                <if test="item.responseBody != null and item.responseBody != ''">
                    response_body = #{item.responseBody,jdbcType=VARCHAR},
                </if>
                <if test="item.responseStatus != null and item.responseStatus != ''">
                    response_status = #{item.responseStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.requestTime != null">
                    request_time = #{item.requestTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.responseTime != null">
                    response_time = #{item.responseTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.resolveStatus != null and item.resolveStatus != ''">
                    resolve_status = #{item.resolveStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.resolveCount != null">
                    resolve_count = #{item.resolveCount,jdbcType=INTEGER},
                </if>
                <if test="item.applyCount != null">
                    apply_count = #{item.applyCount,jdbcType=INTEGER},
                </if>
                <if test="item.status != null and item.status != ''">
                    status = #{item.status,jdbcType=VARCHAR},
                </if>
                <if test="item.serialNum != null and item.serialNum != ''">
                    serial_num = #{item.serialNum,jdbcType=VARCHAR},
                </if>
                <if test="item.lastUpdateTime != null">
                    last_update_time = #{item.lastUpdateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from ext_api_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from ext_api_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
