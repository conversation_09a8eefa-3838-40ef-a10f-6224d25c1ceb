package com.yhl.scp.dcp.externalApi.handler.mes;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesProductQualifiedSupplier;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesResponseData;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.service.ExtApiLogService;
import com.yhl.scp.dcp.common.constants.DcpConstants;
import com.yhl.scp.dcp.enmus.MesApiReqCodeEnum;
import com.yhl.scp.dcp.externalApi.handler.SyncDataHandler;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>ProductQualifiedSupplierHandler</code>
 * <p>
 * MES物料合格供应商同步
 * 同步方式：增量
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 10:49:53
 */
@Component
@Slf4j
public class ProductQualifiedSupplierHandler extends SyncDataHandler<List<MesProductQualifiedSupplier>> {
    @Resource
    private AuthHandler authHandler;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;


    @Resource
    private ExtApiLogService extApiLogService;

    @Override
    protected List<MesProductQualifiedSupplier> convertData(String body) {
        if (StringUtils.isBlank(body)) {
            log.error("MES同步物料合格供应商数据为空！");
            return Collections.emptyList();
        }
        return JSON.parseArray(body, MesProductQualifiedSupplier.class);
    }

    @Override
    protected String handleBody(ApiConfigVO apiConfigVO, Map<String, Object> params, List<MesProductQualifiedSupplier> mesProductQualifiedSuppliers) {
        this.saveSyncCtrlSyncTime(apiConfigVO, params);
        if (CollectionUtils.isEmpty(mesProductQualifiedSuppliers)) {
            log.error("物料合格供应商数据为空");
            return null;
        }
        try {
            BaseResponse<String> mdsScenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
            Assert.isTrue(mdsScenario.getSuccess(), mdsScenario.getMsg());
            log.info("接口返回MES物料合格供应商数据大小:{}",mesProductQualifiedSuppliers.size());
            mdsFeign.handleProductQualifiedSupplier(mdsScenario.getData(), mesProductQualifiedSuppliers);
            this.saveSyncCtrl(apiConfigVO, params, mesProductQualifiedSuppliers);
            return "处理成功";
        } catch (Exception e) {
            return "处理物料合格供应商数据时发生错误: " + e.getMessage();
        }
    }

    @Override
    protected String callApi(ApiConfigVO apiConfigVO, Map<String, Object> params) {
        if (log.isInfoEnabled()) {
            log.info("开始同步MES物料合格供应商:{},{}", apiConfigVO, params);
        }
        ExtApiLogDTO mainLog = extApiLogService.createLog(apiConfigVO, params, null, null, null);
        try {
            String mesToken = authHandler.handle(MapUtil.newHashMap());
            String apiUri = apiConfigVO.getApiUri();
            String apiParams = apiConfigVO.getApiParams();
            String systemNumber = apiConfigVO.getSystemNumber();
            String url = apiUri + "/" + systemNumber + "/"
                    + sequenceService.getSuffix(systemNumber, getCommand(), 5) + apiParams;
            String reqCode = MesApiReqCodeEnum.FY_ASL_FOR_BPIM.getCode();

            String lastUpdateDateStr = this.getSyncRefValue(apiConfigVO, params);
            Date calculateDate = DateUtils.stringToDate(lastUpdateDateStr, DateUtils.COMMON_DATE_STR3);
            Date currentDate = DateUtils.truncateTimeOfDate(new Date());
            if (log.isInfoEnabled()) {
                log.info("apiUri={},systemNumber={},url={},reqCode={},lastUpdateDateStr={},currentDate={}", apiUri, systemNumber,
                        url, reqCode, lastUpdateDateStr, currentDate);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setContentType(MediaType.APPLICATION_JSON);
            httpHeaders.set("Authorization", "Bearer " + mesToken);
            int period = (int) DateUtil.between(calculateDate, currentDate, DateUnit.DAY);
            int calculatePeriod = 5;
            int count = period / calculatePeriod + 1;
            int pageSize = Objects.isNull(apiConfigVO.getOffsetSize())?10000:apiConfigVO.getOffsetSize(); // 根据接口说明设置每页记录数
            Date beginTime = calculateDate;
            List<Object> result = Lists.newArrayList();
            for (int i = 0; i < count; i++) {
                int currentPage = 1;
                boolean hasNextSize = true;
                while (hasNextSize) {
                    HashMap<Object, Object> paramMap = MapUtil.newHashMap();
                    paramMap.put("currentPage", currentPage);
                    paramMap.put("pageSize", pageSize);
                    paramMap.put("reqCode", reqCode);
                    paramMap.put("beginTime", DateUtils.dateToString(beginTime, DateUtils.COMMON_DATE_STR1));
                    Date endTime = org.apache.commons.lang3.time.DateUtils.addDays(beginTime, calculatePeriod);
                    paramMap.put("endTime", DateUtils.dateToString(endTime, DateUtils.COMMON_DATE_STR1));
                    if (log.isInfoEnabled()) {
                        log.info("request paramMap={}", paramMap);
                    }
                    // 创建子日志
                    ExtApiLogDTO subLog = extApiLogService.createLog(apiConfigVO, params, mainLog,
                            httpHeaders.toString(),
                            JSONObject.toJSONString(paramMap));
                    HttpEntity<String> httpEntity = new HttpEntity<>(JSON.toJSONString(paramMap), httpHeaders);
                    ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, httpEntity, String.class);
                    int statusCodeValue = responseEntity.getStatusCodeValue();
                    if (HttpStatus.OK.value() != statusCodeValue) {
                        extApiLogService.updateResponse(subLog, responseEntity, null, DcpConstants.TASKS_STATUS_ERROR);
                    }
                    Assert.isTrue(HttpStatus.OK.value() == statusCodeValue, "MES同步物料合格供应商失败！");
                    String body = responseEntity.getBody();
                    log.info("请求MES物料合格供应商完成,返回数据:{}!", body);
                    MesResponse mesResponse = JSON.parseObject(body, MesResponse.class);
                    MesResponseData data = Objects.requireNonNull(mesResponse).getData();
                    extApiLogService.updateResponse(subLog, responseEntity, data.getMessage().size(), DcpConstants.TASKS_STATUS_SUCCESS);
                    if (Objects.nonNull(data)) {
                        result.addAll(data.getMessage());
                        if (data.getTotalPage() <= data.getCurrentPage()) {
                            hasNextSize = false;
                            beginTime = endTime;
                        } else {
                            currentPage++;
                        }
                    }
                }

            }
            // 3. 完成主日志
            extApiLogService.updateResponse(mainLog, null, result.size(), DcpConstants.TASKS_STATUS_SUCCESS);
            return JSON.toJSONString(result);
        } catch (Exception e) {
            extApiLogService.updateResponse(mainLog, null, null, DcpConstants.TASKS_STATUS_ERROR);
            throw e;
        }
    }

    @Override
    public String getCommand() {
        return String.join(CMD_DELIMITER, TenantCodeEnum.FYQB.getCode(), ApiSourceEnum.MES.getCode(),
                ApiCategoryEnum.PRODUCT_QUALIFIED_SUPPLIER.getCode());
    }

    @Override
    protected String computeMaxSyncRefValue(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                            List<MesProductQualifiedSupplier> mesProductQualifiedSuppliers) {
        Date lastUpdateDate = mesProductQualifiedSuppliers.stream().map(MesProductQualifiedSupplier::getLastUpdateDate).max(Date::compareTo).get();
        return DateUtils.dateToString(lastUpdateDate, DateUtils.COMMON_DATE_STR1);
    }
}
