package com.yhl.scp.dcp.apiLog.service;

import com.yhl.scp.dcp.apiLog.dto.ExtApiLogMessageDTO;

import java.util.List;

/**
 * <code>ExtApiLogMessageService</code>
 * <p>
 * 接口日志报文数据服务接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-04
 */
public interface ExtApiLogMessageService {

    /**
     * 保存请求报文数据
     *
     * @param logId       日志ID
     * @param requestBody 请求体
     */
    void saveRequestMessage(String logId, String requestBody);

    /**
     * 保存响应报文数据
     *
     * @param logId        日志ID
     * @param responseBody 响应体
     */
    void saveResponseMessage(String logId, String responseBody);

    /**
     * 处理报文数据（解析数组并分别存储）
     *
     * @param logId       日志ID
     * @param messageData 报文数据
     * @param messageType 报文类型（REQUEST/RESPONSE）
     */
    void processMessageData(String logId, String messageData, String messageType);

    /**
     * 根据日志ID查询报文数据
     *
     * @param logId 日志ID
     * @return 报文数据列表
     */
    List<ExtApiLogMessageDTO> getMessagesByLogId(String logId);

    /**
     * 根据日志ID和报文类型查询报文数据
     *
     * @param logId       日志ID
     * @param messageType 报文类型
     * @return 报文数据列表
     */
    List<ExtApiLogMessageDTO> getMessagesByLogIdAndType(String logId, String messageType);

    /**
     * 删除日志相关的报文数据
     *
     * @param logId 日志ID
     */
    void deleteMessagesByLogId(String logId);
}
