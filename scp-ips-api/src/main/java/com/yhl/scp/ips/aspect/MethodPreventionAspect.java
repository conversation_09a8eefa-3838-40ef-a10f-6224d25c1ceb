package com.yhl.scp.ips.aspect;

import com.fasterxml.jackson.core.type.TypeReference;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.scp.common.annotation.MethodPrevention;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.enums.ScenarioTypeEnum;
import com.yhl.scp.ips.common.SystemHolder;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <code>MethodPreventionAspect</code>
 * <p>
 * 方法阻断切面
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-01-13 17:33:05
 */
@Component
@Aspect
@Order(-1)
@Slf4j
public class MethodPreventionAspect {

    @Pointcut("@annotation(com.yhl.scp.common.annotation.MethodPrevention)")
    private void pointCut() {
    }

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 使用切面拦截用户请求，设置本次请求数据源
     *
     * @param joinPoint 切入点
     */
    @Before("pointCut()")
    public void before(JoinPoint joinPoint) {
        String scenarioType = ((MethodSignature) joinPoint.getSignature()).getMethod()
                .getAnnotation(MethodPrevention.class).value();
        ListOperations<String, Object> listOps = redisTemplate.opsForList();
        String tenantId = SystemHolder.getTenantId();
        List<Object> primaryList = listOps.range(Constants.MASTER_SCENARIO + "#" + tenantId, 0, -1);
        if (CollectionUtils.isEmpty(primaryList)) {
            return;
        }

        List<String> masterList = new ArrayList<>();
        primaryList.forEach(item -> masterList.addAll(JacksonUtils.toObj(item.toString(),
                new TypeReference<ArrayList<String>>() {
                })));
        boolean isPrimary = masterList.contains(SystemHolder.getScenario());
        if (ScenarioTypeEnum.PRIMARY.getCode().equals(scenarioType) && !isPrimary) {
            throw new BusinessException("子场景不能操作");
        }
        if (ScenarioTypeEnum.SECONDARY.getCode().equals(scenarioType) && isPrimary) {
            throw new BusinessException("主场景不能操作");
        }
    }

}