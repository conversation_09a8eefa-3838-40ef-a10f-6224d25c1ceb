package com.yhl.scp.ips.redisKeyManage.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.redisKeyManage.dto.RedisKeyManageDTO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageDetailVO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;

import java.util.List;

/**
 * <code>RedisKeyManageService</code>
 * <p>
 * redis key管理应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:15:44
 */
public interface RedisKeyManageService extends BaseService<RedisKeyManageDTO, RedisKeyManageVO> {

    /**
     * 查询所有
     *
     * @return list {@link RedisKeyManageVO}
     */
    List<RedisKeyManageVO> selectAll();

    BaseResponse<Void> removeKey(String id);

    BaseResponse<Void> removeKeyByConfigCode(String configCode);

    BaseResponse<List<RedisKeyManageDetailVO>> getDetailByConfigCode(String configCode);
}
