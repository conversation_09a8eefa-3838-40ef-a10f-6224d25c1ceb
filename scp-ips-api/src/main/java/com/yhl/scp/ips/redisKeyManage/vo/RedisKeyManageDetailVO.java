package com.yhl.scp.ips.redisKeyManage.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <code>RedisKeyManageVO</code>
 * <p>
 * redis key管理VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:15:43
 */
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "redis key详情管理VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RedisKeyManageDetailVO extends BaseVO implements Serializable {

    /**
     * redis key
     */
    @ApiModelProperty(value = "redis key")
    @FieldInterpretation(value = "redis key")
    private String configCode;

    /**
     * key值是否存在
     */
    @ApiModelProperty(value = "key值是否存在")
    @FieldInterpretation(value = "key值是否存在")
    private String existKey;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间")
    @FieldInterpretation(value = "过期时间")
    private Long expireTime;
    /**
     * 值集
     */
    @ApiModelProperty(value = "值集")
    @FieldInterpretation(value = "值集")
    private Object keyResult;

    @Override
    public void clean() {

    }
}
