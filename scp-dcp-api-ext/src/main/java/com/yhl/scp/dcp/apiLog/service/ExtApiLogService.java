package com.yhl.scp.dcp.apiLog.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.vo.ApiConfigVO;
import com.yhl.scp.dcp.apiLog.dto.ExtApiLogDTO;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.Map;

/**
 * <code>ExtApiLogService</code>
 * <p>
 * 接口日志表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 13:42:53
 */
public interface ExtApiLogService extends BaseService<ExtApiLogDTO, ExtApiLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link ExtApiLogVO}
     */
    List<ExtApiLogVO> selectAll();

    BaseResponse<Void> update(ExtApiLogDTO extApiLogDTO);

    BaseResponse<Void> create(ExtApiLogDTO extApiLogDTO);
    ExtApiLogDTO createLog(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                  ExtApiLogDTO parentLog, String requestHeaders, String requestParams);

    ExtApiLogDTO createLog(ApiConfigVO apiConfigVO, Map<String, Object> params,
                                  ExtApiLogDTO parentLog, String requestHeaders, String requestParams, String requestBody);

    void updateResponse(ExtApiLogDTO log, ResponseEntity<String> response,
                               Integer dataCount, String status);
}
