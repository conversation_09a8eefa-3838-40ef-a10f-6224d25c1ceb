package com.yhl.scp.dcp.apiLog.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiLogDTO</code>
 * <p>
 * 接口日志表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 13:42:53
 */
@ApiModel(value = "接口日志表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ExtApiLogDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 916409947545562380L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 父日志id
     */
    @ApiModelProperty(value = "父日志id")
    private String parentId;
    /**
     * 配置ID
     */
    @ApiModelProperty(value = "配置ID")
    private String configId;
    /**
     * 批次号
     */
    @ApiModelProperty(value = "批次号")
    private String batchNo;
    /**
     * 子批次号
     */
    @ApiModelProperty(value = "子批次号")
    private String subBatchNo;
    /**
     * 请求方式
     */
    @ApiModelProperty(value = "请求方式")
    private String requestType;
    /**
     * 触发方式
     */
    @ApiModelProperty(value = "触发方式")
    private String triggerType;
    /**
     * 请求头
     */
    @ApiModelProperty(value = "请求头")
    private String requestHeaders;
    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    private String requestParams;
    /**
     * 请求体
     */
    @ApiModelProperty(value = "请求体")
    private String requestBody;
    /**
     * 响应头
     */
    @ApiModelProperty(value = "响应头")
    private String responseHeaders;
    /**
     * 响应体
     */
    @ApiModelProperty(value = "响应体")
    private String responseBody;
    /**
     * 响应状态
     */
    @ApiModelProperty(value = "响应状态")
    private String responseStatus;
    /**
     * 请求时间
     */
    @ApiModelProperty(value = "请求时间")
    private Date requestTime;
    /**
     * 响应时间
     */
    @ApiModelProperty(value = "响应时间")
    private Date responseTime;
    /**
     * 解析状态
     */
    @ApiModelProperty(value = "解析状态")
    private String resolveStatus;
    /**
     * 解析数量
     */
    @ApiModelProperty(value = "解析数量")
    private Integer resolveCount;
    /**
     * 应用数量
     */
    @ApiModelProperty(value = "应用数量")
    private Integer applyCount;
    /**
     * 运行状态
     */
    @ApiModelProperty(value = "运行状态")
    private String status;
    /**
     * 请求流水号
     */
    @ApiModelProperty(value = "请求流水号")
    private String serialNum;
    @ApiModelProperty(value = "${column.comment}")
    private Date lastUpdateTime;
    @ApiModelProperty(value = "${column.comment}")
    private String remark;
    @ApiModelProperty(value = "${column.comment}")
    private String enabled;

}
