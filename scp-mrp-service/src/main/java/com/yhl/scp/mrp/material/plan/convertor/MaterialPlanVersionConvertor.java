package com.yhl.scp.mrp.material.plan.convertor;

import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanVersionDO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanVersionDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanVersionPO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanVersionVO;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <code>MaterialPlanVersionConvertor</code>
 * <p>
 * 物料计划版本转换器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:14:50
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface MaterialPlanVersionConvertor {

    MaterialPlanVersionConvertor INSTANCE = Mappers.getMapper(MaterialPlanVersionConvertor.class);

    MaterialPlanVersionDO dto2Do(MaterialPlanVersionDTO obj);

    List<MaterialPlanVersionDO> dto2Dos(List<MaterialPlanVersionDTO> list);

    MaterialPlanVersionDTO do2Dto(MaterialPlanVersionDO obj);

    List<MaterialPlanVersionDTO> do2Dtos(List<MaterialPlanVersionDO> list);

    MaterialPlanVersionDTO vo2Dto(MaterialPlanVersionVO obj);

    List<MaterialPlanVersionDTO> vo2Dtos(List<MaterialPlanVersionVO> list);

    MaterialPlanVersionVO po2Vo(MaterialPlanVersionPO obj);

    List<MaterialPlanVersionVO> po2Vos(List<MaterialPlanVersionPO> list);

    MaterialPlanVersionPO dto2Po(MaterialPlanVersionDTO obj);

    List<MaterialPlanVersionPO> dto2Pos(List<MaterialPlanVersionDTO> obj);

    MaterialPlanVersionVO do2Vo(MaterialPlanVersionDO obj);

    MaterialPlanVersionPO do2Po(MaterialPlanVersionDO obj);

    MaterialPlanVersionDO po2Do(MaterialPlanVersionPO obj);

    MaterialPlanVersionPO vo2Po(MaterialPlanVersionVO obj);

}
