package com.yhl.scp.mrp.material.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.enums.MaterialPlanTypeEnum;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftPageVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * <code>NoGlassInventoryShiftDataController</code>
 * <p>
 * 非原片库存推移主表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-26 15:25:04
 */
@Slf4j
@Api(tags = "非原片库存推移主表控制器")
@RestController
@RequestMapping("noGlassInventoryShiftData")
public class NoGlassInventoryShiftDataController extends BaseController {

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<NoGlassInventoryShiftDataVO>> page() {
        List<NoGlassInventoryShiftDataVO> noGlassInventoryShiftDataList = noGlassInventoryShiftDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<NoGlassInventoryShiftDataVO> pageInfo = new PageInfo<>(noGlassInventoryShiftDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "分页查询(非组件)")
    @PostMapping(value = "page2")
    public BaseResponse<PageInfo<MaterialPlanInventoryShiftPageVO>> page2(@RequestBody MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO) {
        List<MaterialPlanInventoryShiftPageVO> result = noGlassInventoryShiftDataService.selectPage2(materialPlanInventoryShiftParamDTO);
        PageInfo<MaterialPlanInventoryShiftPageVO> pageInfoResult = new PageInfo<>(result);
        if (CollectionUtils.isNotEmpty(result) && Objects.nonNull(result.get(0).getPageInfo())) {
            PageInfo<NoGlassInventoryShiftDataVO> pageInfo = result.get(0).getPageInfo();
            pageInfoResult.setTotal(pageInfo.getTotal());
            pageInfoResult.setPages(pageInfo.getPages());
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfoResult);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO) {
        return noGlassInventoryShiftDataService.doCreate(noGlassInventoryShiftDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO) {
        return noGlassInventoryShiftDataService.doUpdate(noGlassInventoryShiftDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        noGlassInventoryShiftDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<NoGlassInventoryShiftDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, noGlassInventoryShiftDataService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "publish")
    @BusinessMonitorLog(businessCode = "材料计划发布", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Set<String>> publish(@RequestParam(name = "publishType") String publishType,
                                      @RequestBody List<String> productCodeList) {
        try {
            if (redisUtil.hasKey(MaterialRedisConstants.NO_GLASS_MRP_PUBLISHED)) {
                return BaseResponse.error("当前已有推移结果正在发布，请等待发布完成");
            }
            redisUtil.set(MaterialRedisConstants.NO_GLASS_MRP_PUBLISHED, SystemHolder.getUserId(), 60 * 60);
            Set<String> errorMsgList = new HashSet<>();
            if (StringUtils.equals(publishType, MaterialPlanTypeEnum.NO_GLASS.getCode())) {
                errorMsgList = noGlassInventoryShiftDataService.doPublish(productCodeList);
            }
            return BaseResponse.success(errorMsgList);
        } catch (Exception e) {
            log.error("发布失败", e);
            throw new BusinessException("发布失败,{0}", e.getLocalizedMessage());
        }finally {
            // 释放key
            redisUtil.delete(MaterialRedisConstants.NO_GLASS_MRP_PUBLISHED);
        }
    }

}
