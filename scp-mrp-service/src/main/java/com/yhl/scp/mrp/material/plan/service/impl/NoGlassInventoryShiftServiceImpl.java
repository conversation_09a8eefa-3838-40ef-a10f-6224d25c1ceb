package com.yhl.scp.mrp.material.plan.service.impl;

import cn.hutool.core.date.StopWatch;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.bom.vo.ProductBomVO;
import com.yhl.scp.mds.bom.vo.ProductBomVersionVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.assignment.dto.MaterialDemandAssignmentDTO;
import com.yhl.scp.mrp.assignment.service.MaterialDemandAssignmentService;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.enums.DemandPatternEnum;
import com.yhl.scp.mrp.enums.PlanNeedPublishStatusEnum;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import com.yhl.scp.mrp.inventory.dto.MaterialPlanInventoryShiftParamDTO;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanNeedConvertor;
import com.yhl.scp.mrp.material.plan.convertor.NoGlassInventoryShiftConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.NoGlassInventoryShiftDO;
import com.yhl.scp.mrp.material.plan.domain.service.NoGlassInventoryShiftDomainService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.dto.NoGlassInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.enums.MaterialPlanTypeEnum;
import com.yhl.scp.mrp.material.plan.enums.MrpDemandSourceEnum;
import com.yhl.scp.mrp.material.plan.enums.MrpSupplySourceEnum;
import com.yhl.scp.mrp.material.plan.enums.WarningRemindEnum;
import com.yhl.scp.mrp.material.plan.enums.*;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanVersionDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.NoGlassInventoryShiftDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftPO;
import com.yhl.scp.mrp.material.plan.service.*;
import com.yhl.scp.mrp.material.plan.vo.*;
import com.yhl.scp.mrp.material.purchase.dto.SyncMaterialPurchaseDTO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseRequirementDetailService;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import com.yhl.scp.mrp.published.domain.service.NoGlassInventoryShiftDemandPublishedDomainService;
import com.yhl.scp.mrp.published.domain.service.NoGlassInventoryShiftPublishedDomainService;
import com.yhl.scp.mrp.published.domain.service.NoGlassInventoryShiftSupplyPublishedDomainService;
import com.yhl.scp.mrp.published.dto.MaterialPlanPublishedVersionDTO;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftDemandPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.dao.NoGlassInventoryShiftSupplyPublishedDao;
import com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftDemandPublishedPO;
import com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftPublishedPO;
import com.yhl.scp.mrp.published.infrastructure.po.NoGlassInventoryShiftSupplyPublishedPO;
import com.yhl.scp.mrp.published.service.MaterialPlanPublishedVersionService;
import com.yhl.scp.mrp.published.service.NoGlassInventoryShiftDemandPublishedService;
import com.yhl.scp.mrp.published.service.NoGlassInventoryShiftPublishedService;
import com.yhl.scp.mrp.published.service.NoGlassInventoryShiftSupplyPublishedService;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>NoGlassInventoryShiftServiceImpl</code>
 * <p>
 * 非原片库存推移表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 15:13:59
 */
@Slf4j
@Service
public class NoGlassInventoryShiftServiceImpl extends AbstractService implements NoGlassInventoryShiftService {

    @Resource
    private NoGlassInventoryShiftDao noGlassInventoryShiftDao;

    @Resource
    private NoGlassInventoryShiftDomainService noGlassInventoryShiftDomainService;

    @Resource
    private SupplierPurchaseRatioService supplierPurchaseRatioService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private MaterialPlanInventoryShiftDemandService materialPlanInventoryShiftDemandService;

    @Resource
    private MaterialPlanInventoryShiftSupplyService materialPlanInventoryShiftSupplyService;

    @Resource
    private MaterialPlanVersionService materialPlanVersionService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private PurchaseOrderInfoService purchaseOrderInfoService;

    @Resource
    private MaterialPurchaseRequirementDetailService materialPurchaseRequirementDetailService;

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private MaterialDemandAssignmentService materialDemandAssignmentService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private MaterialPlanPublishedVersionService materialPlanPublishedVersionService;

    @Resource
    private NoGlassInventoryShiftPublishedService noGlassInventoryShiftPublishedService;

    @Resource
    private NoGlassInventoryShiftDemandPublishedService noGlassInventoryShiftDemandPublishedService;

    @Resource
    private NoGlassInventoryShiftSupplyPublishedService noGlassInventoryShiftSupplyPublishedService;

    @Resource
    private NoGlassInventoryShiftPublishedDao noGlassInventoryShiftPublishedDao;

    @Resource
    private NoGlassInventoryShiftDemandPublishedDao noGlassInventoryShiftDemandPublishedDao;

    @Resource
    private NoGlassInventoryShiftSupplyPublishedDao noGlassInventoryShiftSupplyPublishedDao;

    @Resource
    private NoGlassInventoryShiftPublishedDomainService noGlassInventoryShiftPublishedDomainService;

    @Resource
    private NoGlassInventoryShiftDemandPublishedDomainService noGlassInventoryShiftDemandPublishedDomainService;

    @Resource
    private NoGlassInventoryShiftSupplyPublishedDomainService noGlassInventoryShiftSupplyPublishedDomainService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        // 0.数据转换
        NoGlassInventoryShiftDO noGlassInventoryShiftDO = NoGlassInventoryShiftConvertor.INSTANCE.dto2Do(noGlassInventoryShiftDTO);
        NoGlassInventoryShiftPO noGlassInventoryShiftPO = NoGlassInventoryShiftConvertor.INSTANCE.dto2Po(noGlassInventoryShiftDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        noGlassInventoryShiftDomainService.validation(noGlassInventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(noGlassInventoryShiftPO);
        noGlassInventoryShiftDao.insert(noGlassInventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        // 0.数据转换
        NoGlassInventoryShiftDO noGlassInventoryShiftDO = NoGlassInventoryShiftConvertor.INSTANCE.dto2Do(noGlassInventoryShiftDTO);
        NoGlassInventoryShiftPO noGlassInventoryShiftPO = NoGlassInventoryShiftConvertor.INSTANCE.dto2Po(noGlassInventoryShiftDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        noGlassInventoryShiftDomainService.validation(noGlassInventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(noGlassInventoryShiftPO);
        noGlassInventoryShiftDao.update(noGlassInventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NoGlassInventoryShiftDTO> list) {
        List<NoGlassInventoryShiftPO> newList = NoGlassInventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        noGlassInventoryShiftDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<NoGlassInventoryShiftDTO> list) {
        List<NoGlassInventoryShiftPO> newList = NoGlassInventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        noGlassInventoryShiftDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return noGlassInventoryShiftDao.deleteBatch(idList);
        }
        return noGlassInventoryShiftDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NoGlassInventoryShiftVO selectByPrimaryKey(String id) {
        NoGlassInventoryShiftPO po = noGlassInventoryShiftDao.selectByPrimaryKey(id);
        return NoGlassInventoryShiftConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "NO_GLASS_INVENTORY_SHIFT")
    public List<NoGlassInventoryShiftVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "NO_GLASS_INVENTORY_SHIFT")
    public List<NoGlassInventoryShiftVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NoGlassInventoryShiftVO> dataList = noGlassInventoryShiftDao.selectByCondition(sortParam, queryCriteriaParam);
        NoGlassInventoryShiftServiceImpl target = SpringBeanUtils.getBean(NoGlassInventoryShiftServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NoGlassInventoryShiftVO> selectByParams(Map<String, Object> params) {
        List<NoGlassInventoryShiftPO> list = noGlassInventoryShiftDao.selectByParams(params);
        return NoGlassInventoryShiftConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NoGlassInventoryShiftVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
//        return ObjectTypeEnum.NO_GLASS_INVENTORY_SHIFT.getCode();
    }

    @Override
    public List<NoGlassInventoryShiftVO> invocation(List<NoGlassInventoryShiftVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    public NoGlassInventoryShiftServiceImpl() {
        super();
    }

    @Override
    public void doDeleteByMaterialPlanVersionId(String materialPlanVersionId) {
        noGlassInventoryShiftDao.deleteByMaterialPlanVersionId(materialPlanVersionId);
    }

    @Override
    public PageInfo<MaterialPlanInventoryShiftPageVO> selectPage2(MaterialPlanInventoryShiftParamDTO paramDTO) {
        return null;
    }

    @Override
    public Map<String, String> getVehicleModelCode(List<String> productCodeList){
        Map<String, String> productCodeToVehicleModelCode = new HashMap<>(productCodeList.size());
        // 获取材料对应的半成品、成品
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectByProductCode(SystemHolder.getScenario(), productCodeList);
        Map<String, List<NewProductStockPointVO>> productGroupCode = newProductStockPointVOS.stream().collect(Collectors.groupingBy(NewProductStockPointVO::getProductCode));

        // 查询bom表
        List<String> materialIdList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).collect(Collectors.toList());
        List<ProductBomVO> productBomVOList = newMdsFeign.selectProductBomVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ioProductIds", materialIdList));
        Map<String, List<ProductBomVO>> productBomVOGroupOfProductId = productBomVOList.stream().collect(Collectors.groupingBy(ProductBomVO::getIoProductId));

        // 查询bomVersion表
        List<String> bomVersionIdList = productBomVOList.stream().map(ProductBomVO::getBomVersionId).distinct().collect(Collectors.toList());
        List<ProductBomVersionVO> productBomVersionVOList = newMdsFeign.selectProductBomVersionVOByParams(SystemHolder.getScenario(), ImmutableMap.of("ids", bomVersionIdList));
        Map<String, ProductBomVersionVO> productBomVersionVOMapOfId = productBomVersionVOList.stream().collect(Collectors.toMap(ProductBomVersionVO::getId, Function.identity()));

        // 查询本厂编码
        List<String> bomVersionProductIdList = productBomVersionVOList.stream().map(ProductBomVersionVO::getProductId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productStockPointVOS = newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(), bomVersionProductIdList);
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = productStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));

        for (String productCode : productCodeList) {
            List<String> vehicleModelCodeList = new ArrayList<>();
            // 获取productCode对应的Id
            List<NewProductStockPointVO> materialProductList = productGroupCode.get(productCode);
            if (CollectionUtils.isEmpty(materialProductList)){
                continue;
            }
            List<String> bomVersionIds = new ArrayList<>();
            List<String> fgProductIdList = new ArrayList<>();
            for (NewProductStockPointVO newProductStockPointVO : materialProductList) {
                // 获取productId关联的bom
                List<ProductBomVO> productBomVOS = productBomVOGroupOfProductId.get(newProductStockPointVO.getId());
                if (CollectionUtils.isEmpty(productBomVOS)){
                    continue;
                }
                bomVersionIds.addAll(productBomVOS.stream().map(ProductBomVO::getBomVersionId).collect(Collectors.toList()));
            }

            if (CollectionUtils.isEmpty(bomVersionIds)){
                continue;
            }
            for (String bomVersionId : bomVersionIds) {
                ProductBomVersionVO productBomVersionVO = productBomVersionVOMapOfId.get(bomVersionId);
                if (productBomVersionVO != null){
                    fgProductIdList.add(productBomVersionVO.getProductId());
                }
            }
            if (CollectionUtils.isEmpty(fgProductIdList)){
                continue;
            }
            for (String fgProductId : fgProductIdList) {
                NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfId.get(fgProductId);
                if (newProductStockPointVO != null && StringUtils.isNotBlank(newProductStockPointVO.getVehicleModelCode())){
                    vehicleModelCodeList.add(newProductStockPointVO.getVehicleModelCode());
                }
            }
            if (CollectionUtils.isNotEmpty(vehicleModelCodeList)){
                vehicleModelCodeList = vehicleModelCodeList.stream().distinct().collect(Collectors.toList());
                String vehicleModelCodeStr = String.join(",", vehicleModelCodeList);
                productCodeToVehicleModelCode.put(productCode, vehicleModelCodeStr);
            }
        }
        return productCodeToVehicleModelCode;
    }

    @Override
    public List<String> getMaterialCodeByFgProductIdList(List<String> fgProductIdList) {
        List<String> result = new ArrayList<>();
        // 查询工艺路径
        List<RoutingVO> routingVOList = newMdsFeign.selectRoutingByParams(null, ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        Map<String, RoutingVO> routingMapOfProductId = routingVOList.stream().collect(Collectors.toMap(RoutingBasicVO::getProductId, Function.identity(), (k1, k2) -> k2));
        // 查询路径步骤
        List<RoutingStepVO> routingStepVOS = newMdsFeign.selectAllRoutingStep(null);
        Map<String, List<RoutingStepVO>> routingStepListOfRoutingId = routingStepVOS.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        // 查询路径步骤输入
        List<RoutingStepInputVO> stepInputVOS = newMdsFeign.selectByParamsRoutingStepInput(null, ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        Map<String, List<RoutingStepInputVO>> stepInputListOfRoutingStepId = stepInputVOS.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));
        List<String> productIdList = stepInputVOS.stream().map(RoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> newProductStockPointList = new ArrayList<>();
        Lists.partition(productIdList.stream().distinct().collect(Collectors.toList()), 1000).forEach(productStockPointIds -> newProductStockPointList.addAll(newMdsFeign.selectProductStockPointByIds(SystemHolder.getScenario(), productStockPointIds)));
        Map<String, NewProductStockPointVO> productStockPointVOMapOfId = newProductStockPointList.stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));

        for (String productId : fgProductIdList) {
            // 拆解bom
            bomDisassemble(productId, productStockPointVOMapOfId, routingMapOfProductId, routingStepListOfRoutingId, stepInputListOfRoutingStepId, result);
        }
        return result;
    }

    private void bomDisassemble(String productId, Map<String, NewProductStockPointVO> productStockPointVOMapOfId, Map<String, RoutingVO> routingMapOfProductId, Map<String, List<RoutingStepVO>> routingStepListOfRoutingId, Map<String, List<RoutingStepInputVO>> stepInputListOfRoutingStepId, List<String> result) {
        RoutingVO routingVO = routingMapOfProductId.get(productId);
        if (null == routingVO) {
            return;
        }
        List<RoutingStepVO> routingStepVOList = routingStepListOfRoutingId.get(routingVO.getId());
        if (CollectionUtils.isEmpty(routingStepVOList)) {
            return;
        }
        for (RoutingStepVO routingStepVO : routingStepVOList) {
            List<RoutingStepInputVO> routingStepInputVOS = stepInputListOfRoutingStepId.get(routingStepVO.getId());
            if (CollectionUtils.isEmpty(routingStepInputVOS)) {
                continue;
            }
            for (RoutingStepInputVO routingStepInputVO : routingStepInputVOS) {
                NewProductStockPointVO inputProductStockPointVO = productStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                if (null == inputProductStockPointVO) {
                    continue;
                }
                if ("P".equals(inputProductStockPointVO.getProductType())) {
                    result.add(inputProductStockPointVO.getProductCode());
                    continue;
                }
                // 拆解bom
                bomDisassemble(inputProductStockPointVO.getId(), productStockPointVOMapOfId, routingMapOfProductId, routingStepListOfRoutingId, stepInputListOfRoutingStepId, result);
            }
        }

    }

    private List<NoGlassInventoryShiftVO> selectGroupMaterialInfo(Map<String, Object> params) {
        return noGlassInventoryShiftDao.selectGroupMaterialInfo(params);
    }

    private List<MaterialPlanInventoryShiftPageVO> assembleMaterialPlanInventoryShiftPageVO(List<NoGlassInventoryShiftVO> noGlassInventoryShiftVOS,
                                                                                            List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS,
                                                                                            Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                                                                            Map<String, SupplierVO> supplierVOMapOfId,
                                                                                            MaterialPlanInventoryShiftParamDTO materialPlanInventoryShiftParamDTO,
                                                                                            Date lockPeriodStartDate, Map<String, String> vehicleModelCodeOfProductCode) {
        return null;
    }

    public static void main(String[] args) {
        int dayOfWeek = DateUtils.getDayOfWeek(DateUtils.moveDay(new Date(), 2));
        System.out.println("dayOfWeek = " + convertDayOfWeekToChinese(dayOfWeek));

        int requestCargoPlanLockDay = 5;
        Date date = new Date();
        Date moveDay = DateUtils.moveDay(date, 3);
        Long dateInterval = DateUtils.getDateInterval(date, moveDay);
        requestCargoPlanLockDay = requestCargoPlanLockDay - dateInterval.intValue();
        System.out.println("requestCargoPlanLockDay = " + requestCargoPlanLockDay);
    }

    public static String convertDayOfWeekToChinese(int dayOfWeek) {
        Map<Integer, String> dayMap = new HashMap<>();
        dayMap.put(1, "一");
        dayMap.put(2, "二");
        dayMap.put(3, "三");
        dayMap.put(4, "四");
        dayMap.put(5, "五");
        dayMap.put(6, "六");
        dayMap.put(7, "日");
        return dayMap.getOrDefault(dayOfWeek, "未知");
    }

    private static MaterialInformationVO getMaterialInformationVO(List<NoGlassInventoryShiftVO> noGlassInventoryShiftVOList,
                                                                  MaterialSupplierPurchaseVO materialSupplierPurchaseVO,
                                                                  Map<String, NewProductStockPointVO> productStockPointVOMapOfCode,
                                                                  Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
                                                                  Map<String, SupplierVO> supplierVOMapOfId,
                                                                  Map<String, String> vehicleModelCodeOfProductCode) {
        return null;
    }

    private void assembleMrpDemandShiftDemandList(NoGlassInventoryShiftVO noGlassInventoryShiftVO, List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandVOS) {
        // 组装需求
        Map<String, List<MaterialPlanInventoryShiftDemandVO>> mrpDemandGroupOfType = inventoryShiftDemandVOS.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftDemandVO::getDemandType));
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMcbList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.MCB.getCode());
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandMpsList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.MPS.getCode());
        List<MaterialPlanInventoryShiftDemandVO> inventoryShiftDemandZzkList = mrpDemandGroupOfType.get(MrpDemandSourceEnum.ZZK.getCode());

        if (CollectionUtils.isNotEmpty(inventoryShiftDemandMcbList)) {
            noGlassInventoryShiftVO.setForecastDemandList(inventoryShiftDemandMcbList);
            noGlassInventoryShiftVO.setForecastDemandQuantity(inventoryShiftDemandMcbList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (CollectionUtils.isNotEmpty(inventoryShiftDemandMpsList)) {
            noGlassInventoryShiftVO.setProductionDemandList(inventoryShiftDemandMpsList);
            noGlassInventoryShiftVO.setProductionDemandQuantity(inventoryShiftDemandMpsList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        if (CollectionUtils.isNotEmpty(inventoryShiftDemandZzkList)) {
            noGlassInventoryShiftVO.setZzkDemandList(inventoryShiftDemandZzkList);
            noGlassInventoryShiftVO.setZzkDemandQuantity(inventoryShiftDemandZzkList.stream().map(MaterialPlanInventoryShiftDemandVO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
        }
    }

    private void assembleMrpSupplyShiftSupplyList(NoGlassInventoryShiftVO noGlassInventoryShiftVO, List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS) {
        Map<String, List<MaterialPlanInventoryShiftSupplyVO>> mrpSupplyGroupOfType = inventoryShiftSupplyVOS.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftSupplyVO::getSupplyType));
        List<MaterialPlanInventoryShiftSupplyVO> planPurchaseInventoryShiftSupplyVOS = mrpSupplyGroupOfType.get(MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode());
        List<MaterialPlanInventoryShiftSupplyVO> transitOrderInventoryShiftSupplyVOS = mrpSupplyGroupOfType.get(MrpSupplySourceEnum.TRANSIT_ORDER_INVENTORY.getCode());

        BigDecimal supplyQuantitySum = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(planPurchaseInventoryShiftSupplyVOS)) {
            noGlassInventoryShiftVO.setPlanPurchaseSupplyQuantity(planPurchaseInventoryShiftSupplyVOS.stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            noGlassInventoryShiftVO.setPlanPurchaseInventoryShiftSupplyList(planPurchaseInventoryShiftSupplyVOS);
            supplyQuantitySum = supplyQuantitySum.add(noGlassInventoryShiftVO.getPlanPurchaseSupplyQuantity());
        }

        if (CollectionUtils.isNotEmpty(transitOrderInventoryShiftSupplyVOS)) {
            noGlassInventoryShiftVO.setTransitOrderSupplyQuantity(transitOrderInventoryShiftSupplyVOS.stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add));
            noGlassInventoryShiftVO.setTransitOrderInventoryShiftSupplyList(transitOrderInventoryShiftSupplyVOS);
            supplyQuantitySum = supplyQuantitySum.add(noGlassInventoryShiftVO.getTransitOrderSupplyQuantity());
        }
        noGlassInventoryShiftVO.setSupplyQuantity(supplyQuantitySum);
    }

    @Override
    public BaseResponse<String> checkAdjust(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        // 获取材料基础数据的订单下达提前期
        NoGlassInventoryShiftPO noGlassInventoryShiftPO = noGlassInventoryShiftDao.selectByPrimaryKey(noGlassInventoryShiftDTO.getId());
        List<MaterialSupplierPurchaseVO> supplierPurchaseVOList = materialSupplierPurchaseService.selectByParams(ImmutableMap.of("materialCode", noGlassInventoryShiftPO.getProductCode()));
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap = supplierPurchaseVOList.stream().collect(Collectors.toMap(MaterialSupplierPurchaseVO::getMaterialCode, Function.identity(), (k1, k2) -> k2));
        MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMap.get(noGlassInventoryShiftPO.getProductCode());
        if (null != materialSupplierPurchaseVO) {
            // 当前日期加上下达提前期
            Date moveDay = DateUtils.moveDay(new Date(), materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue());
            if (noGlassInventoryShiftPO.getInventoryDate().before(moveDay)) {
                return BaseResponse.error("", "您调整的物料需求采购周期不足，请确认调整计划已和供应商完成确认？");
            }
        }
        return BaseResponse.success("", "校验成功");
    }

    @Override
    public BaseResponse<String> doAdjustData(NoGlassInventoryShiftDTO noGlassInventoryShiftDTO) {
        NoGlassInventoryShiftVO vo = this.selectByPrimaryKey(noGlassInventoryShiftDTO.getId());
        BigDecimal adjustPlanPurchase = noGlassInventoryShiftDTO.getUpdateAdjustQuantity() == null ? BigDecimal.ZERO : vo.getPlanPurchase().add(noGlassInventoryShiftDTO.getUpdateAdjustQuantity());
        if (adjustPlanPurchase.compareTo(BigDecimal.ZERO) < 0) {
            // 减量则不能超过计划供应
            // 获取供应数据
            List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS = materialPlanInventoryShiftSupplyService.selectByParams(ImmutableMap.of("materialPlanInventoryShiftId", noGlassInventoryShiftDTO.getId(), "supplyType", MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode()));
            if (CollectionUtils.isEmpty(inventoryShiftSupplyVOS)) {
                return BaseResponse.error("计划采购扣减不能超过计划供应");
            }

            BigDecimal totalSupplyQuantity = BigDecimal.ZERO;
            for (MaterialPlanInventoryShiftSupplyVO supplyVO : inventoryShiftSupplyVOS) {
                BigDecimal supplyQuantity = supplyVO.getSupplyQuantity();
                if (supplyQuantity != null) {
                    totalSupplyQuantity = totalSupplyQuantity.add(supplyQuantity);
                }
            }

            if (adjustPlanPurchase.abs().compareTo(totalSupplyQuantity) > 0) {
                return BaseResponse.error("计划采购扣减不能超过计划供应");
            }
        }

        //计划调整量 = （计划调整量 + 要修改的计划调整量差值）
        vo.setPlanPurchase(noGlassInventoryShiftDTO.getUpdateAdjustQuantity() == null ? BigDecimal.ZERO : vo.getPlanPurchase().add(noGlassInventoryShiftDTO.getUpdateAdjustQuantity()));

        List<NoGlassInventoryShiftPO> updateNoGlassInventoryShiftPOList = new ArrayList<>();
        // 获取锁定期
        BigDecimal packageLot = null;
        BigDecimal minOrderQty = null;
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseService.selectByParams(ImmutableMap.of("materialCode", vo.getProductCode()));
        if (CollectionUtils.isNotEmpty(materialSupplierPurchaseVOS)) {
            packageLot = materialSupplierPurchaseVOS.get(0).getPackageLot();
            minOrderQty = materialSupplierPurchaseVOS.get(0).getMinOrderQty();
//            if (StringUtils.equals(DemandPatternEnum.PO.getCode(), materialSupplierPurchaseVOS.get(0).getDemandPattern())) {
//                lockPeriod = materialSupplierPurchaseVOS.get(0).getOrderPlacementLeadTimeDay() == null ? 0 : materialSupplierPurchaseVOS.get(0).getOrderPlacementLeadTimeDay().intValue();
//            }
//            if (StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), materialSupplierPurchaseVOS.get(0).getDemandPattern())) {
//                lockPeriod = materialSupplierPurchaseVOS.get(0).getRequestCargoPlanLockDay() == null ? 0 : materialSupplierPurchaseVOS.get(0).getRequestCargoPlanLockDay().intValue();
//            }
        }

        // 查询整个推移数据
        Map<String, Object> params = new HashMap<>();
        params.put("stockPointCode", vo.getStockPointCode());
        params.put("productCode", vo.getProductCode());
        List<NoGlassInventoryShiftPO> noGlassInventoryShiftPOS = noGlassInventoryShiftDao.selectByParams(params);
        NoGlassInventoryShiftPO materialPlanInventoryShiftPO = noGlassInventoryShiftDao.selectByPrimaryKey(noGlassInventoryShiftDTO.getId());

        Date inventoryDate = materialPlanInventoryShiftPO.getInventoryDate();
        // 获取在这库存推移之后的日期数据
        List<NoGlassInventoryShiftPO> adjustNoGlassInventoryShiftPOList = noGlassInventoryShiftPOS.stream().filter(item -> item.getInventoryDate().compareTo(inventoryDate) >= 0).sorted(Comparator.comparing(NoGlassInventoryShiftPO::getInventoryDate)).collect(Collectors.toList());


        List<String> noGlassInventoryShiftIdList = adjustNoGlassInventoryShiftPOList.stream().map(NoGlassInventoryShiftPO::getId).collect(Collectors.toList());
        // 获取供应，要区分预计采购供应和在途订单供应
        List<MaterialPlanInventoryShiftSupplyVO> inventoryShiftSupplyVOS = materialPlanInventoryShiftSupplyService.selectByMaterialPlanInventoryShiftIds(noGlassInventoryShiftIdList);

        int notAdjustDays = noGlassInventoryShiftPOS.size() - adjustNoGlassInventoryShiftPOList.size();



        BigDecimal updateAdjustQuantity = noGlassInventoryShiftDTO.getUpdateAdjustQuantity() == null ? BigDecimal.ZERO : noGlassInventoryShiftDTO.getUpdateAdjustQuantity();
        noGlassInventoryShiftPOS.stream().filter(noGlassInventoryShiftPO -> vo.getId().equals(noGlassInventoryShiftPO.getId())).findFirst().ifPresent(noGlassInventoryShiftPO -> noGlassInventoryShiftPO.setPlanPurchase(noGlassInventoryShiftPO.getPlanPurchase().add(updateAdjustQuantity)));

        computePlanPurchase(noGlassInventoryShiftDTO.getId(), noGlassInventoryShiftPOS, packageLot, minOrderQty, updateNoGlassInventoryShiftPOList);

        if (CollectionUtils.isNotEmpty(updateNoGlassInventoryShiftPOList)){
            noGlassInventoryShiftDao.updateBatch(updateNoGlassInventoryShiftPOList);
        }


//        for (int i = 0; i < adjustNoGlassInventoryShiftPOList.size(); i++) {
//            // 期初库存
//            BigDecimal openingInventory;
//            NoGlassInventoryShiftPO noGlassInventoryShiftPO = adjustNoGlassInventoryShiftPOList.get(i);
//            if (i == 0) {
//                openingInventory = noGlassInventoryShiftPO.getOpeningInventory();
//            } else {
//                openingInventory = endingInventory;
//            }
//
//            // 获取毛需求数据
//            BigDecimal demandQuantity = noGlassInventoryShiftPO.getDemandQuantity();
//            // 获取供应
//            BigDecimal supplyQuantity = noGlassInventoryShiftPO.getSupplyQuantity();
//            // 获取计划调整量
//            BigDecimal adjustQuantity = noGlassInventoryShiftPO.getAdjustQuantity();
//            // 获取最小安全水位
//            BigDecimal safetyStockLevelMin = noGlassInventoryShiftPO.getSafetyStockLevelMin();
//            // 获取标准安全水位
//            BigDecimal safetyStockLevelStandard = noGlassInventoryShiftPO.getSafetyStockLevelStandard();
//            // 获取最大安全水位
//            BigDecimal safetyStockLevelMax = noGlassInventoryShiftPO.getSafetyStockLevelMax();
//
//            // 计算期末库存
//            endingInventory = supplyQuantity.add(openingInventory).add(adjustQuantity).subtract(demandQuantity);
//
//
//
//            BigDecimal planAdjustQuantity;
//            BigDecimal planPurchaseSupplyQuantity = BigDecimal.ZERO;
//
//            // 获取计划采购供应
//            List<MaterialPlanInventoryShiftSupplyVO> shiftSupplyList = noGlassSupplyListGroup.get(noGlassInventoryShiftPO.getId());
//            if (CollectionUtils.isNotEmpty(shiftSupplyList)){
//                Map<String, List<MaterialPlanInventoryShiftSupplyVO>> shiftSupplyGroup = shiftSupplyList.stream().collect(Collectors.groupingBy(MaterialPlanInventoryShiftSupplyVO::getSupplyType));
//                List<MaterialPlanInventoryShiftSupplyVO> planPurchaseSupplyList = shiftSupplyGroup.get(MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode());
//                if (CollectionUtils.isNotEmpty(planPurchaseSupplyList)){
//                    planPurchaseSupplyQuantity = planPurchaseSupplyList.stream().map(MaterialPlanInventoryShiftSupplyVO::getSupplyQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
//                }
//            }
//
//            // 计算推移量
//            planAdjustQuantity = calculateDisplacementQuantity(i, lockPeriod, endingInventory, openingInventory, demandQuantity, supplyQuantity,
//                    safetyStockLevelMin, safetyStockLevelStandard, safetyStockLevelMax);
//
//            // 计算期末库存
//            BigDecimal beforeEndingInventory = endingInventory;
//            // 手动修改的推移数据，不需要重新设置调整量
//            if (!StringUtils.equals(noGlassInventoryShiftPO.getId(), vo.getId())){
//                // 判断调整量是否为负数，负数则是减量。减量的值不能大于计划供应量
//                planAdjustQuantity = reductionPlanAdjustQuantity(planAdjustQuantity, planPurchaseSupplyQuantity);
//                noGlassInventoryShiftPO.setAdjustQuantity(planAdjustQuantity);
//                endingInventory = endingInventory.add(planAdjustQuantity);
//                noGlassInventoryShiftPO.setBeforeEndingInventory(beforeEndingInventory);
//            }
//
//            noGlassInventoryShiftPO.setEndingInventory(endingInventory);
//            noGlassInventoryShiftPO.setOpeningInventory(openingInventory);
//            updateNoGlassInventoryShiftPOList.add(noGlassInventoryShiftPO);
//        }
        return BaseResponse.success(null, "调整成功");
    }

    private void computePlanPurchase(String testId, List<NoGlassInventoryShiftPO> list, BigDecimal packageLot, BigDecimal minOrderQty, List<NoGlassInventoryShiftPO> updateNoGlassInventoryShiftPOList) {
        // 判断是否满足最小起订量、包装批量
        list.sort(Comparator.comparing(NoGlassInventoryShiftPO::getInventoryDate));
        for (int i = 0; i < list.size(); i++) {
            NoGlassInventoryShiftPO noGlassInventoryShiftPO = list.get(i);
            if (StringUtils.equals(testId, noGlassInventoryShiftPO.getId())) {
                log.info("推移日期:{}, 计划采购:{}, 最小采购量:{}, 包装批量:{}", DateUtils.dateToString(noGlassInventoryShiftPO.getInventoryDate()), noGlassInventoryShiftPO.getPlanPurchase(), minOrderQty, packageLot);
            }
            // 期初库存
            BigDecimal openingInventory = noGlassInventoryShiftPO.getOpeningInventory();
            // 供应
            BigDecimal supplyQuantity = noGlassInventoryShiftPO.getSupplyQuantity();
            // 需求
            BigDecimal demandQuantity = noGlassInventoryShiftPO.getDemandQuantity();

            // 计划调整量
            BigDecimal planPurchase = noGlassInventoryShiftPO.getPlanPurchase();
            if (null != minOrderQty && planPurchase.compareTo(minOrderQty) < 0 && planPurchase.compareTo(BigDecimal.ZERO) > 0) {
                // 要货数量小于最小起订量，则要货数量等于最小起订量
                planPurchase = minOrderQty;
            }
            // 正数、负数都需要考虑包装批量，0不需要
            if (null != packageLot && planPurchase.compareTo(BigDecimal.ZERO) > 0) {
                if (StringUtils.equals(testId, noGlassInventoryShiftPO.getId())) {
                    log.info("进入包装批量计算");
                }
                // 是否满足包装批量
                planPurchase = materialPlanNeedService.roundUpwards(planPurchase, packageLot);
            }
            if (StringUtils.equals(testId, noGlassInventoryShiftPO.getId())) {
                log.info("推移后，推移日期:{}, 计划采购:{}, 最小采购量:{}, 包装批量:{}", DateUtils.dateToString(noGlassInventoryShiftPO.getInventoryDate()), noGlassInventoryShiftPO.getPlanPurchase(), minOrderQty, packageLot);
            }
            noGlassInventoryShiftPO.setPlanPurchase(planPurchase);

            // 刷新期末库存
            BigDecimal beforeEndingInventory = openingInventory.subtract(demandQuantity).add(supplyQuantity);
            BigDecimal endingInventory = beforeEndingInventory.add(planPurchase);
            // 刷新调整前期末库存
            noGlassInventoryShiftPO.setBeforeEndingInventory(beforeEndingInventory);
            noGlassInventoryShiftPO.setEndingInventory(endingInventory);
            if (i + 1 <= list.size() - 1) {
                NoGlassInventoryShiftPO noGlassInventoryShiftPO2 = list.get(i + 1);
                // 下一个推移的期初，等于当前的期末库存
                noGlassInventoryShiftPO2.setOpeningInventory(endingInventory);
            }
            updateNoGlassInventoryShiftPOList.add(noGlassInventoryShiftPO);
        }
    }

    @Override
    public void doPublish() {

    }

    private void generatePlanNeedNo(List<MaterialPlanNeedDTO> parentMaterialPlanNeedDTOList, Map<String, Integer> supplierSerialNumberMap, Date nowDate) {
        Map<String, List<MaterialPlanNeedDTO>> materialPlanNeedDtoGroup = parentMaterialPlanNeedDTOList.stream().collect(Collectors.groupingBy(MaterialPlanNeedDTO::getSupplierCode));
        for (Map.Entry<String, List<MaterialPlanNeedDTO>> entry : materialPlanNeedDtoGroup.entrySet()) {
            String supplierCode = entry.getKey();
            List<MaterialPlanNeedDTO> planNeedDTOList = entry.getValue();
            int serialNumber = 1;
            // 获取供应商序列号
            if (supplierSerialNumberMap.containsKey(supplierCode)) {
                serialNumber = supplierSerialNumberMap.get(supplierCode);
            } else {
                // 从缓存里获取
                if (redisUtil.hasKey(String.join("#", supplierCode, MaterialRedisConstants.PLAN_NEED_SERIAL_NUMBER_KEY))) {
                    serialNumber = (int) redisUtil.get(String.join("#", supplierCode, MaterialRedisConstants.PLAN_NEED_SERIAL_NUMBER_KEY));
                }
            }
            String planNeedNo = "P" + supplierCode + DateUtils.dateToString(nowDate, "yyyyMMdd") + String.format("%03d", serialNumber);
            for (MaterialPlanNeedDTO materialPlanNeedDTO : planNeedDTOList) {
                materialPlanNeedDTO.setMaterialPlanNeedNo(planNeedNo);
            }
            serialNumber++;
            supplierSerialNumberMap.put(supplierCode, serialNumber);
            redisUtil.set(String.join("#", supplierCode, MaterialRedisConstants.PLAN_NEED_SERIAL_NUMBER_KEY), serialNumber);
        }
    }

    private MaterialPlanNeedDTO getMergeMaterialPlanNeedDTO(Date inventoryDate, BigDecimal needQuantitySum, BigDecimal purchaseRatio, SupplierVO supplierVO, int orderPlacementLeadTimeDay) {
        MaterialPlanNeedDTO mergeMaterialPlanNeedDTO = new MaterialPlanNeedDTO();
        mergeMaterialPlanNeedDTO.setNeedQuantity(needQuantitySum);
        mergeMaterialPlanNeedDTO.setNeedDate(inventoryDate);
        mergeMaterialPlanNeedDTO.setSupplierCode(supplierVO.getSupplierCode());
        mergeMaterialPlanNeedDTO.setSupplierName(supplierVO.getSupplierName());
        mergeMaterialPlanNeedDTO.setRequirementReleaseDate(DateUtils.moveDay(inventoryDate, -orderPlacementLeadTimeDay));
        mergeMaterialPlanNeedDTO.setId(UUIDUtil.getUUID());
        return mergeMaterialPlanNeedDTO;
    }

    private MaterialDemandAssignmentDTO assembleMaterialDemandAssignmentDTO(MaterialPlanNeedDTO materialPlanNeedDTO, PurchaseOrderInfoVO purchaseOrderInfoVO, BigDecimal assignmentQuantity) {
        MaterialDemandAssignmentDTO materialDemandAssignmentDTO = new MaterialDemandAssignmentDTO();
        materialDemandAssignmentDTO.setId(UUIDUtil.getUUID());
        materialDemandAssignmentDTO.setMaterialDemandId(materialPlanNeedDTO.getId());
        materialDemandAssignmentDTO.setPurchaseOrderInfoId(purchaseOrderInfoVO.getId());
        materialDemandAssignmentDTO.setAssignmentQuantity(assignmentQuantity);
        return materialDemandAssignmentDTO;
    }

    @Override
    public BigDecimal calculateDisplacementQuantity(int dateIndex, int lockPeriod, BigDecimal endingInventory, BigDecimal openingInventory,
                                                    BigDecimal demandQuantity, BigDecimal supplyQuantitySum, BigDecimal minStockQuantity,
                                                    BigDecimal standardStockQuantity, BigDecimal maxStockQuantity, BigDecimal beforeDaySupplyQuantitySum) {

        BigDecimal planAdjustQuantity = BigDecimal.ZERO;
        if (lockPeriod > dateIndex) {
            // 锁定期内的,只有缺料才会补料
            if (endingInventory.compareTo(BigDecimal.ZERO) < 0) {
                // 计算计划调整量, 增量到标准安全库存
                planAdjustQuantity = computePlanAdjustQuantity(standardStockQuantity, openingInventory, demandQuantity, supplyQuantitySum);
            }
        } else {
            // 锁定期外
            // 需要判断期末库存是否在当前规划期的安全库存上下限内
            if (!isWithinStockRange(minStockQuantity, maxStockQuantity, endingInventory)) {
                // 不在安全库存上下限内
                if (endingInventory.compareTo(maxStockQuantity) > 0) {
                    // 期末库存大于安全库存上限
                    // 添加计划调整量,减量到标准安全库存
                    planAdjustQuantity = standardStockQuantity.subtract(openingInventory).add(demandQuantity).subtract(supplyQuantitySum);
                } else {
                    // 添加计划调整量,增量到标准安全库存
                    planAdjustQuantity = computePlanAdjustQuantity(standardStockQuantity, openingInventory, demandQuantity, supplyQuantitySum);
                }
            }
        }
        return planAdjustQuantity;
    }

    @Override
    public String verificationWarning(BigDecimal endingInventory, BigDecimal minStockQuantity, BigDecimal maxStockQuantity) {
        String warningRemind = "";
        // 计算告警，用调整后的期末库存去计算
        if (endingInventory.compareTo(BigDecimal.ZERO) < 0) {
            // 缺料
            warningRemind = WarningRemindEnum.MATERIAL_SHORTAGE_WARNING.getCode();
        } else if (endingInventory.compareTo(minStockQuantity) < 0) {
            // 低于等于下限
            warningRemind = WarningRemindEnum.BELOW_SAFETY_STOCK_LOWER_LIMIT.getCode();
        } else if (endingInventory.compareTo(maxStockQuantity) > 0) {
            // 高于等于上限
            warningRemind = WarningRemindEnum.HIGHER_THAN_SAFETY_STOCK_UPPER_LIMIT.getCode();
        }
        return warningRemind;
    }

    public BigDecimal reductionPlanAdjustQuantity(BigDecimal planAdjustQuantity, BigDecimal planPurchaseSupplyQuantity) {
        // 判断调整量是否为负数，负数则是减量。减量的值不能大于计划供应量
        if (planAdjustQuantity.compareTo(BigDecimal.ZERO) < 0) {
            if (planPurchaseSupplyQuantity.compareTo(BigDecimal.ZERO) == 0) {
                planAdjustQuantity = BigDecimal.ZERO;
            } else if (planAdjustQuantity.compareTo(planPurchaseSupplyQuantity.negate()) < 0) {
                planAdjustQuantity = planPurchaseSupplyQuantity.negate();
            }
        }
        return planAdjustQuantity;
    }

    @Override
    public List<NoGlassInventoryShiftVO> selectNoGlassMaterialShit(String latestVersionId, List<String> productCodeList, String startTime, String endTime) {
        return noGlassInventoryShiftDao.selectNoGlassMaterialShit(latestVersionId, productCodeList, startTime, endTime);
    }

    /**
     * 计算计划调整量
     * @param minStockQuantity 安全库存下限
     * @param openingInventory 期初库存
     * @param demandQuantity 毛需求
     * @param supplyQuantity 计划供应
     * @return 计划调整量
     */
    private BigDecimal computePlanAdjustQuantity(BigDecimal minStockQuantity, BigDecimal openingInventory, BigDecimal demandQuantity, BigDecimal supplyQuantity) {
        return minStockQuantity.subtract(openingInventory).add(demandQuantity).subtract(supplyQuantity);
    }

    /**
     * 判断期末库存是否在安全库存上下限内
     * @param minStockQuantity 最小安全库存
     * @param maxStockQuantity 最高安全库存
     * @param endingInventory 期末库存
     * @return true在上下限内
     */
    private boolean isWithinStockRange(BigDecimal minStockQuantity, BigDecimal maxStockQuantity, BigDecimal endingInventory) {
        return minStockQuantity.compareTo(endingInventory) <= 0 && maxStockQuantity.compareTo(endingInventory) >= 0;
    }

    @Override
    public List<NoGlassInventoryShiftVO> selectVOByParams(Map<String, Object> params) {
        return noGlassInventoryShiftDao.selectVOByParams(params);
    }

    @Override
    public Date getLockPeriodStartTime(String materialPlanVersionId) {
        return noGlassInventoryShiftDao.getLockPeriodStartTime(materialPlanVersionId);
    }

    @Override
    public void deDeleteByProductCodeList(List<String> planUserProductCodeList) {
        noGlassInventoryShiftDao.deleteByProductCodeList(planUserProductCodeList);
    }

    @Override
    public Date getLastCreateTime(String userId) {
        return noGlassInventoryShiftDao.getLastCreateTime(userId);
    }
}
