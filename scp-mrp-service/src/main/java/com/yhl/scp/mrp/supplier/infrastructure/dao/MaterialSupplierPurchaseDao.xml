<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.supplier.infrastructure.dao.MaterialSupplierPurchaseDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO">
        <!--@Table mrp_material_supplier_purchase-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <result column="material_code" jdbcType="VARCHAR" property="materialCode"/>
        <result column="material_name" jdbcType="VARCHAR" property="materialName"/>
        <result column="material_type" jdbcType="VARCHAR" property="materialType"/>
        <result column="material_property" jdbcType="VARCHAR" property="materialProperty"/>
        <result column="planner" jdbcType="VARCHAR" property="planner"/>
        <result column="min_order_qty" jdbcType="VARCHAR" property="minOrderQty"/>
        <result column="package_lot" jdbcType="VARCHAR" property="packageLot"/>
        <result column="specific" jdbcType="VARCHAR" property="specific"/>
        <result column="request_cargo_plan_lock_day" jdbcType="VARCHAR" property="requestCargoPlanLockDay"/>
        <result column="order_placement_lead_time_day" jdbcType="VARCHAR" property="orderPlacementLeadTimeDay"/>
        <result column="purchase_lot" jdbcType="VARCHAR" property="purchaseLot"/>
        <result column="demand_pattern" jdbcType="VARCHAR" property="demandPattern"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="rec_id" jdbcType="VARCHAR" property="recId"/>
        <result column="part_number" jdbcType="VARCHAR" property="partNumber"/>
        <result column="receiving_week_day" jdbcType="VARCHAR" property="receivingWeekDay"/>
        <result column="receiving_month_day" jdbcType="VARCHAR" property="receivingMonthDay"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO">
        <result column="supplier_puchase_ratio_id" jdbcType="VARCHAR" property="supplierPurchaseRatioId"/>
        <result column="supplier_id" jdbcType="VARCHAR" property="supplierId"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="purchase_ratio" jdbcType="VARCHAR" property="purchaseRatio"/>
        <result column="consignment" jdbcType="VARCHAR" property="consignment"/>
        <result column="safety_inventory_id" jdbcType="VARCHAR" property="safetyInventoryId"/>
        <result column="min_inventory_days" jdbcType="VARCHAR" property="minInventoryDays"/>
        <result column="goal_safety_inventory_days" jdbcType="VARCHAR" property="goalSafetyInventoryDays"/>
        <result column="max_inventory_days" jdbcType="VARCHAR" property="maxInventoryDays"/>
        <result column="expire_date" jdbcType="VARCHAR" property="expireDate"/>
    </resultMap>
    <resultMap id="VOResultMapPage" extends="BaseResultMap" type="com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO">
        <!-- TODO -->
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="supplier_name" jdbcType="VARCHAR" property="supplierName"/>
        <result column="item_status" jdbcType="VARCHAR" property="itemStatus"/>
        <result column="purchase_ratio" jdbcType="VARCHAR" property="purchaseRatio"/>
        <result column="consignment" jdbcType="VARCHAR" property="consignment"/>
        <result column="safety_inventory_id" jdbcType="VARCHAR" property="safetyInventoryId"/>
        <result column="min_inventory_days" jdbcType="VARCHAR" property="minInventoryDays"/>
        <result column="goal_safety_inventory_days" jdbcType="VARCHAR" property="goalSafetyInventoryDays"/>
        <result column="max_inventory_days" jdbcType="VARCHAR" property="maxInventoryDays"/>
    </resultMap>
    <sql id="Base_Column_List">
id,stock_point_code,stock_point_name,material_code,material_name,material_type,material_property,planner,min_order_qty,package_lot,consignment,`specific`,remark,enabled,creator,create_time,modifier,modify_time,version_value
,request_cargo_plan_lock_day,order_placement_lead_time_day,purchase_lot,demand_pattern,rec_id,organization_id,part_number,receiving_week_day,receiving_month_day
    </sql>

    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,supplier_puchase_ratio_id,supplier_id,supplier_code,supplier_name,item_status,
        purchase_ratio,consignment,safety_inventory_id,min_inventory_days,goal_safety_inventory_days,max_inventory_days,expire_date
    </sql>
    <sql id="VO_Column_List_Page">
        <!-- TODO -->
        <include refid="Base_Column_List" />,supplier_code,supplier_name,item_status,purchase_ratio
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointName != null and params.stockPointName != ''">
                and stock_point_name = #{params.stockPointName,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCode != null and params.materialCode != ''">
                and material_code = #{params.materialCode,jdbcType=VARCHAR}
            </if>
            <if test="params.materialName != null and params.materialName != ''">
                and material_name = #{params.materialName,jdbcType=VARCHAR}
            </if>
            <if test="params.materialType != null and params.materialType != ''">
                and material_type = #{params.materialType,jdbcType=VARCHAR}
            </if>
            <if test="params.materialProperty != null and params.materialProperty != ''">
                and material_property = #{params.materialProperty,jdbcType=VARCHAR}
            </if>
            <if test="params.planner != null and params.planner != ''">
                and planner = #{params.planner,jdbcType=VARCHAR}
            </if>
            <if test="params.minOrderQty != null">
                and min_order_qty = #{params.minOrderQty,jdbcType=VARCHAR}
            </if>
            <if test="params.packageLot != null">
                and package_lot = #{params.packageLot,jdbcType=VARCHAR}
            </if>
            <if test="params.consignment != null and params.consignment != ''">
                and consignment = #{params.consignment,jdbcType=VARCHAR}
            </if>
            <if test="params.specific != null and params.specific != ''">
                and `specific` = #{params.specific,jdbcType=VARCHAR}
            </if>
            <if test="params.requestCargoPlanLockDay != null">
                and request_cargo_plan_lock_day = #{params.requestCargoPlanLockDay,jdbcType=VARCHAR}
            </if>
            <if test="params.orderPlacementLeadTimeDay != null">
                and order_placement_lead_time_day = #{params.orderPlacementLeadTimeDay,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseLot != null">
                and purchase_lot = #{params.purchaseLot,jdbcType=VARCHAR}
            </if>
            <if test="params.demandPattern != null">
                and demand_pattern = #{params.demandPattern,jdbcType=VARCHAR}
            </if>
            <if test="params.receivingWeekDay != null and params.receivingWeekDay != ''">
                and receiving_week_day = #{params.receivingWeekDay,jdbcType=VARCHAR}
            </if>
            <if test="params.receivingMonthDay != null and params.receivingMonthDay != ''">
                and receiving_month_day = #{params.receivingMonthDay,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.recId != null and params.recId != ''">
                and rec_id = #{params.recId,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.partNumber != null and params.partNumber != ''">
                and part_number = #{params.partNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.materialCodeList != null and params.materialCodeList.size() > 0">
                and material_code in
                <foreach collection="params.materialCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.stockPointCodeList != null and params.stockPointCodeList.size() > 0">
                and stock_point_code in
                <foreach collection="params.stockPointCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_supplier_purchase
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_supplier_purchase
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_material_supplier_purchase
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_supplier_purchase
        <include refid="Base_Where_Condition" />
    </select>

    <!-- 条件查询 VO-->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_mrp_material_supplier_purchase
        <include refid="Base_Where_Condition" />
    </select>

    <select id="selectMaterialSupplierPurchaseVOByStockPointCode" resultType="com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO">
        select
        <include refid="Base_Column_List" />
        from mrp_material_supplier_purchase
        where stock_point_code in
        <foreach collection="stockPointCodeList" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectByMaterialSupplierPurchaseIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_supplier_purchase
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                organization_id = #{item.orgId,jdbcType=VARCHAR} and material_code = #{item.itemCode,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectByConditionNew" resultMap="VOResultMapPage">
        <!-- TODO -->
        select
        <include refid="VO_Column_List_Page" />
        from v_mrp_material_supplier_purchase_page
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_supplier_purchase(
        id,
        rec_id,
        organization_id,
        stock_point_code,
        stock_point_name,
        material_code,
        material_name,
        material_type,
        material_property,
        planner,
        min_order_qty,
        package_lot,
        consignment,
        `specific`,
        request_cargo_plan_lock_day,
        order_placement_lead_time_day,
        purchase_lot,
        demand_pattern,
        part_number,
        receiving_week_day,
        receiving_month_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{recId,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{materialName,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{materialProperty,jdbcType=VARCHAR},
        #{planner,jdbcType=VARCHAR},
        #{minOrderQty,jdbcType=VARCHAR},
        #{packageLot,jdbcType=VARCHAR},
        #{consignment,jdbcType=VARCHAR},
        #{specific,jdbcType=VARCHAR},
        #{requestCargoPlanLockDay,jdbcType=VARCHAR},
        #{orderPlacementLeadTimeDay,jdbcType=VARCHAR},
        #{purchaseLot,jdbcType=VARCHAR},
        #{demandPattern,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{receivingWeekDay,jdbcType=VARCHAR},
        #{receivingMonthDay,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO">
        insert into mrp_material_supplier_purchase(
        id,
        rec_id,
        organization_id,
        stock_point_code,
        stock_point_name,
        material_code,
        material_name,
        material_type,
        material_property,
        planner,
        min_order_qty,
        package_lot,
        consignment,
        `specific`,
        request_cargo_plan_lock_day,
        order_placement_lead_time_day,
        purchase_lot,
        demand_pattern,
        part_number,
        receiving_week_day,
        receiving_month_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{recId,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{stockPointName,jdbcType=VARCHAR},
        #{materialCode,jdbcType=VARCHAR},
        #{materialName,jdbcType=VARCHAR},
        #{materialType,jdbcType=VARCHAR},
        #{materialProperty,jdbcType=VARCHAR},
        #{planner,jdbcType=VARCHAR},
        #{minOrderQty,jdbcType=VARCHAR},
        #{packageLot,jdbcType=VARCHAR},
        #{consignment,jdbcType=VARCHAR},
        #{specific,jdbcType=VARCHAR},
        #{requestCargoPlanLockDay,jdbcType=VARCHAR},
        #{orderPlacementLeadTimeDay,jdbcType=VARCHAR},
        #{purchaseLot,jdbcType=VARCHAR},
        #{demandPattern,jdbcType=VARCHAR},
        #{partNumber,jdbcType=VARCHAR},
        #{receivingWeekDay,jdbcType=VARCHAR},
        #{receivingMonthDay,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_supplier_purchase(
        id,
        rec_id,
        organization_id,
        stock_point_code,
        stock_point_name,
        material_code,
        material_name,
        material_type,
        material_property,
        planner,
        min_order_qty,
        package_lot,
        consignment,
        `specific`,
        request_cargo_plan_lock_day,
        order_placement_lead_time_day,
        purchase_lot,
        demand_pattern,
        part_number,
        receiving_week_day,
        receiving_month_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.recId,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.materialCode,jdbcType=VARCHAR},
        #{entity.materialName,jdbcType=VARCHAR},
        #{entity.materialType,jdbcType=VARCHAR},
        #{entity.materialProperty,jdbcType=VARCHAR},
        #{entity.planner,jdbcType=VARCHAR},
        #{entity.minOrderQty,jdbcType=VARCHAR},
        #{entity.packageLot,jdbcType=VARCHAR},
        #{entity.consignment,jdbcType=VARCHAR},
        #{entity.specific,jdbcType=VARCHAR},
        #{entity.requestCargoPlanLockDay,jdbcType=VARCHAR},
        #{entity.orderPlacementLeadTimeDay,jdbcType=VARCHAR},
        #{entity.purchaseLot,jdbcType=VARCHAR},
        #{entity.demandPattern,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.receivingWeekDay,jdbcType=VARCHAR},
        #{entity.receivingMonthDay,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_supplier_purchase(
        id,
        rec_id,
        organization_id,
        stock_point_code,
        stock_point_name,
        material_code,
        material_name,
        material_type,
        material_property,
        planner,
        min_order_qty,
        package_lot,
        consignment,
        `specific`,
        request_cargo_plan_lock_day,
        order_placement_lead_time_day,
        purchase_lot,
        demand_pattern,
        part_number,
        receiving_week_day,
        receiving_month_day,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.recId,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.stockPointCode,jdbcType=VARCHAR},
        #{entity.stockPointName,jdbcType=VARCHAR},
        #{entity.materialCode,jdbcType=VARCHAR},
        #{entity.materialName,jdbcType=VARCHAR},
        #{entity.materialType,jdbcType=VARCHAR},
        #{entity.materialProperty,jdbcType=VARCHAR},
        #{entity.planner,jdbcType=VARCHAR},
        #{entity.minOrderQty,jdbcType=VARCHAR},
        #{entity.packageLot,jdbcType=VARCHAR},
        #{entity.consignment,jdbcType=VARCHAR},
        #{entity.specific,jdbcType=VARCHAR},
        #{entity.requestCargoPlanLockDay,jdbcType=VARCHAR},
        #{entity.orderPlacementLeadTimeDay,jdbcType=VARCHAR},
        #{entity.purchaseLot,jdbcType=VARCHAR},
        #{entity.demandPattern,jdbcType=VARCHAR},
        #{entity.partNumber,jdbcType=VARCHAR},
        #{entity.receivingWeekDay,jdbcType=VARCHAR},
        #{entity.receivingMonthDay,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO">
        update mrp_material_supplier_purchase set
        rec_id = #{recId,jdbcType=VARCHAR},
        organization_id = #{organizationId,jdbcType=VARCHAR},
        stock_point_code = #{stockPointCode,jdbcType=VARCHAR},
        stock_point_name = #{stockPointName,jdbcType=VARCHAR},
        material_code = #{materialCode,jdbcType=VARCHAR},
        material_name = #{materialName,jdbcType=VARCHAR},
        material_type = #{materialType,jdbcType=VARCHAR},
        material_property = #{materialProperty,jdbcType=VARCHAR},
        planner = #{planner,jdbcType=VARCHAR},
        min_order_qty = #{minOrderQty,jdbcType=VARCHAR},
        package_lot = #{packageLot,jdbcType=VARCHAR},
        consignment = #{consignment,jdbcType=VARCHAR},
        `specific` = #{specific,jdbcType=VARCHAR},
        request_cargo_plan_lock_day = #{requestCargoPlanLockDay,jdbcType=VARCHAR},
        order_placement_lead_time_day = #{orderPlacementLeadTimeDay,jdbcType=VARCHAR},
        purchase_lot = #{purchaseLot,jdbcType=VARCHAR},
        demand_pattern = #{demandPattern,jdbcType=VARCHAR},
        part_number = #{partNumber,jdbcType=VARCHAR},
        receiving_week_day = #{receivingWeekDay,jdbcType=VARCHAR},
        receiving_month_day = #{receivingMonthDay,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.supplier.infrastructure.po.MaterialSupplierPurchasePO">
        update mrp_material_supplier_purchase
        <set>
            <if test="recId != null and recId != ''">
                rec_id = #{recId,jdbcType=VARCHAR},
            </if>
            <if test="organizationId != null and organizationId != ''">
                organization_id = #{organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialName != null and item.materialName != ''">
                material_name = #{item.materialName,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProperty != null and item.materialProperty != ''">
                material_property = #{item.materialProperty,jdbcType=VARCHAR},
            </if>
            <if test="item.planner != null and item.planner != ''">
                planner = #{item.planner,jdbcType=VARCHAR},
            </if>
            <if test="item.minOrderQty != null">
                min_order_qty = #{item.minOrderQty,jdbcType=VARCHAR},
            </if>
            <if test="item.packageLot != null">
                package_lot = #{item.packageLot,jdbcType=VARCHAR},
            </if>
            <if test="item.consignment != null and item.consignment != ''">
                consignment = #{item.consignment,jdbcType=VARCHAR},
            </if>
            <if test="item.specific != null and item.specific != ''">
                `specific` = #{item.specific,jdbcType=VARCHAR},
            </if>
            <if test="item.requestCargoPlanLockDay != null">
                request_cargo_plan_lock_day = #{item.requestCargoPlanLockDay,jdbcType=VARCHAR}
            </if>
            <if test="item.orderPlacementLeadTimeDay != null">
                order_placement_lead_time_day = #{item.orderPlacementLeadTimeDay,jdbcType=VARCHAR}
            </if>
            <if test="item.purchaseLot != null">
                purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR}
            </if>
            <if test="item.demandPattern != null">
                demand_pattern = #{item.demandPattern,jdbcType=VARCHAR}
            </if>
            <if test="item.partNumber != null">
                part_number = #{item.partNumber,jdbcType=VARCHAR}
            </if>
            <if test="item.receivingWeekDay != null and item.receivingWeekDay != ''">
                receiving_week_day = #{item.receivingWeekDay,jdbcType=VARCHAR},
            </if>
            <if test="item.receivingMonthDay != null and item.receivingMonthDay != ''">
                receiving_month_day = #{item.receivingMonthDay,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_supplier_purchase
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="rec_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.recId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="material_property = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.materialProperty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planner = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planner,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="min_order_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.minOrderQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="package_lot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.packageLot,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="consignment = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.consignment,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="`specific` = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.specific,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="request_cargo_plan_lock_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.requestCargoPlanLockDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_placement_lead_time_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderPlacementLeadTimeDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_lot = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseLot,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_pattern = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandPattern,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="part_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiving_week_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receivingWeekDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="receiving_month_day = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.receivingMonthDay,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_material_supplier_purchase 
        <set>
            <if test="item.recId != null and item.recId != ''">
                rec_id = #{item.recId,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointName != null and item.stockPointName != ''">
                stock_point_name = #{item.stockPointName,jdbcType=VARCHAR},
            </if>
            <if test="item.materialCode != null and item.materialCode != ''">
                material_code = #{item.materialCode,jdbcType=VARCHAR},
            </if>
            <if test="item.materialName != null and item.materialName != ''">
                material_name = #{item.materialName,jdbcType=VARCHAR},
            </if>
            <if test="item.materialType != null and item.materialType != ''">
                material_type = #{item.materialType,jdbcType=VARCHAR},
            </if>
            <if test="item.materialProperty != null and item.materialProperty != ''">
                material_property = #{item.materialProperty,jdbcType=VARCHAR},
            </if>
            <if test="item.planner != null and item.planner != ''">
                planner = #{item.planner,jdbcType=VARCHAR},
            </if>
            <if test="item.minOrderQty != null">
                min_order_qty = #{item.minOrderQty,jdbcType=VARCHAR},
            </if>
            <if test="item.packageLot != null">
                package_lot = #{item.packageLot,jdbcType=VARCHAR},
            </if>
            <if test="item.consignment != null and item.consignment != ''">
                consignment = #{item.consignment,jdbcType=VARCHAR},
            </if>
            <if test="item.specific != null and item.specific != ''">
                `specific` = #{item.specific,jdbcType=VARCHAR},
            </if>
            <if test="item.requestCargoPlanLockDay != null">
                request_cargo_plan_lock_day = #{item.requestCargoPlanLockDay,jdbcType=VARCHAR},
            </if>
            <if test="item.orderPlacementLeadTimeDay != null">
                order_placement_lead_time_day = #{item.orderPlacementLeadTimeDay,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseLot != null">
                purchase_lot = #{item.purchaseLot,jdbcType=VARCHAR},
            </if>
            <if test="item.demandPattern != null">
                demand_pattern = #{item.demandPattern,jdbcType=VARCHAR},
            </if>
            <if test="item.partNumber != null">
                part_number = #{item.partNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.receivingWeekDay != null and item.receivingWeekDay != ''">
                receiving_week_day = #{item.receivingWeekDay,jdbcType=VARCHAR},
            </if>
            <if test="item.receivingMonthDay != null and item.receivingMonthDay != ''">
                receiving_month_day = #{item.receivingMonthDay,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_material_supplier_purchase where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_supplier_purchase where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
