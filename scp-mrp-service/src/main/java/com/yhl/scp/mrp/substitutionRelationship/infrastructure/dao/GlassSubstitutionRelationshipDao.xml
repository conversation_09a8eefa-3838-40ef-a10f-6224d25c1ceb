<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.substitutionRelationship.infrastructure.dao.GlassSubstitutionRelationshipDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO">
        <!--@Table mrp_glass_substitution_relationship-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="raw_product_code" jdbcType="VARCHAR" property="rawProductCode"/>
        <result column="raw_product_name" jdbcType="VARCHAR" property="rawProductName"/>
        <result column="production_substitute_product_code" jdbcType="VARCHAR" property="productionSubstituteProductCode"/>
        <result column="production_substitute_product_name" jdbcType="VARCHAR" property="productionSubstituteProductName"/>
        <result column="substitute_product_code" jdbcType="VARCHAR" property="substituteProductCode"/>
        <result column="substitute_product_name" jdbcType="VARCHAR" property="substituteProductName"/>
        <result column="substitute_input_factor" jdbcType="VARCHAR" property="substituteInputFactor"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="color_thickness" jdbcType="VARCHAR" property="colorThickness"/>
        <result column="blank_spec" jdbcType="VARCHAR" property="blankSpec"/>
        <result column="rule" jdbcType="VARCHAR" property="rule"/>
        <result column="product_size" jdbcType="VARCHAR" property="productSize"/>
        <result column="linzi_direction" jdbcType="VARCHAR" property="linziDirection"/>
        <result column="blank_input_factor" jdbcType="VARCHAR" property="blankInputFactor"/>
        <result column="glass_input_factor" jdbcType="VARCHAR" property="glassInputFactor"/>
        <result column="cutting_rate" jdbcType="VARCHAR" property="cuttingRate"/>
        <result column="recommend_substitute" jdbcType="VARCHAR" property="recommendSubstitute"/>
        <result column="production_input_factor" jdbcType="VARCHAR" property="productionInputFactor"/>
        <result column="production_cutting_rate" jdbcType="VARCHAR" property="productionCuttingRate"/>
        <result column="raw_product_inventory_disabled" jdbcType="VARCHAR" property="rawProductInventoryDisabled"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.substitutionRelationship.vo.GlassSubstitutionRelationshipVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,stock_point_code,raw_product_code,raw_product_name,
        production_substitute_product_code,production_substitute_product_name,
        substitute_product_code,substitute_product_name,substitute_input_factor,product_code,product_name,color_thickness,
        blank_spec,rule,product_size,linzi_direction,blank_input_factor,glass_input_factor,cutting_rate,recommend_substitute,
        production_input_factor,production_cutting_rate,raw_product_inventory_disabled,
        remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductCode != null and params.rawProductCode != ''">
                and raw_product_code = #{params.rawProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductName != null and params.rawProductName != ''">
                and raw_product_name = #{params.rawProductName,jdbcType=VARCHAR}
            </if>
            <if test="params.productionSubstituteProductCode != null and params.productionSubstituteProductCode != ''">
                and production_substitute_product_code = #{params.productionSubstituteProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productionSubstituteProductName != null and params.productionSubstituteProductName != ''">
                and production_substitute_product_name = #{params.productionSubstituteProductName,jdbcType=VARCHAR}
            </if>
            <if test="params.substituteProductCode != null and params.substituteProductCode != ''">
                and substitute_product_code = #{params.substituteProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.substituteProductName != null and params.substituteProductName != ''">
                and substitute_product_name = #{params.substituteProductName,jdbcType=VARCHAR}
            </if>
            <if test="params.substituteInputFactor != null">
                and substitute_input_factor = #{params.substituteInputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productName != null and params.productName != ''">
                and product_name = #{params.productName,jdbcType=VARCHAR}
            </if>
            <if test="params.colorThickness != null and params.colorThickness != ''">
                and color_thickness = #{params.colorThickness,jdbcType=VARCHAR}
            </if>
            <if test="params.blankSpec != null and params.blankSpec != ''">
                and blank_spec = #{params.blankSpec,jdbcType=VARCHAR}
            </if>
            <if test="params.rule != null and params.rule != ''">
                and rule = #{params.rule,jdbcType=VARCHAR}
            </if>
            <if test="params.productSize != null and params.productSize != ''">
                and product_size = #{params.productSize,jdbcType=VARCHAR}
            </if>
            <if test="params.linziDirection != null and params.linziDirection != ''">
                and linzi_direction = #{params.linziDirection,jdbcType=VARCHAR}
            </if>
            <if test="params.blankInputFactor != null">
                and blank_input_factor = #{params.blankInputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.glassInputFactor != null">
                and glass_input_factor = #{params.glassInputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.cuttingRate != null">
                and cutting_rate = #{params.cuttingRate,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.recommendSubstitute != null and params.recommendSubstitute != ''">
                and recommend_substitute = #{params.recommendSubstitute,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductCodeList != null and params.rawProductCodeList.size() > 0">
                and raw_product_code in
                <foreach collection="params.rawProductCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productCodeList != null and params.productCodeList.size() > 0">
                and product_code in
                <foreach collection="params.productCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.productionInputFactor != null">
                and production_input_factor = #{params.productionInputFactor,jdbcType=VARCHAR}
            </if>
            <if test="params.productionCuttingRate != null">
                and production_cutting_rate = #{params.productionCuttingRate,jdbcType=VARCHAR}
            </if>
            <if test="params.rawProductInventoryDisabled != null and params.rawProductInventoryDisabled != ''">
                and raw_product_inventory_disabled = #{params.rawProductInventoryDisabled,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_substitution_relationship
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_substitution_relationship
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_glass_substitution_relationship
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_glass_substitution_relationship
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_glass_substitution_relationship(
        id,
        stock_point_code,
        raw_product_code,
        raw_product_name,
        production_substitute_product_code,
        production_substitute_product_name,
        substitute_product_code,
        substitute_product_name,
        substitute_input_factor,
        product_code,
        product_name,
        color_thickness,
        blank_spec,
        rule,
        product_size,
        linzi_direction,
        blank_input_factor,
        glass_input_factor,
        cutting_rate,
        recommend_substitute,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        production_input_factor,
        production_cutting_rate,
        raw_product_inventory_disabled)
        values (
        #{id,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{rawProductCode,jdbcType=VARCHAR},
        #{rawProductName,jdbcType=VARCHAR},
        #{productionSubstituteProductCode,jdbcType=VARCHAR},
        #{productionSubstituteProductName,jdbcType=VARCHAR},
        #{substituteProductCode,jdbcType=VARCHAR},
        #{substituteProductName,jdbcType=VARCHAR},
        #{substituteInputFactor,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productName,jdbcType=VARCHAR},
        #{colorThickness,jdbcType=VARCHAR},
        #{blankSpec,jdbcType=VARCHAR},
        #{rule,jdbcType=VARCHAR},
        #{productSize,jdbcType=VARCHAR},
        #{linziDirection,jdbcType=VARCHAR},
        #{blankInputFactor,jdbcType=VARCHAR},
        #{glassInputFactor,jdbcType=VARCHAR},
        #{cuttingRate,jdbcType=VARCHAR},
        #{recommendSubstitute,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{productionInputFactor,jdbcType=VARCHAR},
        #{productionCuttingRate,jdbcType=VARCHAR},
        #{rawProductInventoryDisabled,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO">
        insert into mrp_glass_substitution_relationship(id,
                                                        stock_point_code,
                                                        raw_product_code,
                                                        raw_product_name,
                                                        production_substitute_product_code,
                                                        production_substitute_product_name,
                                                        substitute_product_code,
                                                        substitute_product_name,
                                                        substitute_input_factor,
                                                        product_code,
                                                        product_name,
                                                        color_thickness,
                                                        blank_spec,
                                                        rule,
                                                        product_size,
                                                        linzi_direction,
                                                        blank_input_factor,
                                                        glass_input_factor,
                                                        cutting_rate,
                                                        recommend_substitute,
                                                        remark,
                                                        enabled,
                                                        creator,
                                                        create_time,
                                                        modifier,
                                                        modify_time,
                                                        version_value,
                                                        production_input_factor,
                                                        production_cutting_rate,
                                                        raw_product_inventory_disabled)
        values (#{id,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{rawProductCode,jdbcType=VARCHAR},
                #{rawProductName,jdbcType=VARCHAR},
                #{productionSubstituteProductCode,jdbcType=VARCHAR},
                #{productionSubstituteProductName,jdbcType=VARCHAR},
                #{substituteProductCode,jdbcType=VARCHAR},
                #{substituteProductName,jdbcType=VARCHAR},
                #{substituteInputFactor,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productName,jdbcType=VARCHAR},
                #{colorThickness,jdbcType=VARCHAR},
                #{blankSpec,jdbcType=VARCHAR},
                #{rule,jdbcType=VARCHAR},
                #{productSize,jdbcType=VARCHAR},
                #{linziDirection,jdbcType=VARCHAR},
                #{blankInputFactor,jdbcType=VARCHAR},
                #{glassInputFactor,jdbcType=VARCHAR},
                #{cuttingRate,jdbcType=VARCHAR},
                #{recommendSubstitute,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{productionInputFactor,jdbcType=VARCHAR},
                #{productionCuttingRate,jdbcType=VARCHAR},
                #{rawProductInventoryDisabled,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_glass_substitution_relationship(
        id,
        stock_point_code,
        raw_product_code,
        raw_product_name,
        production_substitute_product_code,
        production_substitute_product_name,
        substitute_product_code,
        substitute_product_name,
        substitute_input_factor,
        product_code,
        product_name,
        color_thickness,
        blank_spec,
        rule,
        product_size,
        linzi_direction,
        blank_input_factor,
        glass_input_factor,
        cutting_rate,
        recommend_substitute,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        production_input_factor,
        production_cutting_rate,
        raw_product_inventory_disabled)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.rawProductCode,jdbcType=VARCHAR},
            #{entity.rawProductName,jdbcType=VARCHAR},
            #{entity.productionSubstituteProductCode,jdbcType=VARCHAR},
            #{entity.productionSubstituteProductName,jdbcType=VARCHAR},
            #{entity.substituteProductCode,jdbcType=VARCHAR},
            #{entity.substituteProductName,jdbcType=VARCHAR},
            #{entity.substituteInputFactor,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.colorThickness,jdbcType=VARCHAR},
            #{entity.blankSpec,jdbcType=VARCHAR},
            #{entity.rule,jdbcType=VARCHAR},
            #{entity.productSize,jdbcType=VARCHAR},
            #{entity.linziDirection,jdbcType=VARCHAR},
            #{entity.blankInputFactor,jdbcType=VARCHAR},
            #{entity.glassInputFactor,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.recommendSubstitute,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.productionInputFactor,jdbcType=VARCHAR},
            #{entity.productionCuttingRate,jdbcType=VARCHAR},
            #{entity.rawProductInventoryDisabled,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_glass_substitution_relationship(
        id,
        stock_point_code,
        raw_product_code,
        raw_product_name,
        production_substitute_product_code,
        production_substitute_product_name,
        substitute_product_code,
        substitute_product_name,
        substitute_input_factor,
        product_code,
        product_name,
        color_thickness,
        blank_spec,
        rule,
        product_size,
        linzi_direction,
        blank_input_factor,
        glass_input_factor,
        cutting_rate,
        recommend_substitute,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        production_input_factor,
        production_cutting_rate,
        raw_product_inventory_disabled)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.rawProductCode,jdbcType=VARCHAR},
            #{entity.rawProductName,jdbcType=VARCHAR},
            #{entity.productionSubstituteProductCode,jdbcType=VARCHAR},
            #{entity.productionSubstituteProductName,jdbcType=VARCHAR},
            #{entity.substituteProductCode,jdbcType=VARCHAR},
            #{entity.substituteProductName,jdbcType=VARCHAR},
            #{entity.substituteInputFactor,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productName,jdbcType=VARCHAR},
            #{entity.colorThickness,jdbcType=VARCHAR},
            #{entity.blankSpec,jdbcType=VARCHAR},
            #{entity.rule,jdbcType=VARCHAR},
            #{entity.productSize,jdbcType=VARCHAR},
            #{entity.linziDirection,jdbcType=VARCHAR},
            #{entity.blankInputFactor,jdbcType=VARCHAR},
            #{entity.glassInputFactor,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.recommendSubstitute,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.productionInputFactor,jdbcType=VARCHAR},
            #{entity.productionCuttingRate,jdbcType=VARCHAR},
            #{entity.rawProductInventoryDisabled,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO">
        update mrp_glass_substitution_relationship
        set stock_point_code        = #{stockPointCode,jdbcType=VARCHAR},
            raw_product_code        = #{rawProductCode,jdbcType=VARCHAR},
            raw_product_name        = #{rawProductName,jdbcType=VARCHAR},
            production_substitute_product_code        = #{productionSubstituteProductCode,jdbcType=VARCHAR},
            production_substitute_product_name        = #{productionSubstituteProductName,jdbcType=VARCHAR},
            substitute_product_code = #{substituteProductCode,jdbcType=VARCHAR},
            substitute_product_name = #{substituteProductName,jdbcType=VARCHAR},
            substitute_input_factor = #{substituteInputFactor,jdbcType=VARCHAR},
            product_code            = #{productCode,jdbcType=VARCHAR},
            product_name            = #{productName,jdbcType=VARCHAR},
            color_thickness         = #{colorThickness,jdbcType=VARCHAR},
            blank_spec              = #{blankSpec,jdbcType=VARCHAR},
            rule                    = #{rule,jdbcType=VARCHAR},
            product_size            = #{productSize,jdbcType=VARCHAR},
            linzi_direction         = #{linziDirection,jdbcType=VARCHAR},
            blank_input_factor      = #{blankInputFactor,jdbcType=VARCHAR},
            glass_input_factor      = #{glassInputFactor,jdbcType=VARCHAR},
            cutting_rate            = #{cuttingRate,jdbcType=VARCHAR},
            recommend_substitute    = #{recommendSubstitute,jdbcType=VARCHAR},
            remark                  = #{remark,jdbcType=VARCHAR},
            enabled                 = #{enabled,jdbcType=VARCHAR},
            modifier                = #{modifier,jdbcType=VARCHAR},
            modify_time             = #{modifyTime,jdbcType=TIMESTAMP},
            version_value           = #{versionValue,jdbcType=INTEGER},
            production_input_factor      = #{productionInputFactor,jdbcType=VARCHAR},
            production_cutting_rate      = #{productionCuttingRate,jdbcType=VARCHAR},
            raw_product_inventory_disabled      = #{rawProductInventoryDisabled,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO">
        update mrp_glass_substitution_relationship
        <set>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductCode != null and item.rawProductCode != ''">
                raw_product_code = #{item.rawProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductName != null and item.rawProductName != ''">
                raw_product_name = #{item.rawProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.productionSubstituteProductCode != null and item.productionSubstituteProductCode != ''">
                production_substitute_product_code = #{item.productionSubstituteProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productionSubstituteProductName != null and item.productionSubstituteProductName != ''">
                production_substitute_product_name = #{item.productionSubstituteProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductCode != null and item.substituteProductCode != ''">
                substitute_product_code = #{item.substituteProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteProductName != null and item.substituteProductName != ''">
                substitute_product_name = #{item.substituteProductName,jdbcType=VARCHAR},
            </if>
            <if test="item.substituteInputFactor != null and item.substituteInputFactor != ''">
                substitute_input_factor = #{item.substituteInputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productName != null and item.productName != ''">
                product_name = #{item.productName,jdbcType=VARCHAR},
            </if>
            <if test="item.colorThickness != null and item.colorThickness != ''">
                color_thickness = #{item.colorThickness,jdbcType=VARCHAR},
            </if>
            <if test="item.blankSpec != null and item.blankSpec != ''">
                blank_spec = #{item.blankSpec,jdbcType=VARCHAR},
            </if>
            <if test="item.rule != null and item.rule != ''">
                rule = #{item.rule,jdbcType=VARCHAR},
            </if>
            <if test="item.productSize != null and item.productSize != ''">
                product_size = #{item.productSize,jdbcType=VARCHAR},
            </if>
            <if test="item.linziDirection != null and item.linziDirection != ''">
                linzi_direction = #{item.linziDirection,jdbcType=VARCHAR},
            </if>
            <if test="item.blankInputFactor != null">
                blank_input_factor = #{item.blankInputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.glassInputFactor != null">
                glass_input_factor = #{item.glassInputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.cuttingRate != null">
                cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
            </if>
            <if test="item.recommendSubstitute != null and item.recommendSubstitute != ''">
                recommend_substitute = #{item.recommendSubstitute,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.productionInputFactor != null and item.productionInputFactor != ''">
                production_input_factor = #{item.productionInputFactor,jdbcType=VARCHAR},
            </if>
            <if test="item.productionCuttingRate != null and item.productionCuttingRate != ''">
                production_cutting_rate = #{item.productionCuttingRate,jdbcType=VARCHAR},
            </if>
            <if test="item.rawProductInventoryDisabled != null and item.rawProductInventoryDisabled != ''">
                raw_product_inventory_disabled = #{item.rawProductInventoryDisabled,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_glass_substitution_relationship
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_substitute_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionSubstituteProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_substitute_product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionSubstituteProductName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitute_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substituteProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitute_product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substituteProductName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="substitute_input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.substituteInputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="color_thickness = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.colorThickness,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="blank_spec = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.blankSpec,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rule,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_size = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSize,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="linzi_direction = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.linziDirection,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="blank_input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.blankInputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="glass_input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.glassInputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cutting_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cuttingRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="recommend_substitute = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.recommendSubstitute,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="production_input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionInputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_inventory_disabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductInventoryDisabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_input_factor = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionInputFactor,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="production_cutting_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productionCuttingRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_product_inventory_disabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawProductInventoryDisabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_glass_substitution_relationship
            <set>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.rawProductCode != null and item.rawProductCode != ''">
                    raw_product_code = #{item.rawProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.rawProductName != null and item.rawProductName != ''">
                    raw_product_name = #{item.rawProductName,jdbcType=VARCHAR},
                </if>
                <if test="item.productionSubstituteProductCode != null and item.productionSubstituteProductCode != ''">
                    production_substitute_product_code = #{item.productionSubstituteProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productionSubstituteProductName != null and item.productionSubstituteProductName != ''">
                    production_substitute_product_name = #{item.productionSubstituteProductName,jdbcType=VARCHAR},
                </if>
                <if test="item.substituteProductCode != null and item.substituteProductCode != ''">
                    substitute_product_code = #{item.substituteProductCode,jdbcType=VARCHAR},
                </if>
                <if test="item.substituteProductName != null and item.substituteProductName != ''">
                    substitute_product_name = #{item.substituteProductName,jdbcType=VARCHAR},
                </if>
                <if test="item.substituteInputFactor != null and item.substituteInputFactor != ''">
                    substitute_input_factor = #{item.substituteInputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productName != null and item.productName != ''">
                    product_name = #{item.productName,jdbcType=VARCHAR},
                </if>
                <if test="item.colorThickness != null and item.colorThickness != ''">
                    color_thickness = #{item.colorThickness,jdbcType=VARCHAR},
                </if>
                <if test="item.blankSpec != null and item.blankSpec != ''">
                    blank_spec = #{item.blankSpec,jdbcType=VARCHAR},
                </if>
                <if test="item.rule != null and item.rule != ''">
                    rule = #{item.rule,jdbcType=VARCHAR},
                </if>
                <if test="item.productSize != null and item.productSize != ''">
                    product_size = #{item.productSize,jdbcType=VARCHAR},
                </if>
                <if test="item.linziDirection != null and item.linziDirection != ''">
                    linzi_direction = #{item.linziDirection,jdbcType=VARCHAR},
                </if>
                <if test="item.blankInputFactor != null">
                    blank_input_factor = #{item.blankInputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.glassInputFactor != null">
                    glass_input_factor = #{item.glassInputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.cuttingRate != null">
                    cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
                </if>
                <if test="item.recommendSubstitute != null and item.recommendSubstitute != ''">
                    recommend_substitute = #{item.recommendSubstitute,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
                <if test="item.productionInputFactor != null and item.productionInputFactor != ''">
                    production_input_factor = #{item.productionInputFactor,jdbcType=VARCHAR},
                </if>
                <if test="item.productionCuttingRate != null and item.productionCuttingRate != ''">
                    production_cutting_rate = #{item.productionCuttingRate,jdbcType=VARCHAR},
                </if>
                <if test="item.rawProductInventoryDisabled != null and item.rawProductInventoryDisabled != ''">
                    raw_product_inventory_disabled = #{item.rawProductInventoryDisabled,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_glass_substitution_relationship
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_glass_substitution_relationship where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteByParams">
        delete from mrp_glass_substitution_relationship
        where recommend_substitute = #{params.recommendSubstitute,jdbcType=VARCHAR}
        and raw_product_code in
        <foreach collection="params.rawProductCodeList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
