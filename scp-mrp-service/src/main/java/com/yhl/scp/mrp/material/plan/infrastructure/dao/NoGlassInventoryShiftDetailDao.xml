<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.plan.infrastructure.dao.NoGlassInventoryShiftDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDetailPO">
        <!--@Table mrp_no_glass_inventory_shift_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="no_glass_inventory_shift_data_id" jdbcType="VARCHAR" property="noGlassInventoryShiftDataId"/>
        <result column="safety_stock_level_min" jdbcType="VARCHAR" property="safetyStockLevelMin"/>
        <result column="safety_stock_level_standard" jdbcType="VARCHAR" property="safetyStockLevelStandard"/>
        <result column="safety_stock_level_max" jdbcType="VARCHAR" property="safetyStockLevelMax"/>
        <result column="opening_inventory" jdbcType="VARCHAR" property="openingInventory"/>
        <result column="demand_quantity" jdbcType="VARCHAR" property="demandQuantity"/>
        <result column="supply_quantity" jdbcType="VARCHAR" property="supplyQuantity"/>
        <result column="adjust_quantity" jdbcType="VARCHAR" property="adjustQuantity"/>
        <result column="used_as_replace_quantity" jdbcType="VARCHAR" property="usedAsReplaceQuantity"/>
        <result column="before_ending_inventory" jdbcType="VARCHAR" property="beforeEndingInventory"/>
        <result column="ending_inventory" jdbcType="VARCHAR" property="endingInventory"/>
        <result column="inventory_gap" jdbcType="VARCHAR" property="inventoryGap"/>
        <result column="inventory_date" jdbcType="TIMESTAMP" property="inventoryDate"/>
        <result column="plan_purchase" jdbcType="VARCHAR" property="planPurchase"/>
        <result column="real_inventory_gap" jdbcType="VARCHAR" property="realInventoryGap"/>
        <result column="whether_lock_period" jdbcType="VARCHAR" property="whetherLockPeriod"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO">
        <!-- TODO -->
        <result column="before_warning_remind" jdbcType="VARCHAR" property="beforeWarningRemind"/>
        <result column="warning_remind" jdbcType="VARCHAR" property="warningRemind"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,no_glass_inventory_shift_data_id,safety_stock_level_min,safety_stock_level_standard,safety_stock_level_max,opening_inventory,demand_quantity,supply_quantity,adjust_quantity,used_as_replace_quantity,before_ending_inventory,ending_inventory,inventory_gap,inventory_date,plan_purchase,real_inventory_gap,whether_lock_period,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,before_warning_remind,warning_remind
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.noGlassInventoryShiftDataId != null and params.noGlassInventoryShiftDataId != ''">
                and no_glass_inventory_shift_data_id = #{params.noGlassInventoryShiftDataId,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMin != null">
                and safety_stock_level_min = #{params.safetyStockLevelMin,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelStandard != null">
                and safety_stock_level_standard = #{params.safetyStockLevelStandard,jdbcType=VARCHAR}
            </if>
            <if test="params.safetyStockLevelMax != null">
                and safety_stock_level_max = #{params.safetyStockLevelMax,jdbcType=VARCHAR}
            </if>
            <if test="params.openingInventory != null">
                and opening_inventory = #{params.openingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.demandQuantity != null">
                and demand_quantity = #{params.demandQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.supplyQuantity != null">
                and supply_quantity = #{params.supplyQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.adjustQuantity != null">
                and adjust_quantity = #{params.adjustQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.usedAsReplaceQuantity != null">
                and used_as_replace_quantity = #{params.usedAsReplaceQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeEndingInventory != null">
                and before_ending_inventory = #{params.beforeEndingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.endingInventory != null">
                and ending_inventory = #{params.endingInventory,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryGap != null">
                and inventory_gap = #{params.inventoryGap,jdbcType=VARCHAR}
            </if>
            <if test="params.inventoryDate != null">
                and inventory_date = #{params.inventoryDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planPurchase != null">
                and plan_purchase = #{params.planPurchase,jdbcType=VARCHAR}
            </if>
            <if test="params.realInventoryGap != null">
                and real_inventory_gap = #{params.realInventoryGap,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.noGlassInventoryShiftDataIdList != null and params.noGlassInventoryShiftDataIdList.size() > 0">
                and no_glass_inventory_shift_data_id in
                <foreach collection="params.noGlassInventoryShiftDataIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.inventoryDateStart != null and params.inventoryDateEnd != null">
                and inventory_date BETWEEN #{params.inventoryDateStart,jdbcType=TIMESTAMP} AND #{params.inventoryDateEnd,jdbcType=TIMESTAMP}
            </if>
            <if test="params.whetherLockPeriod != null">
                and whether_lock_period = #{params.whetherLockPeriod,jdbcType=VARCHAR}
            </if>
            <if test="params.beforeWarningRemind != null and params.beforeWarningRemind != '' and params.warningRemind != null and params.warningRemind != ''">
                and (
                (before_warning_remind = #{params.beforeWarningRemind,jdbcType=VARCHAR})
                or
                (warning_remind = #{params.warningRemind,jdbcType=VARCHAR})
                )
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mrp_no_glass_inventory_shift_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_no_glass_inventory_shift_detail
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mrp_no_glass_inventory_shift_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_no_glass_inventory_shift_detail(
        id,
        no_glass_inventory_shift_data_id,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        plan_purchase,
        real_inventory_gap,
        whether_lock_period,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{noGlassInventoryShiftDataId,jdbcType=VARCHAR},
        #{safetyStockLevelMin,jdbcType=VARCHAR},
        #{safetyStockLevelStandard,jdbcType=VARCHAR},
        #{safetyStockLevelMax,jdbcType=VARCHAR},
        #{openingInventory,jdbcType=VARCHAR},
        #{demandQuantity,jdbcType=VARCHAR},
        #{supplyQuantity,jdbcType=VARCHAR},
        #{adjustQuantity,jdbcType=VARCHAR},
        #{usedAsReplaceQuantity,jdbcType=VARCHAR},
        #{beforeEndingInventory,jdbcType=VARCHAR},
        #{endingInventory,jdbcType=VARCHAR},
        #{inventoryGap,jdbcType=VARCHAR},
        #{inventoryDate,jdbcType=TIMESTAMP},
        #{planPurchase,jdbcType=VARCHAR},
        #{realInventoryGap,jdbcType=VARCHAR},
        #{whetherLockPeriod,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDetailPO">
        insert into mrp_no_glass_inventory_shift_detail(id,
                                                        no_glass_inventory_shift_data_id,
                                                        safety_stock_level_min,
                                                        safety_stock_level_standard,
                                                        safety_stock_level_max,
                                                        opening_inventory,
                                                        demand_quantity,
                                                        supply_quantity,
                                                        adjust_quantity,
                                                        used_as_replace_quantity,
                                                        before_ending_inventory,
                                                        ending_inventory,
                                                        inventory_gap,
                                                        inventory_date,
                                                        plan_purchase,
                                                        real_inventory_gap,
                                                        whether_lock_period,
                                                        remark,
                                                        enabled,
                                                        creator,
                                                        create_time,
                                                        modifier,
                                                        modify_time)
        values (#{id,jdbcType=VARCHAR},
                #{noGlassInventoryShiftDataId,jdbcType=VARCHAR},
                #{safetyStockLevelMin,jdbcType=VARCHAR},
                #{safetyStockLevelStandard,jdbcType=VARCHAR},
                #{safetyStockLevelMax,jdbcType=VARCHAR},
                #{openingInventory,jdbcType=VARCHAR},
                #{demandQuantity,jdbcType=VARCHAR},
                #{supplyQuantity,jdbcType=VARCHAR},
                #{adjustQuantity,jdbcType=VARCHAR},
                #{usedAsReplaceQuantity,jdbcType=VARCHAR},
                #{beforeEndingInventory,jdbcType=VARCHAR},
                #{endingInventory,jdbcType=VARCHAR},
                #{inventoryGap,jdbcType=VARCHAR},
                #{inventoryDate,jdbcType=TIMESTAMP},
                #{planPurchase,jdbcType=VARCHAR},
                #{realInventoryGap,jdbcType=VARCHAR},
                #{whetherLockPeriod,jdbcType=VARCHAR},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_detail(
        id,
        no_glass_inventory_shift_data_id,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        plan_purchase,
        real_inventory_gap,
        whether_lock_period,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.noGlassInventoryShiftDataId,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.inventoryGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.planPurchase,jdbcType=VARCHAR},
            #{entity.realInventoryGap,jdbcType=VARCHAR},
            #{entity.whetherLockPeriod,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_no_glass_inventory_shift_detail(
        id,
        no_glass_inventory_shift_data_id,
        safety_stock_level_min,
        safety_stock_level_standard,
        safety_stock_level_max,
        opening_inventory,
        demand_quantity,
        supply_quantity,
        adjust_quantity,
        used_as_replace_quantity,
        before_ending_inventory,
        ending_inventory,
        inventory_gap,
        inventory_date,
        plan_purchase,
        real_inventory_gap,
        whether_lock_period,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.noGlassInventoryShiftDataId,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMin,jdbcType=VARCHAR},
            #{entity.safetyStockLevelStandard,jdbcType=VARCHAR},
            #{entity.safetyStockLevelMax,jdbcType=VARCHAR},
            #{entity.openingInventory,jdbcType=VARCHAR},
            #{entity.demandQuantity,jdbcType=VARCHAR},
            #{entity.supplyQuantity,jdbcType=VARCHAR},
            #{entity.adjustQuantity,jdbcType=VARCHAR},
            #{entity.usedAsReplaceQuantity,jdbcType=VARCHAR},
            #{entity.beforeEndingInventory,jdbcType=VARCHAR},
            #{entity.endingInventory,jdbcType=VARCHAR},
            #{entity.inventoryGap,jdbcType=VARCHAR},
            #{entity.inventoryDate,jdbcType=TIMESTAMP},
            #{entity.planPurchase,jdbcType=VARCHAR},
            #{entity.realInventoryGap,jdbcType=VARCHAR},
            #{entity.whetherLockPeriod,jdbcType=VARCHAR},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDetailPO">
        update mrp_no_glass_inventory_shift_detail
        set no_glass_inventory_shift_data_id = #{noGlassInventoryShiftDataId,jdbcType=VARCHAR},
            safety_stock_level_min           = #{safetyStockLevelMin,jdbcType=VARCHAR},
            safety_stock_level_standard      = #{safetyStockLevelStandard,jdbcType=VARCHAR},
            safety_stock_level_max           = #{safetyStockLevelMax,jdbcType=VARCHAR},
            opening_inventory                = #{openingInventory,jdbcType=VARCHAR},
            demand_quantity                  = #{demandQuantity,jdbcType=VARCHAR},
            supply_quantity                  = #{supplyQuantity,jdbcType=VARCHAR},
            adjust_quantity                  = #{adjustQuantity,jdbcType=VARCHAR},
            used_as_replace_quantity         = #{usedAsReplaceQuantity,jdbcType=VARCHAR},
            before_ending_inventory          = #{beforeEndingInventory,jdbcType=VARCHAR},
            ending_inventory                 = #{endingInventory,jdbcType=VARCHAR},
            inventory_gap                    = #{inventoryGap,jdbcType=VARCHAR},
            inventory_date                   = #{inventoryDate,jdbcType=TIMESTAMP},
            plan_purchase                    = #{planPurchase,jdbcType=VARCHAR},
            real_inventory_gap               = #{realInventoryGap,jdbcType=VARCHAR},
            whether_lock_period              = #{whetherLockPeriod,jdbcType=VARCHAR},
            remark                           = #{remark,jdbcType=VARCHAR},
            enabled                          = #{enabled,jdbcType=VARCHAR},
            modifier                         = #{modifier,jdbcType=VARCHAR},
            modify_time                      = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.material.plan.infrastructure.po.NoGlassInventoryShiftDetailPO">
        update mrp_no_glass_inventory_shift_detail
        <set>
            <if test="item.noGlassInventoryShiftDataId != null and item.noGlassInventoryShiftDataId != ''">
                no_glass_inventory_shift_data_id = #{item.noGlassInventoryShiftDataId,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMin != null">
                safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelStandard != null">
                safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
            </if>
            <if test="item.safetyStockLevelMax != null">
                safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
            </if>
            <if test="item.openingInventory != null">
                opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.demandQuantity != null">
                demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyQuantity != null">
                supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.adjustQuantity != null">
                adjust_quantity = #{item.adjustQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.usedAsReplaceQuantity != null">
                used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.beforeEndingInventory != null">
                before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.endingInventory != null">
                ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryGap != null">
                inventory_gap = #{item.inventoryGap,jdbcType=VARCHAR},
            </if>
            <if test="item.inventoryDate != null">
                inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planPurchase != null">
                plan_purchase = #{item.planPurchase,jdbcType=VARCHAR},
            </if>
            <if test="item.realInventoryGap != null">
                real_inventory_gap = #{item.realInventoryGap,jdbcType=VARCHAR},
            </if>
            <if test="item.whetherLockPeriod != null">
                whether_lock_period = #{item.whetherLockPeriod,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_no_glass_inventory_shift_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="no_glass_inventory_shift_data_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.noGlassInventoryShiftDataId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_min = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMin,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_standard = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelStandard,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="safety_stock_level_max = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.safetyStockLevelMax,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="opening_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.openingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="adjust_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.adjustQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="used_as_replace_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.usedAsReplaceQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="before_ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.beforeEndingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ending_inventory = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endingInventory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="inventory_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inventoryDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_purchase = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planPurchase,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="real_inventory_gap = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.realInventoryGap,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="whether_lock_period = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.whetherLockPeriod,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mrp_no_glass_inventory_shift_detail
            <set>
                <if test="item.noGlassInventoryShiftDataId != null and item.noGlassInventoryShiftDataId != ''">
                    no_glass_inventory_shift_data_id = #{item.noGlassInventoryShiftDataId,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMin != null">
                    safety_stock_level_min = #{item.safetyStockLevelMin,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelStandard != null">
                    safety_stock_level_standard = #{item.safetyStockLevelStandard,jdbcType=VARCHAR},
                </if>
                <if test="item.safetyStockLevelMax != null">
                    safety_stock_level_max = #{item.safetyStockLevelMax,jdbcType=VARCHAR},
                </if>
                <if test="item.openingInventory != null">
                    opening_inventory = #{item.openingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.demandQuantity != null">
                    demand_quantity = #{item.demandQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyQuantity != null">
                    supply_quantity = #{item.supplyQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.adjustQuantity != null">
                    adjust_quantity = #{item.adjustQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.usedAsReplaceQuantity != null">
                    used_as_replace_quantity = #{item.usedAsReplaceQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.beforeEndingInventory != null">
                    before_ending_inventory = #{item.beforeEndingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.endingInventory != null">
                    ending_inventory = #{item.endingInventory,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryGap != null">
                    inventory_gap = #{item.inventoryGap,jdbcType=VARCHAR},
                </if>
                <if test="item.inventoryDate != null">
                    inventory_date = #{item.inventoryDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planPurchase != null">
                    plan_purchase = #{item.planPurchase,jdbcType=VARCHAR},
                </if>
                <if test="item.realInventoryGap != null">
                    real_inventory_gap = #{item.realInventoryGap,jdbcType=VARCHAR},
                </if>
                <if test="item.whetherLockPeriod != null">
                    whether_lock_period = #{item.whetherLockPeriod,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mrp_no_glass_inventory_shift_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_no_glass_inventory_shift_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <delete id="deleteDataIds">
        delete from mrp_no_glass_inventory_shift_detail where no_glass_inventory_shift_data_id in
        <foreach collection="dataIdList" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
