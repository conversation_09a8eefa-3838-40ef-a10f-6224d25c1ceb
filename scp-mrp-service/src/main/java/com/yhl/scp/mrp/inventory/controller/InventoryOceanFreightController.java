package com.yhl.scp.mrp.inventory.controller;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.inventory.dto.InventoryOceanFreightDTO;
import com.yhl.scp.mrp.inventory.service.InventoryOceanFreightService;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>InventoryOceanFreightController</code>
 * <p>
 * 浮法海运_提单号控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-04 10:52:16
 */
@Slf4j
@Api(tags = "浮法海运_提单号控制器")
@RestController
@RequestMapping("inventoryOceanFreight")
public class InventoryOceanFreightController extends BaseController {

    @Resource
    private InventoryOceanFreightService inventoryOceanFreightService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<InventoryOceanFreightVO>> page() {
        List<InventoryOceanFreightVO> inventoryOceanFreightList = inventoryOceanFreightService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<InventoryOceanFreightVO> pageInfo = new PageInfo<>(inventoryOceanFreightList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryOceanFreightDTO inventoryOceanFreightDTO) {
        return inventoryOceanFreightService.doCreate(inventoryOceanFreightDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryOceanFreightDTO inventoryOceanFreightDTO) {
        return inventoryOceanFreightService.doUpdate(inventoryOceanFreightDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryOceanFreightService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<InventoryOceanFreightVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryOceanFreightService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "同步")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncInventoryOceanFreight() {

        return inventoryOceanFreightService.syncInventoryOceanFreight(SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "批量创建PO")
    @PostMapping(value = "batchCreatPo")
    @BusinessMonitorLog(businessCode = "创建PO", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<String> doBatchCreatPo(@RequestBody List<String> ids) {
        String result = inventoryOceanFreightService.doBatchCreatPo(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, result);
    }
    @ApiOperation(value = "自动创建PO")
    @PostMapping(value = "syncAutoCreatPo")
    public BaseResponse<String> syncAutoCreatPo() {
        String result =inventoryOceanFreightService.syncAutoCreatPo(SystemHolder.getTenantCode(),new HashMap<>());
        return BaseResponse.success(BaseResponse.OP_SUCCESS, result);
    }
}
