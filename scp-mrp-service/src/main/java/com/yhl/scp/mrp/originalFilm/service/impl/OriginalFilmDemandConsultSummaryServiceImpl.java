package com.yhl.scp.mrp.originalFilm.service.impl;

import java.io.BufferedOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import cn.hutool.core.date.StopWatch;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.*;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mrp.inventory.service.InventoryAlternativeRelationshipService;
import com.yhl.scp.mrp.material.plan.enums.MrpStockPointTypeEnum;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.originalFilm.dto.*;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultVersionService;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultVersionVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.enums.OriginalFilmDemandSourceTypeEnum;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.originalFilm.convertor.OriginalFilmDemandConsultSummaryConvertor;
import com.yhl.scp.mrp.originalFilm.domain.entity.OriginalFilmDemandConsultSummaryDO;
import com.yhl.scp.mrp.originalFilm.domain.service.OriginalFilmDemandConsultSummaryDomainService;
import com.yhl.scp.mrp.originalFilm.infrastructure.dao.OriginalFilmDemandConsultSummaryDao;
import com.yhl.scp.mrp.originalFilm.infrastructure.po.OriginalFilmDemandConsultSummaryPO;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultDetailService;
import com.yhl.scp.mrp.originalFilm.service.OriginalFilmDemandConsultSummaryService;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultDetailVO;
import com.yhl.scp.mrp.originalFilm.vo.OriginalFilmDemandConsultSummaryVO;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>OriginalFilmDemandConsultSummaryServiceImpl</code>
 * <p>
 * 原片需求征询汇总应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-05 09:27:49
 */
@Slf4j
@Service
public class OriginalFilmDemandConsultSummaryServiceImpl extends AbstractService implements OriginalFilmDemandConsultSummaryService {

    private final String EXPORTTYPE_CURRENT = "current";

    @Resource
    private OriginalFilmDemandConsultSummaryDao originalFilmDemandConsultSummaryDao;

    @Resource
    private OriginalFilmDemandConsultSummaryDomainService originalFilmDemandConsultSummaryDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OriginalFilmDemandConsultDetailService originalFilmDemandConsultDetailService;

    @Resource
    private MaterialPlanInventoryShiftService materialPlanInventoryShiftService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private OriginalFilmDemandConsultVersionService originalFilmDemandConsultVersionService;

    @Resource
    private GlassInventoryShiftDataService glassInventoryShiftDataService;

    @Resource
    private GlassInventoryShiftDetailService glassInventoryShiftDetailService;

    @Resource
    private InventoryAlternativeRelationshipService inventoryAlternativeRelationshipService;

    @Override
    public BaseResponse<Void> doCreate(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryDO originalFilmDemandConsultSummaryDO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Do(originalFilmDemandConsultSummaryDTO);
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 1.数据校验
        originalFilmDemandConsultSummaryDomainService.validation(originalFilmDemandConsultSummaryDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.insertWithPrimaryKey(originalFilmDemandConsultSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryDO originalFilmDemandConsultSummaryDO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Do(originalFilmDemandConsultSummaryDTO);
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 1.数据校验
        originalFilmDemandConsultSummaryDomainService.validation(originalFilmDemandConsultSummaryDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.updateSelective(originalFilmDemandConsultSummaryPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OriginalFilmDemandConsultSummaryDTO> list) {
        List<OriginalFilmDemandConsultSummaryPO> newList = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        originalFilmDemandConsultSummaryDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<OriginalFilmDemandConsultSummaryDTO> list) {
        List<OriginalFilmDemandConsultSummaryPO> newList = OriginalFilmDemandConsultSummaryConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        originalFilmDemandConsultSummaryDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        //删除原片需求征询汇总数据
        int deleteNum = originalFilmDemandConsultSummaryDao.deleteBatch(idList);
        //删除原片需求征询汇总明细数据
        originalFilmDemandConsultDetailService.deleteByConsultSummaryIds(idList);
        //TODO 库存扣减处理逻辑
        return deleteNum;
    }

    @Override
    public OriginalFilmDemandConsultSummaryVO selectByPrimaryKey(String id) {
        OriginalFilmDemandConsultSummaryPO po = originalFilmDemandConsultSummaryDao.selectByPrimaryKey(id);
        return OriginalFilmDemandConsultSummaryConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_summary")
    public List<OriginalFilmDemandConsultSummaryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_original_film_demand_consult_summary")
    public List<OriginalFilmDemandConsultSummaryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OriginalFilmDemandConsultSummaryVO> dataList = originalFilmDemandConsultSummaryDao.selectByCondition(sortParam, queryCriteriaParam);
        OriginalFilmDemandConsultSummaryServiceImpl target = springBeanUtils.getBean(OriginalFilmDemandConsultSummaryServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectByParams(Map<String, Object> params) {
        List<OriginalFilmDemandConsultSummaryPO> list = originalFilmDemandConsultSummaryDao.selectByParams(params);
        return OriginalFilmDemandConsultSummaryConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ORIGINAL_FILM_DEMAND_CONSULT_SUMMARY.getCode();
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> invocation(List<OriginalFilmDemandConsultSummaryVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void insertWithPrimaryKey(OriginalFilmDemandConsultSummaryDTO originalFilmDemandConsultSummaryDTO) {
        // 0.数据转换
        OriginalFilmDemandConsultSummaryPO originalFilmDemandConsultSummaryPO = OriginalFilmDemandConsultSummaryConvertor
                .INSTANCE.dto2Po(originalFilmDemandConsultSummaryDTO);
        // 2.数据持久化
        BasePOUtils.insertFiller(originalFilmDemandConsultSummaryPO);
        originalFilmDemandConsultSummaryDao.insertWithPrimaryKey(originalFilmDemandConsultSummaryPO);
    }

    @Override
    public BaseResponse<Void> doDemandCalculation(OriginalFilmDemandConsultSummaryDTO dto) {
        //数据校验
        originalFilmDemandConsultSummaryDomainService.validationForDemandCalculation(dto);
        StopWatch stopWatch = new StopWatch("原片浮法库存推移查询");
        stopWatch.start("查询计划员权限下的物料");
        // 查询用户下权限的物料
        List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));

        if (CollectionUtils.isEmpty(productStockPointVOList)) {
            throw new BusinessException("当前计划员无可用物料权限");
        }
        List<String> planUserProductCodeList = productStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> planUserProductCodeNewList = new ArrayList<>(planUserProductCodeList);
        for (String productCode : planUserProductCodeList) {
            // productCode 第三位替换为*
            planUserProductCodeNewList.add(productCode.substring(0, 2) + "*" + productCode.substring(3));
        }
        planUserProductCodeNewList = planUserProductCodeNewList.stream().distinct().collect(Collectors.toList());


        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MRP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "PURCHASE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        Map<String, Object> queryMap = MapUtil.newHashMap();
        queryMap.put("productColor", dto.getProductColor());
        queryMap.put("startProductThickness", dto.getStartProductThickness());
        queryMap.put("endProductThickness", dto.getEndProductThickness());

//		queryMap.put("stockPointCode", rangeData);
        //1.从库存推移中获取对应的明细数据
        List<GlassInventoryShiftDataVO> shiftList = new ArrayList<>();

        Lists.partition(planUserProductCodeNewList, 1000).forEach(productCodeList -> {
            queryMap.put("planUserProductCodeList", productCodeList);
            List<GlassInventoryShiftDataVO> glassInventoryShiftDataVOList = glassInventoryShiftDataService.selectForDemandCalculation(queryMap);
            if (CollectionUtils.isNotEmpty(glassInventoryShiftDataVOList)) {
                shiftList.addAll(glassInventoryShiftDataVOList);
            }
        });

        if (CollectionUtils.isEmpty(shiftList)) {
            throw new BusinessException("该条件下无原片需求数据可汇总!");
        }

        Map<String, GlassInventoryShiftDataVO> shiftDataMapOfId = shiftList.stream().collect(Collectors.toMap(GlassInventoryShiftDataVO::getId, Function.identity()));
        // 查询detail
        List<String> inventoryShiftDataIds = shiftList.stream().map(GlassInventoryShiftDataVO::getId).collect(Collectors.toList());
        List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOList = glassInventoryShiftDetailService.selectByParams(
                ImmutableMap.of("inventoryShiftDataIds", inventoryShiftDataIds,
                        "startDate", dto.getStartDate(),
                        "endDate", dto.getEndDate()));
        Map<String, List<GlassInventoryShiftDetailVO>> shiftDetailGroup = glassInventoryShiftDetailVOList.stream().collect(Collectors.groupingBy(GlassInventoryShiftDetailVO::getInventoryShiftDataId));

        // 创建新的版本
        OriginalFilmDemandConsultVersionDTO originalFilmDemandConsultVersionDTO = new OriginalFilmDemandConsultVersionDTO();
        originalFilmDemandConsultVersionDTO.setId(UUID.randomUUID().toString());
        originalFilmDemandConsultVersionDTO.setVersionCode(null);
        originalFilmDemandConsultVersionDTO.setProductColor(dto.getProductColor());
        originalFilmDemandConsultVersionDTO.setProductThicknessRange(dto.getStartProductThickness() + "~" + dto.getEndProductThickness());
        originalFilmDemandConsultVersionDTO.setDemandedMonthStart(dto.getStartDate());
        originalFilmDemandConsultVersionDTO.setDemandedMonthEnd(dto.getEndDate());
        originalFilmDemandConsultVersionService.doCreate(originalFilmDemandConsultVersionDTO);


        Map<String, List<GlassInventoryShiftDataVO>> shiftMap = shiftList.stream()
                .collect(Collectors.groupingBy(e -> e.getProductColor() + "_" + e.getProductThickness()));

        //2.明细数据按照厚度，颜色，月份进行汇总统计
        List<OriginalFilmDemandConsultSummaryPO> batchAddSumaryList = new ArrayList<>();
        List<OriginalFilmDemandConsultDetailDTO> batchAddDetailList = new ArrayList<>();
        for (Entry<String, List<GlassInventoryShiftDataVO>> shiftEntry : shiftMap.entrySet()) {
            String[] splitKey = shiftEntry.getKey().split("_");
            OriginalFilmDemandConsultSummaryPO addSummary = new OriginalFilmDemandConsultSummaryPO();
            addSummary.setId(UUIDUtil.getUUID());
            addSummary.setOriginalFilmDemandConsultVersionId(originalFilmDemandConsultVersionDTO.getId());
            addSummary.setProductColor(splitKey[0]);
            addSummary.setProductThickness(new BigDecimal(splitKey[1]));
            addSummary.setDemandedQuantity(null);
            BigDecimal demandedQuantity = BigDecimal.ZERO;
            List<GlassInventoryShiftDataVO> dataVOList = shiftEntry.getValue();

            List<GlassInventoryShiftDetailVO> glassInventoryShiftDataVO = new ArrayList<>();
            for (GlassInventoryShiftDataVO inventoryShiftDataVO : dataVOList) {
                if (shiftDetailGroup.containsKey(inventoryShiftDataVO.getId())){
                    glassInventoryShiftDataVO.addAll(shiftDetailGroup.get(inventoryShiftDataVO.getId()));
                }
            }

            // 按照物品和时间分组
            Map<String, List<GlassInventoryShiftDetailVO>> inventoryShiftGroup = glassInventoryShiftDataVO.stream()
                    .collect(Collectors.groupingBy(e -> e.getInventoryShiftDataId() + "_" + DateUtils.dateToString(e.getInventoryDate(), DateUtils.COMMON_DATE_STR3)));

            // 收集推移物料
            List<String> productCodeList = inventoryShiftGroup.values().stream()
                    .map(glassInventoryShiftDetailVOS -> glassInventoryShiftDetailVOS.get(0))
                    .map(materialPlanInventoryShiftVO -> shiftDataMapOfId.get(materialPlanInventoryShiftVO.getInventoryShiftDataId()))
                    .map(GlassInventoryShiftDataVO::getProductCode)
                    .collect(Collectors.toList());
            // 获取物料对应成品和毛坯
            Map<String, NewProductStockPointVO> blankProductMap = new HashMap<>();
            Map<String, NewProductStockPointVO> finishedProductMap = new HashMap<>();
            inventoryAlternativeRelationshipService.getBlankAndFinishedProduct(productCodeList,blankProductMap,finishedProductMap);

            for (Entry<String, List<GlassInventoryShiftDetailVO>> entry : inventoryShiftGroup.entrySet()) {
                log.info("原片需求汇总, key:{}", entry.getKey());
                List<GlassInventoryShiftDetailVO> materialPlanInventoryShiftVOList = entry.getValue();
                GlassInventoryShiftDetailVO materialPlanInventoryShiftVO = materialPlanInventoryShiftVOList.get(0);
                GlassInventoryShiftDataVO shiftDataVO = shiftDataMapOfId.get(materialPlanInventoryShiftVO.getInventoryShiftDataId());

                // BC 本厂库存
                // 过滤除本厂库存的
                List<GlassInventoryShiftDetailVO> bcShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.BC.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal bcOpeningInventorySum = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(bcShiftVoList)){
                    bcOpeningInventorySum = bcShiftVoList.stream()
                            .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // BCMT
                List<GlassInventoryShiftDetailVO> bcmtShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.BCMT.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());

                // MT 码头   码头库存
                List<GlassInventoryShiftDetailVO> mtShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.MT.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal mtOpeningInventorySum = mtShiftVoList.stream()
                        .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                // FF 浮法   浮法厂库存
                List<GlassInventoryShiftDetailVO> ffShiftVoList = materialPlanInventoryShiftVOList.stream()
                        .filter(item -> StringUtils.equals(MrpStockPointTypeEnum.FF.getCode(), item.getStockPointType()))
                        .collect(Collectors.toList());
                BigDecimal ffOpeningInventorySum = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(ffShiftVoList)){
                    ffOpeningInventorySum = ffShiftVoList.stream()
                            .map(GlassInventoryShiftDetailVO::getOpeningInventory)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }

                // MT 原片在途 transit_quantity_from_float
                BigDecimal mtTansitQuantityFromFloatSum = mtShiftVoList.stream()
                        .map(GlassInventoryShiftDetailVO::getTransitQuantityFromPort)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                //维护对应的明细数据
                OriginalFilmDemandConsultDetailDTO detailDTO = OriginalFilmDemandConsultDetailDTO.builder()
                        .consultSummaryId(addSummary.getId())
                        .demandSourceType(OriginalFilmDemandSourceTypeEnum.INVENTORY_SHIFT_DEMAND.getCode())
                        .originalFilmProductId(shiftDataVO.getProductCode())
                        .productCode(shiftDataVO.getProductFactoryCode())
                        .vehicleModelCode(shiftDataVO.getVehicleModelCode())
                        .demandedQuantity(bcmtShiftVoList.get(0).getDemandQuantity())
                        .demandedDate(materialPlanInventoryShiftVO.getInventoryDate())
                        .floatInventoryQuantity(ffOpeningInventorySum)
                        .portRoadInventory(mtTansitQuantityFromFloatSum)
                        .portInventory(mtOpeningInventorySum)
                        .factoryInventory(bcOpeningInventorySum)
                        .standardDemandedQuantity(materialPlanInventoryShiftVO.getDemandQuantity())
                        .usedAsReplaceQuantity(materialPlanInventoryShiftVO.getUsedAsReplaceQuantity())
                        .useReplaceQuantity(materialPlanInventoryShiftVO.getUseReplaceQuantity())
                        .build();
                // 计算面积，原片规格长 * 宽 / 1000000
                if (null != shiftDataVO.getProductLength() &&
                        null != shiftDataVO.getProductWidth() &&
                        shiftDataVO.getProductLength().compareTo(BigDecimal.ZERO) > 0 &&
                        shiftDataVO.getProductWidth().compareTo(BigDecimal.ZERO) > 0
                ){
                    detailDTO.setArea(shiftDataVO.getProductLength()
                            .multiply(shiftDataVO.getProductWidth())
                            .divide(new BigDecimal("1000000"), 2, RoundingMode.HALF_UP));
                }
                // 总面积，单片面积 * 需求量
                BigDecimal areaSum = BigDecimal.ZERO;
                if(detailDTO.getArea() != null && materialPlanInventoryShiftVO.getDemandQuantity() != null) {
                    areaSum = detailDTO.getArea().multiply(materialPlanInventoryShiftVO.getDemandQuantity());
                }
                // 计算重量，总面积 * 厚度 * 2.5 / 1000
                if (null != shiftDataVO.getProductThickness()){
                    BigDecimal weight = areaSum.multiply(shiftDataVO.getProductThickness())
                            .multiply(new BigDecimal("2.5"))
                            .divide(new BigDecimal("1000"), 2, RoundingMode.HALF_UP);
                    detailDTO.setWeight(weight);
                }

                //净需求量=毛需求+替代规格需求量-库存数据(浮法厂库存,原片在途,码头库存,本厂库存,被替代量)
                BigDecimal netDemandedQuantity = bcmtShiftVoList.get(0).getDemandQuantity();
                if(detailDTO.getUsedAsReplaceQuantity() != null) {
                    netDemandedQuantity = netDemandedQuantity.add(detailDTO.getUsedAsReplaceQuantity());
                }
                if(detailDTO.getUseReplaceQuantity() != null) {
                    netDemandedQuantity = netDemandedQuantity.subtract(detailDTO.getUseReplaceQuantity());
                }
                detailDTO.setNetDemandedQuantity(netDemandedQuantity);
                demandedQuantity = demandedQuantity.add(detailDTO.getDemandedQuantity());

                // 获取毛坯（处理*物料）
                NewProductStockPointVO blankProduct;
                if (shiftDataVO.getProductCode().charAt(2) == '*') {
                    StringBuilder sb = new StringBuilder(shiftDataVO.getProductCode());
                    sb.setCharAt(2, 'B');
                    blankProduct = blankProductMap.get(sb.toString());
                    if (Objects.isNull(blankProduct)) {
                        sb.setCharAt(2, 'T');
                        blankProduct = blankProductMap.get(sb.toString());
                    }
                } else {
                    blankProduct = blankProductMap.get(shiftDataVO.getProductCode());
                }

                if (Objects.nonNull(blankProduct)) {
                    if (null == blankProduct.getProductArea()) {
                        blankProduct.setProductArea(blankProduct.getProductLength()
                                .multiply(blankProduct.getProductWidth())
                                .divide(BigDecimal.valueOf(1000000), 4, RoundingMode.HALF_UP));
                    }
                    detailDTO.setMpblSpec(String.valueOf(blankProduct.getProductArea()));
                }
                batchAddDetailList.add(detailDTO);
            }
            addSummary.setDemandedQuantity(demandedQuantity);
            batchAddSumaryList.add(addSummary);
        }
        if (CollUtil.isNotEmpty(batchAddSumaryList)) {
            BasePOUtils.insertBatchFiller(batchAddSumaryList);
            originalFilmDemandConsultSummaryDao.insertBatchWithPrimaryKey(batchAddSumaryList);
            originalFilmDemandConsultDetailService.doCreateBatch(batchAddDetailList);
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void export(HttpServletResponse response, Pagination pagination, String sortParam, String queryCriteriaParam,
                       String exportType) {
        List<OriginalFilmDemandConsultSummaryVO> consultSummaryList;
        if (EXPORTTYPE_CURRENT.equals(exportType)) {
            //导出当前页面
            consultSummaryList = this.selectByPage(pagination, sortParam, queryCriteriaParam);
        } else {
            //导出所有
            // 查询用户下权限的物料

            FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("product_color"))
                    .queryParam(ImmutableMap.of("materialPlanner",SystemHolder.getUserId(),
                            "productColorIsNotNull", YesOrNoEnum.YES.getCode()))
                    .build();

            List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(),
                    feignDynamicParam);
            if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
                log.info("当前用户没有权限的物料或未维护颜色");
                return;
            }
            List<String> planUserProductColorList = newProductStockPointVOList.stream()
                    .map(NewProductStockPointVO::getProductColor)
                    .distinct().collect(Collectors.toList());

            // 查询当前用户颜色版本
            Map<String, Object> versionParams = new HashMap<>(2);
            versionParams.put("productColorList", planUserProductColorList);
            List<OriginalFilmDemandConsultVersionVO> versionVOS = originalFilmDemandConsultVersionService.selectByParams(versionParams);
            if (CollectionUtils.isEmpty(versionVOS)) {
                log.info("当前用户没有维护版本");
                return;
            }

            List<String> versionIds = versionVOS.stream()
                    .collect(Collectors.groupingBy(
                            OriginalFilmDemandConsultVersionVO::getProductColor,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(OriginalFilmDemandConsultVersionVO::getCreateTime)),
                                    optional -> optional.orElse(null) // 明确处理 Optional
                            )
                    ))
                    .values().stream()
                    .filter(Objects::nonNull) // 过滤掉可能的 null 值
                    .map(OriginalFilmDemandConsultVersionVO::getId)
                    .collect(Collectors.toList());
            Map<String, Object> params = new HashMap<>();
            params.put("originalFilmDemandConsultVersionIds", versionIds);
            consultSummaryList = this.selectByParams(params);
//            consultSummaryList = this.selectByCondition(sortParam, null);
        }

        //查询对应的详情
        List<OriginalFilmDemandConsultExportDTO> exprotDtos = new ArrayList<>();
        if (CollUtil.isNotEmpty(consultSummaryList)) {
            OriginalFilmDemandSourceTypeEnum[] values = OriginalFilmDemandSourceTypeEnum.values();
            Map<String, String> originalFilmDemandSourceTypeEnumMap = Arrays.stream(values).distinct()
                    .collect(Collectors.toMap(OriginalFilmDemandSourceTypeEnum::getCode,
                            OriginalFilmDemandSourceTypeEnum::getDesc, (k1 ,k2) -> k2));

            List<String> consultSummaryIds = consultSummaryList.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());
            List<OriginalFilmDemandConsultDetailVO> detailList = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
            Map<String, List<OriginalFilmDemandConsultDetailVO>> detailMap = detailList.stream()
                    .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));
            for (int i = 0; i < consultSummaryList.size(); i++) {
                OriginalFilmDemandConsultSummaryVO consultSummary = consultSummaryList.get(i);
                OriginalFilmDemandConsultExportDTO addParent = new OriginalFilmDemandConsultExportDTO();
                addParent.setSortNo(i + 1 + "");
                addParent.setDemandedMonth(consultSummary.getDemandedMonth());
                addParent.setTotalDemandedQuantity(consultSummary.getDemandedQuantity());
                addParent.setOriginalFilmProductThickness(consultSummary.getProductThickness());
                addParent.setOriginalFilmProductColor(consultSummary.getProductColor());
                addParent.setSpecialRemark(consultSummary.getSpecialRemark());
                exprotDtos.add(addParent);
                //维护详情
                List<OriginalFilmDemandConsultDetailVO> subDetailList = detailMap.get(consultSummary.getId());
                if (CollUtil.isEmpty(subDetailList)) {
                    continue;
                }
                for (int z = 0; z < subDetailList.size(); z++) {
                    OriginalFilmDemandConsultDetailVO subDetail = subDetailList.get(z);
                    OriginalFilmDemandConsultExportDTO addSubDetail = new OriginalFilmDemandConsultExportDTO();
                    addSubDetail.setOriginalFilmProductThickness(subDetail.getOriginalFilmProductThickness());
                    addSubDetail.setOriginalFilmProductColor(subDetail.getOriginalFilmProductColor());
                    addSubDetail.setDemandSourceId(subDetail.getDemandSourceId());
                    addSubDetail.setVehicleModelCode(subDetail.getVehicleModelCode());
                    addSubDetail.setProductCode(subDetail.getProductCode());
                    addSubDetail.setProductName(subDetail.getProductName());
                    addSubDetail.setOriginalFilmProductCode(subDetail.getOriginalFilmProductCode());
                    addSubDetail.setOriginalFilmProductName(subDetail.getOriginalFilmProductName());
                    addSubDetail.setDemandedQuantity(subDetail.getDemandedQuantity());
                    addSubDetail.setFloatInventoryQuantity(subDetail.getFloatInventoryQuantity());
                    addSubDetail.setPortRoadInventory(subDetail.getPortRoadInventory());
                    addSubDetail.setPortInventory(subDetail.getPortInventory());
                    addSubDetail.setFactoryInventory(subDetail.getFactoryInventory());
                    addSubDetail.setStandardDemandedQuantity(subDetail.getStandardDemandedQuantity());
                    addSubDetail.setUsedAsReplaceQuantity(subDetail.getUsedAsReplaceQuantity());
                    addSubDetail.setUseReplaceQuantity(subDetail.getUseReplaceQuantity());
                    addSubDetail.setNetDemandedQuantity(subDetail.getNetDemandedQuantity());

                    addSubDetail.setSortNo(addParent.getSortNo() + "/" + (i + 1));
                    addSubDetail.setDemandedDate(DateUtils.dateToString(subDetail.getDemandedDate(), DateUtils.COMMON_DATE_STR3));
                    addSubDetail.setSpecialRequirements(subDetail.getSpecialRequirements());
                    addSubDetail.setDemandSourceType(EnumUtils.getDescByCode(OriginalFilmDemandSourceTypeEnum.class, subDetail.getDemandSourceType()));
                    addSubDetail.setDemandSourceType(originalFilmDemandSourceTypeEnumMap.get(subDetail.getDemandSourceType()));
                    if (subDetail.getOriginalFilmProductLength() != null && subDetail.getOriginalFilmProductWidth() != null) {
                        addSubDetail.setOriginalFilmProductSpec(subDetail.getOriginalFilmProductLength().stripTrailingZeros().toPlainString()
                                + "*" + subDetail.getOriginalFilmProductWidth().stripTrailingZeros().toPlainString());
                    }
                    exprotDtos.add(addSubDetail);
                }
            }
        }

        try (OutputStream out = new BufferedOutputStream(response.getOutputStream())) {
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            String fileName = URLEncoder.encode("原片需求征询.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);

            EasyExcel.write(out, OriginalFilmDemandConsultExportDTO.class)
                    //.registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()) // 关闭自动列宽
                    .registerWriteHandler(new SimpleColumnWidthStyleStrategy(25 * 256)) // 设置固定列宽
                    .sheet("原片需求征询")
                    .doWrite(exprotDtos);
        } catch (IOException e) {
            log.error("Excel导出失败", e);
            throw new BusinessException("导出失败，请稍后再试");
        }


    }

    @Override
    public List<LabelValue<String>> selectAllColor() {
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(), TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> selectAllProductStockPoint = newMdsFeign.selectProductStockPointByParams(defaultScenario.getData(), ImmutableMap.of("productColorIsNotNull", YesOrNoEnum.YES.getCode()));
        List<String> colorList = selectAllProductStockPoint.stream().filter(e -> StringUtils.isNotEmpty(e.getProductColor()))
                .map(NewProductStockPointVO::getProductColor).distinct().collect(Collectors.toList());
        return colorList.stream()
                .map(x -> new LabelValue<>(x, x))
                .collect(Collectors.toList());
    }

    @Override
    public List<OriginalFilmDemandConsultSummaryVO> selectByPage2(OriginalFilmDemandConsultParam originalFilmDemandConsultParam) {
        // 原片汇总查询条件
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotBlank(originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId())) {
            params.put("originalFilmDemandConsultVersionId", originalFilmDemandConsultParam.getOriginalFilmDemandConsultVersionId());
        } else {
            // 查询用户下权限的物料
            List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                    ImmutableMap.of("materialPlanner", SystemHolder.getUserId()));
            if (CollectionUtils.isEmpty(newProductStockPointVOList)) {
                return null;
            }
            List<String> planUserProductColorList = newProductStockPointVOList.stream()
                    .map(NewProductStockPointVO::getProductColor)
                    .filter(Objects::nonNull)
                    .distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(planUserProductColorList)) {
                return null;
            }
            String productThickness = originalFilmDemandConsultParam.getProductThickness();
            List<BigDecimal> productThicknessList = new ArrayList<>();
            if (StringUtils.isNotBlank(productThickness)) {
                productThicknessList = Arrays.stream(productThickness.split(","))
                        .map(BigDecimal::new)
                        .collect(Collectors.toList());
            }

            // 查询当前用户颜色版本
            Map<String, Object> versionParams = new HashMap<>(2);
            versionParams.put("productColor", originalFilmDemandConsultParam.getProductColor());
            versionParams.put("productColorList", planUserProductColorList);
            List<OriginalFilmDemandConsultVersionVO> versionVOS = originalFilmDemandConsultVersionService.selectByParams(versionParams);
            if (CollectionUtils.isEmpty(versionVOS)) {
                return null;
            }

            List<String> versionIds = versionVOS.stream()
                    .collect(Collectors.groupingBy(
                            OriginalFilmDemandConsultVersionVO::getProductColor,
                            Collectors.collectingAndThen(
                                    Collectors.maxBy(Comparator.comparing(OriginalFilmDemandConsultVersionVO::getCreateTime)),
                                    optional -> optional.orElse(null) // 明确处理 Optional
                            )
                    ))
                    .values().stream()
                    .filter(Objects::nonNull) // 过滤掉可能的 null 值
                    .map(OriginalFilmDemandConsultVersionVO::getId)
                    .collect(Collectors.toList());
            params.put("productThicknessList", productThicknessList);
            params.put("demandedMonth", originalFilmDemandConsultParam.getDemandedMonth());
            params.put("originalFilmDemandConsultVersionIds", versionIds);
        }

        PageHelper.startPage(originalFilmDemandConsultParam.getPageNum(), originalFilmDemandConsultParam.getPageSize());
        List<OriginalFilmDemandConsultSummaryVO> originalFilmDemandConsultSummaryVOS = this.selectByParams(params);
        if (CollectionUtils.isEmpty(originalFilmDemandConsultSummaryVOS)) {
            return originalFilmDemandConsultSummaryVOS;
        }
        if (StringUtils.isNotBlank(originalFilmDemandConsultParam.getOrderBy())) {
            com.yhl.platform.common.utils.CollectionUtils.sort(originalFilmDemandConsultSummaryVOS, originalFilmDemandConsultParam.getOrderBy());
        }
        // 查询原片需求汇总详情
        List<String> consultSummaryIds = originalFilmDemandConsultSummaryVOS.stream().map(OriginalFilmDemandConsultSummaryVO::getId).collect(Collectors.toList());
        List<OriginalFilmDemandConsultDetailVO> originalFilmDemandConsultDetailVOS = originalFilmDemandConsultDetailService.selectByDemandSourceIds(consultSummaryIds);
        Map<String, List<OriginalFilmDemandConsultDetailVO>> consultDetailGroupOfSummaryId = originalFilmDemandConsultDetailVOS
                .stream()
                .collect(Collectors.groupingBy(OriginalFilmDemandConsultDetailVO::getConsultSummaryId));
        for (OriginalFilmDemandConsultSummaryVO originalFilmDemandConsultSummaryVO : originalFilmDemandConsultSummaryVOS) {
            List<OriginalFilmDemandConsultDetailVO> consultDetailVOS = consultDetailGroupOfSummaryId.get(originalFilmDemandConsultSummaryVO.getId());
            if (CollectionUtils.isEmpty(consultDetailVOS)) {
                continue;
            }
            Map<String, BigDecimal> monthDemandQuantity = new HashMap<>();
            // 详情按照日期进行分组，年月
            Map<String, List<OriginalFilmDemandConsultDetailVO>> consultDetailGroupOfDate = consultDetailVOS.stream()
                    .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH)));
            for (Entry<String, List<OriginalFilmDemandConsultDetailVO>> entry : consultDetailGroupOfDate.entrySet()) {
                String month = entry.getKey();
                List<OriginalFilmDemandConsultDetailVO> value = entry.getValue();
                // 汇总value的demandedQuantity需求量
                BigDecimal sumDemandedQuantity = value.stream()
                        .map(OriginalFilmDemandConsultDetailVO::getDemandedQuantity)
                        .filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                monthDemandQuantity.put(month, sumDemandedQuantity);
            }
            originalFilmDemandConsultSummaryVO.setMonthDemandQuantity(monthDemandQuantity);
        }

        List<String> demandedMonthList = originalFilmDemandConsultDetailVOS.stream()
                .map(item -> DateUtils.dateToString(item.getDemandedDate(), DateUtils.YEAR_MONTH))
                .distinct().sorted().collect(Collectors.toList());
        // 排序demandedMonthList
        originalFilmDemandConsultSummaryVOS.forEach(item -> item.setDemandedMonthList(demandedMonthList));
        return originalFilmDemandConsultSummaryVOS;
    }
}
