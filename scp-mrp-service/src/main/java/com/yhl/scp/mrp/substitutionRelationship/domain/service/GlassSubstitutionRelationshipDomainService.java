package com.yhl.scp.mrp.substitutionRelationship.domain.service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.mrp.substitutionRelationship.domain.entity.GlassSubstitutionRelationshipDO;
import com.yhl.scp.mrp.substitutionRelationship.dto.GlassSubstitutionRelationshipDTO;
import com.yhl.scp.mrp.substitutionRelationship.infrastructure.dao.GlassSubstitutionRelationshipDao;
import com.yhl.scp.mrp.substitutionRelationship.infrastructure.po.GlassSubstitutionRelationshipPO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <code>GlassSubstitutionRelationshipDomainService</code>
 * <p>
 * 原片替代关系表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-14 09:39:56
 */
@Service
public class GlassSubstitutionRelationshipDomainService {

    @Resource
    private GlassSubstitutionRelationshipDao glassSubstitutionRelationshipDao;

    /**
     * 数据校验
     *
     * @param glassSubstitutionRelationshipDO 领域对象
     */
    public void validation(GlassSubstitutionRelationshipDO glassSubstitutionRelationshipDO) {
        checkNotNull(glassSubstitutionRelationshipDO);
        checkUniqueCode(glassSubstitutionRelationshipDO);
        // TODO 补充其他校验逻辑
        // 根据生产BOM、本厂编码查询数据
        List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao.
                selectByParams(ImmutableMap.of("rawProductCode", glassSubstitutionRelationshipDO.getRawProductCode(),
                        "productCode", glassSubstitutionRelationshipDO.getProductCode()));
        List<GlassSubstitutionRelationshipPO> relationshipPOS = glassSubstitutionRelationshipPOS.stream()
                .filter(item -> StringUtils.isBlank(glassSubstitutionRelationshipDO.getId()) ||
                        item.getId().equals(glassSubstitutionRelationshipDO.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(relationshipPOS)){
            return;
        }
        List<String> productionSubstituteProductCodeList = relationshipPOS.stream()
                .map(GlassSubstitutionRelationshipPO::getProductionSubstituteProductCode)
                .distinct().collect(Collectors.toList());
        if (!isAllElementsEqual(productionSubstituteProductCodeList, glassSubstitutionRelationshipDO.getProductionSubstituteProductCode())){
            throw new BusinessException(glassSubstitutionRelationshipDO.getProductCode() +"#"+ glassSubstitutionRelationshipDO.getRawProductCode() + "生产BOM不一致");
        }
    }

    public boolean isAllElementsEqual(List<String> productionSubstituteProductCodeList, String productionSubstituteProductCode) {
        // 处理列表为 null 的情况
        if (productionSubstituteProductCodeList == null || StringUtils.isBlank(productionSubstituteProductCode)) {
            return true;
        }
        // 遍历列表检查每个元素
        for (String element : productionSubstituteProductCodeList) {
            if (StringUtils.isNotBlank(element) && !element.equals(productionSubstituteProductCode)) {
                // 发现不一致立即返回
                return false;
            }
        }
        // 所有元素一致
        return true;
    }

    /**
     * 非空检验
     *
     * @param glassSubstitutionRelationshipDO 领域对象
     */
    private void checkNotNull(GlassSubstitutionRelationshipDO glassSubstitutionRelationshipDO) {

    }

    /**
     * 唯一性校验
     *
     * @param glassSubstitutionRelationshipDO 领域对象
     */
    private void checkUniqueCode(GlassSubstitutionRelationshipDO glassSubstitutionRelationshipDO) {

    }

    public void checkProductionSubstituteProductCode(List<GlassSubstitutionRelationshipDTO> list) {
        List<String> productCodeList = list.stream().map(GlassSubstitutionRelationshipDTO::getProductCode).distinct().collect(Collectors.toList());
        List<String> rawProductCodeList = list.stream().map(GlassSubstitutionRelationshipDTO::getRawProductCode).distinct().collect(Collectors.toList());

        List<GlassSubstitutionRelationshipPO> glassSubstitutionRelationshipPOS = glassSubstitutionRelationshipDao.
                selectByParams(ImmutableMap.of("rawProductCodeList", rawProductCodeList, "productCodeList", productCodeList));
        Map<String, List<GlassSubstitutionRelationshipPO>> glassSubstitutionRelationshipPOGroup = glassSubstitutionRelationshipPOS.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        Map<String, List<GlassSubstitutionRelationshipDTO>> glassSubstitutionRelationshipGroup = list.stream()
                .collect(Collectors.groupingBy(item -> String.join("#", item.getProductCode(), item.getRawProductCode())));

        for (Map.Entry<String, List<GlassSubstitutionRelationshipDTO>> entry : glassSubstitutionRelationshipGroup.entrySet()) {
            List<GlassSubstitutionRelationshipPO> relationshipPOList = glassSubstitutionRelationshipPOGroup.get(entry.getKey());
            if (CollectionUtils.isEmpty(relationshipPOList)){
                continue;
            }
            List<GlassSubstitutionRelationshipDTO> value = entry.getValue();
            List<String> newDataIdList = value.stream().map(GlassSubstitutionRelationshipDTO::getId).collect(Collectors.toList());

            List<String> updateList = value.stream()
                    .map(GlassSubstitutionRelationshipDTO::getProductionSubstituteProductCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct().collect(Collectors.toList());
            List<String> databaseList = relationshipPOList.stream()
                    .filter(item -> !newDataIdList.contains(item.getId()))
                    .map(GlassSubstitutionRelationshipPO::getProductionSubstituteProductCode)
                    .filter(StringUtils::isNotBlank)
                    .distinct().collect(Collectors.toList());

            List<String> allList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(updateList)){
                allList.addAll(updateList);
            }
            if (CollectionUtils.isNotEmpty(databaseList)){
                allList.addAll(databaseList);
            }
            if (CollectionUtils.isNotEmpty(allList)) {
                List<String> distinctAllList = allList.stream().distinct().collect(Collectors.toList());
                if (distinctAllList.size() > 1){
                    throw new BusinessException(entry.getKey() + "生产BOM不一致");
                }
            }
        }

    }
}
