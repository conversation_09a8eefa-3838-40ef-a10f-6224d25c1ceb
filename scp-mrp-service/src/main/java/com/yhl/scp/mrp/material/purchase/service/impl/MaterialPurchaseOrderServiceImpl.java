package com.yhl.scp.mrp.material.purchase.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.enums.MrpCalcTypeEnum;
import com.yhl.scp.mrp.material.plan.dto.MrpParamDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import com.yhl.scp.mrp.material.arrival.service.MaterialDeliveryNoteService;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseStorageService;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialDeliveryNoteVO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseStorageVO;
import com.yhl.scp.mrp.material.plan.service.impl.NoGlassMrpServiceImpl;
import com.yhl.scp.mrp.material.purchase.convertor.MaterialPurchaseOrderConvertor;
import com.yhl.scp.mrp.material.purchase.domain.entity.MaterialPurchaseOrderDO;
import com.yhl.scp.mrp.material.purchase.domain.service.MaterialPurchaseOrderDomainService;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseOrderDTO;
import com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseOrderDao;
import com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseOrderPO;
import com.yhl.scp.mrp.material.purchase.service.MaterialPurchaseOrderService;
import com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseOrderVO;
import com.yhl.scp.mrp.order.service.PurchaseOrderInfoService;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * <code>MaterialPurchaseOrderServiceImpl</code>
 * <p>
 * 物料采购订单信息表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-30 10:14:58
 */
@Slf4j
@Service
public class MaterialPurchaseOrderServiceImpl extends AbstractService implements MaterialPurchaseOrderService {

    @Resource
    private MaterialPurchaseOrderDao materialPurchaseOrderDao;

    @Resource
    private MaterialPurchaseOrderDomainService materialPurchaseOrderDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private MaterialDeliveryNoteService materialDeliveryNoteService;
    
    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;
    
    @Resource
    private MaterialPurchaseStorageService materialPurchaseStorageService;
    
    @Resource
	private NoGlassMrpServiceImpl noGlassMrpService;
    
    @Resource
	private PurchaseOrderInfoService purchaseOrderInfoService;
    
    @Override
    public BaseResponse<Void> doCreate(MaterialPurchaseOrderDTO materialPurchaseOrderDTO) {
        // 0.数据转换
        MaterialPurchaseOrderDO materialPurchaseOrderDO = MaterialPurchaseOrderConvertor.INSTANCE.dto2Do(materialPurchaseOrderDTO);
        MaterialPurchaseOrderPO materialPurchaseOrderPO = MaterialPurchaseOrderConvertor.INSTANCE.dto2Po(materialPurchaseOrderDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseOrderDomainService.validation(materialPurchaseOrderDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPurchaseOrderPO);
        materialPurchaseOrderDao.insertWithPrimaryKey(materialPurchaseOrderPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPurchaseOrderDTO materialPurchaseOrderDTO) {
        // 0.数据转换
        MaterialPurchaseOrderDO materialPurchaseOrderDO = MaterialPurchaseOrderConvertor.INSTANCE.dto2Do(materialPurchaseOrderDTO);
        MaterialPurchaseOrderPO materialPurchaseOrderPO = MaterialPurchaseOrderConvertor.INSTANCE.dto2Po(materialPurchaseOrderDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        materialPurchaseOrderDomainService.validation(materialPurchaseOrderDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPurchaseOrderPO);
        materialPurchaseOrderDao.update(materialPurchaseOrderPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPurchaseOrderDTO> list) {
        List<MaterialPurchaseOrderPO> newList = MaterialPurchaseOrderConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPurchaseOrderDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPurchaseOrderDTO> list) {
        List<MaterialPurchaseOrderPO> newList = MaterialPurchaseOrderConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPurchaseOrderDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPurchaseOrderDao.deleteBatch(idList);
        }
        return materialPurchaseOrderDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPurchaseOrderVO selectByPrimaryKey(String id) {
        MaterialPurchaseOrderPO po = materialPurchaseOrderDao.selectByPrimaryKey(id);
        return MaterialPurchaseOrderConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_order")
    public List<MaterialPurchaseOrderVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_purchase_order")
    public List<MaterialPurchaseOrderVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPurchaseOrderVO> dataList = materialPurchaseOrderDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPurchaseOrderServiceImpl target = springBeanUtils.getBean(MaterialPurchaseOrderServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPurchaseOrderVO> selectByParams(Map<String, Object> params) {
        List<MaterialPurchaseOrderPO> list = materialPurchaseOrderDao.selectByParams(params);
        return MaterialPurchaseOrderConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPurchaseOrderVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MATERIAL_PURCHASE_ORDER.getCode();
    }

    @Override
    public List<MaterialPurchaseOrderVO> invocation(List<MaterialPurchaseOrderVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doDisposeWaitDeliveryQuantity(Integer moveMinute, String scenario) {
        log.info("材料到货跟踪维护待发货数量开始");
        //每三十分钟跑一次，每次获取40分钟前变更的数据维护到货跟踪对应数据的预计发货时间和数量
        Date startModifyTime = DateUtils.moveMinute(new Date(), moveMinute);
        // 获取采购订单数据
        List<PurchaseOrderInfoVO> purchaseOrderList = purchaseOrderInfoService.selectByParams(ImmutableMap.of("startModifyTime", startModifyTime));
        purchaseOrderList = purchaseOrderList.stream().filter(
                        e -> StringUtils.isNotEmpty(e.getOrderHeader())
                                && e.getQuantity() != null)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(purchaseOrderList)) {
            log.info("材料到货跟踪维护待发货数量，未获取到物料采购单数据信息");
            return;
        }
        // 采购订单根据采购单号 + 采购订单行号 + 物料编码 分组
        Map<String, List<PurchaseOrderInfoVO>> orderHeaderMap = purchaseOrderList.stream().collect(Collectors.groupingBy(data -> data.getOrderHeader() + "_" + data.getOrderLine() + "_" + data.getProductCode()));

        // 汇总采购单数据中的采购单号
        List<String> orderHeaderlist = purchaseOrderList.stream().map(PurchaseOrderInfoVO::getOrderHeader).distinct().collect(Collectors.toList());

        // 根据采购单号获取对应的材料到货跟踪数据
        List<MaterialArrivalTrackingVO> trackingList = materialArrivalTrackingService.selectByParams(ImmutableMap.of("purchaseOrderCodes", orderHeaderlist));
        if (CollUtil.isEmpty(trackingList)) {
            log.info("材料到货跟踪维护待发货数量，未维护材料到货跟踪数据");
            return;
        }
        log.info("到货跟踪数据为{}", JSON.toJSONString(trackingList));
        // 根据采购订单号 + 采购订单行号 + 物料编码分组
        Map<String, List<MaterialArrivalTrackingVO>> materialArrivalTrackingMap = trackingList.stream()
                .collect(Collectors.groupingBy(data -> data.getPurchaseOrderCode() + "_" + data.getPurchaseOrderLineCode() + "_" + data.getMaterialCode()));
        // 收集采购订单号
        List<String> purchaseOrderCodeList = trackingList.stream().map(MaterialArrivalTrackingVO::getPurchaseOrderCode).distinct().collect(Collectors.toList());

        // 根据订单号获取对应的入库记录数据
        List<MaterialPurchaseStorageVO> purchaseStorageList = materialPurchaseStorageService.selectByParams(ImmutableMap.of("purchaseOrderCodes", purchaseOrderCodeList));
        if (CollUtil.isEmpty(purchaseStorageList)) {
            log.info("材料到货跟踪维护待发货数量，未获取入库记录数据");
            return;
        }
        // 根据采购订单号分组
        Map<String, List<MaterialPurchaseStorageVO>> purchaseStorageMap = purchaseStorageList.stream()
                .collect(Collectors.groupingBy(data -> data.getPurchaseOrderCode() + "_" + data.getPurchaseOrderLineCode() + "_" + data.getProductCode()));

        //根据ID跟新对应的预计送达时间，数量，状态
        List<MaterialArrivalTrackingDTO> batchUpdate = new ArrayList<>();
        // 只有状态为采购跟踪的到货跟踪需要重新修改待发货数量
        List<MaterialArrivalTrackingVO> filterList = trackingList.stream().filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.PLAN_PRUCHASE.getCode())).collect(Collectors.toList());
        // 没有要修改的数据直接返回
        if (CollectionUtils.isEmpty(filterList)) return;
        // 遍历过滤后的到货跟踪
        for (MaterialArrivalTrackingVO sourceTrackingVO : filterList) {
            MaterialArrivalTrackingDTO update = MaterialArrivalTrackingDTO.builder()
                    .id(sourceTrackingVO.getId())
                    .build();
            // 采购订单号 + 采购订单行号 + 物料
            String combineKey = sourceTrackingVO.getPurchaseOrderCode() + "_" + sourceTrackingVO.getPurchaseOrderLineCode() + "_" + sourceTrackingVO.getMaterialCode();

            // 入库数量（根据采购订单号匹配采购入库记录中的数量）
            BigDecimal materialStorageQuantity = BigDecimal.ZERO;
            // 根据采购订单号 + 物料编码获取采购入库记录
            List<MaterialPurchaseStorageVO> materialStorageList = purchaseStorageMap.get(combineKey);
            if (CollUtil.isNotEmpty(materialStorageList)) {
                materialStorageQuantity = materialStorageList.stream().map(MaterialPurchaseStorageVO::getStorageQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            // 送货单数量（根据采购订单号 + 物料编码、有送货单号，且到货状态为已送货 的数据行汇总预计到货数量）
            BigDecimal arrivalQuantity = BigDecimal.ZERO;
            // 根据采购订单号获取到货跟踪
            List<MaterialArrivalTrackingVO> materialArrivalTrackingVOList = materialArrivalTrackingMap.get(combineKey);
            if (CollUtil.isNotEmpty(materialArrivalTrackingVOList)) {
                arrivalQuantity = materialArrivalTrackingVOList.stream()
                        .filter(data -> StringUtils.isNotEmpty(data.getDeliveryNoteCode()))
                        .filter(data -> data.getArrivalStatus().equals(ArrivalStatusEnum.DELIVERED.getCode()))
                        .map(MaterialArrivalTrackingVO::getPredictArrivalQuantity)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            //采购订单订单数量, 待发货数量 = 采购订单数量-送货单数量（不包含入库）- 已入库数量
            List<PurchaseOrderInfoVO> purchaseOrderInfoVOList = orderHeaderMap.get(combineKey);
            BigDecimal orderQuantity = CollectionUtils.isNotEmpty(purchaseOrderInfoVOList) ? BigDecimal.ZERO : purchaseOrderInfoVOList.stream().map(PurchaseOrderInfoVO::getQuantity).reduce(BigDecimal.ZERO,BigDecimal::add);
            BigDecimal waitDeliveryQuantity = orderQuantity.subtract(arrivalQuantity).subtract(materialStorageQuantity);
            log.info("key值{}，采购订单数量{}，送货到数量{}，已入库数量{}",combineKey,orderQuantity,arrivalQuantity,materialStorageQuantity);
            update.setWaitDeliveryQuantity(waitDeliveryQuantity);
            batchUpdate.add(update);
        }
        if (CollUtil.isNotEmpty(batchUpdate)) {
            materialArrivalTrackingService.doUpdateBatch(batchUpdate);
            List<String> computeProductCodeList = batchUpdate.stream()
                    .map(MaterialArrivalTrackingDTO::getMaterialCode)
                    .distinct().collect(Collectors.toList());

            CompletableFuture.runAsync(() -> {
                try {
                    String executionSequence = UUIDUtil.getUUID();
                    log.info("$$$$$$$$$$$$$$$$$$$$$ 到货跟踪剩余待发货数量发生变更，开始刷新MRP计算结果, 执行序列编码:{}", executionSequence);
                    noGlassMrpService.doRunMrp(new MrpParamDTO(MrpCalcTypeEnum.NO_GLASS.getCode(), SystemHolder.getScenario(),
                            null, DateUtils.getDayFirstTime(new Date()),null, null, Boolean.TRUE, computeProductCodeList));
                    log.info("$$$$$$$$$$$$$$$$$$$$$ 到货跟踪剩余待发货数量发生变更，刷新MRP计算结果结束, 执行序列编码：{}", executionSequence);
                } catch (Exception e) {
                    log.error("MRP材料推移失败", e);
                }
            });

            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                @Override
                public void afterCommit() {
                    //需要提交之后执行的代码
                    log.info("MRP材料推移");
                }
            });
        }
        log.info("材料到货跟踪维护待发货数量结束");
    }

}
