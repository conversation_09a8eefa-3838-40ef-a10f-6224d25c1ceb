package com.yhl.scp.mrp.material.arrival.domain.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.material.arrival.domain.entity.MaterialArrivalTrackingDO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingDao;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <code>MaterialArrivalTrackingDomainService</code>
 * <p>
 * 材料到货跟踪领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-21 16:57:40
 */
@Service
public class MaterialArrivalTrackingDomainService {

    @Resource
    private MaterialArrivalTrackingDao materialArrivalTrackingDao;

	@Resource
	private NewMdsFeign newMdsFeign;

    /**
     * 数据校验
     *
     * @param materialArrivalTrackingDO 领域对象
     */
    public void validation(MaterialArrivalTrackingDO materialArrivalTrackingDO) {
        checkNotNull(materialArrivalTrackingDO);
        checkUniqueCode(materialArrivalTrackingDO);
		checkProductPermissions(materialArrivalTrackingDO);
	}

    /**
     * 非空检验
     *
     * @param materialArrivalTrackingDO 领域对象
     */
	private void checkNotNull(MaterialArrivalTrackingDO materialArrivalTrackingDO) {
//    	if(StringUtils.isEmpty(materialArrivalTrackingDO.getPurchaseOrderCode())) {
//			throw new BusinessException("采购单号不为空");
//		}
		if (StringUtils.isEmpty(materialArrivalTrackingDO.getProductId())) {
			throw new BusinessException("物料不为空");
		}
//		if (Objects.isNull(materialArrivalTrackingDO.getPredictArrivalQuantity())) {
//			throw new BusinessException("预计到货数量不为空");
//		}
		if (StringUtils.isEmpty(materialArrivalTrackingDO.getArrivalStatus())) {
			throw new BusinessException("到货不为空");
		}

	}

    /**
     * 唯一性校验
     *
     * @param materialArrivalTrackingDO 领域对象
     */
    private void checkUniqueCode(MaterialArrivalTrackingDO materialArrivalTrackingDO) {
        // Do nothing because of X and Y.
    }

	private void checkProductPermissions(MaterialArrivalTrackingDO materialArrivalTrackingDO){
		// 校验权限
		FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
				.dynamicColumnParam(Lists.newArrayList("product_code"))
				.queryParam(ImmutableMap.of("materialPlanner", SystemHolder.getUserId()))
				.build();
		List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
		if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
			throw new BusinessException("您没有权限操作该数据");
		}
		List<String> materialPlannerProductCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
		if (!materialPlannerProductCodeList.contains(materialArrivalTrackingDO.getMaterialCode())){
			throw new BusinessException("您没有权限操作该数据");
		}
	}

}
