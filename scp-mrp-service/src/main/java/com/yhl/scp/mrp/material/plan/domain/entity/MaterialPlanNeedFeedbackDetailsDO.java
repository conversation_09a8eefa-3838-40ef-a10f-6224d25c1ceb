package com.yhl.scp.mrp.material.plan.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPlanNeedFeedbackDetailsDO</code>
 * <p>
 * 要货计划反馈明细DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03 14:31:23
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MaterialPlanNeedFeedbackDetailsDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = 575402565438353548L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 反馈状态（true,false）
     */
    private String status;
    /**
     * 接口参数
     */
    private String interfaceParameters;
    /**
     * 异常原因
     */
    private String exceptionReason;

}
