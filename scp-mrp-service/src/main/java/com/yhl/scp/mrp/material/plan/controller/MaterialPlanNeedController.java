package com.yhl.scp.mrp.material.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedDTO;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanNeedService;
import com.yhl.scp.mrp.extension.material.vo.MaterialPlanNeedVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Set;

/**
 * <code>MaterialPlanNeedController</code>
 * <p>
 * 要货计划控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:16:04
 */
@Slf4j
@Api(tags = "要货计划控制器")
@RestController
@RequestMapping("materialPlanNeed")
public class MaterialPlanNeedController extends BaseController {

    @Resource
    private MaterialPlanNeedService materialPlanNeedService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MaterialPlanNeedVO>> page() {
        List<MaterialPlanNeedVO> materialPlanNeedList = materialPlanNeedService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialPlanNeedVO> pageInfo = new PageInfo<>(materialPlanNeedList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialPlanNeedDTO materialPlanNeedDTO) {
        return materialPlanNeedService.doCreate(materialPlanNeedDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MaterialPlanNeedDTO materialPlanNeedDTO) {
        return materialPlanNeedService.doUpdate(materialPlanNeedDTO);
    }

    @ApiOperation(value = "批量修改（选择性）")
    @PostMapping(value = "updateBatchSelective")
    public BaseResponse<Void> updateBatchSelective(@RequestBody List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        return materialPlanNeedService.updateBatchSelective(materialPlanNeedDTOList);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialPlanNeedService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialPlanNeedVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialPlanNeedService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "下发要货数据到SRM")
    @PostMapping(value = "issueData")
    @BusinessMonitorLog(businessCode = "要货计划发布", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> issueData(@RequestBody List<String> ids) {
        String redisKey = MaterialRedisConstants.MATERIAL_PLAN_NEED_ISSUE;
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有要货计划正在下发，请等待下发完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 下发要货计划
            return materialPlanNeedService.issueData(ids, false);
        } catch (Exception e) {
            log.error("下发要货计划失败", e);
            throw new BusinessException("下发要货计划失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

    @ApiOperation(value = "下发到货")
    @PostMapping(value = "arrived")
    @BusinessMonitorLog(businessCode = "要货计划下发到货", moduleCode = "MRP", businessFrequency = "DAY")
    public BaseResponse<Void> arrived(@RequestBody List<String> ids) {
        return materialPlanNeedService.arrived(ids);
    }

    @ApiOperation(value = "计划员确认")
    @PostMapping(value = "plannerConfirm")
    public BaseResponse<Void> plannerConfirm(@RequestBody List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        return materialPlanNeedService.plannerConfirm(materialPlanNeedDTOList);
    }


    @ApiOperation(value = "重置要货计划序列号")
    @GetMapping(value = "resetPlanNeedSerialNumber")
    public BaseResponse<Void> resetPlanNeedSerialNumber() {
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MDS.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            return BaseResponse.error( "租户下不存在MPS模块信息");
        }
        for (Scenario scenario : scenarios) {
            // 获取承运商数据
            List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(scenario.getDataBaseName(), new HashMap<>());
            Set<String> syncKeys = redisUtil.keys("*"+ MaterialRedisConstants.PLAN_NEED_SERIAL_NUMBER_KEY);
            for (String key : syncKeys) {
                redisUtil.delete(key);
            }
            for (SupplierVO supplierVO : supplierVOS) {
                redisUtil.set(String.join("#", supplierVO.getSupplierCode(), MaterialRedisConstants.PLAN_NEED_SERIAL_NUMBER_KEY), 0, 0);
            }
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "拆单")
    @PostMapping(value = "splitTheOrder")
    public BaseResponse<Void> splitTheOrder(@RequestBody List<MaterialPlanNeedDTO> materialPlanNeedDTOList) {
        return materialPlanNeedService.splitTheOrder(materialPlanNeedDTOList);
    }

    @ApiOperation(value = "定时任务下发要货计划")
    @GetMapping(value = "issueJon")
    public BaseResponse<Void> issueJon() {
        materialPlanNeedService.issueJob();
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
