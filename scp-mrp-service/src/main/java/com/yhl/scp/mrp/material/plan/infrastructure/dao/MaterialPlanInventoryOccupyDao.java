package com.yhl.scp.mrp.material.plan.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryOccupyPO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryOccupyVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanInventoryOccupyDao</code>
 * <p>
 * 库存占用明细DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-14 09:49:38
 */
public interface MaterialPlanInventoryOccupyDao extends BaseDao<MaterialPlanInventoryOccupyPO, MaterialPlanInventoryOccupyVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link MaterialPlanInventoryOccupyVO}
     */
    List<MaterialPlanInventoryOccupyVO> selectVOByParams(@Param("params") Map<String, Object> params);

    void deleteByVersionId(@Param("materialPlanVersionId")String materialPlanVersionId);

    void deleteByVersionIdAndProductCode(@Param("materialPlanVersionId")String materialPlanVersionId, @Param("productCode")String productCode);

    void deleteByProductCodes(@Param("planUserProductCodeList") List<String> planUserProductCodeList);

    void deleteAll();
}
