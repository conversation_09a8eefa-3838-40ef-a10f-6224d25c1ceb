package com.yhl.scp.mrp.excel.service.impl;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.excel.write.handler.WriteHandler;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.model.handler.DropDownHandler;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.common.excel.service.ProcessEnumsService;
import com.yhl.scp.common.vo.SimpleVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.convertor.MaterialArrivalTrackingConvertor;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.infrastructure.dao.MaterialArrivalTrackingDao;
import com.yhl.scp.mrp.material.arrival.infrastructure.po.MaterialArrivalTrackingPO;
import com.yhl.scp.mrp.material.arrival.service.MaterialArrivalTrackingService;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName MaterialArrivalTrackingExcelService
 * @Description TODO
 * @Date 2025-04-07 16:05:35
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Service
public class MaterialArrivalTrackingExcelService extends AbstractExcelService<MaterialArrivalTrackingDTO, MaterialArrivalTrackingPO, MaterialArrivalTrackingVO> {

    @Resource
    private MaterialArrivalTrackingService materialArrivalTrackingService;

    @Resource
    private MaterialArrivalTrackingDao materialArrivalTrackingDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    ProcessEnumsService<MaterialArrivalTrackingDTO> processEnumsService;

    @Override
    public BaseDao<MaterialArrivalTrackingPO, MaterialArrivalTrackingVO> getBaseDao() {
        return materialArrivalTrackingDao;
    }

    @Override
    public Function<MaterialArrivalTrackingDTO, MaterialArrivalTrackingPO> getDTO2POConvertor() {
        return MaterialArrivalTrackingConvertor.INSTANCE::dto2Po;
    }

    @Override
    public Class<MaterialArrivalTrackingDTO> getDTOClass() {
        return MaterialArrivalTrackingDTO.class;
    }

    @Override
    public BaseService<MaterialArrivalTrackingDTO, MaterialArrivalTrackingVO> getBaseService() {
        return materialArrivalTrackingService;
    }

    @Override
    protected void fillIdForUpdateData(List<MaterialArrivalTrackingDTO> list, Map<String, MaterialArrivalTrackingPO> map) {
        for (MaterialArrivalTrackingDTO dto : list) {
            MaterialArrivalTrackingPO materialArrivalTrackingPO = map.get(String.join("#", dto.getMaterialCode(), dto.getSupplierCode(), DateUtils.dateToString(dto.getRequireDate())));
            if (materialArrivalTrackingPO != null){
                dto.setId(materialArrivalTrackingPO.getId());
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<MaterialArrivalTrackingPO> prepareData(List<MaterialArrivalTrackingDTO> list) {
        //查询已存在数据
        List<MaterialArrivalTrackingPO> materialArrivalTrackingPOList = materialArrivalTrackingDao.selectByParams(new HashMap<>());
        List<String> uniqueKeys = ListUtil.of("materialCode", "supplierCode", "requireDate");
        List<String> foreignKeys = new ArrayList<>();
        Map<String, List<SimpleVO>> foreignDataMap = new HashMap<>();

        // 校验权限
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(com.google.common.collect.Lists.newArrayList("product_code"))
                .queryParam(ImmutableMap.of("materialPlanner", SystemHolder.getUserId()))
                .build();
        List<NewProductStockPointVO> productStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(productStockPointVOList)) {
            throw new BusinessException("您没有权限操作该数据");
        }
        List<String> materialPlannerProductCodeList = productStockPointVOList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());

        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("productCodes", list.stream().map(MaterialArrivalTrackingDTO::getMaterialCode).distinct().collect(Collectors.toList()));
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), queryMap);
        List<SupplierVO> supplierVOS = newMdsFeign.selectSupplierByParams(SystemHolder.getScenario(), queryMap);
        Map<String, NewProductStockPointVO> pointVOMap = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (o1, o2) -> o1));
        Map<String, SupplierVO> supplierVOMap = supplierVOS.stream().collect(Collectors.toMap(SupplierVO::getSupplierCode, Function.identity(), (o1, o2) -> o1));

        for (MaterialArrivalTrackingDTO dto : list) {
            NewProductStockPointVO productStockPointVO = pointVOMap.get(dto.getMaterialCode());
            if (productStockPointVO == null){
                throw new BusinessException("未找到对应物料：" + dto.getMaterialCode());
            }
            if (!materialPlannerProductCodeList.contains(dto.getMaterialCode())){
                throw new BusinessException("您没有权限操作该数据" + dto.getMaterialCode());
            }
            dto.setMaterialName(productStockPointVO.getProductName());
            dto.setSupplierName(supplierVOMap.get(dto.getSupplierCode()) != null ? supplierVOMap.get(dto.getSupplierCode()).getSupplierName() : null);
        }
        List<SimpleVO> productSimpleVOS = CollectionUtils.isEmpty(newProductStockPointVOS) ?
                Lists.newArrayList() :
                newProductStockPointVOS.stream().map(x -> {
                    SimpleVO simpleVO = new SimpleVO();
                    simpleVO.setId(x.getProductCode());
                    simpleVO.setCode(x.getProductCode());
                    return simpleVO;
                }).collect(Collectors.toList());
        foreignDataMap.put("materialCode", productSimpleVOS);
        Map<String, MaterialArrivalTrackingPO> existingDataMap = materialArrivalTrackingPOList.stream()
                .collect(Collectors.toMap(item -> String.join("#", item.getMaterialCode(), item.getSupplierCode(), DateUtils.dateToString(item.getRequireDate())), Function.identity(), (k1, k2) -> k1));
        return ImportRelatedDataHolder.<MaterialArrivalTrackingPO>builder()
                .existingData(materialArrivalTrackingPOList)
                .mainKeys(uniqueKeys)
                .foreignKeys(foreignKeys)
                .foreignDataMap(foreignDataMap)
                .existingDataMap(existingDataMap)
                .build();
    }

    @Override
    protected List<WriteHandler> getCustomizedHandlers(Class clazz, Map<String, Integer> headMap) {
        List<WriteHandler> list = new ArrayList<>();
        List<WriteHandler> customizedHandlers = super.getCustomizedHandlers(clazz, headMap);
        list.addAll(customizedHandlers);
        DropDownHandler dropDownHandler = new DropDownHandler(clazz, headMap, ipsFeign);
        list.add(dropDownHandler);
        return list;
    }

    @Override
    protected void processEnums(List<MaterialArrivalTrackingDTO> materialArrivalTrackingDTOS) {
        super.processEnums(materialArrivalTrackingDTOS);
        processEnumsService.processEnums(materialArrivalTrackingDTOS);
    }
}
