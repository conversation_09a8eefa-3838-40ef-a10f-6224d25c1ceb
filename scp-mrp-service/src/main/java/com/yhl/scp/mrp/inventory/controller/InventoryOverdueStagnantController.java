package com.yhl.scp.mrp.inventory.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.inventory.dto.InventoryOverdueStagnantDTO;
import com.yhl.scp.mrp.inventory.service.InventoryOverdueStagnantService;
import com.yhl.scp.mrp.inventory.vo.InventoryOverdueStagnantVO;
import com.yhl.scp.mrp.overdueInventory.dto.OverdueStagnantInventoryParamDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>InventoryOverdueStagnantController</code>
 * <p>
 * 超期呆滞库存（历史）控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:48:25
 */
@Slf4j
@Api(tags = "超期呆滞库存（历史）控制器")
@RestController
@RequestMapping("inventoryOverdueStagnant")
public class InventoryOverdueStagnantController extends BaseController {

    @Resource
    private InventoryOverdueStagnantService inventoryOverdueStagnantService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryOverdueStagnantVO>> page() {
        List<InventoryOverdueStagnantVO> inventoryOverdueStagnantList = inventoryOverdueStagnantService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<InventoryOverdueStagnantVO> pageInfo = new PageInfo<>(inventoryOverdueStagnantList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryOverdueStagnantDTO inventoryOverdueStagnantDTO) {
        return inventoryOverdueStagnantService.doCreate(inventoryOverdueStagnantDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryOverdueStagnantDTO inventoryOverdueStagnantDTO) {
        return inventoryOverdueStagnantService.doUpdate(inventoryOverdueStagnantDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryOverdueStagnantService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<InventoryOverdueStagnantVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryOverdueStagnantService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "计算")
    @PostMapping(value = "calculate")
    @BusinessMonitorLog(businessCode = "超期呆滞库存计算", moduleCode = "MRP", businessFrequency = "WEEK")
    public BaseResponse<Void> publish(@RequestBody OverdueStagnantInventoryParamDTO overdueStagnantInventoryParamDTO) {
        String redisKey = MaterialRedisConstants.INVENTORY_OVERDUE_STAGNANT_PUBLISH;

        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有超期呆滞库存正在计算，请等待计算完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 60);

            // 发布
            inventoryOverdueStagnantService.doCalculate(SystemHolder.getScenario(), overdueStagnantInventoryParamDTO);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("超期呆滞库存计算失败", e);
            throw new BusinessException("超期呆滞库存计算失败,{0}", e.getLocalizedMessage());
        } finally {
            // 释放key
            redisUtil.delete(redisKey);
        }
    }

}
