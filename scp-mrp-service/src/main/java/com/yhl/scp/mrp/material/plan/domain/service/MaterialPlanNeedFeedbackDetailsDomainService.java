package com.yhl.scp.mrp.material.plan.domain.service;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanNeedFeedbackDetailsDO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanNeedFeedbackDetailsDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanNeedFeedbackDetailsPO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPlanNeedFeedbackDetailsDomainService</code>
 * <p>
 * 要货计划反馈明细领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03 14:31:24
 */
@Service
public class MaterialPlanNeedFeedbackDetailsDomainService {

    @Resource
    private MaterialPlanNeedFeedbackDetailsDao materialPlanNeedFeedbackDetailsDao;

    /**
     * 数据校验
     *
     * @param materialPlanNeedFeedbackDetailsDO 领域对象
     */
    public void validation(MaterialPlanNeedFeedbackDetailsDO materialPlanNeedFeedbackDetailsDO) {
        checkNotNull(materialPlanNeedFeedbackDetailsDO);
        checkUniqueCode(materialPlanNeedFeedbackDetailsDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param materialPlanNeedFeedbackDetailsDO 领域对象
     */
    private void checkNotNull(MaterialPlanNeedFeedbackDetailsDO materialPlanNeedFeedbackDetailsDO) {}

    /**
     * 唯一性校验
     *
     * @param materialPlanNeedFeedbackDetailsDO 领域对象
     */
    private void checkUniqueCode(MaterialPlanNeedFeedbackDetailsDO materialPlanNeedFeedbackDetailsDO) {}

}
