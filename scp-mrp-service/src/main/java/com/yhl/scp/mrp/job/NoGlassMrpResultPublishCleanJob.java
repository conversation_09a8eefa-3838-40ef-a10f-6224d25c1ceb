package com.yhl.scp.mrp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mrp.material.arrival.service.MaterialPurchaseStorageService;
import com.yhl.scp.mrp.published.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName NoGlassMrpResultPublishCleanJob
 * @Description TODO
 * @Date 2025-04-16 09:20:25
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
@Component
@Slf4j
public class NoGlassMrpResultPublishCleanJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private MaterialPlanPublishedVersionService materialPlanPublishedVersionService;

    @XxlJob("noGlassMrpResultPublishCleanJob")
    private ReturnT<String> noGlassMrpResultPublishCleanJob() {
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.MRP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            try {
                DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
                materialPlanPublishedVersionService.doCleanNoGlassData();
            } catch (Exception e) {
                XxlJobHelper.log("租户下不存在该数据", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        return ReturnT.SUCCESS;
    }
}
