<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.material.purchase.infrastructure.dao.MaterialPurchaseRequestDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO">
        <!--@Table mrp_material_purchase_request-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="purchase_request_code" jdbcType="VARCHAR" property="purchaseRequestCode"/>
        <result column="purchase_request_line_id" jdbcType="VARCHAR" property="purchaseRequestLineId"/>
        <result column="purchase_request_line_code" jdbcType="VARCHAR" property="purchaseRequestLineCode"/>
        <result column="purchase_order_code" jdbcType="VARCHAR" property="purchaseOrderCode"/>
        <result column="purchase_order_line_code" jdbcType="VARCHAR" property="purchaseOrderLineCode"/>
        <result column="approval_status" jdbcType="VARCHAR" property="approvalStatus"/>
        <result column="cancel_flag" jdbcType="VARCHAR" property="cancelFlag"/>
        <result column="closed_code" jdbcType="VARCHAR" property="closedCode"/>
        <result column="whether_matches" jdbcType="VARCHAR" property="whetherMatches"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.material.purchase.vo.MaterialPurchaseRequestVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,org_id,purchase_request_code,purchase_request_line_id,purchase_request_line_code
        ,purchase_order_code,purchase_order_line_code,approval_status,cancel_flag,closed_code,whether_matches
        ,remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.orgId != null and params.orgId != ''">
                and org_id = #{params.orgId,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseRequestCode != null and params.purchaseRequestCode != ''">
                and purchase_request_code = #{params.purchaseRequestCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseRequestLineId != null and params.purchaseRequestLineId != ''">
                and purchase_request_line_id = #{params.purchaseRequestLineId,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseRequestLineCode != null and params.purchaseRequestLineCode != ''">
                and purchase_request_line_code = #{params.purchaseRequestLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderCode != null and params.purchaseOrderCode != ''">
                and purchase_order_code = #{params.purchaseOrderCode,jdbcType=VARCHAR}
            </if>
            <if test="params.purchaseOrderLineCode != null and params.purchaseOrderLineCode != ''">
                and purchase_order_line_code = #{params.purchaseOrderLineCode,jdbcType=VARCHAR}
            </if>
            <if test="params.approvalStatus != null and params.approvalStatus != ''">
                and approval_status = #{params.approvalStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.cancelFlag != null and params.cancelFlag != ''">
                and cancel_flag = #{params.cancelFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.closedCode != null and params.closedCode != ''">
                and closed_code = #{params.closedCode,jdbcType=VARCHAR}
            </if>
            <if test="params.whetherMatches != null and params.whetherMatches != ''">
                and whether_matches = #{params.whetherMatches,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.startModifyTime != null">
                and modify_time <![CDATA[ >= ]]> #{params.startModifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endModifyTime != null">
                and modify_time <![CDATA[ <= ]]> #{params.endModifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.combineKeys != null and params.combineKeys.size() > 0">
                and CONCAT_WS('_',purchase_request_code,purchase_request_line_code) in
                <foreach collection="params.combineKeys" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_purchase_request
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_purchase_request
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_material_purchase_request
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mrp_material_purchase_request
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_mrp_material_purchase_request
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByMaterialPurchaseIds" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mrp_material_purchase_request
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                org_id = #{item.orgId,jdbcType=VARCHAR} and purchase_request_code =
                #{item.applicationNo,jdbcType=VARCHAR}
                and purchase_request_line_code = #{item.lineNumber,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mrp_material_purchase_request(
        id,
        org_id,
        purchase_request_code,
        purchase_request_line_id,
        purchase_request_line_code,
        purchase_order_code,
        purchase_order_line_code,
        approval_status,
        cancel_flag,
        closed_code,
        whether_matches,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{purchaseRequestCode,jdbcType=VARCHAR},
        #{purchaseRequestLineId,jdbcType=VARCHAR},
        #{purchaseRequestLineCode,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{approvalStatus,jdbcType=VARCHAR},
        #{cancelFlag,jdbcType=VARCHAR},
        #{closedCode,jdbcType=VARCHAR},
        #{whetherMatches,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO">
        insert into mrp_material_purchase_request(
        id,
        org_id,
        purchase_request_code,
        purchase_request_line_id,
        purchase_request_line_code,
        purchase_order_code,
        purchase_order_line_code,
        approval_status,
        cancel_flag,
        closed_code,
        whether_matches,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{purchaseRequestCode,jdbcType=VARCHAR},
        #{purchaseRequestLineId,jdbcType=VARCHAR},
        #{purchaseRequestLineCode,jdbcType=VARCHAR},
        #{purchaseOrderCode,jdbcType=VARCHAR},
        #{purchaseOrderLineCode,jdbcType=VARCHAR},
        #{approvalStatus,jdbcType=VARCHAR},
        #{cancelFlag,jdbcType=VARCHAR},
        #{closedCode,jdbcType=VARCHAR},
        #{whetherMatches,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mrp_material_purchase_request(
        id,
        org_id,
        purchase_request_code,
        purchase_request_line_id,
        purchase_request_line_code,
        purchase_order_code,
        purchase_order_line_code,
        approval_status,
        cancel_flag,
        closed_code,
        whether_matches,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.orgId,jdbcType=VARCHAR},
        #{entity.purchaseRequestCode,jdbcType=VARCHAR},
        #{entity.purchaseRequestLineId,jdbcType=VARCHAR},
        #{entity.purchaseRequestLineCode,jdbcType=VARCHAR},
        #{entity.purchaseOrderCode,jdbcType=VARCHAR},
        #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
        #{entity.approvalStatus,jdbcType=VARCHAR},
        #{entity.cancelFlag,jdbcType=VARCHAR},
        #{entity.closedCode,jdbcType=VARCHAR},
        #{entity.whetherMatches,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mrp_material_purchase_request(
        id,
        org_id,
        purchase_request_code,
        purchase_request_line_id,
        purchase_request_line_code,
        purchase_order_code,
        purchase_order_line_code,
        approval_status,
        cancel_flag,
        closed_code,
        whether_matches,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.orgId,jdbcType=VARCHAR},
        #{entity.purchaseRequestCode,jdbcType=VARCHAR},
        #{entity.purchaseRequestLineId,jdbcType=VARCHAR},
        #{entity.purchaseRequestLineCode,jdbcType=VARCHAR},
        #{entity.purchaseOrderCode,jdbcType=VARCHAR},
        #{entity.purchaseOrderLineCode,jdbcType=VARCHAR},
        #{entity.approvalStatus,jdbcType=VARCHAR},
        #{entity.cancelFlag,jdbcType=VARCHAR},
        #{entity.closedCode,jdbcType=VARCHAR},
        #{entity.whetherMatches,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO">
        update mrp_material_purchase_request set
        org_id = #{orgId,jdbcType=VARCHAR},
        purchase_request_code = #{purchaseRequestCode,jdbcType=VARCHAR},
        purchase_request_line_id = #{purchaseRequestLineId,jdbcType=VARCHAR},
        purchase_request_line_code = #{purchaseRequestLineCode,jdbcType=VARCHAR},
        purchase_order_code = #{purchaseOrderCode,jdbcType=VARCHAR},
        purchase_order_line_code = #{purchaseOrderLineCode,jdbcType=VARCHAR},
        approval_status = #{approvalStatus,jdbcType=VARCHAR},
        cancel_flag = #{cancelFlag,jdbcType=VARCHAR},
        closed_code = #{closedCode,jdbcType=VARCHAR},
        whether_matches = #{whetherMatches,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mrp.material.purchase.infrastructure.po.MaterialPurchaseRequestPO">
        update mrp_material_purchase_request
        <set>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestCode != null and item.purchaseRequestCode != ''">
                purchase_request_code = #{item.purchaseRequestCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestLineId != null and item.purchaseRequestLineId != ''">
                purchase_request_line_id = #{item.purchaseRequestLineId,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestLineCode != null and item.purchaseRequestLineCode != ''">
                purchase_request_line_code = #{item.purchaseRequestLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.approvalStatus != null and item.approvalStatus != ''">
                approval_status = #{item.approvalStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.cancelFlag != null and item.cancelFlag != ''">
                cancel_flag = #{item.cancelFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.closedCode != null and item.closedCode != ''">
                closed_code = #{item.closedCode,jdbcType=VARCHAR},
            </if>
            <if test="item.whetherMatches != null and item.whetherMatches != ''">
                whether_matches = #{item.whetherMatches,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mrp_material_purchase_request
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_request_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseRequestCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_request_line_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseRequestLineId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_request_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseRequestLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="purchase_order_line_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.purchaseOrderLineCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="approval_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.approvalStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cancel_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cancelFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="closed_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.closedCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="whether_matches = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.whetherMatches,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mrp_material_purchase_request 
        <set>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestCode != null and item.purchaseRequestCode != ''">
                purchase_request_code = #{item.purchaseRequestCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestLineId != null and item.purchaseRequestLineId != ''">
                purchase_request_line_id = #{item.purchaseRequestLineId,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseRequestLineCode != null and item.purchaseRequestLineCode != ''">
                purchase_request_line_code = #{item.purchaseRequestLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderCode != null and item.purchaseOrderCode != ''">
                purchase_order_code = #{item.purchaseOrderCode,jdbcType=VARCHAR},
            </if>
            <if test="item.purchaseOrderLineCode != null and item.purchaseOrderLineCode != ''">
                purchase_order_line_code = #{item.purchaseOrderLineCode,jdbcType=VARCHAR},
            </if>
            <if test="item.approvalStatus != null and item.approvalStatus != ''">
                approval_status = #{item.approvalStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.cancelFlag != null and item.cancelFlag != ''">
                cancel_flag = #{item.cancelFlag,jdbcType=VARCHAR},
            </if>
            <if test="item.closedCode != null and item.closedCode != ''">
                closed_code = #{item.closedCode,jdbcType=VARCHAR},
            </if>
            <if test="item.whetherMatches != null and item.whetherMatches != ''">
                whether_matches = #{item.whetherMatches,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mrp_material_purchase_request where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mrp_material_purchase_request where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
