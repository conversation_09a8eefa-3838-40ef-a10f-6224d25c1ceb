package com.yhl.scp.mrp.material.plan.service.impl;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepInputVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mds.substitution.vo.ProductSubstitutionRelationshipVO;
import com.yhl.scp.mrp.enums.ArrivalStatusEnum;
import com.yhl.scp.mrp.enums.ArrivalTrackingDataSourceEnum;
import com.yhl.scp.mrp.enums.DemandPatternEnum;
import com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO;
import com.yhl.scp.mrp.inventory.vo.SafetyInventoryVO;
import com.yhl.scp.mrp.extension.material.vo.MaterialArrivalTrackingVO;
import com.yhl.scp.mrp.material.arrival.dto.MaterialArrivalTrackingDTO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialNeedDateRecommendVO;
import com.yhl.scp.mrp.material.plan.domain.entity.MrpAllocateResultDO;
import com.yhl.scp.mrp.material.plan.dto.*;
import com.yhl.scp.mrp.material.plan.enums.*;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MrpResultPO;
import com.yhl.scp.mrp.material.plan.service.AbstractMrpService;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName NoGlassMrpServiceImpl
 * @Description TODO
 * @Date 2024-10-25 10:20:27
 * <AUTHOR>
 * @Copyright 瑞之泽
 * @Version 1.0
 */
@Service
@Slf4j
public class NoGlassMrpServiceImpl extends AbstractMrpService {

    /**
     * 查询物料替代关系
     * @return
     */
//    protected List<ProductSubstitutionRelationshipVO> getProductSubstitutionRelationshipVOList() {
//        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList = mdsFeign.getProductSubstitutionRelationshipVOByParams(Maps.newHashMap());
//        if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOList)) {
//            return null;
//        }
//        return productSubstitutionRelationshipVOList.stream().filter(mrpDemandDTO -> noGlassMaterialTypeList.stream().anyMatch(materialType -> mrpDemandDTO.getProductClassify().matches(materialType + ".*")) || StringUtils.equals(MrpDemandSourceEnum.ZZK.getCode(), mrpDemandDTO.getProductClassify())).collect(Collectors.toList());
//    }

    @Override
    protected MrpResultPO doMrpCalc(MrpContextDTO mrpContextDTO) {
        Set<String> errorMegList = new HashSet<>();
        MrpResultPO mrpResultPO = new MrpResultPO();
        Date nowDate = mrpContextDTO.getMrpCalcDate();
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();
        // 需要持久化的数据
        // 版本数据
        String newMaterialVersionId = UUIDUtil.getUUID();
        MaterialPlanVersionDTO materialPlanVersionDTO = new MaterialPlanVersionDTO();
        materialPlanVersionDTO.setId(newMaterialVersionId);

        // 获取版本
        materialPlanVersionDTO = getNewMaterialPlanVersionDTO(materialPlanVersionDTO);
        boolean isNewVersion = true;
        if (!StringUtils.equals(newMaterialVersionId, materialPlanVersionDTO.getId())) {
            isNewVersion = false;
        }

        // 库存推移表数据
        List<NoGlassInventoryShiftDataDTO> insertNoGlassInventoryShiftDataList = new ArrayList<>();
        List<NoGlassInventoryShiftDetailDTO> insertNoGlassInventoryShiftDetailList = new ArrayList<>();
        // 替代计划数据
        List<MaterialPlanReplacePO> insertMaterialPlanReplacePOList = new ArrayList<>();
        // 毛需求数据
        List<MaterialPlanInventoryShiftDemandDTO> insertNoGlassDemandList = new ArrayList<>();
        // 供应数据
        List<MaterialPlanInventoryShiftSupplyDTO> insertNoGlassSupplyList = new ArrayList<>();

        List<NewProductStockPointVO> newProductStockPointVOList = mrpContextDTO.getNewProductStockPointVOList();
        Map<String, NewProductStockPointVO> productStockPointVOMapOfCode = newProductStockPointVOList.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        // 获取所有毛需求数据
        List<MrpDemandDTO> mrpDemandDTOList = mrpContextDTO.getMrpDemandList();
        //获取辅料子库存
        List<String> auxiliarySubInventoryList = mrpContextDTO.getAuxiliarySubInventoryList();
        // 过滤出PVB、B类的物料需求
        // 使用正则表达式进行模糊匹配
        mrpDemandDTOList = getCaleMrpDemandList(mrpDemandDTOList);
        // 获取替代料数据
//        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOList = mrpContextDTO.getProductSubstitutionRelationshipVOList();
        // 替代料按照物品进行分组
        Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionRelationshipMapOfProductCode = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(productSubstitutionRelationshipVOList)) {
//            productSubstitutionRelationshipMapOfProductCode = productSubstitutionRelationshipVOList.stream().collect(Collectors.groupingBy(ProductSubstitutionRelationshipVO::getRawProductCode));
//        }

        // 推移计算的毛需求(包含替代料转换的)
        List<MrpDemandDTO> useMrpDemandDTOList = mrpDemandDTOList;
//        Map<String, String> noGlassDemandProductCodeMap = useMrpDemandDTOList.stream().collect(Collectors.toMap(MrpDemandDTO::getProductCode, MrpDemandDTO::getDemandSource, (k1, k2) -> k2));

        // 生成替代料数据
        //        substituteReplaceAllocate(mrpDemandMapOfProductCode,
        //        productSubstitutionRelationshipMapOfProductCode, useMrpDemandDTOList,
        //        insertMaterialPlanReplacePOList);
        // useMrpDemandDTOList按照productCode去重

        // demand、supply数据进行分组
        Map<String, Map<String, List<MrpDemandDTO>>> mrpDemandGroupOfProductCode = useMrpDemandDTOList.stream()
                .collect(Collectors.groupingBy(MrpDemandDTO::getProductCode,
                        Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(),
                                DateUtils.COMMON_DATE_STR3))));

        // 采购计划需求
        List<MrpSupplyDTO> planPurchaseSupplyAllList = mrpContextDTO.getPlanPurchaseSupplyList();
        // 在途订单需求
        List<MrpSupplyDTO> transitOrderSupplyAllList = mrpContextDTO.getTransitOrderSupplyList();

        List<MrpSupplyDTO> mrpSupplyDTOAllList = new ArrayList<>();
        mrpSupplyDTOAllList.addAll(planPurchaseSupplyAllList);
        mrpSupplyDTOAllList.addAll(transitOrderSupplyAllList);

        // 供应先根据物料进行分组
        Map<String, List<MrpSupplyDTO>> transitOrderSupplyGroupOfProductCode = transitOrderSupplyAllList.stream()
                .collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode));

        Map<String, List<MrpSupplyDTO>> planPurchaseSupplyGroupOfProductCode = planPurchaseSupplyAllList.stream()
                .collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode));

        Map<String, String> productCategoryMap = new HashMap<>();
        Map<String, String> demandProductCategoryMap = useMrpDemandDTOList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getProductClassify()))
                .collect(Collectors.toMap(MrpDemandDTO::getProductCode, item -> item.getProductClassify().startsWith("RA.V") ? "PVB" : "B", (k1, k2) -> k2));

        Map<String, String> supplyProductCategoryMap = mrpSupplyDTOAllList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getProductClassify()))
                .collect(Collectors.toMap(MrpSupplyDTO::getProductCode, item -> item.getProductClassify().startsWith("RA.V") ? "PVB" : "B", (k1, k2) -> k2));
        if (MapUtils.isNotEmpty(demandProductCategoryMap)) {
            productCategoryMap.putAll(demandProductCategoryMap);
        }
        if (MapUtils.isNotEmpty(supplyProductCategoryMap)) {
            productCategoryMap.putAll(supplyProductCategoryMap);
        }

        // mrp需要计算的物料
        List<String> mrpCalcProductCodeList = getNeedMrpCalcProductCodeList(useMrpDemandDTOList, mrpSupplyDTOAllList, planUserProductCodeList);

        // 获取材料与供应商关系
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode = mrpContextDTO.getSupplierPurchaseVOMapOfMaterialCode();
        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = mrpContextDTO.getSupplierPurchaseRatioVOGroup();
        Map<String, SupplierVO> supplierVOMapOfId = mrpContextDTO.getSupplierVOMapOfId();

        // 获取安全库存天数
        List<SafetyInventoryVO> safetyInventoryVOList = mrpContextDTO.getSafetyInventoryVOList();
        Map<String, SafetyInventoryVO> safetyInventoryVOMapProductCode = mrpContextDTO.getSafetyInventoryVOMapProductCode();
        String errorMessage = "%s没有维护安全库存";
        if (CollectionUtils.isEmpty(safetyInventoryVOList)) {
            String collect = mrpCalcProductCodeList.stream().distinct().sorted().collect(Collectors.joining(","));
            errorMegList.add(String.format(errorMessage, collect));
        } else {
            List<String> collect = safetyInventoryVOList.stream().map(SafetyInventoryVO::getMaterialCode).distinct().collect(Collectors.toList());
            List<String> diffSection = new ArrayList<>(com.yhl.platform.common.utils.CollectionUtils.getDiffSection(mrpCalcProductCodeList, collect));
            if (CollectionUtils.isNotEmpty(diffSection)) {
                String collect1 = diffSection.stream().distinct().sorted().collect(Collectors.joining(","));
                errorMegList.add(String.format(errorMessage, collect1));
            }
        }
        // 获取期初库存
        Map<String, List<InventoryBatchDetailVO>> openingInventoryMap = mrpContextDTO.getOpeningInventoryMap();

        // 获取当前日期到6个月、8个月后的日期
        List<Date> inventoryShiftDateSixMonthList = getInventoryShiftDateList(new Date(), 180);
        List<Date> inventoryShiftDateEightMonthList = getInventoryShiftDateList(new Date(), 240);

        for (String productCode : mrpCalcProductCodeList) {
            // 每天对应的期末库存
            Map<Integer, BigDecimal> endingInventoryMapOfDateIndex = new HashMap<>();
            // 获取物料大类。PVB还是B类
            String productCategory = productCategoryMap.get(productCode);

            // 获取该物品的demand数据
            Map<String, List<MrpDemandDTO>> mrpDemandMapOfDateStr = mrpDemandGroupOfProductCode.get(productCode);

            // 获取该物品的supply数据
            List<MrpSupplyDTO> planPurchaseSupplyListOfProductCode = planPurchaseSupplyGroupOfProductCode.get(productCode);
            List<MrpSupplyDTO> transitOrderSupplyListOfProductCode = transitOrderSupplyGroupOfProductCode.get(productCode);


            // 获取材料与供应商关系
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMapOfMaterialCode.getOrDefault(productCode, new MaterialSupplierPurchaseVO());
            BigDecimal minOrderQty = materialSupplierPurchaseVO.getMinOrderQty();
            BigDecimal packageLot = materialSupplierPurchaseVO.getPackageLot();
            // 获取关联的供应商数据
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.getOrDefault(materialSupplierPurchaseVO.getId(), new ArrayList<>());
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
                // 为空，虚拟出供应商采购关系
                supplierPurchaseRatioVOList.add(new SupplierPurchaseRatioVO());
            }

            // 只考虑一个供应商，不考虑供货比例
            SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(0);

            String supplierId = supplierPurchaseRatioVO.getSupplierId();
            SupplierVO supplierVO = supplierVOMapOfId.get(supplierId);

            List<Date> transferDateList = new ArrayList<>();

            // 优先从MaterialSupplierPurchaseVO获取收货日历信息，为空则从SupplierVO获取
            String receivingWeekDay;
            String receivingMonthDay;

            receivingWeekDay = materialSupplierPurchaseVO.getReceivingWeekDay();
            receivingMonthDay = materialSupplierPurchaseVO.getReceivingMonthDay();

            // 如果MaterialSupplierPurchaseVO中没有设置，则尝试从SupplierVO获取（周和月都没有才取）
            if (StringUtils.isBlank(receivingWeekDay) && StringUtils.isBlank(receivingMonthDay) && null != supplierVO) {
                receivingWeekDay = supplierVO.getReceivingWeekDay();
                receivingMonthDay = supplierVO.getReceivingMonthDay();
            }

            // 周和月都有，只看周的
            if (StringUtils.isNotEmpty(receivingWeekDay) && StringUtils.isNotEmpty(receivingMonthDay)) {
                receivingMonthDay = null;
            }

            // 解析收货日历信息
            List<String> weekDayList = new ArrayList<>();
            List<String> monthDayList = new ArrayList<>();
            if (StringUtils.isNotBlank(receivingWeekDay)) {
                weekDayList.addAll(Arrays.asList(receivingWeekDay.split(",")));
            }
            if (StringUtils.isNotBlank(receivingMonthDay)) {
                monthDayList.addAll(Arrays.asList(receivingMonthDay.split(",")));
            }

            for (int i = 0; i < 365; i++) {
                Date transferDate = DateUtils.moveDay(nowDate, i);
                // 未维护收货日历，则每天都收货
                if (null == supplierVO ||
                        (StringUtils.isBlank(receivingWeekDay) && StringUtils.isBlank(receivingMonthDay)) ||

                        (StringUtils.isBlank(receivingMonthDay) && StringUtils.isNotBlank(receivingWeekDay) &&
                                weekDayList.contains(String.valueOf(DateUtils.getDayOfWeek(transferDate)))) ||

                        (StringUtils.isBlank(receivingWeekDay) && StringUtils.isNotBlank(receivingMonthDay) &&
                                monthDayList.contains(String.valueOf(DateUtils.getDayOfMonth(transferDate))))) {
                    transferDateList.add(transferDate);
                }
            }
            Collections.sort(transferDateList);

            if (CollectionUtils.isEmpty(transferDateList)) {
                log.info("supply{},weekDayList{},monthDayList{}", supplierVO, weekDayList, monthDayList);
            }

            if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                for (Date date : transferDateList) {
                    log.info("### 物料:{}, 合并日期:{}", productCode, DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3));
                }
            }

            List<Date> inventoryShiftDateList = inventoryShiftDateSixMonthList;
            // 默认计算周期为6个月,如果物料的订单下达提前期大于30天，则计算8个月
            if (materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() != null &&
                    materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().compareTo(new BigDecimal(30)) > 0) {
                inventoryShiftDateList = inventoryShiftDateEightMonthList;
            }
            Collections.sort(inventoryShiftDateList);
            // 获取锁定期
            int lockPeriod = 0;
            if (supplierPurchaseVOMapOfMaterialCode.containsKey(productCode)) {
                if (StringUtils.equals(DemandPatternEnum.PO.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    lockPeriod = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() == null ? 0 : materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue();
                }
                if (StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    lockPeriod = materialSupplierPurchaseVO.getRequestCargoPlanLockDay() == null ? 0 : materialSupplierPurchaseVO.getRequestCargoPlanLockDay().intValue();
                }
            }
            // 锁定期最晚时间
            Date lockPeriodDate = DateUtils.moveDay(inventoryShiftDateList.get(0), lockPeriod != 0 ? lockPeriod - 1 : 0);
            // 锁定最晚时间时分秒为00:00:00
            lockPeriodDate = DateUtils.getDayFirstTime(lockPeriodDate);

            // 获取物料信息
            NewProductStockPointVO newProductStockPointVO = productStockPointVOMapOfCode.get(productCode);
            // 安全库存数据
            SafetyInventoryVO safetyInventoryVO = safetyInventoryVOMapProductCode.getOrDefault(productCode, new SafetyInventoryVO());
            // 获取最小库存天数
            BigDecimal minStockDay = safetyInventoryVO.getMinInventoryDays() == null ? BigDecimal.ZERO : safetyInventoryVO.getMinInventoryDays();
            // 获取标准安全库存天数
            BigDecimal standardStockDay = safetyInventoryVO.getGoalSafetyInventoryDays() == null ? BigDecimal.ZERO : safetyInventoryVO.getGoalSafetyInventoryDays();
            // 获取最大安全库存天数
            BigDecimal maxStockDay = safetyInventoryVO.getMaxInventoryDays() == null ? BigDecimal.ZERO : safetyInventoryVO.getMaxInventoryDays();

            // 获取该材料的期初库存
            BigDecimal openingInventory = BigDecimal.ZERO;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOList = openingInventoryMap.get(productCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)) {
                // 对inventoryBatchDetailVOList求和CurrentQuantity
                for (InventoryBatchDetailVO inventoryBatchDetailVO : inventoryBatchDetailVOList) {
                    openingInventory = openingInventory.add(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                }
            }

            // 原始期初
            BigDecimal originOpeningInventory = openingInventory;
            // 真实库存缺口
            BigDecimal realInventoryGap = BigDecimal.ZERO;

            // 循环推移日期
            List<NoGlassInventoryShiftDetailDTO> mergeNoGlassInventoryList = new ArrayList<>();
            int transferDateStartIndex = 0;
            int transferDateNextIndex = 1;
            boolean flag = true;
            boolean addIndexFlag = true;

            // 维护推移的data数据
            NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO = assembleNoGlassInventoryShiftDataDTO(productCode, newProductStockPointVO.getProductClassify(),
                    minStockDay, standardStockDay, maxStockDay, productCategory);

            // 维护本厂编码和车型数据
            if (MapUtils.isNotEmpty(mrpDemandMapOfDateStr)) {
                List<MrpDemandDTO> allMrpDemands = mrpDemandMapOfDateStr.values().stream()
                        .flatMap(List::stream)
                        .collect(Collectors.toList());
                List<String> vehicleModeCodeList = allMrpDemands.stream().map(MrpDemandDTO::getVehicleModeCode).distinct().collect(Collectors.toList());
                noGlassInventoryShiftDataDTO.setVehicleModeCode(String.join(",", vehicleModeCodeList));
            }
            insertNoGlassInventoryShiftDataList.add(noGlassInventoryShiftDataDTO);


            for (int i = 0; i < inventoryShiftDateList.size(); i++) {
                String materialPlanInventoryShiftId = UUIDUtil.getUUID();
                // 推移时间
                Date inventoryShiftDate = inventoryShiftDateList.get(i);
                Date beforeDate = DateUtils.moveDay(inventoryShiftDate, -1);

                // 获取当天的demand的数据
                List<MrpDemandDTO> dayMrpDemandList = new ArrayList<>();
                if (MapUtils.isNotEmpty(mrpDemandMapOfDateStr)) {
                    dayMrpDemandList = mrpDemandMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                }

                List<MrpSupplyDTO> planPurchaseSupplyList = new ArrayList<>();
                List<MrpSupplyDTO> transitOrderSupplyList = new ArrayList<>();
                List<MrpSupplyDTO> beforeDayPlanPurchaseSupplyList = new ArrayList<>();
                List<MrpSupplyDTO> beforeDaytransitOrderSupplyList = new ArrayList<>();


                // 过滤出在锁定期前的供应数据
                Date finalLockPeriodDate = lockPeriodDate;
                if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
                    planPurchaseSupplyListOfProductCode = planPurchaseSupplyListOfProductCode
                            .stream().filter(item ->
                                    item.getSupplyTime().compareTo(finalLockPeriodDate) <= 0 ||
                                            StringUtils.isNotBlank(item.getPurchaseOrderCode()) ||
                                    StringUtils.equals(item.getDataSource(), ArrivalTrackingDataSourceEnum.MATERIAL_PURCHASE.getCode()))
                            .collect(Collectors.toList());
                    // 获取当天的supply的数据
                    if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
                        Map<String, List<MrpSupplyDTO>> planPurchaseSupplyMapOfDateStr = planPurchaseSupplyListOfProductCode.stream()
                                .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                        planPurchaseSupplyList = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                        if (null != beforeDate){
                            beforeDayPlanPurchaseSupplyList = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(beforeDate, DateUtils.COMMON_DATE_STR3));
                        }
                    }
                }

                if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
                    transitOrderSupplyListOfProductCode = transitOrderSupplyListOfProductCode
                            .stream().filter(item ->
                                    item.getSupplyTime().compareTo(finalLockPeriodDate) <= 0 ||
                                    StringUtils.equals(item.getDataSource(), ArrivalTrackingDataSourceEnum.MATERIAL_PURCHASE.getCode()))
                            .collect(Collectors.toList());
                    // 获取当天的supply的数据
                    if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
                        Map<String, List<MrpSupplyDTO>> transitOrderSupplyMapOfDateStr = transitOrderSupplyListOfProductCode.stream()
                                .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                        transitOrderSupplyList = transitOrderSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                        if (null != beforeDate){
                            beforeDaytransitOrderSupplyList = transitOrderSupplyMapOfDateStr.get(DateUtils.dateToString(beforeDate, DateUtils.COMMON_DATE_STR3));
                        }
                    }
                }

                // 期末库存
                BigDecimal endingInventory;
                if (i != 0) {
                    // 期初库存 = 前1天的期末库存
                    endingInventory = endingInventoryMapOfDateIndex.get(i - 1);
                    openingInventory = endingInventory;
                    originOpeningInventory = realInventoryGap;
                }

                // 获取库存水位，计算公式
                // 标准安全库存天数为3,后面3天的毛需求量为100，200，300，则标准库存水位为600
                // 标准安全库存天数为1,后面1天的毛需求量为100，则最小库存水位为100
                BigDecimal minStockQuantity = BigDecimal.ZERO;
                BigDecimal standardStockQuantity = BigDecimal.ZERO;
                BigDecimal maxStockQuantity = BigDecimal.ZERO;
                if (MapUtils.isNotEmpty(mrpDemandMapOfDateStr)) {
                    minStockQuantity = caleInventoryWaterLevel(inventoryShiftDate, minStockDay, mrpDemandMapOfDateStr);
                    minStockQuantity = minStockQuantity.setScale(0, RoundingMode.HALF_UP);

                    standardStockQuantity = caleInventoryWaterLevel(inventoryShiftDate, standardStockDay, mrpDemandMapOfDateStr);
                    standardStockQuantity = standardStockQuantity.setScale(0, RoundingMode.HALF_UP);

                    maxStockQuantity = caleInventoryWaterLevel(inventoryShiftDate, maxStockDay, mrpDemandMapOfDateStr);
                    maxStockQuantity = maxStockQuantity.setScale(0, RoundingMode.HALF_UP);
                }

                // 计划调整量
                BigDecimal planAdjustQuantity = BigDecimal.ZERO;

                // 获取毛需求
                BigDecimal demandQuantity = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(dayMrpDemandList)) {
                    demandQuantity = dayMrpDemandList.stream().map(MrpDemandDTO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(0, RoundingMode.CEILING);
                }

                // 当日supply数据
                BigDecimal planPurchaseSupplyQuantity = calculateSupplyQuantitySum(planPurchaseSupplyList);
                BigDecimal transitOrderSupplyQuantity = calculateSupplyQuantitySum(transitOrderSupplyList);

                // 昨日 supply 数据
                BigDecimal beforeDayPlanPurchaseSupplyQuantity = calculateSupplyQuantitySum(beforeDayPlanPurchaseSupplyList);
                BigDecimal beforeDayTransitOrderSupplyQuantity = calculateSupplyQuantitySum(beforeDaytransitOrderSupplyList);

                // 总和计算
                BigDecimal supplyQuantitySum = planPurchaseSupplyQuantity.add(transitOrderSupplyQuantity);
                BigDecimal beforeDaySupplyQuantitySum = beforeDayPlanPurchaseSupplyQuantity.add(beforeDayTransitOrderSupplyQuantity);

                // 获取需求的未分配数量
                BigDecimal unfulfilledDemandQuantity = demandQuantity.subtract(openingInventory).subtract(supplyQuantitySum);

                // 考虑替代关系
                MrpAllocateResultDO mrpAllocateResultDO = mixReplaceAllocate(unfulfilledDemandQuantity, productCode,
                        inventoryShiftDate, materialPlanInventoryShiftId, mrpSupplyDTOAllList,
                        productSubstitutionRelationshipMapOfProductCode, insertMaterialPlanReplacePOList);

                // 获取期末库存 = 期初库存量 - 毛需求 + 计划供应量(计划采购供应、订单在途供应) + 计划调整量
                endingInventory = openingInventory.subtract(demandQuantity).add(supplyQuantitySum).add(mrpAllocateResultDO.getFulfillmentQuantity()).add(planAdjustQuantity);

                // 真实库存缺口不需要考虑计划调整量
                realInventoryGap = originOpeningInventory.subtract(demandQuantity).add(supplyQuantitySum).add(mrpAllocateResultDO.getFulfillmentQuantity());

                if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                    log.info("物料:{}, 日期:{}, 日期索引:{}, 锁定期:{}, 最小安全库存:{}, 标准安全库存:{}, 最高安全库存:{},毛需求:{}, 前一天供应:{}, 供应:{}, 期末库存:{}, 期初库存:{}",
                            productCode, DateUtils.dateToString(inventoryShiftDate), i, lockPeriod,
                            minStockQuantity, standardStockQuantity, maxStockQuantity, demandQuantity, beforeDaySupplyQuantitySum, supplyQuantitySum, endingInventory, openingInventory);
                }
                planAdjustQuantity = noGlassInventoryShiftService.calculateDisplacementQuantity(i, lockPeriod, endingInventory,
                        openingInventory, demandQuantity, supplyQuantitySum, minStockQuantity, standardStockQuantity,
                        maxStockQuantity, beforeDaySupplyQuantitySum);
                // 计算推移量
                if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                    log.info("物料:{}, 计划调整量:{}", productCode, planAdjustQuantity);
                }

                // 调整量如果小于0，则置为0
                planAdjustQuantity = planAdjustQuantity.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : planAdjustQuantity;
                // 判断调整量是否为负数，负数则是减量。减量的值不能大于计划供应量
//                planAdjustQuantity = noGlassInventoryShiftService.reductionPlanAdjustQuantity(planAdjustQuantity, planPurchaseSupplyQuantity);

                // 计算期末库存
                BigDecimal beforeEndingInventory = endingInventory;
                endingInventory = endingInventory.add(planAdjustQuantity);
                // 更新期末库存
                endingInventoryMapOfDateIndex.put(i, endingInventory);

                // 新增推移数据
                NoGlassInventoryShiftDetailDTO noGlassInventoryShiftDTO = NoGlassInventoryShiftDetailDTO.builder()
                        .id(materialPlanInventoryShiftId)
                        .noGlassInventoryShiftDataId(noGlassInventoryShiftDataDTO.getId())
                        .inventoryDate(inventoryShiftDate)
                        .safetyStockLevelMin(minStockQuantity)
                        .safetyStockLevelStandard(standardStockQuantity)
                        .safetyStockLevelMax(maxStockQuantity)
                        .openingInventory(openingInventory)
                        .adjustQuantity(planAdjustQuantity)
                        .demandQuantity(demandQuantity)
                        .supplyQuantity(supplyQuantitySum)
                        .beforeEndingInventory(beforeEndingInventory)
                        .endingInventory(endingInventory)
                        .planPurchase(BigDecimal.ZERO)
                        .realInventoryGap(realInventoryGap)
                        .whetherLockPeriod(lockPeriod > i ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode())
                        .build();
                insertNoGlassInventoryShiftDetailList.add(noGlassInventoryShiftDTO);
                mergeNoGlassInventoryList.add(noGlassInventoryShiftDTO);

                // 组装新增毛需求数据
                assembleNoGlassDemandData(dayMrpDemandList, materialPlanInventoryShiftId, materialPlanVersionDTO, insertNoGlassDemandList);
                // 组装新增供应数据
                assembleNoGlassSupplyData(transitOrderSupplyList, planPurchaseSupplyList, materialPlanInventoryShiftId, materialPlanVersionDTO, insertNoGlassSupplyList);

                // 进行合并数据
                Date startDate = transferDateList.get(Math.min(transferDateStartIndex, transferDateList.size() - 1));
                Date endDate;
                if (flag && startDate.compareTo(inventoryShiftDateList.get(0)) > 0) {
                    // 送货日期的第一天要大于推移日期，则开始时间为推移日期，第二段收货日期等于第一天送货日期
                    endDate = startDate;
                    startDate = inventoryShiftDateList.get(0);
                    addIndexFlag = false;
                } else {
                    startDate = transferDateList.get(Math.min(transferDateStartIndex, transferDateList.size() - 1));
                    endDate = transferDateList.get(Math.min(transferDateNextIndex, transferDateList.size() - 1));
                }

                if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                    log.info("合并 start date :{}, end date :{}", DateUtils.dateToString(startDate), DateUtils.dateToString(endDate));
                }
                if (DateUtils.dateToString(inventoryShiftDate).equals(DateUtils.dateToString(endDate))) {
                    if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                        log.info("结束日期相等合并的日期，start date :{}, end date :{}", DateUtils.dateToString(startDate), DateUtils.dateToString(endDate));
                    }
                    // 推移日期等于送货日期，则需要合并之前的数据
                    Date finalStartDate = startDate;
                    List<NoGlassInventoryShiftDetailDTO> list = mergeNoGlassInventoryList.stream()
                            .filter(item -> item.getInventoryDate().compareTo(endDate) <= 0 &&
                                    item.getInventoryDate().compareTo(finalStartDate) >= 0)
                            .collect(Collectors.toList());

                    if (CollectionUtils.isNotEmpty(list)) {
                        if ("BGD0052G".equals(productCode) || "BGD0054G".equals(productCode)) {
                            for (NoGlassInventoryShiftDetailDTO inventoryShiftDetailDTO : list) {
                                log.info("合并后的数据,日期:{},计划调整量:{},计划采购:{}", DateUtils.dateToString(inventoryShiftDetailDTO.getInventoryDate()), inventoryShiftDetailDTO.getAdjustQuantity(), inventoryShiftDetailDTO.getPlanPurchase());
                            }
                        }
                        // 合并数据
                        list.sort(Comparator.comparing(NoGlassInventoryShiftDetailDTO::getInventoryDate));
                        NoGlassInventoryShiftDetailDTO beforeNoGlassInventoryShiftDTO = list.get(0);
                        //推移的时间在供应商送货日历时间之前，则需要统计第一天数据
                        BigDecimal planPurchaseSum = BigDecimal.ZERO;
                        if (flag && beforeNoGlassInventoryShiftDTO.getInventoryDate().compareTo(startDate) <= 0) {
                            planPurchaseSum = planPurchaseSum.add(beforeNoGlassInventoryShiftDTO.getAdjustQuantity());
                        }
                        for (int j = 0; j < list.size(); j++) {
                            NoGlassInventoryShiftDetailDTO inventoryShiftDTO = list.get(j);
                            if (StringUtils.equals(DateUtils.dateToString(inventoryShiftDTO.getInventoryDate()), DateUtils.dateToString(startDate))) {
                                continue;
                            }
                            planPurchaseSum = planPurchaseSum.add(inventoryShiftDTO.getAdjustQuantity());
                        }

                        // 调减数量不能合并
                        if (planPurchaseSum.compareTo(BigDecimal.ZERO) < 0) {
                            for (NoGlassInventoryShiftDetailDTO inventoryShiftDTO : list) {
                                if (StringUtils.equals(DateUtils.dateToString(inventoryShiftDTO.getInventoryDate()), DateUtils.dateToString(startDate))) {
                                    continue;
                                }
                                inventoryShiftDTO.setPlanPurchase(inventoryShiftDTO.getAdjustQuantity());
                            }
                        }

                        // 《计划采购》按包装批量取整
                        planPurchaseSum = computePlanPurchase(minOrderQty, packageLot, planPurchaseSum);

                        if (null != beforeNoGlassInventoryShiftDTO.getPlanPurchase() && planPurchaseSum.compareTo(BigDecimal.ZERO) > 0) {
                            // 求和之后，还需要按照包装批量取整
                            planPurchaseSum = beforeNoGlassInventoryShiftDTO.getPlanPurchase().add(planPurchaseSum);
                            planPurchaseSum = computePlanPurchase(minOrderQty, packageLot, planPurchaseSum);
                            beforeNoGlassInventoryShiftDTO.setPlanPurchase(planPurchaseSum);
                        }

                        // 刷新期末库存
                        for (int j = 0; j < list.size(); j++) {
                            NoGlassInventoryShiftDetailDTO inventoryShiftDTO = list.get(j);
                            BigDecimal beforeEndingInventoryMerge = inventoryShiftDTO.getOpeningInventory()
                                    .subtract(inventoryShiftDTO.getDemandQuantity())
                                    .add(inventoryShiftDTO.getSupplyQuantity());
                            BigDecimal endingInventoryMerge = beforeEndingInventoryMerge.add(inventoryShiftDTO.getPlanPurchase());
                            inventoryShiftDTO.setEndingInventory(endingInventoryMerge);
                            if (j + 1 <= list.size() - 1) {
                                NoGlassInventoryShiftDetailDTO nextNoGlassInventoryShiftDTO = list.get(j + 1);
                                // 下一个推移的期初，等于当前的期末库存
                                nextNoGlassInventoryShiftDTO.setOpeningInventory(endingInventoryMerge);
                            }
                            endingInventoryMapOfDateIndex.put(i, endingInventoryMerge);
                        }
                    }
                    if (addIndexFlag) {
                        transferDateStartIndex++;
                        transferDateNextIndex++;
                    }
                    flag = false;
                    addIndexFlag = true;
                }
            }
        }

        if (isNewVersion) {
            // 初始化版本
            mrpResultPO.setInsertMaterialPlanVersionDTO(materialPlanVersionDTO);
        } else {
            // 修改版本
            mrpResultPO.setUpdateMaterialPlanVersionDTO(materialPlanVersionDTO);
        }

        // 删除材料计划员权限下的物料
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(planUserProductCodeList)) {
            List<List<String>> partition = Lists.partition(planUserProductCodeList, 1000);
            // 查询data数据
            for (List<String> item : partition) {
                List<NoGlassInventoryShiftDataVO> shiftDataVOList = noGlassInventoryShiftDataService.selectByParams(ImmutableMap.of("productCodeList", item));
                // 删除推移data数据
                List<String> dataIdList = shiftDataVOList.stream().map(NoGlassInventoryShiftDataVO::getId).collect(Collectors.toList());
                Lists.partition(dataIdList, 1000).forEach(dataIds -> noGlassInventoryShiftDataService.doDelete(dataIds));
                // 删除detail数据
                if (CollectionUtils.isNotEmpty(dataIdList)) {
                    noGlassInventoryShiftDetailService.doDeleteDataIds(dataIdList);
                }
                // 删除需求
                materialPlanInventoryShiftDemandService.doDeleteByProductCodeList(item);
                // 删除供应
                materialPlanInventoryShiftSupplyService.doDeleteByProductCodeList(item);
            }
        }

        mrpResultPO.setNoGlassInventoryShiftDataList(insertNoGlassInventoryShiftDataList);
        mrpResultPO.setNoGlassInventoryShiftDetailList(insertNoGlassInventoryShiftDetailList);
        mrpResultPO.setDemandList(insertNoGlassDemandList);
        mrpResultPO.setSupplyList(insertNoGlassSupplyList);
        mrpResultPO.setReplacePOList(insertMaterialPlanReplacePOList);
        mrpResultPO.setErrorMsgList(errorMegList);
        return mrpResultPO;
    }

    private List<String> getNeedMrpCalcProductCodeList(List<MrpDemandDTO> useMrpDemandDTOList, List<MrpSupplyDTO> mrpSupplyDTOAllList,
                                                       List<String> planUserProductCodeList) {
        List<String> mrpCalcProductCodeList = new ArrayList<>();
        List<String> mrpDemandProductCodeList = useMrpDemandDTOList.stream().map(MrpDemandDTO::getProductCode).distinct().collect(Collectors.toList());
        List<String> mrpSupplyProductCodeList = mrpSupplyDTOAllList.stream().map(MrpSupplyDTO::getProductCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mrpDemandProductCodeList)) {
            mrpCalcProductCodeList.addAll(mrpDemandProductCodeList);
        }
        if (CollectionUtils.isNotEmpty(mrpSupplyProductCodeList)) {
            mrpCalcProductCodeList.addAll(mrpSupplyProductCodeList);
        }
        mrpCalcProductCodeList = mrpCalcProductCodeList.stream().distinct().collect(Collectors.toList());
        mrpCalcProductCodeList = mrpCalcProductCodeList.stream().filter(planUserProductCodeList::contains).collect(Collectors.toList());
        return mrpCalcProductCodeList;
    }

    private NoGlassInventoryShiftDataDTO assembleNoGlassInventoryShiftDataDTO(String productCode, String productClassify, BigDecimal minStockDay,
                                                                              BigDecimal standardStockDay, BigDecimal maxStockDay, String productCategory) {
        NoGlassInventoryShiftDataDTO noGlassInventoryShiftDataDTO = new NoGlassInventoryShiftDataDTO();
        noGlassInventoryShiftDataDTO.setId(UUIDUtil.getUUID());
        noGlassInventoryShiftDataDTO.setProductCode(productCode);
        noGlassInventoryShiftDataDTO.setProductClassify(productClassify);
        noGlassInventoryShiftDataDTO.setSafetyStockDaysMin(minStockDay);
        noGlassInventoryShiftDataDTO.setSafetyStockDaysStandard(standardStockDay);
        noGlassInventoryShiftDataDTO.setSafetyStockDaysMax(maxStockDay);
        noGlassInventoryShiftDataDTO.setProductCategory(productCategory);
        return noGlassInventoryShiftDataDTO;
    }

    private void assembleNoGlassSupplyData(List<MrpSupplyDTO> transitOrderSupplyList, List<MrpSupplyDTO> planPurchaseSupplyList,
                                           String materialPlanInventoryShiftId, MaterialPlanVersionDTO materialPlanVersionDTO,
                                           List<MaterialPlanInventoryShiftSupplyDTO> insertNoGlassSupplyList) {
        // 新增供应详情
        if (CollectionUtils.isNotEmpty(transitOrderSupplyList)) {
            for (MrpSupplyDTO mrpSupplyDTO : transitOrderSupplyList) {
                MaterialPlanInventoryShiftSupplyDTO materialPlanInventoryShiftSupplyDTO = MaterialPlanInventoryShiftSupplyDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .materialPlanInventoryShiftId(materialPlanInventoryShiftId)
                        .materialPlanVersionId(materialPlanVersionDTO.getId())
                        .supplyQuantity(mrpSupplyDTO.getSupplyQuantity())
                        .productCode(mrpSupplyDTO.getProductCode())
                        .supplyType(mrpSupplyDTO.getSupplySource())
                        .purchaseOrderCode(mrpSupplyDTO.getPurchaseOrderCode())
                        .materilalEnquiryTrack(mrpSupplyDTO.getSupplyId())
                        .build();
                insertNoGlassSupplyList.add(materialPlanInventoryShiftSupplyDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(planPurchaseSupplyList)) {
            for (MrpSupplyDTO mrpSupplyDTO : planPurchaseSupplyList) {
                MaterialPlanInventoryShiftSupplyDTO materialPlanInventoryShiftSupplyDTO = MaterialPlanInventoryShiftSupplyDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .materialPlanInventoryShiftId(materialPlanInventoryShiftId)
                        .materialPlanVersionId(materialPlanVersionDTO.getId())
                        .supplyQuantity(mrpSupplyDTO.getSupplyQuantity())
                        .supplyType(mrpSupplyDTO.getSupplySource())
                        .productCode(mrpSupplyDTO.getProductCode())
                        .purchaseOrderCode(mrpSupplyDTO.getPurchaseOrderCode())
                        .materilalEnquiryTrack(mrpSupplyDTO.getSupplyId())
                        .build();
                insertNoGlassSupplyList.add(materialPlanInventoryShiftSupplyDTO);
            }
        }
    }

    private void assembleNoGlassDemandData(List<MrpDemandDTO> dayMrpDemandList, String materialPlanInventoryShiftId,
                                           MaterialPlanVersionDTO materialPlanVersionDTO, List<MaterialPlanInventoryShiftDemandDTO> insertNoGlassDemandList) {
        if (CollectionUtils.isNotEmpty(dayMrpDemandList)) {
            for (MrpDemandDTO mrpDemandDTO : dayMrpDemandList) {
                MaterialPlanInventoryShiftDemandDTO materialPlanInventoryShiftDemandDTO = MaterialPlanInventoryShiftDemandDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .materialPlanInventoryShiftId(materialPlanInventoryShiftId)
                        .materialPlanVersionId(materialPlanVersionDTO.getId())
                        .demandQuantity(mrpDemandDTO.getDemandQuantity())
                        .workOrderNo(mrpDemandDTO.getWorkOrderCode())
                        .productCode(mrpDemandDTO.getProductCode())
                        .demandType(mrpDemandDTO.getDemandSource())
                        .build();
                insertNoGlassDemandList.add(materialPlanInventoryShiftDemandDTO);
            }
        }
    }


    /**
     *  计算供应数量的总和
     * @param supplyList 供应数据
     * @return 供应数量总和
     */
    private BigDecimal calculateSupplyQuantitySum(List<MrpSupplyDTO> supplyList) {
        if (CollectionUtils.isEmpty(supplyList)) {
            return BigDecimal.ZERO;
        }
        return supplyList.stream()
                .map(MrpSupplyDTO::getSupplyQuantity)
                .filter(Objects::nonNull) // 防止 SupplyQuantity 为 null
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

//    private void postComputePlanPurchase(List<NoGlassInventoryShiftDTO> insertNoGlassInventoryShiftDtoList,
//                                         Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode,
//                                         Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup,
//                                         Map<String, SupplierVO> supplierVOMapOfId, Date firstTransferDate) {
//
//        Map<String, List<NoGlassInventoryShiftDTO>> noGlassInventoryShiftGroup = insertNoGlassInventoryShiftDtoList.stream().collect(Collectors.groupingBy(NoGlassInventoryShiftDTO::getProductCode));
//
//        for (Map.Entry<String, List<NoGlassInventoryShiftDTO>> entry : noGlassInventoryShiftGroup.entrySet()) {
//            String productCode = entry.getKey();
//            List<NoGlassInventoryShiftDTO> shiftDTOList = entry.getValue();
//
//            // shiftDTOList按照推移时间排序
//            shiftDTOList.sort(Comparator.comparing(NoGlassInventoryShiftDTO::getInventoryDate));
//
//            // 获取材料与供应商关系
//            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMapOfMaterialCode.getOrDefault(productCode, new MaterialSupplierPurchaseVO());
//            BigDecimal minOrderQty = materialSupplierPurchaseVO.getMinOrderQty();
//            BigDecimal packageLot = materialSupplierPurchaseVO.getPackageLot();
//            // 获取关联的供应商数据
//            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.getOrDefault(materialSupplierPurchaseVO.getId(), new ArrayList<>());
//            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
//                // 为空，虚拟出供应商采购关系
//                supplierPurchaseRatioVOList.add(new SupplierPurchaseRatioVO());
//            }
//
//            // 只考虑一个供应商，不考虑供货比例
//            SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(0);
//            BigDecimal purchaseRatio = BigDecimal.ONE;
//
//
//            String supplierId = supplierPurchaseRatioVO.getSupplierId();
//            SupplierVO supplierVO = supplierVOMapOfId.get(supplierId);
//
//            List<Date> transferDateList = new ArrayList<>();
//            for (int i = 0; i < 365; i++) {
//                Date transferDate = DateUtils.moveDay(firstTransferDate, i);
//                // 未维护收货日历，则每天都收货
//                if (null == supplierVO || (StringUtils.isBlank(supplierVO.getReceivingWeekDay()) && StringUtils.isBlank(supplierVO.getReceivingMonthDay())) || (StringUtils.isNotBlank(supplierVO.getReceivingWeekDay()) && supplierVO.getReceivingWeekDay().contains(String.valueOf(DateUtils.getDayOfWeek(transferDate)))) || (StringUtils.isNotBlank(supplierVO.getReceivingMonthDay()) && supplierVO.getReceivingMonthDay().contains(String.valueOf(DateUtils.getDayOfMonth(transferDate))))) {
//                    transferDateList.add(transferDate);
//                }
//            }
//
//            // 遍历供应商送货时间
//            Collections.sort(transferDateList);
//
//            boolean flag = true;
//            for (int i = 0; i < transferDateList.size() - 1; i++) {
//                Date transferDate = transferDateList.get(i);
//                Date transferDateNext = transferDateList.get(i + 1);
//                if ("BY60271G".equals(productCode)) {
//                    log.info("合并推移日期在之前的数据，最早推移得数据:{}, 供应商送货开始时间：{}", DateUtils.dateToString(shiftDTOList.get(0).getInventoryDate()), DateUtils.dateToString(transferDate));
//                }
//                if (flag && shiftDTOList.get(0).getInventoryDate().compareTo(transferDate) < 0) {
//                    if ("BY60271G".equals(productCode)) {
//                        log.info("&&&&&& 进入条件");
//                    }
//                    // 合并推移日期在之前的数据
//                    NoGlassInventoryShiftDTO noGlassInventoryShiftDTO = shiftDTOList.get(0);
//                    transferDateNext = transferDate;
//                    transferDate = noGlassInventoryShiftDTO.getInventoryDate();
//                    i--;
//                }
//
//                // 过滤出供应商送货日历里的推移数据
//                List<NoGlassInventoryShiftDTO> list = new ArrayList<>();
//                for (NoGlassInventoryShiftDTO noGlassInventoryShiftDTO : shiftDTOList) {
//                    if (noGlassInventoryShiftDTO.getInventoryDate().compareTo(transferDate) >= 0 && noGlassInventoryShiftDTO.getInventoryDate().compareTo(transferDateNext) <= 0) {
//                        list.add(noGlassInventoryShiftDTO);
//                    }
//                }
//
//                if ("BY60271G".equals(productCode)) {
//                    log.info("====== 供应商送货日历, 最早送货时间:{}, 开始时间:{}, 结束时间:{}, 合并的推移数据条数:{}", DateUtils.dateToString(transferDateList.get(0)), DateUtils.dateToString(transferDate), DateUtils.dateToString(transferDateNext), list.size());
//                }
//                if (CollectionUtils.isNotEmpty(list)) {
//                    if ("BY60271G".equals(productCode)) {
//                        for (NoGlassInventoryShiftDTO item : list) {
//                            log.info("合并推移数据详情, 推移日期:{}, 调整量:{}, 计划采购量:{}, 供应比例:{}", DateUtils.dateToString(item.getInventoryDate()), item.getAdjustQuantity(), item.getPlanPurchase(), purchaseRatio);
//                        }
//                    }
//
//                    list.sort(Comparator.comparing(NoGlassInventoryShiftDTO::getInventoryDate));
//                    NoGlassInventoryShiftDTO noGlassInventoryShiftDTO = list.get(0);
//                    //推移的时间在供应商送货日历时间之前，则需要统计第一天数据
//                    BigDecimal planPurchaseSum = BigDecimal.ZERO;
//                    if (flag && noGlassInventoryShiftDTO.getInventoryDate().compareTo(transferDate) <= 0) {
//                        planPurchaseSum = planPurchaseSum.add(noGlassInventoryShiftDTO.getAdjustQuantity());
//                    }
//                    for (int j = 0; j < list.size(); j++) {
//                        NoGlassInventoryShiftDTO inventoryShiftDTO = list.get(j);
//                        if (StringUtils.equals(DateUtils.dateToString(inventoryShiftDTO.getInventoryDate()), DateUtils.dateToString(transferDate))) {
//                            continue;
//                        }
//                        planPurchaseSum = planPurchaseSum.add(inventoryShiftDTO.getAdjustQuantity());
//                    }
//
//                    if ("BY60271G".equals(productCode)) {
//                        log.info("供应商开始时间:{}, 结束时间:{}, 计划采购量:{}", DateUtils.dateToString(transferDate), DateUtils.dateToString(transferDateNext), planPurchaseSum);
//                    }
//
//                    // 调减数量不能合并
//                    if (planPurchaseSum.compareTo(BigDecimal.ZERO) < 0) {
//                        for (NoGlassInventoryShiftDTO inventoryShiftDTO : list) {
//                            if (StringUtils.equals(DateUtils.dateToString(inventoryShiftDTO.getInventoryDate()), DateUtils.dateToString(transferDate))) {
//                                continue;
//                            }
//                            inventoryShiftDTO.setPlanPurchase(inventoryShiftDTO.getAdjustQuantity());
//                        }
//                    } else {
//                        noGlassInventoryShiftDTO.setPlanPurchase(planPurchaseSum.multiply(purchaseRatio));
//                    }
//                }
//                flag = false;
//            }
//            // 按照包装批量计算
//            planPurchaseRounded(shiftDTOList, packageLot, minOrderQty);
//        }
//    }

    private List<Date> getInventoryShiftDateList(Date date, Integer month) {
        // 获取当前日期到12后的日期
        Date currentDate = DateUtils.getDayFirstTime(date);
//        Date currentDate = DateUtils.stringToDate("2025-01-17");
        Date moveDate = DateUtils.moveMonth(currentDate, month);
        return DateUtils.getIntervalDates(currentDate, moveDate);
    }

//    private void planPurchaseRounded(List<NoGlassInventoryShiftDTO> list, BigDecimal packageLot, BigDecimal minOrderQty) {
//        // 判断是否满足最小起订量、包装批量
//        list.sort(Comparator.comparing(NoGlassInventoryShiftDTO::getInventoryDate));
//        for (int i = 0; i < list.size(); i++) {
//            NoGlassInventoryShiftDTO noGlassInventoryShiftDTO = list.get(i);
//            // 期初库存
//            BigDecimal openingInventory = noGlassInventoryShiftDTO.getOpeningInventory();
//            // 供应
//            BigDecimal supplyQuantity = noGlassInventoryShiftDTO.getSupplyQuantity();
//            // 需求
//            BigDecimal demandQuantity = noGlassInventoryShiftDTO.getDemandQuantity();
//
//            // 计划采购
//            BigDecimal planPurchase = noGlassInventoryShiftDTO.getPlanPurchase();
//            planPurchaseSum = computePlanPurchase(minOrderQty, packageLot, planPurchaseSum);
//            noGlassInventoryShiftDTO.setPlanPurchase(planPurchase);
//
//            // 刷新期末库存
//            BigDecimal beforeEndingInventory = openingInventory.subtract(demandQuantity).add(supplyQuantity);
//            BigDecimal endingInventory = beforeEndingInventory.add(planPurchase);
//            noGlassInventoryShiftDTO.setEndingInventory(endingInventory);
//            if (i + 1 <= list.size() - 1) {
//                NoGlassInventoryShiftDTO nextNoGlassInventoryShiftDTO = list.get(i + 1);
//                // 下一个推移的期初，等于当前的期末库存
//                nextNoGlassInventoryShiftDTO.setOpeningInventory(endingInventory);
//            }
//        }
//    }

    private MrpAllocateResultDO mixReplaceAllocate(BigDecimal unfulfilledDemandQuantity, String productCode,
                                                   Date inventoryShiftDate, String materialPlanInventoryShiftId,
                                                   List<MrpSupplyDTO> mrpSupplyDTOList,
                                                   Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionRelationshipMapOfProductCode,
                                                   List<MaterialPlanReplacePO> insertMaterialPlanReplacePOList) {

        BigDecimal fulfillmentQuantity = BigDecimal.ZERO;
        MrpAllocateResultDO mrpAllocateResultDO = new MrpAllocateResultDO();
        mrpAllocateResultDO.setFulfillmentQuantity(fulfillmentQuantity);
        if (unfulfilledDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
            return mrpAllocateResultDO;
        }
        // 生成替代计划
        // 获取替代料数据，混合替代(C)
        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = productSubstitutionRelationshipMapOfProductCode.get(productCode);
        if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOS)) {
            return mrpAllocateResultDO;
        }
        List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOListOfRule = productSubstitutionRelationshipVOS.stream().filter(item -> StringUtils.equals(item.getRule(), "C")).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOListOfRule)) {
            return mrpAllocateResultDO;
        }
        for (ProductSubstitutionRelationshipVO productSubstitutionRelationshipVO : productSubstitutionRelationshipVOListOfRule) {
            if (unfulfilledDemandQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                return mrpAllocateResultDO;
            }
            // 获取替代料物品
            String substituteProductCode = productSubstitutionRelationshipVO.getSubstituteProductCode();
            // 获取供应，供应时间需要在inventoryShiftDate之前
            List<MrpSupplyDTO> replaceMrpSupplyDTOList = mrpSupplyDTOList.stream().filter(item -> item.getSupplyTime().before(inventoryShiftDate) && StringUtils.equals(productCode, substituteProductCode) && item.getSupplyQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(replaceMrpSupplyDTOList)) {
                continue;
            }
            for (MrpSupplyDTO mrpSupplyDTO : replaceMrpSupplyDTOList) {
                BigDecimal replaceQuantity;
                if (mrpSupplyDTO.getSupplyQuantity().compareTo(unfulfilledDemandQuantity) >= 0) {
                    mrpSupplyDTO.setSupplyQuantity(mrpSupplyDTO.getSupplyQuantity().subtract(unfulfilledDemandQuantity));
                    fulfillmentQuantity = fulfillmentQuantity.add(unfulfilledDemandQuantity);
                    replaceQuantity = unfulfilledDemandQuantity;
                    unfulfilledDemandQuantity = BigDecimal.ZERO;
                } else {
                    unfulfilledDemandQuantity = unfulfilledDemandQuantity.subtract(mrpSupplyDTO.getSupplyQuantity());
                    fulfillmentQuantity = fulfillmentQuantity.add(mrpSupplyDTO.getSupplyQuantity());
                    replaceQuantity = mrpSupplyDTO.getSupplyQuantity();
                    mrpSupplyDTO.setSupplyQuantity(BigDecimal.ZERO);
                }
                MaterialPlanReplacePO materialPlanReplacePO = generateMaterialPlanReplacePO(materialPlanInventoryShiftId, productCode, mrpSupplyDTO.getProductCode(), replaceQuantity, inventoryShiftDate);
                insertMaterialPlanReplacePOList.add(materialPlanReplacePO);
            }
        }
        mrpAllocateResultDO.setFulfillmentQuantity(fulfillmentQuantity);
        return mrpAllocateResultDO;
    }

    /**
     *  生成替代计划
     * @param mrpDemandMapOfProductCode 毛需求数据，key是物品代码
     * @param productSubstitutionRelationshipMapOfProductCode 替代料关系数据
     * @param useMrpDemandDTOList 考虑替代料之后的毛需求
     * @param insertMaterialPlanReplacePOList 新增的替代料计划
     */
    private void substituteReplaceAllocate(Map<String, List<MrpDemandDTO>> mrpDemandMapOfProductCode, Map<String, List<ProductSubstitutionRelationshipVO>> productSubstitutionRelationshipMapOfProductCode, List<MrpDemandDTO> useMrpDemandDTOList, List<MaterialPlanReplacePO> insertMaterialPlanReplacePOList) {
        for (Map.Entry<String, List<MrpDemandDTO>> entry : mrpDemandMapOfProductCode.entrySet()) {
            String productCode = entry.getKey();

            // 1、获取该物品的替代料信息
            List<ProductSubstitutionRelationshipVO> productSubstitutionRelationshipVOS = productSubstitutionRelationshipMapOfProductCode.get(productCode);
            if (CollectionUtils.isEmpty(productSubstitutionRelationshipVOS)) {
                useMrpDemandDTOList.addAll(entry.getValue());
                continue;
            }

            for (MrpDemandDTO mrpDemandDTO : entry.getValue()) {
                // 2、获取在生效内的替代料数据(根据需求时间判断)
                List<ProductSubstitutionRelationshipVO> effectiveProductSubstitutionRelationshipVOS = productSubstitutionRelationshipVOS.stream()
                        .filter(item -> null != item.getEffectiveTime() && item.getEffectiveTime().before(mrpDemandDTO.getDemandTime()) && null != item.getFailureTime() && item.getFailureTime().after(mrpDemandDTO.getDemandTime()))
                        .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(effectiveProductSubstitutionRelationshipVOS)) {
                    useMrpDemandDTOList.add(mrpDemandDTO);
                    continue;
                }
                // 3、按照优先级排序,过滤出替代料优先的替代料数据
                effectiveProductSubstitutionRelationshipVOS = effectiveProductSubstitutionRelationshipVOS.stream()
                        .filter(item -> "B".equals(item.getRule())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(effectiveProductSubstitutionRelationshipVOS)) {
                    useMrpDemandDTOList.add(mrpDemandDTO);
                    continue;
                }

                // 替代料的转化比列,默认数量全部转化
                ProductSubstitutionRelationshipVO productSubstitutionRelationshipVO = effectiveProductSubstitutionRelationshipVOS.get(0);
                MrpDemandDTO substitutionMrpDemand = MrpDemandDTO.builder().productCode(productSubstitutionRelationshipVO.getSubstituteProductCode()).demandTime(mrpDemandDTO.getDemandTime()).demandQuantity(mrpDemandDTO.getDemandQuantity()).demandSource(mrpDemandDTO.getDemandSource()).build();
                useMrpDemandDTOList.add(substitutionMrpDemand);

                // 需要删除主料需求，并生成替代计划
                MaterialPlanReplacePO materialPlanReplacePO = generateMaterialPlanReplacePO(null, mrpDemandDTO.getProductCode(), productSubstitutionRelationshipVO.getSubstituteProductCode(), mrpDemandDTO.getDemandQuantity(), mrpDemandDTO.getDemandTime());
                insertMaterialPlanReplacePOList.add(materialPlanReplacePO);
            }
        }
    }

    private MaterialPlanReplacePO generateMaterialPlanReplacePO(String materialPlanInventoryShiftId, String masterProductCode, String substituteProductCode, BigDecimal replaceQuantity, Date demandTime) {
        MaterialPlanReplacePO materialPlanReplacePO = new MaterialPlanReplacePO();
        materialPlanReplacePO.setPriority(1);
        materialPlanReplacePO.setMaterialPlanInventoryShiftId(materialPlanInventoryShiftId);
        materialPlanReplacePO.setMasterProductCode(masterProductCode);
        materialPlanReplacePO.setReplaceProductCode(substituteProductCode);
        materialPlanReplacePO.setReplaceQuantity(replaceQuantity);
        materialPlanReplacePO.setReplaceDate(demandTime);
        materialPlanReplacePO.setId(UUID.randomUUID().toString());
        return materialPlanReplacePO;
    }

    /**
     * 获取实时库存数据
     * @return
     */
    private Map<String, List<InventoryBatchDetailVO>> getOpeningInventoryMap(List<String> productCodeList, List<String> stockPointCodeList,
                                                                             List<String> auxiliarySubInventoryList, String scenario) {
        Map<String, List<InventoryBatchDetailVO>> result = new HashMap<>();
        // 过滤子库存为以下类型
        List<String> subinventoryList = Lists.newArrayList("FLZZ", "FLDS", "BSFY", "ZZFZC");
        // 查询实时库存
        Map<String, Object> params = new HashMap<>();
        params.put("stockPointCodes", stockPointCodeList);
        params.put("productCodes", productCodeList);
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);
        if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)) {
            result = inventoryBatchDetailVOList.stream()
                    .filter(item -> !subinventoryList.contains(item.getSubinventory()))
                    .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        }
        return result;
    }

    public static void main(String[] args) {
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode = new HashMap<>();
        supplierPurchaseVOMapOfMaterialCode.put("bbb", new MaterialSupplierPurchaseVO());

        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = new HashMap<>();
        supplierPurchaseRatioVOGroup.put("ccc", new ArrayList<>());

        Map<String, SupplierVO> supplierVOMapOfId = new HashMap<>();
        supplierVOMapOfId.put("ddd", new SupplierVO());

        MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMapOfMaterialCode.getOrDefault("aaa", new MaterialSupplierPurchaseVO());
        BigDecimal minOrderQty = materialSupplierPurchaseVO.getMinOrderQty();
        BigDecimal packageLot = materialSupplierPurchaseVO.getPackageLot();
        // 获取关联的供应商数据
        List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.getOrDefault(materialSupplierPurchaseVO.getId(), new ArrayList<>());
        if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
            // 为空，虚拟出供应商采购关系
            supplierPurchaseRatioVOList.add(new SupplierPurchaseRatioVO());
        }

        for (SupplierPurchaseRatioVO supplierPurchaseRatioVO : supplierPurchaseRatioVOList) {
            BigDecimal purchaseRatio = BigDecimal.ONE;
            if (supplierPurchaseRatioVO.getPurchaseRatio() != null) {
                purchaseRatio = supplierPurchaseRatioVO.getPurchaseRatio();
            }

            String supplierId = supplierPurchaseRatioVO.getSupplierId();
            SupplierVO supplierVO = supplierVOMapOfId.get(supplierId);

            if (null == supplierVO || StringUtils.isBlank(supplierVO.getReceivingWeekDay())) {
                System.out.println("条件成立");
            }
        }


        List<Date> transferDateList = new ArrayList<>();
        Date date = new Date();
        Date date2 = new Date();
        transferDateList.add(date);
        transferDateList.add(date2);
        for (int i = 0; i < transferDateList.size() - 1; i++) {
            Date transferDate = transferDateList.get(i);
            Date transferDateNext = transferDateList.get(i + 1);
        }

        BigDecimal bigDecimal = new BigDecimal(3.563);

        BigDecimal planPurchaseSupplyQuantity = new BigDecimal(11);
        BigDecimal planAdjustQuantity = new BigDecimal(-10);

        if (planAdjustQuantity.compareTo(BigDecimal.ZERO) < 0) {
            // 则是减量，判断减的数量是否多余计划采购供应
            if (planPurchaseSupplyQuantity.compareTo(BigDecimal.ZERO) == 0) {
                planAdjustQuantity = BigDecimal.ZERO;
            } else if (planAdjustQuantity.compareTo(planPurchaseSupplyQuantity.negate()) < 0) {
                // 计划调整量小于计划供应量，需要减量
                planAdjustQuantity = planPurchaseSupplyQuantity.negate();
            }
        }
        System.out.println("planAdjustQuantity = " + planAdjustQuantity);
    }

    private BigDecimal computePlanPurchase(BigDecimal minOrderQty, BigDecimal packageLot, BigDecimal planPurchaseSum) {
        if (null != minOrderQty && planPurchaseSum.compareTo(minOrderQty) < 0 && planPurchaseSum.compareTo(BigDecimal.ZERO) > 0) {
            // 计划采购小于最小起订量，则要货数量等于最小起订量
            planPurchaseSum = minOrderQty;
        }
        // 正数、负数都需要考虑包装批量，0不需要
        if (null != packageLot && planPurchaseSum.compareTo(BigDecimal.ZERO) > 0) {
            // 是否满足包装批量
            planPurchaseSum = materialPlanNeedService.roundUpwards(planPurchaseSum, packageLot);
        }
        return planPurchaseSum;
    }

    /**
     * 获取中转库需求
     * @return
     */
    protected void getGradeInventoryDemand(MrpContextDTO mrpContextDTO) {
        String scenario = mrpContextDTO.getScenario();
        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfCode = mrpContextDTO.getNewProductStockPointVOMapOfCode();
        Map<String, Date> lastDeliveryPlanTimeMapOfProductCode = mrpContextDTO.getLastDeliveryPlanTimeMapOfProductCode();

        // 查询中转库半品辅料表（不超过百条数据，查询全表）

        log.info("查询中转库半品辅料表、数据源:{}", scenario);
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<WarehouseHalfSubinventoryVO> warehouseHalfSubinventoryVOS = warehouseHalfSubinventoryService.selectAll();
        // 获取辅料子库存
        List<String> auxiliarySubInventoryList = warehouseHalfSubinventoryVOS.stream()
                .map(WarehouseHalfSubinventoryVO::getAuxiliarySubinventory)
                .distinct().collect(Collectors.toList());
        // 获取辅料货位
        List<String> auxiliaryStorageList = warehouseHalfSubinventoryVOS.stream()
                .map(WarehouseHalfSubinventoryVO::getAuxiliaryStorage)
                .distinct().collect(Collectors.toList());

        // 中转库需求定义：【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料
        // 查询出中转库库存点
//        List<String> stockPointTypeList = Lists.newArrayList("CK", "GN");
//        List<NewStockPointVO> filterStockPointList = stockPointVOList.stream()
//                .filter(item -> stockPointTypeList.contains(item.getStockPointType()))
//                .collect(Collectors.toList());

        Map<String, Object> params = new HashMap<>();
        params.put("subinventory", "BPK");
        // 查询实时库存《半品库》
        List<InventoryBatchDetailVO> bpkInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 查询实时库存《原材料》
        params.put("subinventory", null);
        params.put("subinventoryList", auxiliarySubInventoryList);
        params.put("freightSpaceList", auxiliaryStorageList);
        List<InventoryBatchDetailVO> materialInventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams(scenario, params);

        // 获取中转库原材料库存
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailVOMapOfProductCode = materialInventoryBatchDetailVOList.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        mrpContextDTO.setAuxiliarySubInventoryList(auxiliarySubInventoryList);

        // 需要查询半成品的工艺路径
        List<String> productCodeList = bpkInventoryBatchDetailVOList.stream().map(InventoryBatchDetailVO::getProductCode).distinct().collect(Collectors.toList());
        // 查询物品
        List<NewProductStockPointVO> newProductStockPointVOS = new ArrayList<>();
        for (String productCode : productCodeList) {
            if (newProductStockPointVOMapOfCode.containsKey(productCode)) {
                newProductStockPointVOS.add(newProductStockPointVOMapOfCode.get(productCode));
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            // 查询工艺路径
            List<String> productIdList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getId).distinct().collect(Collectors.toList());
            List<RoutingVO> routingVOList = mdsFeign.selectRoutingByParams(scenario, ImmutableMap.of("productIdList", productIdList));
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(routingVOList)) {
                List<RoutingVO> routingVOListAll = mrpContextDTO.getRoutingVOList();
                routingVOListAll.addAll(routingVOList);
                // 根据ID去重
                routingVOListAll = routingVOListAll.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RoutingVO::getId))), ArrayList::new));
                mrpContextDTO.setRoutingVOList(routingVOListAll);

                // 查询工艺步骤
                List<String> routingIdList = routingVOList.stream().map(RoutingVO::getId).collect(Collectors.toList());
                List<List<String>> partition = Lists.partition(routingIdList, 1000);
                List<RoutingStepVO> routingStepList = new ArrayList<>();
                for (List<String> list : partition) {
                    routingStepList.addAll(mdsFeign.getRoutingStepByRoutingIds(scenario, list));
                }

                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(routingStepList)) {
                    // 根据ID去重
                    List<RoutingStepVO> routingStepVOListAll = mrpContextDTO.getRoutingStepVOList();
                    routingStepVOListAll.addAll(routingStepList);
                    routingStepVOListAll = routingStepVOListAll.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RoutingStepVO::getId))), ArrayList::new));
                    mrpContextDTO.setRoutingStepVOList(routingStepVOListAll);

                    List<String> stepIdList = routingStepList.stream().map(RoutingStepVO::getId).collect(Collectors.toList());
                    // 查询输入物品
                    List<List<String>> partition1 = Lists.partition(stepIdList, 1000);
                    List<RoutingStepInputVO> stepInputVOS = new ArrayList<>();
                    for (List<String> list : partition1) {
                        stepInputVOS.addAll(mdsFeign.selectInputByRoutingStepIds(scenario, list));
                    }
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(stepInputVOS)) {
                        // 根据ID去重
                        List<RoutingStepInputVO> routingStepInputVOListAll = mrpContextDTO.getRoutingStepInputVOList();
                        routingStepInputVOListAll.addAll(stepInputVOS);
                        routingStepInputVOListAll = routingStepInputVOListAll.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(RoutingStepInputVO::getId))), ArrayList::new));
                        mrpContextDTO.setRoutingStepInputVOList(routingStepInputVOListAll);

                        // 查询输入物品
//                        List<String> inputProductIdList = stepInputVOS.stream().map(RoutingStepInputVO::getInputProductId).distinct().collect(Collectors.toList());
//                        List<NewProductStockPointVO> newProductStockPointVOList = mdsFeign.selectProductStockPointByIds(scenario, inputProductIdList);
//                        List<NewProductStockPointVO> newProductStockPointVOListAll = mrpContextDTO.getNewProductStockPointVOList();
//                        newProductStockPointVOListAll.addAll(newProductStockPointVOList);
//                        newProductStockPointVOListAll = newProductStockPointVOListAll.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(NewProductStockPointVO::getId))), ArrayList::new));
//                        mrpContextDTO.setNewProductStockPointVOList(newProductStockPointVOListAll);
                    }
                }
            }
        }

        // 获取半成品物品的工艺路径
        List<RoutingVO> routingVOList = mrpContextDTO.getRoutingVOList();
        // 查看现有数据，现在stockPointId为S1,存的是code
        Map<String, RoutingVO> routingVOMapOfJoinKey = routingVOList.stream().collect(Collectors.toMap(RoutingBasicVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        // 查询工艺路径步骤
        List<RoutingStepVO> routingStepVOList = mrpContextDTO.getRoutingStepVOList();
        Map<String, List<RoutingStepVO>> routingStepCollectOfRoutingId = routingStepVOList.stream().collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));

        // 查询步骤的输入物品
        List<RoutingStepInputVO> routingStepInputVOList = mrpContextDTO.getRoutingStepInputVOList();
        Map<String, List<RoutingStepInputVO>> routingStepInputCollectOfStepId = routingStepInputVOList.stream().collect(Collectors.groupingBy(RoutingStepInputVO::getRoutingStepId));

        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId = mrpContextDTO.getNewProductStockPointVOList().stream().collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity(), (k1, k2) -> k2));
        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfProductCode = mrpContextDTO.getNewProductStockPointVOList().stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));

        // 获取最新版本的发货计划
        List<DeliveryPlanPublishedVO> deliveryPlanDetailVOList = mrpContextDTO.getDeliveryPlanDetailVOList();
        List<String> deliveryPlanProductCodeList = deliveryPlanDetailVOList.stream().map(DeliveryPlanPublishedVO::getProductCode).distinct().collect(Collectors.toList());

        // 最新已发布的发货计划数据按照productCode组装Map

        Map<String, String> deliveryPlanProductCodeToDemandVersionId = deliveryPlanDetailVOList.stream().filter(item -> org.apache.commons.lang3.StringUtils.isNotBlank(item.getDemandVersionId())).sorted(Comparator.comparing(DeliveryPlanPublishedVO::getCreateTime).reversed()).collect(Collectors.toMap(DeliveryPlanPublishedVO::getProductCode, DeliveryPlanPublishedVO::getDemandVersionId, (k1, k2) -> k1 // 保留第一个出现的值，即创建时间最晚的
        ));

        // 获取日需求版本ID
        List<String> demandVersionIdList = deliveryPlanDetailVOList.stream()
                .map(DeliveryPlanPublishedVO::getDemandVersionId).filter(org.apache.commons.lang3.StringUtils::isNotBlank)
                .distinct().collect(Collectors.toList());

        // 根据日需求版本ID查询日需求
//        List<CleanDemandDataVO> demandDataVOList = dfpFeign.selectCleanDemandDataByParams(
//                ImmutableMap.of("versionIds", demandVersionIdList, "productCodeList", productCodeList));
//
//        Map<String, List<CleanDemandDataVO>> cleanDemandDataVoOfVersionId = demandDataVOList.stream().collect(Collectors.groupingBy(CleanDemandDataVO::getVersionId));
//
//        Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailMapOfDataId = new HashMap<>();
//        log.info("材料MRP-------开始查询日需求数据");
//        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(demandDataVOList)) {
//            // 查询日需求明细数据
//            List<String> cleanDemandDataIdList = demandDataVOList.stream().map(CleanDemandDataVO::getId).distinct().collect(Collectors.toList());
//            log.info("材料MRP-------开始查询日需求数据明细, 明细的dataId数据量:{}", cleanDemandDataIdList.size());
//            List<CleanDemandDataDetailVO> cleanDemandDataDetailVOList = new ArrayList<>();
//            Lists.partition(cleanDemandDataIdList, 1000).forEach(dataIdList ->
//                    cleanDemandDataDetailVOList.addAll(dfpFeign.selectCleanDemandDataDetailVOByDataIds(dataIdList)));
//
//            cleanDemandDataDetailMapOfDataId = cleanDemandDataDetailVOList.stream().collect(Collectors.groupingBy(CleanDemandDataDetailVO::getCleanDemandDataId));
//        }
        log.info("材料MRP-------日需求查询结束");
        // 存放缺口数据
        Map<String, BigDecimal> gapDemandQuantityMap = new HashMap<>();
        Map<String, List<InventoryBatchDetailVO>> bpkInventoryBatchDetailVOGroup = bpkInventoryBatchDetailVOList.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        for (String deliveryPlanProductCode : deliveryPlanProductCodeList) {
            BigDecimal currentQuantitySum = BigDecimal.ZERO;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = bpkInventoryBatchDetailVOGroup.get(deliveryPlanProductCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)) {
                currentQuantitySum = inventoryBatchDetailVOS.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add);
            }

            if (deliveryPlanProductCode.equals("00393LRF00006")) {
                log.info("------------ 半品:{}, 中转库库存:{}", deliveryPlanProductCode, currentQuantitySum);
            }

            //【本厂编码】的物品类型为成品、【工序】为末道工序、【供应方式】为推式的材料
            // 获取工艺路径
            RoutingVO routingVO = routingVOMapOfJoinKey.get(deliveryPlanProductCode);
            if (null == routingVO) {
                continue;
            }
            List<RoutingStepVO> stepVOList = routingStepCollectOfRoutingId.get(routingVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepVOList)) {
                continue;
            }
            // 获取末道工序, 顺序号最大的
            stepVOList = stepVOList.stream().filter(item -> null != item.getSequenceNo()).sorted(Comparator.comparing(RoutingStepVO::getSequenceNo).reversed()).collect(Collectors.toList());

            // 判断是否末道工序
            RoutingStepVO routingStepVO = stepVOList.get(0);
            if (null == routingStepVO) {
                continue;
            }
            // 获取末道工序输入物品
            List<RoutingStepInputVO> stepInputVOSList = routingStepInputCollectOfStepId.get(routingStepVO.getId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOSList)) {
                continue;
            }
            // 获取BOM类型为推式的，并且为采购件和B类
            List<RoutingStepInputVO> filterInputList = stepInputVOSList.stream()
                    .filter(item -> org.apache.commons.lang3.StringUtils.equals("推式", item.getSupplyType()))
                    .collect(Collectors.toList());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(filterInputList)) {
                continue;
            }

            // 获取该半成品的日需求数据
            BigDecimal dayDemandQuantitySum = BigDecimal.ZERO;
//            String demandVersionId = deliveryPlanProductCodeToDemandVersionId.get(deliveryPlanProductCode);
//            if (org.apache.commons.lang3.StringUtils.isNotBlank(demandVersionId) && cleanDemandDataVoOfVersionId.containsKey(demandVersionId)) {
//                List<CleanDemandDataVO> productCleanDemandDataVOList = cleanDemandDataVoOfVersionId.get(demandVersionId);
//                // 获取日需求明细
//                List<String> dataIdList = productCleanDemandDataVOList.stream().map(CleanDemandDataVO::getId).collect(Collectors.toList());
//                for (String dataId : dataIdList) {
//                    List<CleanDemandDataDetailVO> cleanDemandDataDetailVOList = cleanDemandDataDetailMapOfDataId.get(dataId);
//                    if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(cleanDemandDataDetailVOList)){
//                        continue;
//                    }
//                    // 获取demandQuantity
//                    // 引入临时变量以提高可读性
//                    List<BigDecimal> demandQuantities = cleanDemandDataDetailVOList
//                            .stream().map(CleanDemandDataDetailVO::getDemandQuantity)
//                            .filter(Objects::nonNull) // 过滤掉可能的 null 值
//                            .collect(Collectors.toList());
//
//                    // 使用单一流操作计算总和
//                    dayDemandQuantitySum = dayDemandQuantitySum.add(demandQuantities.stream().reduce(BigDecimal.ZERO, BigDecimal::add));
//                }
//            }

            // 中转库材料的需求
            for (RoutingStepInputVO routingStepInputVO : filterInputList) {
                NewProductStockPointVO inputNewProductStockPointVO = newProductStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == inputNewProductStockPointVO ||
                        !org.apache.commons.lang3.StringUtils.equals(inputNewProductStockPointVO.getProductType(), "P") ||
                        noGlassMaterialTypeList.stream().noneMatch(materialType -> inputNewProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }

                BigDecimal currentQuantity = currentQuantitySum.multiply(routingStepInputVO.getInputFactor());

                BigDecimal rawMaterialsCurrentQuantity;
                // 获取原材料的现有量
                rawMaterialsCurrentQuantity = Optional.ofNullable(inventoryBatchDetailVOMapOfProductCode.get(inputNewProductStockPointVO.getProductCode()))
                        .map(list -> list.stream().map(item -> new BigDecimal(item.getCurrentQuantity())).reduce(BigDecimal.ZERO, BigDecimal::add)).orElse(BigDecimal.ZERO);
                // 原材料库存数量
                if (gapDemandQuantityMap.containsKey(inputNewProductStockPointVO.getProductCode())) {
                    BigDecimal bigDecimal = gapDemandQuantityMap.get(inputNewProductStockPointVO.getProductCode());
                    if (bigDecimal.compareTo(BigDecimal.ZERO) < 0) {
                        bigDecimal = bigDecimal.abs();
                        rawMaterialsCurrentQuantity = bigDecimal;
                    }
                }

                // 获取currentQuantity和demandQuantity的最小值
                BigDecimal minDemandQuantity = currentQuantity.min(rawMaterialsCurrentQuantity);
                if (org.apache.commons.lang3.StringUtils.equals("BLA2150G", inputNewProductStockPointVO.getProductCode())) {
                    log.info("&&&&&&&&&&&&&&&&&&& MRP计算中转库需求,半成品编码:{}, 半成品库存:{}, 需要原材料的库存:{}, 原材料库存:{}, 日需求数量:{}，最小库存数量:{}", deliveryPlanProductCode, currentQuantitySum, currentQuantity, rawMaterialsCurrentQuantity, dayDemandQuantitySum, minDemandQuantity);
                }
                if (dayDemandQuantitySum.compareTo(minDemandQuantity) <= 0) {
                    // 如果原材料库存大于半品库存，作为负缺口
                    if (rawMaterialsCurrentQuantity.compareTo(currentQuantitySum) > 0) {
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), currentQuantity.subtract(rawMaterialsCurrentQuantity));
                    } else {
                        // 产生库存
                        // rawMaterialsCurrentQuantity 取绝对值
                        gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), rawMaterialsCurrentQuantity.abs());
                    }
                    // 则不产生缺口
                    continue;
                }

                if (rawMaterialsCurrentQuantity.compareTo(BigDecimal.ZERO) == 0) {
                    // 产生缺口
                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), currentQuantity);
                } else {
                    // 缺口 = 半成品库存 - 原材料库存
                    BigDecimal demandQuantity = currentQuantity.subtract(rawMaterialsCurrentQuantity);
                    gapDemandQuantityMap.put(inputNewProductStockPointVO.getProductCode(), demandQuantity);
                }
            }
        }

        // 获取发货计划
        List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList = new ArrayList<>();
        Map<String, List<DeliveryPlanPublishedVO>> collect = deliveryPlanDetailVOList.stream().collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        for (Map.Entry<String, List<DeliveryPlanPublishedVO>> entry : collect.entrySet()) {
            String productCode = entry.getKey();
            List<DeliveryPlanPublishedVO> value = entry.getValue();
            NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMapOfProductCode.get(productCode);
            if (null == newProductStockPointVO) {
                continue;
            }
            // 需要汇总所有原材料的需求
            for (DeliveryPlanPublishedVO deliveryPlanDetailVO : value) {
                if (productCode.equals("00393LRF00006")) {
                    log.info("########  中转库需求, 发货计划物品:{}, 发货计划时间:{}, 发货计划数量:{}", productCode, DateUtils.dateToString(deliveryPlanDetailVO.getDemandTime()), deliveryPlanDetailVO.getDemandQuantity());
                }
                if (deliveryPlanDetailVO.getDemandQuantity() > 0) {
                    // 发货计划与中转库需求扣减
                    Date date = lastDeliveryPlanTimeMapOfProductCode.get(productCode);
                    if (null != date && date.compareTo(deliveryPlanDetailVO.getDemandTime()) > 0) {
                        lastDeliveryPlanTimeMapOfProductCode.put(productCode, date);
                    } else {
                        lastDeliveryPlanTimeMapOfProductCode.put(productCode, deliveryPlanDetailVO.getDemandTime());
                    }
                }
                // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
                getTransitDepotsDemandData(newProductStockPointVO, deliveryPlanDetailVO.getDemandQuantity(), deliveryPlanDetailVO.getDemandTime(),
                        routingVOMapOfJoinKey, routingStepCollectOfRoutingId, routingStepInputCollectOfStepId,
                        newProductStockPointVOMapOfId, transferWarehouseDemandDTOList);
            }
        }

        // 原材料需求数量汇总，生成MRP中转库需求
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(transferWarehouseDemandDTOList)) {
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            // 按照原材料分组中转库需求
            Map<String, List<TransferWarehouseDemandDTO>> collect1 = transferWarehouseDemandDTOList.stream().collect(Collectors.groupingBy(TransferWarehouseDemandDTO::getProductCode));
            for (Map.Entry<String, List<TransferWarehouseDemandDTO>> entry : collect1.entrySet()) {
                String productCode = entry.getKey();
                List<TransferWarehouseDemandDTO> value = entry.getValue();
                String productClassify = value.get(0).getProductClassify();
                // 获取原材料缺口
                BigDecimal gapDemandQuantity = gapDemandQuantityMap.get(productCode);

                // 使用Map根据demandTime分组，并累加quantity
                Map<String, BigDecimal> mergedQuantities = value.stream()
                        .collect(Collectors.groupingBy(item ->
                                        DateUtils.dateToString(item.getDemandTime()),
                                Collectors.reducing(BigDecimal.ZERO, TransferWarehouseDemandDTO::getDemandQuantity, BigDecimal::add)
                        ));
                if ("BLA2150G".equals(productCode)) {
                    for (Map.Entry<String, BigDecimal> decimalEntry : mergedQuantities.entrySet()) {
                        log.info("######## 生成中转库需求,原材料;{}, 需求时间:{}, 需求数量:{}", productCode, decimalEntry.getKey(), decimalEntry.getValue());
                    }
                }

                // 将Map转换为List并排序
                List<Map.Entry<String, BigDecimal>> sortedEntries = new ArrayList<>(mergedQuantities.entrySet());
                sortedEntries.sort(Comparator.comparing(e -> {
                    try {
                        return dateFormat.parse(e.getKey());
                    } catch (ParseException ex) {
                        throw new RuntimeException(ex);
                    }
                }));

                for (Map.Entry<String, BigDecimal> sortedEntry : sortedEntries) {
                    String demandDate = sortedEntry.getKey();
                    BigDecimal demandQuantity = sortedEntry.getValue();
                    if ("BLA2150G".equals(productCode)) {
                        log.info("######## MRP计算中转库需求,原材料:{}, 需求时间:{}, 需求数量:{},缺口数量:{}", productCode, demandDate, demandQuantity, gapDemandQuantity);
                    }
                    if (null != gapDemandQuantity && gapDemandQuantity.compareTo(BigDecimal.ZERO) != 0) {
                        // 计算差异量
                        BigDecimal needDemandQuantity = demandQuantity.add(gapDemandQuantity);
                        if (needDemandQuantity.compareTo(BigDecimal.ZERO) > 0) {
                            // 生成中转库需求
                            MrpDemandDTO mrpDemandDTO = createMrpDemandDTO(productCode, productClassify, needDemandQuantity, DateUtils.stringToDate(demandDate));
                            mrpContextDTO.getMrpDemandList().add(mrpDemandDTO);
                        }
                        // 计算新的缺口
                        if (gapDemandQuantity.compareTo(BigDecimal.ZERO) < 0) {
                            gapDemandQuantity = gapDemandQuantity.add(demandQuantity);
                            gapDemandQuantity = gapDemandQuantity.compareTo(BigDecimal.ZERO) > 0 ? BigDecimal.ZERO : gapDemandQuantity;
                        } else {
                            gapDemandQuantity = BigDecimal.ZERO;
                        }
                    } else {
                        // 生成中转库需求
                        MrpDemandDTO mrpDemandDTO = createMrpDemandDTO(productCode, productClassify, demandQuantity, DateUtils.stringToDate(demandDate));
                        mrpContextDTO.getMrpDemandList().add(mrpDemandDTO);
                    }
                }
            }
        }
    }

    // 根据BOM拆解成品所需原材料需求数量(中转库)
    private void getTransitDepotsDemandData(NewProductStockPointVO productStockPointVO, Integer demandQuantityInt,
                                            Date demandTime,
                                            Map<String, RoutingVO> routingVOMap,
                                            Map<String, List<RoutingStepVO>> routingStepGroup,
                                            Map<String, List<RoutingStepInputVO>> routingStepInputGroup,
                                            Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId,
                                            List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList) {
        // 生成中转库材料需求
        getTransitDepotsDemand(productStockPointVO, routingVOMap, routingStepGroup, routingStepInputGroup, demandQuantityInt, demandTime, newProductStockPointVOMapOfId, transferWarehouseDemandDTOList);
    }

    private List<RoutingStepVO> getTransitDepotsDemand(NewProductStockPointVO productStockPointVO, Map<String, RoutingVO> routingVOMap,
                                                       Map<String, List<RoutingStepVO>> routingStepGroup, Map<String, List<RoutingStepInputVO>> routingStepInputGroup,
                                                       Integer demandQuantityInt, Date demandDate,
                                                       Map<String, NewProductStockPointVO> newProductStockPointVOMapOfId,
                                                       List<TransferWarehouseDemandDTO> transferWarehouseDemandDTOList) {
        // 根据发货计划拆解BOM,拆出推式的原材料需求，推式材料具体的需求时间 = 发货计划时间 - 中转提前期
        RoutingVO routingVO = routingVOMap.get(productStockPointVO.getProductCode());
        if (null == routingVO) {
            return null;
        }
        // 获取路径步骤
        List<RoutingStepVO> routingStepVOS = routingStepGroup.get(routingVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(routingStepVOS)) {
            return null;
        }
        // 获取末道步骤
        RoutingStepVO routingStepVO = routingStepVOS.stream().sorted(Comparator.comparing(RoutingStepVO::getSequenceNo)).collect(Collectors.toList()).get(routingStepVOS.size() - 1);
        // 获取输入物品
        List<RoutingStepInputVO> stepInputVOS = routingStepInputGroup.get(routingStepVO.getId());
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(stepInputVOS)) {
            return routingStepVOS;
        }
        // 过滤出BOM类型为推式的
        List<RoutingStepInputVO> pushInputList = stepInputVOS.stream().filter(item -> org.apache.commons.lang3.StringUtils.equals("推式", item.getSupplyType())).collect(Collectors.toList());

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pushInputList)) {
            for (RoutingStepInputVO routingStepInputVO : pushInputList) {
                NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMapOfId.get(routingStepInputVO.getInputProductId());
                // 需要为采购件、并且是B类
                if (null == newProductStockPointVO ||
                        !org.apache.commons.lang3.StringUtils.equals(newProductStockPointVO.getProductType(), "P") ||
                        noGlassMaterialTypeList.stream().noneMatch(materialType -> newProductStockPointVO.getProductClassify().matches(materialType + ".*"))) {
                    continue;
                }

                BigDecimal demandQuantity = new BigDecimal(demandQuantityInt).multiply(routingStepInputVO.getInputFactor()).setScale(6, RoundingMode.HALF_UP);
                TransferWarehouseDemandDTO transferWarehouseDemandDTO = TransferWarehouseDemandDTO.builder()
                        .productCode(newProductStockPointVO.getProductCode())
                        .productClassify(newProductStockPointVO.getProductClassify())
                        .demandTime(demandDate)
                        .demandQuantity(demandQuantity)
                        .build();
                transferWarehouseDemandDTOList.add(transferWarehouseDemandDTO);
            }
        }
        return routingStepVOS;
    }


    @Override
    protected void initOtherInfo(MrpContextDTO mrpContextDTO) {
        // 设置供应数据
        initMrpSupply(mrpContextDTO);
        // 设置材料与供应商关系数据
        initMaterialSupplierPurchase(mrpContextDTO);
        // 设置安全库存数据
        initSafetyInventory(mrpContextDTO);
        // 设置非中转库的期初库存
        initInventoryBatchDetail(mrpContextDTO);
    }

    private void initInventoryBatchDetail(MrpContextDTO mrpContextDTO) {
        DynamicDataSourceContextHolder.setDataSource(mrpContextDTO.getScenario());
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();
        List<String> auxiliarySubInventoryList = mrpContextDTO.getAuxiliarySubInventoryList();

        List<String> stockPointTypeList = Lists.newArrayList("CK", "GN");
        // 查询出中转库库存点
        List<NewStockPointVO> stockPointVOList = mdsFeign.selectNewStockPointVOByParams(mrpContextDTO.getScenario(), new HashMap<>());
        stockPointVOList = stockPointVOList.stream().filter(item -> !stockPointTypeList.contains(item.getStockPointType())).collect(Collectors.toList());
        List<String> stockPointCodeList = stockPointVOList.stream().map(NewStockPointVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String, List<InventoryBatchDetailVO>> openingInventoryMap = getOpeningInventoryMap(planUserProductCodeList, stockPointCodeList, auxiliarySubInventoryList,
                mrpContextDTO.getScenario());
        mrpContextDTO.setOpeningInventoryMap(openingInventoryMap);
    }

    private void initSafetyInventory(MrpContextDTO mrpContextDTO) {
        // 查询安全库存天数
        DynamicDataSourceContextHolder.setDataSource(mrpContextDTO.getScenario());
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();
        Map<String, SafetyInventoryVO> safetyInventoryVOMapProductCode;
        List<SafetyInventoryVO> safetyInventoryVOList = safetyInventoryService.selectByParams(ImmutableMap.of("materialCodeList", planUserProductCodeList));
        safetyInventoryVOMapProductCode = safetyInventoryVOList.stream()
                .collect(Collectors.toMap(SafetyInventoryVO::getMaterialCode, Function.identity(), (k1, k2) -> k2));
        mrpContextDTO.setSafetyInventoryVOList(safetyInventoryVOList);
        mrpContextDTO.setSafetyInventoryVOMapProductCode(safetyInventoryVOMapProductCode);
    }

    private void initMaterialSupplierPurchase(MrpContextDTO mrpContextDTO) {
        // 获取材料与供应商关系
        DynamicDataSourceContextHolder.setDataSource(mrpContextDTO.getScenario());
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();
        List<MaterialSupplierPurchaseVO> materialSupplierPurchaseVOS = materialSupplierPurchaseService
                .selectByParams(ImmutableMap.of("materialCodeList", planUserProductCodeList));

        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode = materialSupplierPurchaseVOS.stream()
                .collect(Collectors.toMap(MaterialSupplierPurchaseVO::getMaterialCode, Function.identity(), (k1, k2) -> k2));

        List<String> materialSupplierPurchaseIdList = materialSupplierPurchaseVOS.stream().map(MaterialSupplierPurchaseVO::getId).collect(Collectors.toList());

        List<SupplierPurchaseRatioVO> purchaseRatioVOList = supplierPurchaseRatioService
                .selectByParams(ImmutableMap.of("supplierProductIds", materialSupplierPurchaseIdList));
        if (CollectionUtils.isEmpty(purchaseRatioVOList)) {
            throw new BusinessException("没有供应商采购比例数据");
        }
        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = purchaseRatioVOList.stream()
                .collect(Collectors.groupingBy(SupplierPurchaseRatioVO::getSupplierProductId));

        // 获取承运商数据
        List<SupplierVO> supplierVOS = mdsFeign.selectSupplierByParams(mrpContextDTO.getScenario(), new HashMap<>());
        Map<String, SupplierVO> supplierVOMapOfId = supplierVOS.stream().collect(Collectors.toMap(SupplierVO::getId, Function.identity(), (k1, k2) -> k2));

        mrpContextDTO.setSupplierPurchaseVOMapOfMaterialCode(supplierPurchaseVOMapOfMaterialCode);
        mrpContextDTO.setSupplierPurchaseRatioVOGroup(supplierPurchaseRatioVOGroup);
        mrpContextDTO.setSupplierVOMapOfId(supplierVOMapOfId);
    }


    private void initMrpSupply(MrpContextDTO mrpContextDTO){
        String scenario = mrpContextDTO.getScenario();
        Date nowDate = mrpContextDTO.getMrpCalcDate();
        DynamicDataSourceContextHolder.setDataSource(scenario);
        Map<String, NewProductStockPointVO> newProductStockPointVOMapOfCode = mrpContextDTO.getNewProductStockPointVOMapOfCode();
        // 计划采购供应
        List<MrpSupplyDTO> planPurchaseSupplyList = new ArrayList<>();
        // 订单在途供应
        List<MrpSupplyDTO> transitOrderSupplyList = new ArrayList<>();
        // 1、查询材料到货跟踪
        List<MaterialArrivalTrackingVO> materialEnquiryTrackVOList = materialArrivalTrackingService.selectByParams(
                ImmutableMap.of("arrivalStatusList", Lists.newArrayList(ArrivalStatusEnum.PLAN_PRUCHASE.getCode(), ArrivalStatusEnum.DELIVERED.getCode())));
        if (CollectionUtils.isNotEmpty(materialEnquiryTrackVOList)) {
            // 1.1、查询计划采购订单
            List<MaterialArrivalTrackingVO> planPurchasesOrderList = materialEnquiryTrackVOList.stream()
                    .filter(item ->
                            StringUtils.equals(item.getArrivalStatus(), ArrivalStatusEnum.PLAN_PRUCHASE.getCode()) &&
                                    null != item.getRequireDate() &&
                                    StringUtils.isNotBlank(item.getMaterialCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(planPurchasesOrderList)) {
                for (MaterialArrivalTrackingVO materialArrivalTrackingVO : planPurchasesOrderList) {
                    Date requireDate = materialArrivalTrackingVO.getRequireDate();
                    if (null != materialArrivalTrackingVO.getPredictArrivalDate()) {
                        requireDate = materialArrivalTrackingVO.getPredictArrivalDate();
                    }
                    NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMapOfCode.get(materialArrivalTrackingVO.getMaterialCode());
                    // 要获取剩余待发货数量
                    MrpSupplyDTO mrpSupplyDTO = assembleMrpSupplyDTO(materialArrivalTrackingVO.getId(),
                            materialArrivalTrackingVO.getMaterialCode(),
                            materialArrivalTrackingVO.getWaitDeliveryQuantity(),
                            requireDate.compareTo(nowDate) < 0 ? nowDate : requireDate,
                            MrpSupplySourceEnum.PLAN_PURCHASE_INVENTORY.getCode(),
                            materialArrivalTrackingVO.getPurchaseOrderCode(),
                            null == newProductStockPointVO ? "" : newProductStockPointVO.getProductClassify(),
                            materialArrivalTrackingVO.getSourceId(),
                            materialArrivalTrackingVO.getDataSource());
                    planPurchaseSupplyList.add(mrpSupplyDTO);
                }
            }

            // 1.2、查询在途订单
            List<MaterialArrivalTrackingVO> transitOrderList = materialEnquiryTrackVOList.stream()
                    .filter(item -> StringUtils.equals(item.getArrivalStatus(), ArrivalStatusEnum.DELIVERED.getCode()) &&
                            null != item.getRequireDate() &&
                            StringUtils.isNotBlank(item.getMaterialCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(transitOrderList)) {
                for (MaterialArrivalTrackingVO materialArrivalTrackingVO : transitOrderList) {
                    Date requireDate = materialArrivalTrackingVO.getRequireDate();
                    if (null != materialArrivalTrackingVO.getPredictArrivalDate()) {
                        requireDate = materialArrivalTrackingVO.getPredictArrivalDate();
                    }
                    NewProductStockPointVO newProductStockPointVO = newProductStockPointVOMapOfCode.get(materialArrivalTrackingVO.getMaterialCode());
                    // 取预计到货数量，日期取预计到货日期
                    MrpSupplyDTO mrpSupplyDTO = assembleMrpSupplyDTO(materialArrivalTrackingVO.getId(),
                            materialArrivalTrackingVO.getMaterialCode(),
                            materialArrivalTrackingVO.getPredictArrivalQuantity(),
                            requireDate.compareTo(nowDate) < 0 ? nowDate : requireDate,
                            MrpSupplySourceEnum.TRANSIT_ORDER_INVENTORY.getCode(),
                            materialArrivalTrackingVO.getPurchaseOrderCode(),
                            null == newProductStockPointVO ? "" : newProductStockPointVO.getProductClassify(),
                            materialArrivalTrackingVO.getSourceId(),
                            materialArrivalTrackingVO.getDataSource());
                    transitOrderSupplyList.add(mrpSupplyDTO);
                }
            }
        }
        mrpContextDTO.setPlanPurchaseSupplyList(planPurchaseSupplyList);
        mrpContextDTO.setTransitOrderSupplyList(transitOrderSupplyList);
    }

    @Override
    protected List<MaterialArrivalTrackingDTO> doDateRecommend(MrpContextDTO mrpContextDTO) {
        Date nowDate = mrpContextDTO.getMrpCalcDate();
        // 获取用户权限下的物料
        List<String> planUserProductCodeList = mrpContextDTO.getPlanUserProductCodeList();
        // 获取毛需求数据
        List<MrpDemandDTO> mrpDemandList = mrpContextDTO.getMrpDemandList();
        mrpDemandList = getCaleMrpDemandList(mrpDemandList);

        // demand、supply数据进行分组
        Map<String, Map<String, List<MrpDemandDTO>>> mrpDemandGroupOfProductCode = mrpDemandList.stream()
                .collect(Collectors.groupingBy(MrpDemandDTO::getProductCode,
                        Collectors.groupingBy(item -> DateUtils.dateToString(item.getDemandTime(),
                                DateUtils.COMMON_DATE_STR3))));

        // 采购计划需求
        List<MrpSupplyDTO> planPurchaseSupplyAllList = mrpContextDTO.getPlanPurchaseSupplyList();
        // 在途订单需求
        List<MrpSupplyDTO> transitOrderSupplyAllList = mrpContextDTO.getTransitOrderSupplyList();
        List<MrpSupplyDTO> mrpSupplyDTOAllList = new ArrayList<>();
        mrpSupplyDTOAllList.addAll(planPurchaseSupplyAllList);
        mrpSupplyDTOAllList.addAll(transitOrderSupplyAllList);
        // 供应先根据物料进行分组
        Map<String, List<MrpSupplyDTO>> transitOrderSupplyGroupOfProductCode = transitOrderSupplyAllList.stream()
                .collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode));

        Map<String, List<MrpSupplyDTO>> planPurchaseSupplyGroupOfProductCode = planPurchaseSupplyAllList.stream()
                .collect(Collectors.groupingBy(MrpSupplyDTO::getProductCode));

        // 获取期初库存
        Map<String, List<InventoryBatchDetailVO>> openingInventoryMap = mrpContextDTO.getOpeningInventoryMap();

        // 获取材料与供应商关系
        Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMapOfMaterialCode = mrpContextDTO.getSupplierPurchaseVOMapOfMaterialCode();
        Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup = mrpContextDTO.getSupplierPurchaseRatioVOGroup();
        Map<String, SupplierVO> supplierVOMapOfId = mrpContextDTO.getSupplierVOMapOfId();

        // 获取安全库存天数
        Map<String, SafetyInventoryVO> safetyInventoryVOMapProductCode = mrpContextDTO.getSafetyInventoryVOMapProductCode();

        // 获取当前日期到6个月、8个月后的日期
        List<Date> inventoryShiftDateSixMonthList = getInventoryShiftDateList(nowDate, 180);
        List<Date> inventoryShiftDateEightMonthList = getInventoryShiftDateList(nowDate, 240);
        Collections.sort(inventoryShiftDateSixMonthList);
        Collections.sort(inventoryShiftDateEightMonthList);
        // 预先转换整个列表（适合多次查询）
        Map<String, Integer> dateIndexMap = new HashMap<>();
        SimpleDateFormat sdf = new SimpleDateFormat(DateUtils.COMMON_DATE_STR3);

        for (int i = 0; i < inventoryShiftDateEightMonthList.size(); i++) {
            Date date = inventoryShiftDateEightMonthList.get(i);
            if (date != null) {
                String key = sdf.format(date);
                // 只记录首次出现的索引
                dateIndexMap.putIfAbsent(key, i);
            }
        }

        // mrp需要计算的物料
        List<String> mrpCalcProductCodeList = getNeedMrpCalcProductCodeList(mrpDemandList, mrpSupplyDTOAllList, planUserProductCodeList);

        for (String productCode : mrpCalcProductCodeList) {
            // 每天对应的期末库存
            Map<Integer, BigDecimal> endingInventoryMapOfDateIndex = new HashMap<>();
            // 获取该物品的demand数据
            Map<String, List<MrpDemandDTO>> mrpDemandMapOfDateStr = mrpDemandGroupOfProductCode.get(productCode);

            // 获取该物品的supply数据
            List<MrpSupplyDTO> planPurchaseSupplyListOfProductCode = planPurchaseSupplyGroupOfProductCode.get(productCode);
            List<MrpSupplyDTO> transitOrderSupplyListOfProductCode = transitOrderSupplyGroupOfProductCode.get(productCode);

            // 获取材料与供应商关系
            MaterialSupplierPurchaseVO materialSupplierPurchaseVO = supplierPurchaseVOMapOfMaterialCode.getOrDefault(productCode, new MaterialSupplierPurchaseVO());
            // 获取关联的供应商数据
            List<SupplierPurchaseRatioVO> supplierPurchaseRatioVOList = supplierPurchaseRatioVOGroup.getOrDefault(materialSupplierPurchaseVO.getId(), new ArrayList<>());
            if (CollectionUtils.isEmpty(supplierPurchaseRatioVOList)) {
                // 为空，虚拟出供应商采购关系
                supplierPurchaseRatioVOList.add(new SupplierPurchaseRatioVO());
            }

            // 只考虑一个供应商，不考虑供货比例
            SupplierPurchaseRatioVO supplierPurchaseRatioVO = supplierPurchaseRatioVOList.get(0);

            String supplierId = supplierPurchaseRatioVO.getSupplierId();
            SupplierVO supplierVO = supplierVOMapOfId.get(supplierId);

            List<Date> transferDateList = new ArrayList<>();

            // 优先从MaterialSupplierPurchaseVO获取收货日历信息，为空则从SupplierVO获取
            String receivingWeekDay = materialSupplierPurchaseVO.getReceivingWeekDay();
            String receivingMonthDay = materialSupplierPurchaseVO.getReceivingMonthDay();

            // 如果MaterialSupplierPurchaseVO中没有设置，则尝试从SupplierVO获取
            if (StringUtils.isBlank(receivingWeekDay) && supplierVO != null) {
                receivingWeekDay = supplierVO.getReceivingWeekDay();
            }

            if (StringUtils.isBlank(receivingMonthDay) && supplierVO != null) {
                receivingMonthDay = supplierVO.getReceivingMonthDay();
            }

            // 解析收货日历信息
            List<String> weekDayList = new ArrayList<>();
            List<String> monthDayList = new ArrayList<>();
            if (StringUtils.isNotBlank(receivingWeekDay)) {
                weekDayList.addAll(Arrays.asList(receivingWeekDay.split(",")));
            }
            if (StringUtils.isNotBlank(receivingMonthDay)) {
                monthDayList.addAll(Arrays.asList(receivingMonthDay.split(",")));
            }

            for (int i = 0; i < 365; i++) {
                Date transferDate = DateUtils.moveDay(nowDate, i);
                // 未维护收货日历，则每天都收货
                if (supplierVO == null ||
                        (StringUtils.isBlank(receivingWeekDay) && StringUtils.isBlank(receivingMonthDay)) ||

                        (StringUtils.isBlank(receivingMonthDay) && StringUtils.isNotBlank(receivingWeekDay) &&
                                weekDayList.contains(String.valueOf(DateUtils.getDayOfWeek(transferDate)))) ||

                        (StringUtils.isBlank(receivingWeekDay) && StringUtils.isNotBlank(receivingMonthDay) &&
                                monthDayList.contains(String.valueOf(DateUtils.getDayOfMonth(transferDate))))) {
                    transferDateList.add(transferDate);
                }
            }
            Collections.sort(transferDateList);

            List<Date> inventoryShiftDateList = inventoryShiftDateSixMonthList;
            // 默认计算周期为6个月,如果物料的订单下达提前期大于30天，则计算8个月
            if (materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() != null &&
                    materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().compareTo(new BigDecimal(30)) > 0) {
                inventoryShiftDateList = inventoryShiftDateEightMonthList;
            }
            // 获取锁定期
            int lockPeriod = 0;
            if (supplierPurchaseVOMapOfMaterialCode.containsKey(productCode)) {
                if (StringUtils.equals(DemandPatternEnum.PO.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    lockPeriod = materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay() == null ? 0 : materialSupplierPurchaseVO.getOrderPlacementLeadTimeDay().intValue();
                }
                if (StringUtils.equals(DemandPatternEnum.PLAN_NEED.getCode(), materialSupplierPurchaseVO.getDemandPattern())) {
                    lockPeriod = materialSupplierPurchaseVO.getRequestCargoPlanLockDay() == null ? 0 : materialSupplierPurchaseVO.getRequestCargoPlanLockDay().intValue();
                }
            }
            // 锁定期最晚时间
            Date lockPeriodDate = DateUtils.moveDay(inventoryShiftDateList.get(0), lockPeriod != 0 ? lockPeriod - 1 : 0);


            // 安全库存数据
            SafetyInventoryVO safetyInventoryVO = safetyInventoryVOMapProductCode.getOrDefault(productCode, new SafetyInventoryVO());
            // 获取最小库存天数
            BigDecimal minStockDay = safetyInventoryVO.getMinInventoryDays() == null ? BigDecimal.ZERO : safetyInventoryVO.getMinInventoryDays();
            // 获取最大安全库存天数
            BigDecimal maxStockDay = safetyInventoryVO.getMaxInventoryDays() == null ? BigDecimal.ZERO : safetyInventoryVO.getMaxInventoryDays();
            // 获取该材料的期初库存
            BigDecimal openingInventory = BigDecimal.ZERO;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOList = openingInventoryMap.get(productCode);
            if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)) {
                for (InventoryBatchDetailVO inventoryBatchDetailVO : inventoryBatchDetailVOList) {
                    openingInventory = openingInventory.add(new BigDecimal(inventoryBatchDetailVO.getCurrentQuantity()));
                }
            }

            Map<Integer, Integer> indexCountMap = new HashMap<>();
            for (int i = 0; i < inventoryShiftDateList.size(); i++) {
                if (indexCountMap.containsKey(i)){
                    indexCountMap.put(i, indexCountMap.get(i) + 1);
                }else {
                    indexCountMap.put(i, 1);
                }
                Date inventoryShiftDate = inventoryShiftDateList.get(i);
                // 获取当天的demand的数据
                List<MrpDemandDTO> dayMrpDemandList = new ArrayList<>();
                if (MapUtils.isNotEmpty(mrpDemandMapOfDateStr)) {
                    dayMrpDemandList = mrpDemandMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                }
                List<MrpSupplyDTO> planPurchaseSupplyList = new ArrayList<>();
                List<MrpSupplyDTO> transitOrderSupplyList = new ArrayList<>();

                // 过滤出在锁定期前的供应数据
                if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
                    // 获取当天的supply的数据
                    Map<String, List<MrpSupplyDTO>> planPurchaseSupplyMapOfDateStr = planPurchaseSupplyListOfProductCode.stream()
                            .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                    planPurchaseSupplyList = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                }

                if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
                    transitOrderSupplyListOfProductCode = transitOrderSupplyListOfProductCode
                            .stream().filter(item -> item.getSupplyTime().compareTo(lockPeriodDate) <= 0)
                            .collect(Collectors.toList());
                    // 获取当天的supply的数据
                    if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
                        Map<String, List<MrpSupplyDTO>> transitOrderSupplyMapOfDateStr = transitOrderSupplyListOfProductCode.stream()
                                .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                        transitOrderSupplyList = transitOrderSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                    }
                }

                // 期末库存
                BigDecimal endingInventory;
                if (i != 0) {
                    // 期初库存 = 前1天的期末库存
                    endingInventory = endingInventoryMapOfDateIndex.get(i - 1);
                    openingInventory = endingInventory;
                }

                // 获取库存水位
                BigDecimal minStockQuantity = BigDecimal.ZERO;
                BigDecimal maxStockQuantity = BigDecimal.ZERO;
                if (MapUtils.isNotEmpty(mrpDemandMapOfDateStr)) {
                    minStockQuantity = caleInventoryWaterLevel(inventoryShiftDate, minStockDay, mrpDemandMapOfDateStr);
                    minStockQuantity = minStockQuantity.setScale(0, RoundingMode.HALF_UP);

                    maxStockQuantity = caleInventoryWaterLevel(inventoryShiftDate, maxStockDay, mrpDemandMapOfDateStr);
                    maxStockQuantity = maxStockQuantity.setScale(0, RoundingMode.HALF_UP);
                }

                // 获取毛需求
                BigDecimal demandQuantity = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(dayMrpDemandList)) {
                    demandQuantity = dayMrpDemandList.stream().map(MrpDemandDTO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .setScale(0, RoundingMode.CEILING);
                }

                // 当日supply数据
                BigDecimal planPurchaseSupplyQuantity = calculateSupplyQuantitySum(planPurchaseSupplyList);
                BigDecimal planPurchaseSupplyRecommendedQuantity = BigDecimal.ZERO;
                if (CollectionUtils.isNotEmpty(planPurchaseSupplyList)){
                    for (MrpSupplyDTO mrpSupplyDTO : planPurchaseSupplyList) {
                        if (StringUtils.isNotBlank(mrpSupplyDTO.getWhetherRecommended()) &&
                                StringUtils.equals(mrpSupplyDTO.getWhetherRecommended(), YesOrNoEnum.YES.getCode()) &&
                                null != mrpSupplyDTO.getSupplyQuantity()){
                            planPurchaseSupplyRecommendedQuantity = planPurchaseSupplyRecommendedQuantity.add(mrpSupplyDTO.getSupplyQuantity());
                        }
                    }
                }

                BigDecimal transitOrderSupplyQuantity = calculateSupplyQuantitySum(transitOrderSupplyList);

                // 总和计算
                BigDecimal supplyQuantitySum = planPurchaseSupplyQuantity.add(transitOrderSupplyQuantity);
                // 获取期末库存 = 期初库存量 - 毛需求 + 供应数量
                endingInventory = openingInventory.subtract(demandQuantity).add(supplyQuantitySum);
                // 获取期末库存，不包含在途的供应, 需要包括推荐的在途供应
//                BigDecimal endingInventoryRecommend = openingInventory.subtract(demandQuantity).add(transitOrderSupplyQuantity).add(planPurchaseSupplyRecommendedQuantity);
                if (productCode.equals("BFA1456G") || productCode.equals("BEA0730G")){
                    log.info("物料:{},推移日期:{}, 毛需求数量:{}, 期末库存:{}, 最大安全库存:{}, 最小安全库存:{}, 计划采购供应的数量:{}, 调整后计划采购供应的数量:{}",
                            productCode, DateUtils.dateToString(inventoryShiftDate), demandQuantity, endingInventory, maxStockQuantity,
                            minStockQuantity, planPurchaseSupplyQuantity, planPurchaseSupplyRecommendedQuantity);
                }
                if (endingInventory.compareTo(maxStockQuantity) > 0){
                    // 期末库存大于最大安全库存，当前的采购库存需求的时间需求往后挪，一直挪到小于等于最大安全库存
                    // 修改计划供应的要货日期,为下一段的发货日历日期
                    List<Date> nextTransferDateList = transferDateList.stream()
                            .filter(item -> item.compareTo(inventoryShiftDate) > 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(nextTransferDateList) && CollectionUtils.isNotEmpty(planPurchaseSupplyList)){
                        // 倒序
                        Collections.sort(nextTransferDateList);
                        endingInventory = recommendAfterNeedDate(nextTransferDateList, planPurchaseSupplyList, endingInventory, planPurchaseSupplyQuantity);
                        endingInventoryMapOfDateIndex.put(i, endingInventory);
                        continue;
                    }
                }

                if (endingInventory.compareTo(minStockQuantity) < 0){
                    // 期末库存小于最小安全库存
                    // 修改计划供应的要货日期,为上一段的发货日历日期
                    List<Date> beforeTransferDateList = transferDateList.stream()
                            .filter(item -> item.compareTo(inventoryShiftDate) <= 0)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(beforeTransferDateList)){
                        Collections.reverse(beforeTransferDateList);
                        // 判断当前日期是否为供应商收货日期，如果不是则往前找
                        Date preNeedDate = beforeTransferDateList.get(0);
                        BigDecimal afterSupplyQuantity = recommendBeforeNeedDate(preNeedDate, minStockQuantity, endingInventory,
                                inventoryShiftDateList, i, lockPeriodDate, planPurchaseSupplyListOfProductCode);
                        if (afterSupplyQuantity.compareTo(BigDecimal.ZERO) != 0 && indexCountMap.get(i) <= 1) {
                            // 更新索引i为上个收货日历的数据
                            Integer index = dateIndexMap.get(sdf.format(preNeedDate));
                            if (null != index){
                                endingInventoryMapOfDateIndex.put(i, endingInventory);
                                i = --index;
                                continue;
                            }
                        }
                    }
                }
                endingInventoryMapOfDateIndex.put(i, endingInventory);
            }
        }


        // 返回推荐的供应时间数据
        List<MrpSupplyDTO> recommendedMrpSupplyList = planPurchaseSupplyAllList.stream()
                .filter(item -> StringUtils.isNotBlank(item.getWhetherRecommended()))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(recommendedMrpSupplyList)){
            // 更新到货跟踪数据
            List<MaterialArrivalTrackingDTO> updateList = new ArrayList<>();
            for (MrpSupplyDTO mrpSupplyDTO : recommendedMrpSupplyList) {
                if (DateUtils.dateToString(mrpSupplyDTO.getSupplyTime()).equals(DateUtils.dateToString(mrpSupplyDTO.getOriginSupplyTime()))){
                    continue;
                }
                MaterialArrivalTrackingDTO materialArrivalTrackingDTO = new MaterialArrivalTrackingDTO();
                materialArrivalTrackingDTO.setId(mrpSupplyDTO.getSupplyId());
                materialArrivalTrackingDTO.setRecommendRequireDate(mrpSupplyDTO.getSupplyTime());
                updateList.add(materialArrivalTrackingDTO);
            }
            return updateList;
        }
        return Collections.emptyList();
    }

    private static MaterialNeedDateRecommendVO getMaterialNeedDateRecommendVO(MrpSupplyDTO mrpSupplyDTO) {
        MaterialNeedDateRecommendVO materialNeedDateRecommendVO = new MaterialNeedDateRecommendVO();
        materialNeedDateRecommendVO.setId(mrpSupplyDTO.getSupplyId());
        materialNeedDateRecommendVO.setProductCode(mrpSupplyDTO.getProductCode());
        materialNeedDateRecommendVO.setSourceId(mrpSupplyDTO.getSourceId());
        materialNeedDateRecommendVO.setDataSource(mrpSupplyDTO.getDataSource());
        materialNeedDateRecommendVO.setQuantity(mrpSupplyDTO.getSupplyQuantity());
        materialNeedDateRecommendVO.setNeedDate(mrpSupplyDTO.getOriginSupplyTime());
        materialNeedDateRecommendVO.setRecommendNeedDate(mrpSupplyDTO.getSupplyTime());
        return materialNeedDateRecommendVO;
    }

    private BigDecimal recommendBeforeNeedDate(Date preNeedDate, BigDecimal minStockQuantity, BigDecimal endingInventory,
                                               List<Date> inventoryShiftDateList, int index, Date lockPeriodDate,
                                               List<MrpSupplyDTO> planPurchaseSupplyListOfProductCode) {
        // 判断当前日期是否为供应商收货日期，如果不是则往前找
        // 需要找当前日期往后的供应,包含当天的计划采购供应
        // 获取差异量
        BigDecimal differenceQuantity = minStockQuantity.subtract(endingInventory);
        BigDecimal afterSupplyQuantity = BigDecimal.ZERO;
        for (int j = index; j < inventoryShiftDateList.size(); j++) {
            // 获取采购供应数量
            List<MrpSupplyDTO> planPurchaseListOfNextDate = new ArrayList<>();

            // 过滤出在锁定期前的供应数据
            if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
                // 获取当天的supply的数据
                Map<String, List<MrpSupplyDTO>> planPurchaseSupplyMapOfDateStr = planPurchaseSupplyListOfProductCode.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                planPurchaseListOfNextDate = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDateList.get(j), DateUtils.COMMON_DATE_STR3));
            }


            if (CollectionUtils.isEmpty(planPurchaseListOfNextDate)){
                continue;
            }
            // 按照需求数量进行排序，升序排序
            planPurchaseListOfNextDate.sort(Comparator.comparing(MrpSupplyDTO::getSupplyQuantity));
            for (MrpSupplyDTO mrpSupplyDTO : planPurchaseListOfNextDate) {
                if (afterSupplyQuantity.compareTo(differenceQuantity) >= 0){
                    break;
                }
                afterSupplyQuantity = afterSupplyQuantity.add(mrpSupplyDTO.getSupplyQuantity());
                mrpSupplyDTO.setSupplyTime(preNeedDate);
                mrpSupplyDTO.setWhetherRecommended(YesOrNoEnum.YES.getCode());
            }
            if (afterSupplyQuantity.compareTo(differenceQuantity) >= 0){
                break;
            }
        }
        return afterSupplyQuantity;
    }

    private BigDecimal recommendAfterNeedDate(List<Date> nextTransferDateList, List<MrpSupplyDTO> planPurchaseSupplyList,
                                              BigDecimal endingInventory, BigDecimal planPurchaseSupplyQuantity) {
//        List<MrpSupplyDTO> list = planPurchaseSupplyList.stream()
//                .filter(item -> StringUtils.isBlank(item.getWhetherRecommended()))
//                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planPurchaseSupplyList)){
            return BigDecimal.ZERO;
        }
        planPurchaseSupplyList.forEach(item -> {
            item.setSupplyTime(nextTransferDateList.get(0));
            item.setWhetherRecommended(YesOrNoEnum.YES.getCode());
        });
        // 期末库存需求扣减当前的采购供应数量
        return endingInventory.subtract(planPurchaseSupplyQuantity);
    }

    private List<MrpDemandDTO> getCaleMrpDemandList(List<MrpDemandDTO> mrpDemandList){
        // 过滤出PVB、B类的物料需求
        // 使用正则表达式进行模糊匹配
        mrpDemandList = mrpDemandList.stream()
                .filter(mrpDemandDTO ->
                        noGlassMaterialTypeList.stream().anyMatch(materialType -> mrpDemandDTO.getProductClassify().matches(materialType + ".*")) ||
                                StringUtils.equals(MrpDemandSourceEnum.ZZK.getCode(), mrpDemandDTO.getDemandSource()))
                .collect(Collectors.toList());
        return mrpDemandList;
    }

    private void assembleCurrentSupplyList(List<MrpSupplyDTO> planPurchaseSupplyList, List<MrpSupplyDTO> transitOrderSupplyList,
                                           List<MrpSupplyDTO> beforeDayPlanPurchaseSupplyList, List<MrpSupplyDTO> beforeDaytransitOrderSupplyList,
                                           List<MrpSupplyDTO> planPurchaseSupplyListOfProductCode, List<MrpSupplyDTO> transitOrderSupplyListOfProductCode,
                                           Date lockPeriodDate, Date inventoryShiftDate, Date beforeDate){
        // 过滤出在锁定期前的供应数据
        if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
            planPurchaseSupplyListOfProductCode = planPurchaseSupplyListOfProductCode
                    .stream().filter(item -> item.getSupplyTime().compareTo(lockPeriodDate) <= 0
                            || StringUtils.isNotBlank(item.getPurchaseOrderCode()))
                    .collect(Collectors.toList());
            // 获取当天的supply的数据
            if (CollectionUtils.isNotEmpty(planPurchaseSupplyListOfProductCode)) {
                Map<String, List<MrpSupplyDTO>> planPurchaseSupplyMapOfDateStr = planPurchaseSupplyListOfProductCode.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                planPurchaseSupplyList = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                if (null != beforeDate){
                    beforeDayPlanPurchaseSupplyList = planPurchaseSupplyMapOfDateStr.get(DateUtils.dateToString(beforeDate, DateUtils.COMMON_DATE_STR3));
                }
            }
        }

        if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
            transitOrderSupplyListOfProductCode = transitOrderSupplyListOfProductCode
                    .stream().filter(item -> item.getSupplyTime().compareTo(lockPeriodDate) <= 0)
                    .collect(Collectors.toList());
            // 获取当天的supply的数据
            if (CollectionUtils.isNotEmpty(transitOrderSupplyListOfProductCode)) {
                Map<String, List<MrpSupplyDTO>> transitOrderSupplyMapOfDateStr = transitOrderSupplyListOfProductCode.stream()
                        .collect(Collectors.groupingBy(item -> DateUtils.dateToString(item.getSupplyTime(), DateUtils.COMMON_DATE_STR3)));
                transitOrderSupplyList = transitOrderSupplyMapOfDateStr.get(DateUtils.dateToString(inventoryShiftDate, DateUtils.COMMON_DATE_STR3));
                if (null != beforeDate){
                    beforeDaytransitOrderSupplyList = transitOrderSupplyMapOfDateStr.get(DateUtils.dateToString(beforeDate, DateUtils.COMMON_DATE_STR3));
                }
            }
        }
    }

}