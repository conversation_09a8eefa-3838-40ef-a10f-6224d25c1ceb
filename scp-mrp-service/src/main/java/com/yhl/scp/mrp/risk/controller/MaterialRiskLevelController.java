package com.yhl.scp.mrp.risk.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import com.yhl.scp.mrp.risk.dto.MaterialRiskLevelDTO;
import com.yhl.scp.mrp.risk.service.MaterialRiskLevelService;
import com.yhl.scp.mrp.risk.vo.MaterialRiskLevelVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>MaterialRiskLevelController</code>
 * <p>
 * 材料风险等级控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-23 15:39:05
 */
@Slf4j
@Api(tags = "材料风险等级控制器")
@RestController
@RequestMapping("materialRiskLevel")
public class MaterialRiskLevelController extends BaseController {

    @Resource
    private MaterialRiskLevelService materialRiskLevelService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
//    @BusinessMonitorLog(businessCode = "风险等级确认", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<PageInfo<MaterialRiskLevelVO>> page() {
        List<MaterialRiskLevelVO> materialRiskLevelList = materialRiskLevelService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MaterialRiskLevelVO> pageInfo = new PageInfo<>(materialRiskLevelList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MaterialRiskLevelDTO materialRiskLevelDTO) {
        return materialRiskLevelService.doCreate(materialRiskLevelDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    @BusinessMonitorLog(businessCode = "风险等级确认", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<Void> update(@RequestBody MaterialRiskLevelDTO materialRiskLevelDTO) {
        return materialRiskLevelService.doUpdate(materialRiskLevelDTO);
    }

    @ApiOperation(value = "批量修改")
    @PostMapping(value = "updateBatch")
    @BusinessMonitorLog(businessCode = "风险等级确认", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<Void> updateBatch(@RequestBody List<MaterialRiskLevelDTO> materialRiskLevelDTO) {
         materialRiskLevelService.doUpdateBatch(materialRiskLevelDTO);
         return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        materialRiskLevelService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MaterialRiskLevelVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, materialRiskLevelService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "风险等级更新")
    @PostMapping(value = "updateRiskLevel")
    @BusinessMonitorLog(businessCode = "风险等级确认", moduleCode = "MRP", businessFrequency = "MONTH")
    public BaseResponse<Void> updateRiskLevel(@RequestBody List<String> ids) {

        String redisKey = MaterialRedisConstants.MATERIAL_RISK_LEVEL_RISK_KEY;
        try {
            if (Boolean.TRUE.equals(redisUtil.hasKey(redisKey))) {
                return BaseResponse.error("当前已有风险等级正在计算中，请等待计算完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 5);
            // 材料风险等级计算
            return materialRiskLevelService.updateRiskLevel(ids);
        } catch (Exception e) {
            log.error("材料风险等级计算失败", e);
            throw new BusinessException("材料风险等级计算失败,{0}", e.getLocalizedMessage());
        } finally {
            redisUtil.delete(redisKey);
        }
    }

}
