package com.yhl.scp.mrp.material.plan.domain.entity;

import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryOccupyPO;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanReplacePO;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <code>MrpAllocationResultDO</code>
 * <p>
 * 分配结果
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-05 17:25:42
 */
@Data
public class MrpAllocateResultDO {
    // 已分配数量
    private BigDecimal fulfillmentQuantity;
    // 使用标准规格数量
    private BigDecimal useStandardQuantity;
    // 使用替代料数量
    private BigDecimal useReplaceQuantity = BigDecimal.ZERO;
    // 库存占用信息
    private List<MaterialPlanInventoryOccupyPO> materialPlanInventoryOccupyList;

}
