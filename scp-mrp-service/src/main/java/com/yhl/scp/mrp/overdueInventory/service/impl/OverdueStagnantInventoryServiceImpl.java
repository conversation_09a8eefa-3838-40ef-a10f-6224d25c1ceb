package com.yhl.scp.mrp.overdueInventory.service.impl;


import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanInventoryShiftService;
import com.yhl.scp.mrp.material.plan.service.MaterialPlanVersionService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftService;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanInventoryShiftVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftVO;
import com.yhl.scp.mrp.overdueInventory.dto.OverdueStagnantInventoryDTO;
import com.yhl.scp.mrp.overdueInventory.dto.OverdueStagnantInventoryParamDTO;
import com.yhl.scp.mrp.overdueInventory.service.OverdueStagnantInventoryService;
import com.yhl.scp.mrp.overdueInventory.vo.OverdueStagnantInventoryVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OverdueStagnantInventoryServiceImpl</code>
 * <p>
 * 超期呆滞库存应用层实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-10 09:13:35
 */
@Slf4j
@Service
public class OverdueStagnantInventoryServiceImpl extends AbstractService implements OverdueStagnantInventoryService {

    @Resource
    private MaterialPlanInventoryShiftService materialPlanInventoryShiftService;

    @Resource
    private MaterialPlanVersionService materialPlanVersionService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private MpsFeign mpsFeign;

    @Resource
    private NoGlassInventoryShiftService noGlassInventoryShiftService;

    Map<String, BigDecimal> materialPlanInventoryDemandQuantityMap = new HashMap<>();

    List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS = new ArrayList<>();

    List<CleanDemandDataVO> cleanDemandDataVOS = new ArrayList<>();

    Map<String, BigDecimal> capatityBalabceDemandQuantityMap = new HashMap<>();

    Map<String, BigDecimal> cleanDemandQuantityMap = new HashMap<>();

    Map<String, List<CleanDemandDataVO>> cleanDemandDataMap = new HashMap<>();

    Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailIdMap = new HashMap<>();

    Map<String, List<String>> productCodeToCleanDemandIdListMap = new HashMap<>();

    Map<String, NewProductStockPointVO> productCodeVOMap = new HashMap<>();

    Map<String, MdsProductStockPointBaseVO> productCodeToProductBaseMap = new HashMap<>();

    @Override
    public BaseResponse<Void> doCreate(OverdueStagnantInventoryDTO dto) {
        return null;
    }

    @Override
    public BaseResponse<Void> doUpdate(OverdueStagnantInventoryDTO dto) {
        return null;
    }

    @Override
    public void doCreateBatch(List<OverdueStagnantInventoryDTO> list) {

    }

    @Override
    public void doUpdateBatch(List<OverdueStagnantInventoryDTO> list) {

    }

    @Override
    public int doDelete(List<String> ids) {
        return 0;
    }

    @Override
    public OverdueStagnantInventoryVO selectByPrimaryKey(String id) {
        return null;
    }

    @Override
    public List<OverdueStagnantInventoryVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        return Collections.emptyList();
    }

    @Override
    public List<OverdueStagnantInventoryVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        return Collections.emptyList();
    }

    @Override
    public List<OverdueStagnantInventoryVO> selectByParams(Map<String, Object> params) {
        return Collections.emptyList();
    }

    @Override
    public String getObjectType() {
        return "";
    }

    @Override
    public List<OverdueStagnantInventoryVO> invocation(List<OverdueStagnantInventoryVO> dataList, Map<String, Object> params, String invocation) {
        return Collections.emptyList();
    }


}
