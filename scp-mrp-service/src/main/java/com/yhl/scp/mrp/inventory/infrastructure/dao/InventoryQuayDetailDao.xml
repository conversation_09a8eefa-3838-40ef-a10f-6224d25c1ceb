<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryQuayDetailDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO">
        <!--@Table sds_material_inventory_quay_detail-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="product_spec" jdbcType="VARCHAR" property="productSpec"/>
        <result column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <result column="level" jdbcType="VARCHAR" property="level"/>
        <result column="lot_level_code" jdbcType="VARCHAR" property="lotLevelCode"/>
        <result column="lot_number" jdbcType="VARCHAR" property="lotNumber"/>
        <result column="per_box" jdbcType="VARCHAR" property="perBox"/>
        <result column="box" jdbcType="VARCHAR" property="box"/>
        <result column="actual_sent_quantity" jdbcType="VARCHAR" property="actualSentQuantity"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="weight" jdbcType="VARCHAR" property="weight"/>
        <result column="package_type" jdbcType="VARCHAR" property="packageType"/>
        <result column="po" jdbcType="VARCHAR" property="po"/>
        <result column="container_number" jdbcType="VARCHAR" property="containerNumber"/>
        <result column="actual_arrival_time" jdbcType="TIMESTAMP" property="actualArrivalTime"/>
        <result column="overdue_time" jdbcType="TIMESTAMP" property="overdueTime"/>
        <result column="port_name" jdbcType="VARCHAR" property="portName"/>
        <result column="carrier" jdbcType="VARCHAR" property="carrier"/>
        <result column="cutting_rate" jdbcType="VARCHAR" property="cuttingRate"/>
        <result column="container_delivery_time" jdbcType="TIMESTAMP" property="containerDeliveryTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="plan_number" jdbcType="VARCHAR" property="planNumber"/>
        <result column="line_id" jdbcType="VARCHAR" property="lineId"/>
        <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer"/>
        <result column="order_price" jdbcType="VARCHAR" property="orderPrice"/>
        <result column="po_number" jdbcType="VARCHAR" property="poNumber"/>
        <result column="bill_no" jdbcType="VARCHAR" property="billNo"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="storage_flag" jdbcType="VARCHAR" property="storageFlag"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO">
        <result column="product_thickness" jdbcType="VARCHAR" property="productThickness"/>
        <result column="product_color" jdbcType="VARCHAR" property="productColor"/>
        <result column="classify_desc" jdbcType="VARCHAR" property="classifyDesc"/>
        <result column="delivered" jdbcType="VARCHAR" property="delivered"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,product_id,product_code,product_spec,stock_point_code,level,lot_level_code,lot_number,per_box,box
        ,actual_sent_quantity,area,weight,package_type,po,container_number
        ,actual_arrival_time,overdue_time,port_name,carrier,cutting_rate,container_delivery_time
        ,remark,enabled,creator,create_time,modifier,modify_time,plan_number,line_id,manufacturer
        ,order_price,po_number,bill_no,delivery_time,storage_flag
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_thickness,product_color,classify_desc,delivered
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach item="item" index="index" collection="params.ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach item="item" index="index" collection="params.productCodes" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.productSpec != null and params.productSpec != ''">
                and product_spec = #{params.productSpec,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCode != null and params.stockPointCode != ''">
                and stock_point_code = #{params.stockPointCode,jdbcType=VARCHAR}
            </if>
            <if test="params.level != null and params.level != ''">
                and level = #{params.level,jdbcType=VARCHAR}
            </if>
            <if test="params.lotLevelCode != null and params.lotLevelCode != ''">
                and lot_level_code = #{params.lotLevelCode,jdbcType=VARCHAR}
            </if>
            <if test="params.lotNumber != null and params.lotNumber != ''">
                and lot_number = #{params.lotNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.lotNumbers != null and params.lotNumbers.size() > 0">
                and lot_number in
                <foreach collection="params.lotNumbers" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.perBox != null">
                and per_box = #{params.perBox,jdbcType=VARCHAR}
            </if>
            <if test="params.box != null">
                and box = #{params.box,jdbcType=VARCHAR}
            </if>
            <if test="params.actualSentQuantity != null">
                and actual_sent_quantity = #{params.actualSentQuantity,jdbcType=VARCHAR}
            </if>
            <if test="params.area != null">
                and area = #{params.area,jdbcType=VARCHAR}
            </if>
            <if test="params.weight != null">
                and weight = #{params.weight,jdbcType=VARCHAR}
            </if>
            <if test="params.packageType != null and params.packageType != ''">
                and package_type = #{params.packageType,jdbcType=VARCHAR}
            </if>
            <if test="params.po != null and params.po != ''">
                and po = #{params.po,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNumber != null and params.containerNumber != ''">
                and container_number = #{params.containerNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.containerNumberList != null and params.containerNumberList.size() > 0">
                and container_number IN
                <foreach item="item" index="index" collection="params.containerNumberList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.actualArrivalTime != null">
                and actual_arrival_time = #{params.actualArrivalTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.overdueTime != null">
                and overdue_time = #{params.overdueTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.portName != null and params.portName != ''">
                and port_name = #{params.portName,jdbcType=VARCHAR}
            </if>
            <if test="params.carrier != null and params.carrier != ''">
                and carrier = #{params.carrier,jdbcType=VARCHAR}
            </if>
            <if test="params.cuttingRate != null">
                and cutting_rate = #{params.cuttingRate,jdbcType=VARCHAR}
            </if>
            <if test="params.containerDeliveryTime != null">
                and container_delivery_time = #{params.containerDeliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.planNumber != null and params.planNumber != ''">
                and plan_number = #{params.planNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.lineId != null and params.lineId != ''">
                and line_id = #{params.lineId,jdbcType=VARCHAR}
            </if>
            <if test="params.billNo != null and params.billNo != ''">
                and bill_no = #{params.billNo,jdbcType=VARCHAR}
            </if>
            <if test="params.deliveryTime != null">
                and delivery_time = #{params.deliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.delivered != null and params.delivered != ''">
                and delivered = #{params.delivered,jdbcType=VARCHAR}
            </if>
            <if test="params.delivered != null and params.delivered != ''">
                and delivered = #{params.delivered,jdbcType=VARCHAR}
            </if>
            <if test="params.manufacturer != null and params.manufacturer != ''">
                and manufacturer = #{params.manufacturer,jdbcType=VARCHAR}
            </if>
            <if test="params.orderPrice != null and params.orderPrice != ''">
                and order_price = #{params.orderPrice,jdbcType=VARCHAR}
            </if>
            <if test="params.poNumber != null and params.poNumber != ''">
                and po_number = #{params.poNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.storageFlag != null and params.storageFlag != ''">
                and storage_flag = #{params.storageFlag,jdbcType=VARCHAR}
            </if>
            <if test="params.poList != null and params.poList.size() > 0">
                and po in
                <foreach item="item" index="index" collection="params.poList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_quay_detail
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_quay_detail
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_sds_material_inventory_quay_detail
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <!-- 动态排序 -->
        <choose>
            <when test="sortParam != null and sortParam != ''">
                order by ${sortParam}
            </when>
            <when test="overdueSort != null and overdueSort != '' and overdueSort == 'YES'">
                order by overdue_time ASC
            </when>
        </choose>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_quay_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 条件查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_sds_material_inventory_quay_detail
        <include refid="Base_Where_Condition"/>
    </select>
    <select id="selectByInventoryQuayDetailIds" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_quay_detail
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                plan_number = #{item.detailVO.planNumber,jdbcType=VARCHAR} and line_id =
                #{item.detailVO.lineId,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <select id="selectByInventoryQuayDetailFloatGlassShippedDetail" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from sds_material_inventory_quay_detail
        <where>
            <foreach collection="list" item="item" index="index" separator="or">
                (
                lot_number = #{item.lotNumber,jdbcType=VARCHAR} and plan_number =
                #{item.planNumber,jdbcType=VARCHAR} and line_id =
                #{item.lineId,jdbcType=VARCHAR}

                )
            </foreach>
        </where>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_material_inventory_quay_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        box,
        actual_sent_quantity,
        area,
        weight,
        package_type,
        po,
        container_number,
        actual_arrival_time,
        overdue_time,
        port_name,
        carrier,
        cutting_rate,
        container_delivery_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        plan_number,
        line_id,
        bill_no,
        delivery_time,
        manufacturer,
        order_price,
        po_number,
        storage_flag)
        values (
        #{id,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{productSpec,jdbcType=VARCHAR},
        #{stockPointCode,jdbcType=VARCHAR},
        #{level,jdbcType=VARCHAR},
        #{lotLevelCode,jdbcType=VARCHAR},
        #{lotNumber,jdbcType=VARCHAR},
        #{perBox,jdbcType=VARCHAR},
        #{box,jdbcType=VARCHAR},
        #{actualSentQuantity,jdbcType=VARCHAR},
        #{area,jdbcType=VARCHAR},
        #{weight,jdbcType=VARCHAR},
        #{packageType,jdbcType=VARCHAR},
        #{po,jdbcType=VARCHAR},
        #{containerNumber,jdbcType=VARCHAR},
        #{actualArrivalTime,jdbcType=TIMESTAMP},
        #{overdueTime,jdbcType=TIMESTAMP},
        #{portName,jdbcType=VARCHAR},
        #{carrier,jdbcType=VARCHAR},
        #{cuttingRate,jdbcType=VARCHAR},
        #{containerDeliveryTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{planNumber,jdbcType=VARCHAR},
        #{lineId,jdbcType=VARCHAR},
        #{billNo,jdbcType=VARCHAR},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{manufacturer,jdbcType=VARCHAR},
        #{orderPrice,jdbcType=VARCHAR},
        #{poNumber,jdbcType=VARCHAR},
        #{storageFlag,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO">
        insert into sds_material_inventory_quay_detail(id,
                                                       product_id,
                                                       product_code,
                                                       product_spec,
                                                       stock_point_code,
                                                       level,
                                                       lot_level_code,
                                                       lot_number,
                                                       per_box,
                                                       box,
                                                       actual_sent_quantity,
                                                       area,
                                                       weight,
                                                       package_type,
                                                       po,
                                                       container_number,
                                                       actual_arrival_time,
                                                       overdue_time,
                                                       port_name,
                                                       carrier,
                                                       cutting_rate,
                                                       container_delivery_time,
                                                       remark,
                                                       enabled,
                                                       creator,
                                                       create_time,
                                                       modifier,
                                                       modify_time,
                                                       plan_number,
                                                       line_id,
                                                       bill_no,
                                                       delivery_time,
                                                       manufacturer,
                                                       order_price,
                                                       po_number,
                                                       storage_flag)
        values (#{id,jdbcType=VARCHAR},
                #{productId,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{productSpec,jdbcType=VARCHAR},
                #{stockPointCode,jdbcType=VARCHAR},
                #{level,jdbcType=VARCHAR},
                #{lotLevelCode,jdbcType=VARCHAR},
                #{lotNumber,jdbcType=VARCHAR},
                #{perBox,jdbcType=VARCHAR},
                #{box,jdbcType=VARCHAR},
                #{actualSentQuantity,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR},
                #{weight,jdbcType=VARCHAR},
                #{packageType,jdbcType=VARCHAR},
                #{po,jdbcType=VARCHAR},
                #{containerNumber,jdbcType=VARCHAR},
                #{actualArrivalTime,jdbcType=TIMESTAMP},
                #{overdueTime,jdbcType=TIMESTAMP},
                #{portName,jdbcType=VARCHAR},
                #{carrier,jdbcType=VARCHAR},
                #{cuttingRate,jdbcType=VARCHAR},
                #{containerDeliveryTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{planNumber,jdbcType=VARCHAR},
                #{lineId,jdbcType=VARCHAR},
                #{billNo,jdbcType=VARCHAR},
                #{deliveryTime,jdbcType=TIMESTAMP},
                #{manufacturer,jdbcType=VARCHAR},
                #{orderPrice,jdbcType=VARCHAR},
                #{poNumber,jdbcType=VARCHAR},
                #{storageFlag,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_material_inventory_quay_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        box,
        actual_sent_quantity,
        area,
        weight,
        package_type,
        po,
        container_number,
        actual_arrival_time,
        overdue_time,
        port_name,
        carrier,
        cutting_rate,
        container_delivery_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        plan_number,
        line_id,
        bill_no,
        delivery_time,
        manufacturer,
        order_price,
        po_number,
        storage_flag)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productSpec,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.level,jdbcType=VARCHAR},
            #{entity.lotLevelCode,jdbcType=VARCHAR},
            #{entity.lotNumber,jdbcType=VARCHAR},
            #{entity.perBox,jdbcType=VARCHAR},
            #{entity.box,jdbcType=VARCHAR},
            #{entity.actualSentQuantity,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.packageType,jdbcType=VARCHAR},
            #{entity.po,jdbcType=VARCHAR},
            #{entity.containerNumber,jdbcType=VARCHAR},
            #{entity.actualArrivalTime,jdbcType=TIMESTAMP},
            #{entity.overdueTime,jdbcType=TIMESTAMP},
            #{entity.portName,jdbcType=VARCHAR},
            #{entity.carrier,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.containerDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.planNumber,jdbcType=VARCHAR},
            #{entity.lineId,jdbcType=VARCHAR},
            #{entity.billNo,jdbcType=VARCHAR},
            #{entity.deliveryTime,jdbcType=TIMESTAMP},
            #{entity.manufacturer,jdbcType=VARCHAR},
            #{entity.orderPrice,jdbcType=VARCHAR},
            #{entity.poNumber,jdbcType=VARCHAR},
            #{entity.storageFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_material_inventory_quay_detail(
        id,
        product_id,
        product_code,
        product_spec,
        stock_point_code,
        level,
        lot_level_code,
        lot_number,
        per_box,
        box,
        actual_sent_quantity,
        area,
        weight,
        package_type,
        po,
        container_number,
        actual_arrival_time,
        overdue_time,
        port_name,
        carrier,
        cutting_rate,
        container_delivery_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        plan_number,
        line_id,
        bill_no,
        delivery_time,
        manufacturer,
        order_price,
        po_number,
        storage_flag)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.productId,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.productSpec,jdbcType=VARCHAR},
            #{entity.stockPointCode,jdbcType=VARCHAR},
            #{entity.level,jdbcType=VARCHAR},
            #{entity.lotLevelCode,jdbcType=VARCHAR},
            #{entity.lotNumber,jdbcType=VARCHAR},
            #{entity.perBox,jdbcType=VARCHAR},
            #{entity.box,jdbcType=VARCHAR},
            #{entity.actualSentQuantity,jdbcType=VARCHAR},
            #{entity.area,jdbcType=VARCHAR},
            #{entity.weight,jdbcType=VARCHAR},
            #{entity.packageType,jdbcType=VARCHAR},
            #{entity.po,jdbcType=VARCHAR},
            #{entity.containerNumber,jdbcType=VARCHAR},
            #{entity.actualArrivalTime,jdbcType=TIMESTAMP},
            #{entity.overdueTime,jdbcType=TIMESTAMP},
            #{entity.portName,jdbcType=VARCHAR},
            #{entity.carrier,jdbcType=VARCHAR},
            #{entity.cuttingRate,jdbcType=VARCHAR},
            #{entity.containerDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.planNumber,jdbcType=VARCHAR},
            #{entity.lineId,jdbcType=VARCHAR},
            #{entity.billNo,jdbcType=VARCHAR},
            #{entity.deliveryTime,jdbcType=TIMESTAMP},
            #{entity.manufacturer,jdbcType=VARCHAR},
            #{entity.orderPrice,jdbcType=VARCHAR},
            #{entity.poNumber,jdbcType=VARCHAR},
            #{entity.storageFlag,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO">
        update sds_material_inventory_quay_detail
        set product_id              = #{productId,jdbcType=VARCHAR},
            product_code            = #{productCode,jdbcType=VARCHAR},
            product_spec            = #{productSpec,jdbcType=VARCHAR},
            stock_point_code        = #{stockPointCode,jdbcType=VARCHAR},
            level                   = #{level,jdbcType=VARCHAR},
            lot_level_code          = #{lotLevelCode,jdbcType=VARCHAR},
            lot_number              = #{lotNumber,jdbcType=VARCHAR},
            per_box                 = #{perBox,jdbcType=VARCHAR},
            box                     = #{box,jdbcType=VARCHAR},
            actual_sent_quantity    = #{actualSentQuantity,jdbcType=VARCHAR},
            area                    = #{area,jdbcType=VARCHAR},
            weight                  = #{weight,jdbcType=VARCHAR},
            package_type            = #{packageType,jdbcType=VARCHAR},
            po                      = #{po,jdbcType=VARCHAR},
            container_number        = #{containerNumber,jdbcType=VARCHAR},
            actual_arrival_time     = #{actualArrivalTime,jdbcType=TIMESTAMP},
            overdue_time            = #{overdueTime,jdbcType=TIMESTAMP},
            port_name               = #{portName,jdbcType=VARCHAR},
            carrier                 = #{carrier,jdbcType=VARCHAR},
            cutting_rate            = #{cuttingRate,jdbcType=VARCHAR},
            container_delivery_time = #{containerDeliveryTime,jdbcType=TIMESTAMP},
            remark                  = #{remark,jdbcType=VARCHAR},
            enabled                 = #{enabled,jdbcType=VARCHAR},
            modifier                = #{modifier,jdbcType=VARCHAR},
            modify_time             = #{modifyTime,jdbcType=TIMESTAMP},
            plan_number             = #{planNumber,jdbcType=VARCHAR},
            line_id                 = #{lineId,jdbcType=VARCHAR},
            bill_no                 = #{billNo,jdbcType=VARCHAR},
            delivery_time           = #{deliveryTime,jdbcType=TIMESTAMP},
            manufacturer             = #{manufacturer,jdbcType=VARCHAR},
            order_price             = #{orderPrice,jdbcType=VARCHAR},
            po_number               = #{poNumber,jdbcType=VARCHAR},
            storage_flag         = #{storageFlag,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mrp.inventory.infrastructure.po.InventoryQuayDetailPO">
        update sds_material_inventory_quay_detail
        <set>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.productSpec != null and item.productSpec != ''">
                product_spec = #{item.productSpec,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointCode != null and item.stockPointCode != ''">
                stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
            </if>
            <if test="item.level != null and item.level != ''">
                level = #{item.level,jdbcType=VARCHAR},
            </if>
            <if test="item.lotLevelCode != null and item.lotLevelCode != ''">
                lot_level_code = #{item.lotLevelCode,jdbcType=VARCHAR},
            </if>
            <if test="item.lotNumber != null and item.lotNumber != ''">
                lot_number = #{item.lotNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.perBox != null">
                per_box = #{item.perBox,jdbcType=VARCHAR},
            </if>
            <if test="item.box != null">
                box = #{item.box,jdbcType=VARCHAR},
            </if>
            <if test="item.actualSentQuantity != null">
                actual_sent_quantity = #{item.actualSentQuantity,jdbcType=VARCHAR},
            </if>
            <if test="item.area != null">
                area = #{item.area,jdbcType=VARCHAR},
            </if>
            <if test="item.weight != null">
                weight = #{item.weight,jdbcType=VARCHAR},
            </if>
            <if test="item.packageType != null and item.packageType != ''">
                package_type = #{item.packageType,jdbcType=VARCHAR},
            </if>
            <if test="item.po != null and item.po != ''">
                po = #{item.po,jdbcType=VARCHAR},
            </if>
            <if test="item.containerNumber != null and item.containerNumber != ''">
                container_number = #{item.containerNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.actualArrivalTime != null">
                actual_arrival_time = #{item.actualArrivalTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.overdueTime != null">
                overdue_time = #{item.overdueTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.portName != null and item.portName != ''">
                port_name = #{item.portName,jdbcType=VARCHAR},
            </if>
            <if test="item.carrier != null and item.carrier != ''">
                carrier = #{item.carrier,jdbcType=VARCHAR},
            </if>
            <if test="item.cuttingRate != null">
                cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
            </if>
            <if test="item.containerDeliveryTime != null">
                container_delivery_time = #{item.containerDeliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.planNumber != null and item.planNumber != ''">
                plan_number = #{item.planNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.lineId != null and item.lineId != ''">
                line_id = #{item.lineId,jdbcType=VARCHAR},
            </if>
            <if test="item.manufacturer != null and item.manufacturer != ''">
                manufacturer = #{item.manufacturer,jdbcType=VARCHAR},
            </if>
            <if test="item.orderPrice != null and item.orderPrice != ''">
                order_price = #{item.orderPrice,jdbcType=VARCHAR},
            </if>
            <if test="item.poNumber != null and item.poNumber != ''">
                po_number = #{item.poNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.billNo != null and item.billNo != ''">
                bill_no = #{item.billNo,jdbcType=VARCHAR},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.storageFlag != null and item.storageFlag != ''">
                storage_flag = #{item.storageFlag,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_material_inventory_quay_detail
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_spec = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productSpec,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stockPointCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="level = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.level,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_level_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotLevelCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lot_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lotNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="per_box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.perBox,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.box,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="actual_sent_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualSentQuantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="area = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.area,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="weight = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.weight,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="package_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.packageType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="po = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.po,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="actual_arrival_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.actualArrivalTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="overdue_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overdueTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="port_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.portName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="carrier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.carrier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cutting_rate = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.cuttingRate,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="container_delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.containerDeliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="plan_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.planNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="line_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lineId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="bill_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.billNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="manufacturer = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.manufacturer,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderPrice,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="po_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.poNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="storage_flag = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.storageFlag,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update sds_material_inventory_quay_detail
            <set>
                <if test="item.productId != null and item.productId != ''">
                    product_id = #{item.productId,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.productSpec != null and item.productSpec != ''">
                    product_spec = #{item.productSpec,jdbcType=VARCHAR},
                </if>
                <if test="item.stockPointCode != null and item.stockPointCode != ''">
                    stock_point_code = #{item.stockPointCode,jdbcType=VARCHAR},
                </if>
                <if test="item.level != null and item.level != ''">
                    level = #{item.level,jdbcType=VARCHAR},
                </if>
                <if test="item.lotLevelCode != null and item.lotLevelCode != ''">
                    lot_level_code = #{item.lotLevelCode,jdbcType=VARCHAR},
                </if>
                <if test="item.lotNumber != null and item.lotNumber != ''">
                    lot_number = #{item.lotNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.perBox != null">
                    per_box = #{item.perBox,jdbcType=VARCHAR},
                </if>
                <if test="item.box != null">
                    box = #{item.box,jdbcType=VARCHAR},
                </if>
                <if test="item.actualSentQuantity != null">
                    actual_sent_quantity = #{item.actualSentQuantity,jdbcType=VARCHAR},
                </if>
                <if test="item.area != null">
                    area = #{item.area,jdbcType=VARCHAR},
                </if>
                <if test="item.weight != null">
                    weight = #{item.weight,jdbcType=VARCHAR},
                </if>
                <if test="item.packageType != null and item.packageType != ''">
                    package_type = #{item.packageType,jdbcType=VARCHAR},
                </if>
                <if test="item.po != null and item.po != ''">
                    po = #{item.po,jdbcType=VARCHAR},
                </if>
                <if test="item.containerNumber != null and item.containerNumber != ''">
                    container_number = #{item.containerNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.actualArrivalTime != null">
                    actual_arrival_time = #{item.actualArrivalTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.overdueTime != null">
                    overdue_time = #{item.overdueTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.portName != null and item.portName != ''">
                    port_name = #{item.portName,jdbcType=VARCHAR},
                </if>
                <if test="item.carrier != null and item.carrier != ''">
                    carrier = #{item.carrier,jdbcType=VARCHAR},
                </if>
                <if test="item.cuttingRate != null">
                    cutting_rate = #{item.cuttingRate,jdbcType=VARCHAR},
                </if>
                <if test="item.containerDeliveryTime != null">
                    container_delivery_time = #{item.containerDeliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.planNumber != null and item.planNumber != ''">
                    plan_number = #{item.planNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.lineId != null and item.lineId != ''">
                    line_id = #{item.lineId,jdbcType=VARCHAR},
                </if>
                <if test="item.manufacturer != null and item.manufacturer != ''">
                    manufacturer = #{item.manufacturer,jdbcType=VARCHAR},
                </if>
                <if test="item.orderPrice != null and item.orderPrice != ''">
                    order_price = #{item.orderPrice,jdbcType=VARCHAR},
                </if>
                <if test="item.poNumber != null and item.poNumber != ''">
                    po_number = #{item.poNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.billNo != null and item.billNo != ''">
                    bill_no = #{item.billNo,jdbcType=VARCHAR},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.storageFlag != null and item.storageFlag != ''">
                    storage_flag = #{item.storageFlag,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from sds_material_inventory_quay_detail
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_material_inventory_quay_detail where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
