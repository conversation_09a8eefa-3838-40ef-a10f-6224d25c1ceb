package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.Role;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.convertor.MaterialPlanInventoryShiftConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.MaterialPlanInventoryShiftDO;
import com.yhl.scp.mrp.material.plan.domain.service.MaterialPlanInventoryShiftDomainService;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanInventoryShiftDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.MaterialPlanInventoryShiftDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.MaterialPlanInventoryShiftPO;
import com.yhl.scp.mrp.material.plan.service.*;
import com.yhl.scp.mrp.material.plan.service.utils.GlassMrpUtil;
import com.yhl.scp.mrp.material.plan.vo.*;
import com.yhl.scp.mrp.supplier.service.MaterialSupplierPurchaseService;
import com.yhl.scp.mrp.supplier.service.SupplierPurchaseRatioService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MaterialPlanInventoryShiftServiceImpl</code>
 * <p>
 * 物料库存推移应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-31 21:18:25
 */
@Slf4j
@Service
public class MaterialPlanInventoryShiftServiceImpl extends AbstractService implements MaterialPlanInventoryShiftService {

    @Resource
    private MaterialPlanInventoryShiftDao materialPlanInventoryShiftDao;

    @Resource
    private MaterialPlanInventoryShiftDomainService materialPlanInventoryShiftDomainService;

    @Resource
    private MaterialSupplierPurchaseService materialSupplierPurchaseService;

    @Resource
    private MaterialPlanVersionService materialPlanVersionService;

    @Resource
    private SupplierPurchaseRatioService supplierPurchaseRatioService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private NoGlassInventoryShiftService noGlassInventoryShiftService;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(MaterialPlanInventoryShiftDTO materialPlanInventoryShiftDTO) {
        // 0.数据转换
        MaterialPlanInventoryShiftDO materialPlanInventoryShiftDO = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Do(materialPlanInventoryShiftDTO);
        MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Po(materialPlanInventoryShiftDTO);
        // 1.数据校验
        materialPlanInventoryShiftDomainService.validation(materialPlanInventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(materialPlanInventoryShiftPO);
        materialPlanInventoryShiftDao.insertWithPrimaryKey(materialPlanInventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MaterialPlanInventoryShiftDTO materialPlanInventoryShiftDTO) {
        // 0.数据转换
        MaterialPlanInventoryShiftDO materialPlanInventoryShiftDO = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Do(materialPlanInventoryShiftDTO);
        MaterialPlanInventoryShiftPO materialPlanInventoryShiftPO = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Po(materialPlanInventoryShiftDTO);
        // 1.数据校验
        materialPlanInventoryShiftDomainService.validation(materialPlanInventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(materialPlanInventoryShiftPO);
        materialPlanInventoryShiftDao.update(materialPlanInventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MaterialPlanInventoryShiftDTO> list) {
        List<MaterialPlanInventoryShiftPO> newList = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        materialPlanInventoryShiftDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MaterialPlanInventoryShiftDTO> list) {
        List<MaterialPlanInventoryShiftPO> newList = MaterialPlanInventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        materialPlanInventoryShiftDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return materialPlanInventoryShiftDao.deleteBatch(idList);
        }
        return materialPlanInventoryShiftDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MaterialPlanInventoryShiftVO selectByPrimaryKey(String id) {
        MaterialPlanInventoryShiftPO po = materialPlanInventoryShiftDao.selectByPrimaryKey(id);
        return MaterialPlanInventoryShiftConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_inventory_shift")
    public List<MaterialPlanInventoryShiftVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_material_plan_inventory_shift")
    public List<MaterialPlanInventoryShiftVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MaterialPlanInventoryShiftVO> dataList = materialPlanInventoryShiftDao.selectByCondition(sortParam, queryCriteriaParam);
        MaterialPlanInventoryShiftServiceImpl target = SpringBeanUtils.getBean(MaterialPlanInventoryShiftServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> selectByParams(Map<String, Object> params) {
        List<MaterialPlanInventoryShiftPO> list = materialPlanInventoryShiftDao.selectByParams(params);
        return MaterialPlanInventoryShiftConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
//        return ObjectTypeEnum.MATERIAL_PLAN_INVENTORY_SHIFT.getCode();
        return "";
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> invocation(List<MaterialPlanInventoryShiftVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> selectForDemandCalculation(Map<String, Object> queryMap) {
        return materialPlanInventoryShiftDao.selectForDemandCalculation(queryMap);
    }

    @Override
    public PageInfo<MaterialPlanInventoryShiftGlassPageVO> selectPageGlass(GlassInventoryShiftQueryParamDTO dto) {




        List<String> dynamicColumnParam = Lists.newArrayList("product_code");
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("materialPlanner", SystemHolder.getUserId());
//        queryParams.put("materialPlanner", "8d62ab0f-018d-72b118fe-8a87cdd8-0029");
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(dynamicColumnParam)
                .queryParam(queryParams).build();

        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(newProductStockPointVOList)){
            throw new BusinessException("用户没有物料权限");
        }
        // 当前用户权限物料
        List<String> ypProductCodes = newMdsFeign.getYpProductCodes(SystemHolder.getScenario());
        Map<String, List<String>> substitutionProductGroup = GlassMrpUtil.getMixProductCodeGroup(ypProductCodes);
        List<String> productCodes = newProductStockPointVOList.stream()
                .map(t->GlassMrpUtil.getNewProductCode(t.getProductCode(), substitutionProductGroup))
                .collect(Collectors.toList());

        List<MaterialPlanInventoryShiftGlassPageVO> result;
        Map<String, Object> params = new HashMap<>();

        params.put("productCodes", productCodes);
        params.put("stockPointType", dto.getStockPointType());
        params.put("productColor", dto.getProductColor());
        params.put("productThickness", dto.getProductThickness());
        params.put("warningRemindStart", dto.getWarningRemindStart());
        params.put("warningRemindEnd", dto.getWarningRemindEnd());

        // 根据物料分组作为total
        // 固定为10个
        dto.setPageSize(10);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<MaterialPlanInventoryShiftVO> materialPlanInventoryShiftVOPagesList = this.selectGroupGlassInfoTotal(params);
//        if (CollectionUtils.isEmpty(materialPlanInventoryShiftVOPagesList)) {
//            throw new BusinessException("当前用户没有原片物料权限");
//        }
        log.info("库存推移，数量为{}", materialPlanInventoryShiftVOPagesList.size());
        PageInfo<MaterialPlanInventoryShiftVO> pageInfoResult1 = new PageInfo<>(materialPlanInventoryShiftVOPagesList);




        // 提取物料编码列表
        List<String> productCodeList = materialPlanInventoryShiftVOPagesList.stream()
                .map(MaterialPlanInventoryShiftVO::getProductCode)
                .collect(Collectors.toList());

        // 构建查询参数
        Map<String, Object> detailParams = new HashMap<>();
        detailParams.put("productCodeList", productCodeList);
        detailParams.put("startDate", dto.getStartDate());
        detailParams.put("endDate", dto.getEndDate());

        // 查询推移数据
        List<MaterialPlanInventoryShiftVO> materialPlanInventoryShiftVOS = materialPlanInventoryShiftDao.selectVOByParams(detailParams);

        // 组装MaterialPlanInventoryShiftGlassPageVO
        result = assembleMaterialPlanInventoryShiftGlassPageVO(materialPlanInventoryShiftVOS, dto.getStartDate(), dto.getEndDate());

        // 补充根据物料排序
        result = result.stream().sorted(Comparator.comparing(data -> data.getProductStockPointVO().getProductCode())).collect(Collectors.toList());

        // 根据物料编码+库存点类型对物料数据排序
        result = result.stream()
                .sorted(Comparator.comparing((MaterialPlanInventoryShiftGlassPageVO item) -> item.getProductStockPointVO().getProductCode())
                        .thenComparing(item -> item.getProductStockPointVO().getStockPointType(),
                                Comparator.comparingInt(type -> {
                                    // 自定义排序规则
                                    if (type.equals("BC")) {
                                        // 包含 "本" 字的排在第一
                                        return 1;
                                    } else if (type.equals("MT")) {
                                        // 包含 "码" 字的排在第二
                                        return 2;
                                    } else if (type.equals("BCMT")) {
                                        // 包含 "浮" 字的排在第三
                                        return 3;
                                    } else {
                                        return Integer.MAX_VALUE;
                                    }
                                })))
                .collect(Collectors.toList());

        PageInfo<MaterialPlanInventoryShiftGlassPageVO> pageInfoResult = new PageInfo<>(result);
        pageInfoResult.setTotal(pageInfoResult1.getTotal());
        pageInfoResult.setPageSize(productCodeList.size());
        return pageInfoResult;
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> selectMonthTotalDemand(String latestVersionId) {
        return materialPlanInventoryShiftDao.selectMonthTotalDemand(latestVersionId);
    }


    private List<MaterialPlanInventoryShiftVO> selectGroupGlassInfoTotal(Map<String, Object> params) {
        return materialPlanInventoryShiftDao.selectGroupGlassInfoTotal(params);
    }

    private List<MaterialPlanInventoryShiftVO> selectGroupGlassInfo(Map<String, Object> params) {
        return materialPlanInventoryShiftDao.selectGroupGlassInfo(params);
    }

    @Override
    public BaseResponse<Void> updateAdjustQuantity(MaterialPlanInventoryShiftDTO materialPlanInventoryShiftDTO) {
        MaterialPlanInventoryShiftVO vo = this.selectByPrimaryKey(materialPlanInventoryShiftDTO.getId());
        //计划调整量 = （计划调整量 + 要修改的计划调整量差值）
        vo.setAdjustQuantityFromPort(vo.getAdjustQuantityFromPort().add(materialPlanInventoryShiftDTO.getUpdateAdjustQuantity()));
        return this.doUpdate(MaterialPlanInventoryShiftConvertor.INSTANCE.vo2Dto(vo));
    }

    /**
     * 组装（原片库存推移数据）
     *
     * @param inventoryShiftMaterialVOList 库存推移数据
     * @param startDate  开始时间
     * @param endDate 结束时间
     * @return 原片库存推移数据
     */
    private List<MaterialPlanInventoryShiftGlassPageVO> assembleMaterialPlanInventoryShiftGlassPageVO(
            List<MaterialPlanInventoryShiftVO> inventoryShiftMaterialVOList,
            Date startDate,
            Date endDate) {
        List<MaterialPlanInventoryShiftGlassPageVO> result = new ArrayList<>();

        // 根据物料编码+库存点编码分组（treeMap排序）
        Map<String, List<MaterialPlanInventoryShiftVO>> materialPlanInventoryShiftVOListMap =
                inventoryShiftMaterialVOList.stream()
                        .collect(Collectors.groupingBy(data -> String.join("&", data.getProductCode(), data.getStockPointCode()),
                                TreeMap::new, Collectors.toList()));


        for (Map.Entry<String, List<MaterialPlanInventoryShiftVO>> entry : materialPlanInventoryShiftVOListMap.entrySet()) {
            // 物料库存推移（原片）对象
            MaterialPlanInventoryShiftGlassPageVO glassVO = new MaterialPlanInventoryShiftGlassPageVO();
            // 取任意一个值赋值物品数据，分组后都是一样的
            MaterialPlanInventoryShiftVO materialPlanInventoryShiftVO = entry.getValue().get(0);
            // 赋值物品数据
            glassVO.setProductStockPointVO(
                    NewProductStockPointVO.builder()
                            .productCode(materialPlanInventoryShiftVO.getProductCode())
                            .productName(materialPlanInventoryShiftVO.getProductName())
                            .stockPointType(materialPlanInventoryShiftVO.getStockPointType())
                            .productLength(materialPlanInventoryShiftVO.getProductLength())
                            .productWidth(materialPlanInventoryShiftVO.getProductWidth())
                            .productColor(materialPlanInventoryShiftVO.getProductColor())
                            .productThickness(materialPlanInventoryShiftVO.getProductThickness())
                            .stockPointCode(materialPlanInventoryShiftVO.getStockPointCode())
                            .stockPointName(materialPlanInventoryShiftVO.getStockPointName())
                            .vehicleModelCode(materialPlanInventoryShiftVO.getVehicleModelCode())
                            .build());

            // 构建 Map，日期部分仅保留到年月日
            Map<Date, MaterialPlanInventoryShiftVO> inventoryDateMap = entry.getValue().stream()
                    .filter(data -> null != data.getInventoryDate())
                    .collect(Collectors.toMap(data -> DateUtils.formatDate(data.getInventoryDate(), "yyyy-MM-dd"), Function.identity(), (v1, v2) -> v1));

            List<MaterialPlanInventoryShiftVO> list = new ArrayList<>();
            list.addAll(getDayMaterialPlanInventoryShiftList(inventoryDateMap, startDate));
            list.addAll(getWeekMaterialPlanInventoryShiftList(entry.getValue(), list, endDate));
            glassVO.setMaterialPlanInventoryShiftList(list);
            result.add(glassVO);
        }

        // 汇总浮法库存
        for (MaterialPlanInventoryShiftGlassPageVO materialPlanInventoryShiftGlassPageVO : result) {
            // 求和浮法库存
            List<MaterialPlanInventoryShiftVO> materialPlanInventoryShiftList = materialPlanInventoryShiftGlassPageVO.getMaterialPlanInventoryShiftList();
            if (CollectionUtils.isEmpty(materialPlanInventoryShiftList)){
                materialPlanInventoryShiftGlassPageVO.setAllFfInventory(BigDecimal.ZERO);
            }
            materialPlanInventoryShiftGlassPageVO.setAllFfInventory(materialPlanInventoryShiftList.get(0).getAllFfInventory());
        }

        return result;
    }


    /**
     * 处理库存推移（天）维度数据
     *
     * @param inventoryDateMap 库存推移Map
     * @param startDate        开始时间
     * @return 根据库存点类型赋值后的库存推移（天）维度数据
     */
    private List<MaterialPlanInventoryShiftVO> getDayMaterialPlanInventoryShiftList(Map<Date, MaterialPlanInventoryShiftVO> inventoryDateMap,
                                                                                    Date startDate) {
        List<MaterialPlanInventoryShiftVO> result = new ArrayList<>();
        // 默认当前日期
        if (startDate == null) {
            startDate = new Date();
        }

        // 获取所需筛选时间

        List<Date> nextNDaysList = getNextDays(startDate, true, 30);

        // 遍历接下来的日期，处理库存数据
        for (Date date : nextNDaysList) {
            MaterialPlanInventoryShiftVO vo = new MaterialPlanInventoryShiftVO();
            MaterialPlanInventoryShiftVO materialPlanInventoryShiftVO = inventoryDateMap.get(date);
            if (Objects.nonNull(materialPlanInventoryShiftVO)){
                vo = materialPlanInventoryShiftVO;
            }

            vo.setWhetherDisplayedColor(false);
            vo.setInventoryDate(date);
            vo.setInventoryDateDimension(DateUtils.dateToString(date));
            result.add(vo);
        }
        return result;
    }

    /**
     * 处理库存推移（周）维度数据
     *
     * @param inventoryShiftList 库存推移数据
     * @param list               赋值天后的库存推移数据
     * @param customEndDate      结束时间
     * @return 根据库存点类型赋值后的库存推移（周）维度数据
     */
    private List<MaterialPlanInventoryShiftVO> getWeekMaterialPlanInventoryShiftList(List<MaterialPlanInventoryShiftVO> inventoryShiftList,
                                                                                     List<MaterialPlanInventoryShiftVO> list, Date customEndDate) {

        List<MaterialPlanInventoryShiftVO> result = new ArrayList<>();

        // 获取推移天后的最后一个日期
        Date inventoryDate = list.get(list.size() - 1).getInventoryDate();
        Date customStartDate = DateUtils.moveCalendar(inventoryDate, Calendar.DATE, 1);

        // 获取周维度
        List<String> weekRangeGroups = getWeekRangeGroups(customStartDate, customEndDate);
        for (String weekRangeGroup : weekRangeGroups) {
            // 解析 weekRangeGroup 获取开始日期和结束日期
            String[] dates = weekRangeGroup.split("~");
            Date startDate = DateUtils.stringToDate(dates[0]);
            Date endDate = DateUtils.stringToDate(dates[1]);

            // 汇总推移
            MaterialPlanInventoryShiftVO vo = summarizeInventoryShift(inventoryShiftList, startDate, endDate);
            vo.setInventoryDateDimension(weekRangeGroup);
            vo.setWhetherDisplayedColor(false);
            result.add(vo);
        }

        return result;
    }

    /**
     * 获取日期后 N 天的日期（只保留年月日部分）
     *
     * @param date         日期
     * @param includeToday 是否包括当前日期
     * @param days         要获取的天数
     * @return 后 N 天日期集合（只保留年月日）
     */
    public static List<Date> getNextDays(Date date, boolean includeToday, int days) {
        List<Date> dateList = new ArrayList<>();
        // 使用 Calendar 来操作日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);

        // 创建 SimpleDateFormat 实例来格式化日期为年月日
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");

        // 如果 includeToday 为 true，直接将当前日期加入列表，并格式化后添加
        if (includeToday) {
            try {
                dateList.add(dateFormat.parse(dateFormat.format(calendar.getTime())));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // 获取从当前日期（如果 includeToday 为 true 已经加入）开始的后续 N-1 天
        for (int i = 1; i < days; i++) {
            calendar.add(Calendar.DATE, 1);
            try {
                dateList.add(dateFormat.parse(dateFormat.format(calendar.getTime())));
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return dateList;
    }


    /**
     * 获取指定日期往后按天数增加的日期列表，格式为yyyy-MM-dd
     *
     * @param startDate 起始日期
     * @param daysToAdd 天数，往后增加的天数
     * @return 日期列表，包含从指定日期开始的日期，格式为yyyy-MM-dd
     */
    public static List<String> getDateAfterDays(Date startDate, int daysToAdd) {
        List<String> result = new ArrayList<>();

        // 创建SimpleDateFormat对象，格式化日期为yyyy-MM-dd
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

        // 获取Calendar实例，并设置为起始日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startDate);

        // 循环添加日期，直到达到天数要求
        for (int i = 0; i < daysToAdd; i++) {
            // 添加一天
            calendar.add(Calendar.DAY_OF_MONTH, 1);
            // 获取添加一天后的日期，并加入结果列表
            result.add(sdf.format(calendar.getTime()));
        }

        return result;
    }

    /**
     * 获取指定开始日期，并按7天一组进行分组（直到指定结束时间）
     * 如果传入的自定义开始日期为null，则使用当前时间作为开始日期
     * 如果传入的自定义结束日期为null，则使用当前时间下一年作为结束日期
     *
     * @param customStartDate 自定义开始日期，如果为null则使用当前日期
     * @param customEndDate   自定义结束日期，如果为null则使用当前日期的下一年作为结束日期
     * @return 结果集合，包含按7天一组的日期范围
     */
    public static List<String> getWeekRangeGroups(Date customStartDate, Date customEndDate) {
        List<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        // 处理开始日期
        Date startDate = (customStartDate != null) ? customStartDate : calendar.getTime();

        // 处理结束日期
        if (customEndDate == null) {
            calendar.setTime(startDate);
            calendar.add(Calendar.YEAR, 1);
        }
        Date endDate = (customEndDate != null) ? customEndDate : calendar.getTime();

        Calendar currentGroupStart = Calendar.getInstance();
        currentGroupStart.setTime(startDate);
        Calendar currentGroupEnd = Calendar.getInstance();

        while (currentGroupStart.getTime().before(endDate)) {
            currentGroupEnd.setTime(currentGroupStart.getTime());
            // 计算每组的结束日期，加 6 天保证每组 7 天
            currentGroupEnd.add(Calendar.DAY_OF_MONTH, 6);

            if (currentGroupEnd.getTime().after(endDate)) {
                currentGroupEnd.setTime(endDate);
            }

            result.add(sdf.format(currentGroupStart.getTime()) + "~" + sdf.format(currentGroupEnd.getTime()));

            // 移动到下一组的开始日期
            currentGroupStart.setTime(currentGroupEnd.getTime());
            currentGroupStart.add(Calendar.DAY_OF_MONTH, 1);
        }

        return result;
    }

    @Override
    public void doDeleteByMaterialPlanVersionId(String materialPlanVersionId) {
        materialPlanInventoryShiftDao.deleteByVersionId(materialPlanVersionId);
    }

    @Override
    public List<MaterialPlanInventoryShiftVO> selectMaterialInventoryShift(String latestVersionId, List<String> productCodeList, String scopeStart, String scopeEnd) {
        return materialPlanInventoryShiftDao.selectMaterialInventoryShift(latestVersionId, productCodeList, scopeStart, scopeEnd);
    }

    /**
     * 取一个合适的库存点类型（优先级分别是本厂、码头、浮法）
     *
     * @param stockPointTypeSet
     * @return
     */
    private String getOneSuitableStockPointType(Set<String> stockPointTypeSet) {
        if (stockPointTypeSet.contains(StockPointTypeEnum.BC.getCode())) {
            return StockPointTypeEnum.BC.getCode();
        } else if (stockPointTypeSet.contains(StockPointTypeEnum.MT.getCode())) {
            return StockPointTypeEnum.MT.getCode();
        } else if (stockPointTypeSet.contains(StockPointTypeEnum.FF.getCode())) {
            return StockPointTypeEnum.FF.getCode();
        }
        return null;
    }

    /**
     * 按条件汇总推移数据
     *
     * @param inventoryShiftList 原片库存推移
     * @param startDate          开始日期
     * @param endDate            结束日期
     * @return 汇总后原片库存推移对象
     */
    public static MaterialPlanInventoryShiftVO summarizeInventoryShift(List<MaterialPlanInventoryShiftVO> inventoryShiftList, Date startDate, Date endDate) {
        if (inventoryShiftList == null || inventoryShiftList.isEmpty()) {
            return new MaterialPlanInventoryShiftVO();
        }

        List<MaterialPlanInventoryShiftVO> filteredList = inventoryShiftList.stream()
                .filter(data -> data.getInventoryDate() != null)
                .filter(data -> !data.getInventoryDate().before(startDate) && !data.getInventoryDate().after(endDate))
                .sorted(Comparator.comparing(MaterialPlanInventoryShiftVO::getInventoryDate))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) {
            return new MaterialPlanInventoryShiftVO();
        }

        MaterialPlanInventoryShiftVO result = new MaterialPlanInventoryShiftVO();
        result.setId(filteredList.get(0).getId());
        result.setWarningRemind(filteredList.get(0).getWarningRemind());
        result.setOpeningInventory(filteredList.get(0).getOpeningInventory());
        result.setEndingInventory(filteredList.get(filteredList.size() - 1).getEndingInventory());

        // 计算安全库存平均值
        result.setSafetyStockLevelMin(calculateAverage(filteredList, MaterialPlanInventoryShiftVO::getSafetyStockLevelMin));
        result.setSafetyStockLevelStandard(calculateAverage(filteredList, MaterialPlanInventoryShiftVO::getSafetyStockLevelStandard));
        result.setSafetyStockLevelMax(calculateAverage(filteredList, MaterialPlanInventoryShiftVO::getSafetyStockLevelMax));

        // 累加其他属性
        result.setSafetyStockDaysStandard(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getSafetyStockDaysStandard));
        result.setProductionDemandQuantity(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getProductionDemandQuantity));
        result.setDemandQuantity(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getDemandQuantity));
        result.setUsedAsReplaceQuantity(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getUsedAsReplaceQuantity));
        result.setUseReplaceQuantity(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getUseReplaceQuantity));
        result.setAdjustQuantityFromPort(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getAdjustQuantityFromPort));
        result.setInputQuantity(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getInputQuantity));
        result.setOutputQuantityToBc(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getOutputQuantityToBc));
        result.setTransitQuantityFromPort(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getTransitQuantityFromPort));
        result.setSafetyStockGap(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getSafetyStockGap));
        result.setAdjustQuantityFromFloat(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getAdjustQuantityFromFloat));
        result.setDecisionOutputQuantityToBc(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getDecisionOutputQuantityToBc));
        result.setDecisionOutputQuantityToPort(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getDecisionOutputQuantityToPort));
        result.setTransitQuantityFromFloat(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getTransitQuantityFromFloat));
        result.setOutputQuantityToPort(sumBigDecimals(filteredList, MaterialPlanInventoryShiftVO::getOutputQuantityToPort));
        return result;
    }

    /**
     * 计算列表中指定属性的平均值。
     *
     * @param list   包含 MaterialPlanInventoryShiftVO 对象的列表，用于计算平均值的数据来源。
     * @param mapper 一个函数，用于从 MaterialPlanInventoryShiftVO 对象中提取需要计算平均值的 BigDecimal 类型属性。
     * @return 返回计算得到的平均值，结果保留两位小数，采用四舍五入的方式进行处理。
     * 如果输入的列表为空，则直接返回 BigDecimal.ZERO。
     */
    private static BigDecimal calculateAverage(List<MaterialPlanInventoryShiftVO> list, Function<MaterialPlanInventoryShiftVO, BigDecimal> mapper) {
        // 检查列表是否为空，如果为空则无需计算，直接返回 0
        if (list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 将累加的总和除以列表的大小，得到平均值
        // 使用 divide 方法时，指定保留两位小数，并采用四舍五入的方式进行处理
        return sum.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
    }

    /**
     * 计算列表中指定属性的总和
     *
     * @param list   包含 MaterialPlanInventoryShiftVO 对象的列表，用于计算总和的数据来源
     * @param mapper 一个函数，用于从 MaterialPlanInventoryShiftVO 对象中提取需要计算总和的 BigDecimal 类型属性
     * @return 返回计算得到的总和。如果列表中所有元素对应的属性值都为 null 或者列表为空，返回 BigDecimal.ZERO
     */
    private static BigDecimal sumBigDecimals(List<MaterialPlanInventoryShiftVO> list, Function<MaterialPlanInventoryShiftVO, BigDecimal> mapper) {
        return list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public Date getLastCreateTime(String userId) {
        // 查询该用户的物料权限
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code"))
                .queryParam(ImmutableMap.of("materialPlanner", userId))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)){
            List<Date> dateList = new ArrayList<>();
            List<String> productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
            List<List<String>> partition = Lists.partition(productCodeList, 1000);
            for (List<String> productCodes : partition) {
                Date date = materialPlanInventoryShiftDao.getLastCreateTime(productCodes);
                if (null != date){
                    dateList.add(date);
                }
            }
            if (CollectionUtils.isNotEmpty(dateList)){
                return Collections.max(dateList);
            }
        }
        return null;
    }
}
