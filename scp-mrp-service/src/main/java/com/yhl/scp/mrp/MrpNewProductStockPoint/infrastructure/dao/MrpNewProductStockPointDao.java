package com.yhl.scp.mrp.MrpNewProductStockPoint.infrastructure.dao;

import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.order.infrastructure.po.PurchaseOrderInfoPO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName MrpNewProductStockPointDao
 * @Description TODO
 * @Date 2025-06-20 17:43:59
 * <AUTHOR>
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @Version 1.0
 */
public interface MrpNewProductStockPointDao {

    List<NewProductStockPointVO> selectVOByParams(@Param("params") Map<String, Object> params);

    List<String> selectYpProductCodes();

    List<NewProductStockPointVO> selectColumnVOByParams(@Param("params") Map<String, Object> params);
    List<NewProductStockPointVO> selectYpFactoryCode();

}
