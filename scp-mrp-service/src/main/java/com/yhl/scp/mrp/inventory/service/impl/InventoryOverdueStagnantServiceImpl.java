package com.yhl.scp.mrp.inventory.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mrp.inventory.convertor.InventoryOverdueStagnantConvertor;
import com.yhl.scp.mrp.inventory.domain.entity.InventoryOverdueStagnantDO;
import com.yhl.scp.mrp.inventory.domain.service.InventoryOverdueStagnantDomainService;
import com.yhl.scp.mrp.inventory.dto.InventoryOverdueStagnantDTO;
import com.yhl.scp.mrp.inventory.dto.InventoryOverdueStagnantVersionDTO;
import com.yhl.scp.mrp.inventory.infrastructure.dao.InventoryOverdueStagnantDao;
import com.yhl.scp.mrp.inventory.infrastructure.po.InventoryOverdueStagnantPO;
import com.yhl.scp.mrp.inventory.service.InventoryOverdueStagnantService;
import com.yhl.scp.mrp.inventory.service.InventoryOverdueStagnantVersionService;
import com.yhl.scp.mrp.inventory.vo.InventoryOverdueStagnantVO;
import com.yhl.scp.mrp.inventory.vo.InventoryOverdueStagnantVersionVO;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.NoGlassInventoryShiftDetailService;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.GlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.overdueInventory.dto.OverdueStagnantInventoryParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>InventoryOverdueStagnantServiceImpl</code>
 * <p>
 * 超期呆滞库存（历史）应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-15 16:48:26
 */
@Slf4j
@Service
public class InventoryOverdueStagnantServiceImpl extends AbstractService implements InventoryOverdueStagnantService {

    @Resource
    private InventoryOverdueStagnantDao inventoryOverdueStagnantDao;

    @Resource
    private InventoryOverdueStagnantDomainService inventoryOverdueStagnantDomainService;

    @Resource
    private InventoryOverdueStagnantVersionService inventoryOverdueStagnantVersionService;

    @Resource
    private GlassInventoryShiftDataService glassInventoryShiftDataService;

    @Resource
    private GlassInventoryShiftDetailService glassInventoryShiftDetailService;

    @Resource
    private NoGlassInventoryShiftDataService noGlassInventoryShiftDataService;

    @Resource
    private NoGlassInventoryShiftDetailService noGlassInventoryShiftDetailService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private DfpFeign dfpFeign;


    @Override
    public BaseResponse<Void> doCreate(InventoryOverdueStagnantDTO inventoryOverdueStagnantDTO) {
        // 0.数据转换
        InventoryOverdueStagnantDO inventoryOverdueStagnantDO = InventoryOverdueStagnantConvertor.INSTANCE.dto2Do(inventoryOverdueStagnantDTO);
        InventoryOverdueStagnantPO inventoryOverdueStagnantPO = InventoryOverdueStagnantConvertor.INSTANCE.dto2Po(inventoryOverdueStagnantDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryOverdueStagnantDomainService.validation(inventoryOverdueStagnantDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryOverdueStagnantPO);
        inventoryOverdueStagnantDao.insert(inventoryOverdueStagnantPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(InventoryOverdueStagnantDTO inventoryOverdueStagnantDTO) {
        // 0.数据转换
        InventoryOverdueStagnantDO inventoryOverdueStagnantDO = InventoryOverdueStagnantConvertor.INSTANCE.dto2Do(inventoryOverdueStagnantDTO);
        InventoryOverdueStagnantPO inventoryOverdueStagnantPO = InventoryOverdueStagnantConvertor.INSTANCE.dto2Po(inventoryOverdueStagnantDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryOverdueStagnantDomainService.validation(inventoryOverdueStagnantDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryOverdueStagnantPO);
        inventoryOverdueStagnantDao.update(inventoryOverdueStagnantPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryOverdueStagnantDTO> list) {
        List<InventoryOverdueStagnantPO> newList = InventoryOverdueStagnantConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryOverdueStagnantDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryOverdueStagnantDTO> list) {
        List<InventoryOverdueStagnantPO> newList = InventoryOverdueStagnantConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryOverdueStagnantDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryOverdueStagnantDao.deleteBatch(idList);
        }
        return inventoryOverdueStagnantDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryOverdueStagnantVO selectByPrimaryKey(String id) {
        InventoryOverdueStagnantPO po = inventoryOverdueStagnantDao.selectByPrimaryKey(id);
        return InventoryOverdueStagnantConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_inventory_batch_detail")
    public List<InventoryOverdueStagnantVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        InventoryOverdueStagnantVersionVO inventoryOverdueStagnantVersionVO = inventoryOverdueStagnantVersionService.selectLastVersion();
        // 不包含版本，默认查最新的
        if (null != inventoryOverdueStagnantVersionVO) {
            if (null != queryCriteriaParam && !queryCriteriaParam.contains("version_id")) {
                queryCriteriaParam = queryCriteriaParam + "and version_id ='" + inventoryOverdueStagnantVersionVO.getId() + "'";
            } else if (null == queryCriteriaParam) {
                queryCriteriaParam = "and version_id ='" + inventoryOverdueStagnantVersionVO.getId() + "'";
            }
        }

        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_inventory_batch_detail")
    public List<InventoryOverdueStagnantVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryOverdueStagnantVO> dataList = inventoryOverdueStagnantDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryOverdueStagnantServiceImpl target = SpringBeanUtils.getBean(InventoryOverdueStagnantServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryOverdueStagnantVO> selectByParams(Map<String, Object> params) {
        List<InventoryOverdueStagnantPO> list = inventoryOverdueStagnantDao.selectByParams(params);
        return InventoryOverdueStagnantConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryOverdueStagnantVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public void doCalculate(String scenario, OverdueStagnantInventoryParamDTO overdueStagnantInventoryParamDTO) {
        // 查询所有的实时库存
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailVOByParams03(scenario,new HashMap<>());

        String currentTime = DateUtils.dateToString(new Date());
        inventoryBatchDetailVOList.forEach(data -> {
            if (null == data.getAssignedTime()) {
                data.setAssignedTime(currentTime);
            }
        });

        // 根据入库时间升序排序，时间相同的情况下根据currentQuantity转换为BigDecimal后降序排序
        inventoryBatchDetailVOList = inventoryBatchDetailVOList.stream()
                .sorted(Comparator.comparing(InventoryBatchDetailVO::getAssignedTime)
                        .thenComparing(vo -> new BigDecimal(vo.getCurrentQuantity()))
                        .reversed())
                .collect(Collectors.toList());

        List<String> inventoryBatchDetailProductCodes = inventoryBatchDetailVOList.stream()
                .map(InventoryBatchDetailVO::getProductCode).distinct().collect(Collectors.toList());

        // 查询产品工艺基础数据
        List<MdsProductStockPointBaseVO> mdsProductStockPointBaseVOList = mdsFeign.selectProductStockPointBaseByParams(
                scenario, ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        Map<String, String> itemTypeMap = mdsProductStockPointBaseVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getItemType()))
                .collect(Collectors.toMap(MdsProductStockPointBaseVO::getProductCode, MdsProductStockPointBaseVO::getItemType, (v1, v2) -> v1));

        // 查询库龄超期界定天数
        List<MdsOverDeadlineDaysVO> overDeadlineDaysVOList = mdsFeign.selectOverDeadlineDaysByParams(
                scenario, ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode()));
        List<Function<MdsOverDeadlineDaysVO, String>> keyFunctions = Arrays.asList(
                vo -> String.join("&", vo.getMaterialsType(), vo.getMaterialsMainClassification(),
                        vo.getMaterialsSecondClassification(), vo.getColorCode()),
                vo -> String.join("&", vo.getMaterialsType(), vo.getMaterialsMainClassification(),
                        vo.getMaterialsSecondClassification()),
                vo -> String.join("&", vo.getMaterialsType(), vo.getMaterialsMainClassification())
        );
        // 物料类型 + 大类 + 小类 + 颜色
        // 创建一个函数，根据提供的键函数列表生成对应的映射
        Map<String, BigDecimal> overDeadlineDayMap = keyFunctions.stream()
                .flatMap(keyFunction -> overDeadlineDaysVOList.stream()
                        .filter(vo -> vo.getOverDeadlineDay() != null)
                        .map(vo -> new AbstractMap.SimpleEntry<>(keyFunction.apply(vo), vo.getOverDeadlineDay())))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        // 保留第一个遇到的值
                        (existing, replacement) -> existing,
                        // 保持键函数的顺序
                        LinkedHashMap::new
                ));

        // 原片
        List<GlassInventoryShiftDataVO> glassInventoryShiftDataVOList =
                glassInventoryShiftDataService.selectByParams(ImmutableMap.of("productCodes", inventoryBatchDetailProductCodes));
        List<String> glassInventoryShiftDataIds = glassInventoryShiftDataVOList.stream().map(BaseVO::getId).collect(Collectors.toList());
        List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOList =
                glassInventoryShiftDetailService.selectByParams(ImmutableMap.of("inventoryShiftDataIds", glassInventoryShiftDataIds))
                        .stream().filter(data -> null != data.getDemandQuantity()).collect(Collectors.toList());
        if (null != overdueStagnantInventoryParamDTO.getScopeStart() && null != overdueStagnantInventoryParamDTO.getScopeEnd()) {
            glassInventoryShiftDetailVOList = glassInventoryShiftDetailVOList.stream()
                    .filter(data -> !data.getInventoryDate().before(overdueStagnantInventoryParamDTO.getScopeStart()) &&
                            !data.getInventoryDate().after(overdueStagnantInventoryParamDTO.getScopeEnd()))
                    .collect(Collectors.toList());
        }
        if (null != overdueStagnantInventoryParamDTO.getOverdueTime()) {
            glassInventoryShiftDetailVOList = glassInventoryShiftDetailVOList.stream()
                    .filter(data -> !data.getInventoryDate().before(new Date()) &&
                            !data.getInventoryDate().after(overdueStagnantInventoryParamDTO.getOverdueTime()))
                    .collect(Collectors.toList());
        }
        Map<String, List<GlassInventoryShiftDetailVO>> glassInventoryShiftDetailMap =
                glassInventoryShiftDetailVOList.stream().collect(Collectors.groupingBy(GlassInventoryShiftDetailVO::getInventoryShiftDataId));
        // 原片需求
        Map<String, BigDecimal> glassDemandMap = glassInventoryShiftDataVOList.stream()
                .filter(data -> glassInventoryShiftDetailMap.containsKey(data.getId()))
                .flatMap(data -> {
                    String productCode = data.getProductCode();
                    List<String> keys = new ArrayList<>();

                    // 检查productCode第三位是否为*
                    if (productCode.length() >= 3 && productCode.charAt(2) == '*') {
                        // 替换*为B和T
                        keys.add(productCode.substring(0, 2) + 'B' + productCode.substring(3));
                        keys.add(productCode.substring(0, 2) + 'T' + productCode.substring(3));
                    } else {
                        // 不是特殊情况，使用原productCode
                        keys.add(productCode);
                    }

                    // 计算当前productCode的总需求
                    BigDecimal totalDemand = glassInventoryShiftDetailMap.get(data.getId()).stream()
                            .map(GlassInventoryShiftDetailVO::getDemandQuantity)
                            .filter(Objects::nonNull)
                            .reduce(BigDecimal::add)
                            .orElse(BigDecimal.ZERO);

                    // 为每个键创建一个条目
                    return keys.stream()
                            .map(key -> new AbstractMap.SimpleEntry<>(key, totalDemand));
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));

        // 原片需求明细
        Map<String, List<GlassInventoryShiftDetailVO>> glassDetailMap = glassInventoryShiftDataVOList.stream()
                .filter(data -> glassInventoryShiftDetailMap.containsKey(data.getId()))
                .flatMap(data -> {
                    String productCode = data.getProductCode();
                    List<String> keys = new ArrayList<>();

                    // 检查productCode第三位是否为*
                    if (productCode.length() >= 3 && productCode.charAt(2) == '*') {
                        // 替换*为B和T
                        keys.add(productCode.substring(0, 2) + 'B' + productCode.substring(3));
                        keys.add(productCode.substring(0, 2) + 'T' + productCode.substring(3));
                    } else {
                        // 不是特殊情况，使用原productCode
                        keys.add(productCode);
                    }

                    return keys.stream()
                            .map(key -> new AbstractMap.SimpleEntry<>(key, glassInventoryShiftDetailMap.get(data.getId())));
                })
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (existing, replacement) -> existing
                ));

        // 非原片
        List<NoGlassInventoryShiftDataVO> noGlassInventoryShiftDataVOList =
                noGlassInventoryShiftDataService.selectByParams(ImmutableMap.of("productCodes", inventoryBatchDetailProductCodes));
        List<String> noGlassInventoryShiftDataIds = noGlassInventoryShiftDataVOList.stream().map(BaseVO::getId).collect(Collectors.toList());
        List<NoGlassInventoryShiftDetailVO> noGlassInventoryShiftDetailVOList =
                noGlassInventoryShiftDetailService.selectByParams(ImmutableMap.of("noGlassInventoryShiftDataIdList", noGlassInventoryShiftDataIds))
                        .stream().filter(data -> null != data.getDemandQuantity()).collect(Collectors.toList());
        if (null != overdueStagnantInventoryParamDTO.getScopeStart() && null != overdueStagnantInventoryParamDTO.getScopeEnd()) {
            noGlassInventoryShiftDetailVOList = noGlassInventoryShiftDetailVOList.stream()
                    .filter(data -> !data.getInventoryDate().before(overdueStagnantInventoryParamDTO.getScopeStart()) &&
                            !data.getInventoryDate().after(overdueStagnantInventoryParamDTO.getScopeEnd()))
                    .collect(Collectors.toList());
        }
        if (null != overdueStagnantInventoryParamDTO.getOverdueTime()) {
            noGlassInventoryShiftDetailVOList = noGlassInventoryShiftDetailVOList.stream()
                    .filter(data -> !data.getInventoryDate().before(new Date()) &&
                            !data.getInventoryDate().after(overdueStagnantInventoryParamDTO.getOverdueTime()))
                    .collect(Collectors.toList());
        }
        Map<String, List<NoGlassInventoryShiftDetailVO>> noGlassInventoryShiftDetailMap =
                noGlassInventoryShiftDetailVOList.stream().collect(Collectors.groupingBy(NoGlassInventoryShiftDetailVO::getNoGlassInventoryShiftDataId));
        // 非原片需求
        Map<String, BigDecimal> noGlassDemandMap = noGlassInventoryShiftDataVOList.stream()
                .filter(data -> noGlassInventoryShiftDetailMap.containsKey(data.getId()))
                .collect(Collectors.toMap(
                        NoGlassInventoryShiftDataVO::getProductCode,
                        data -> noGlassInventoryShiftDetailMap.get(data.getId()).stream()
                                .map(NoGlassInventoryShiftDetailVO::getDemandQuantity)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal::add)
                                .orElse(BigDecimal.ZERO),
                        (v1, v2) -> v1
                ));
        // 非原片需求明细
        Map<String, List<NoGlassInventoryShiftDetailVO>> noGlassDetailMap = noGlassInventoryShiftDataVOList.stream()
                .filter(data -> noGlassInventoryShiftDetailMap.containsKey(data.getId()))
                .collect(Collectors.toMap(
                        NoGlassInventoryShiftDataVO::getProductCode,
                        data -> noGlassInventoryShiftDetailMap.get(data.getId())));

        // 成品/半成品需求
        Map<String, Integer> cleanDemandMap = new HashMap<>();

        // 获取最新版本原始需求ID
        String originId = dfpFeign.selectNewdemandVersion(scenario);
        if (StringUtils.isNotEmpty(originId)) {
            // 移除首尾双引号
            originId = originId.replaceAll("^\"|\"$", "");
            // 获取需求版本
            List<DemandVersionVO> demandVersionVOList = dfpFeign.selectDemandVersionVOListByParams(scenario,
                    ImmutableMap.of("originVersionId", originId,"versionType", VersionTypeEnum.CLEAN_FORECAST.getCode()));
            if (CollectionUtils.isNotEmpty(demandVersionVOList)) {
                // 获取需求版本id
                String demandVersionId = demandVersionVOList.get(0).getId();

                // 获取滚动预测
                List<CleanForecastDataVO> cleanForecastDataVOList = dfpFeign.selectCleanForecastDataVOListByParams(
                        scenario, ImmutableMap.of("versionId", demandVersionId));

                if (CollectionUtils.isNotEmpty(cleanForecastDataVOList)) {

                    // 按产品代码分组
                    Map<String, List<CleanForecastDataVO>> productToDataMap = cleanForecastDataVOList.stream()
                            .collect(Collectors.groupingBy(CleanForecastDataVO::getProductCode));

                    // 获取所有数据ID
                    Set<String> cleanForecastDataIds = cleanForecastDataVOList.stream()
                            .map(BaseVO::getId)
                            .collect(Collectors.toSet());

                    // 获取明细数据并按数据ID分组
                    Map<String, List<CleanForecastDataDetailVO>> detailMap = dfpFeign.selectCleanForecastDataDetailVOListByParams(
                                    scenario, ImmutableMap.of("cleanForecastDataIds", cleanForecastDataIds))
                            .stream()
                            .collect(Collectors.groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));

                    // 计算每个产品的总需求数量
                    cleanDemandMap = productToDataMap.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    entry -> entry.getValue().stream()
                                            .map(BaseVO::getId)
                                            .map(detailMap::get)
                                            .filter(Objects::nonNull)
                                            .flatMap(List::stream)
                                            .map(CleanForecastDataDetailVO::getForecastQuantity)
                                            .filter(Objects::nonNull)
                                            .reduce(0, Integer::sum),
                                    (existing, replacement) -> existing
                            ));
                }
            }
        }

        Map<String, Integer> finalCleanDemandMap = cleanDemandMap;

        // 新增发布版本
        InventoryOverdueStagnantVersionDTO versionDTO = new InventoryOverdueStagnantVersionDTO();
        versionDTO.setId(UUID.randomUUID().toString());
        versionDTO.setVersionCode(inventoryOverdueStagnantVersionService.getVersionCode());
        versionDTO.setVersionName("超期呆滞库存发布版本");

        List<InventoryOverdueStagnantDTO> inventoryOverdueStagnantAddList = inventoryBatchDetailVOList.stream().map(data -> {

            InventoryOverdueStagnantDTO inventoryOverdueStagnantDTO = new InventoryOverdueStagnantDTO();

            // 版本id
            inventoryOverdueStagnantDTO.setVersionId(versionDTO.getId());

            // 实时库存id
            inventoryOverdueStagnantDTO.setFdpInventoryBatchDetailId(data.getId());

            // 工艺类型
            if (!itemTypeMap.isEmpty() && itemTypeMap.containsKey(data.getProductCode())) {
                inventoryOverdueStagnantDTO.setItemType(itemTypeMap.get(data.getProductCode()));
            }

            // 库龄天数（当前时间 - 入库时间）
            if (data.getAssignedTime() != null) {
                Date assignedDate = DateUtils.stringToDate(data.getAssignedTime());
                Date currentDate = new Date();

                // 计算时间差（毫秒）
                long diffInMillis = currentDate.getTime() - assignedDate.getTime();

                // 转换为天数（向上取整为整数）
                long days = (long) Math.ceil((double) diffInMillis / (24 * 60 * 60 * 1000));

                inventoryOverdueStagnantDTO.setStockAgeDay(BigDecimal.valueOf(days));
            }

            // 库龄超期界定天数
            if (StringUtils.isNotEmpty(data.getProductClassify())) {
                String materialsMainClassification = null;
                String materialsSecondClassification = null;
                // 最多分割为3部分
                String[] parts = data.getProductClassify().split("\\.", 3);
                if (parts.length >= 1) {
                    materialsMainClassification = parts[0];
                    if (parts.length >= 2) {
                        materialsSecondClassification = parts[1];
                    }
                }

                // 先带上颜色找，找不到就去掉颜色再找一遍，再找不到则去掉小类再找一遍
                // 查找逻辑：按优先级依次尝试匹配
                String[] keysToTry = {
                        String.join("&", data.getProductType(), materialsMainClassification,
                                materialsSecondClassification, data.getProductColor()),
                        String.join("&", data.getProductType(), materialsMainClassification,
                                materialsSecondClassification),
                        String.join("&", data.getProductType(), materialsMainClassification)
                };

                BigDecimal overDeadlineDay = Arrays.stream(keysToTry)
                        .map(overDeadlineDayMap::get)
                        .filter(Objects::nonNull)
                        .findFirst()
                        .orElse(null);

                if (null != overDeadlineDay) {
                    inventoryOverdueStagnantDTO.setOverDeadlineDay(overDeadlineDay);
                } else {
                    inventoryOverdueStagnantDTO.setOverDeadlineDay(BigDecimal.ZERO);
                }
            }

            // 超期天数（当前日期-入库时间-库龄超期界定天数 ，即库龄天数-库龄超期界定天数）
            BigDecimal stockAgeDay = Optional.ofNullable(inventoryOverdueStagnantDTO.getStockAgeDay())
                    .orElse(BigDecimal.ZERO);
            BigDecimal overDeadlineDay = Optional.ofNullable(inventoryOverdueStagnantDTO.getOverDeadlineDay())
                    .orElse(BigDecimal.ZERO);
            inventoryOverdueStagnantDTO.setOverdueDays(stockAgeDay.subtract(overDeadlineDay));

            // 筛选范围内需求
            if (glassDemandMap.containsKey(data.getProductCode())) {
                inventoryOverdueStagnantDTO.setScopeDemandQuantity(glassDemandMap.get(data.getProductCode()));
            } else if (noGlassDemandMap.containsKey(data.getProductCode())) {
                inventoryOverdueStagnantDTO.setScopeDemandQuantity(noGlassDemandMap.get(data.getProductCode()));
            } else if (finalCleanDemandMap.containsKey(data.getProductCode())) {
                inventoryOverdueStagnantDTO.setScopeDemandQuantity(BigDecimal.valueOf(finalCleanDemandMap.get(data.getProductCode())));
            }

            // 库存耗尽日期
            if (glassDetailMap.containsKey(data.getProductCode())) {
                List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOS = glassDetailMap.get(data.getProductCode()).stream()
                        .sorted(Comparator.comparing(GlassInventoryShiftDetailVO::getInventoryDate))
                        .collect(Collectors.toList());

                // 将currentQuantity转换为BigDecimal，处理可能的小数和高精度计算
                BigDecimal currentQuantity = new BigDecimal(data.getCurrentQuantity());
                Date depletionDate = null;

                // 遍历库存记录，按日期先后顺序消耗库存
                for (GlassInventoryShiftDetailVO detail : glassInventoryShiftDetailVOS) {
                    // 获取当前库存记录的数量
                    BigDecimal demandQuantity = detail.getDemandQuantity();

                    if (currentQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                        // 如果当前消耗量已为0或负数，停止消耗
                        break;
                    }

                    if (demandQuantity.compareTo(currentQuantity) >= 0) {
                        // 当前库存足够满足剩余消耗量
                        detail.setDemandQuantity(demandQuantity.subtract(currentQuantity));
                        depletionDate = detail.getInventoryDate();
                        currentQuantity = BigDecimal.ZERO;
                    } else {
                        // 当前库存不足以满足剩余消耗量，继续消耗下一批库存
                        currentQuantity = currentQuantity.subtract(demandQuantity);
                        detail.setDemandQuantity(BigDecimal.ZERO);
                        depletionDate = detail.getInventoryDate();
                    }
                }

                // 如果最后没有被需求消耗完，就给空
                if (currentQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    depletionDate = null;
                }

                inventoryOverdueStagnantDTO.setInventoryDepletionDate(depletionDate);
            } else if (noGlassDetailMap.containsKey(data.getProductCode())) {
                List<NoGlassInventoryShiftDetailVO> glassInventoryShiftDetailVOS = noGlassDetailMap.get(data.getProductCode()).stream()
                        .sorted(Comparator.comparing(NoGlassInventoryShiftDetailVO::getInventoryDate))
                        .collect(Collectors.toList());

                // 将currentQuantity转换为BigDecimal，处理可能的小数和高精度计算
                BigDecimal currentQuantity = new BigDecimal(data.getCurrentQuantity());
                Date depletionDate = null;

                // 遍历库存记录，按日期先后顺序消耗库存
                for (NoGlassInventoryShiftDetailVO detail : glassInventoryShiftDetailVOS) {
                    // 获取当前库存记录的数量
                    BigDecimal demandQuantity = detail.getDemandQuantity();

                    if (currentQuantity.compareTo(BigDecimal.ZERO) <= 0) {
                        // 如果当前消耗量已为0或负数，停止消耗
                        break;
                    }

                    if (demandQuantity.compareTo(currentQuantity) >= 0) {
                        // 当前库存足够满足剩余消耗量
                        detail.setDemandQuantity(demandQuantity.subtract(currentQuantity));
                        depletionDate = detail.getInventoryDate();
                        currentQuantity = BigDecimal.ZERO;
                    } else {
                        // 当前库存不足以满足剩余消耗量，继续消耗下一批库存
                        currentQuantity = currentQuantity.subtract(demandQuantity);
                        detail.setDemandQuantity(BigDecimal.ZERO);
                        depletionDate = detail.getInventoryDate();
                    }
                }

                // 如果最后没有被需求消耗完，就给空
                if (currentQuantity.compareTo(BigDecimal.ZERO) > 0) {
                    depletionDate = null;
                }

                inventoryOverdueStagnantDTO.setInventoryDepletionDate(depletionDate);
            }

            return inventoryOverdueStagnantDTO;
        }).collect(Collectors.toList());

        // 落库超期呆滞库存版本
        inventoryOverdueStagnantVersionService.doCreateWithPrimaryKey(versionDTO);

        // 创建一个线程池，根据实际情况调整线程数量
        Executor executor = Executors.newFixedThreadPool(5);
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        // 落库超期呆滞库存
        processBatch(inventoryOverdueStagnantAddList, 2000, this::doCreateBatch, futures, executor);
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 异步批量处理方法
     *
     * @param list           待处理列表
     * @param batchSize      每批大小
     * @param batchProcessor 批处理器
     * @param futures        异步任务列表
     * @param executor       线程池
     * @param <T>            泛型类型
     */
    private <T> void processBatch(List<T> list, int batchSize,
                                  Consumer<List<T>> batchProcessor,
                                  List<CompletableFuture<Void>> futures,
                                  Executor executor) {
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(list)) {
            return;
        }

        int size = list.size();
        // 直接使用索引进行分批，避免创建多个子List对象
        for (int i = 0; i < size; i += batchSize) {
            int end = Math.min(i + batchSize, size);
            List<T> batch = list.subList(i, end);

            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    batchProcessor.accept(batch);
                } catch (Exception e) {
                    // 记录批处理异常，避免影响其他批次
                    log.error("批处理失败，批次大小: {}", batch.size(), e);
                    throw e;
                }
            }, executor));
        }
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<InventoryOverdueStagnantVO> invocation(List<InventoryOverdueStagnantVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
