package com.yhl.scp.mrp.material.plan.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.params.FeignDynamicParam;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingStepBasicVO;
import com.yhl.scp.mds.enums.ProductTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.enums.CoatingWarningReasonEnum;
import com.yhl.scp.mrp.inventory.dto.GlassInventoryShiftQueryParamDTO;
import com.yhl.scp.mrp.material.plan.convertor.GlassInventoryShiftDataConvertor;
import com.yhl.scp.mrp.material.plan.domain.entity.GlassInventoryShiftDataDO;
import com.yhl.scp.mrp.material.plan.domain.service.GlassInventoryShiftDataDomainService;
import com.yhl.scp.mrp.material.plan.dto.GlassInventoryShiftDataDTO;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDataDao;
import com.yhl.scp.mrp.material.plan.infrastructure.dao.GlassInventoryShiftDetailDao;
import com.yhl.scp.mrp.material.plan.infrastructure.po.GlassInventoryShiftDataPO;
import com.yhl.scp.mrp.material.plan.service.GlassInventoryShiftDataService;
import com.yhl.scp.mrp.material.plan.service.utils.GlassMrpUtil;
import com.yhl.scp.mrp.material.plan.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>GlassInventoryShiftDataServiceImpl</code>
 * <p>
 * 物料库存推移应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-02 11:29:32
 */
@Slf4j
@Service
public class GlassInventoryShiftDataServiceImpl extends AbstractService implements GlassInventoryShiftDataService {

    @Resource
    private GlassInventoryShiftDataDao glassInventoryShiftDataDao;

    @Resource
    private GlassInventoryShiftDetailDao glassInventoryShiftDetailDao;

    @Resource
    private GlassInventoryShiftDataDomainService glassInventoryShiftDataDomainService;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private SpringBeanUtils springBeanUtils;



    @Override
    public BaseResponse<Void> doCreate(GlassInventoryShiftDataDTO glassInventoryShiftDataDTO) {
        // 0.数据转换
        GlassInventoryShiftDataDO glassInventoryShiftDataDO = GlassInventoryShiftDataConvertor.INSTANCE.dto2Do(glassInventoryShiftDataDTO);
        GlassInventoryShiftDataPO glassInventoryShiftDataPO = GlassInventoryShiftDataConvertor.INSTANCE.dto2Po(glassInventoryShiftDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryShiftDataDomainService.validation(glassInventoryShiftDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(glassInventoryShiftDataPO);
        glassInventoryShiftDataDao.insertWithPrimaryKey(glassInventoryShiftDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(GlassInventoryShiftDataDTO glassInventoryShiftDataDTO) {
        // 0.数据转换
        GlassInventoryShiftDataDO glassInventoryShiftDataDO = GlassInventoryShiftDataConvertor.INSTANCE.dto2Do(glassInventoryShiftDataDTO);
        GlassInventoryShiftDataPO glassInventoryShiftDataPO = GlassInventoryShiftDataConvertor.INSTANCE.dto2Po(glassInventoryShiftDataDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        glassInventoryShiftDataDomainService.validation(glassInventoryShiftDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(glassInventoryShiftDataPO);
        glassInventoryShiftDataDao.update(glassInventoryShiftDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<GlassInventoryShiftDataDTO> list) {
        List<GlassInventoryShiftDataPO> newList = GlassInventoryShiftDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        glassInventoryShiftDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<GlassInventoryShiftDataDTO> list) {
        List<GlassInventoryShiftDataPO> newList = GlassInventoryShiftDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        glassInventoryShiftDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return glassInventoryShiftDataDao.deleteBatch(idList);
        }
        return glassInventoryShiftDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public GlassInventoryShiftDataVO selectByPrimaryKey(String id) {
        GlassInventoryShiftDataPO po = glassInventoryShiftDataDao.selectByPrimaryKey(id);
        return GlassInventoryShiftDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_mrp_glass_inventory_shift_data")
    public List<GlassInventoryShiftDataVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_mrp_glass_inventory_shift_data")
    public List<GlassInventoryShiftDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<GlassInventoryShiftDataVO> dataList = glassInventoryShiftDataDao.selectByCondition(sortParam, queryCriteriaParam);
        GlassInventoryShiftDataServiceImpl target = SpringBeanUtils.getBean(GlassInventoryShiftDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public PageInfo<GlassInventoryShiftPageVO> pageCustom(GlassInventoryShiftQueryParamDTO dto) {
        String scenario = SystemHolder.getScenario();

        List<String> dynamicColumnParam = Lists.newArrayList("product_code");
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("materialPlanner", SystemHolder.getUserId());
        queryParams.put("productType", ProductTypeEnum.P.getCode());
        queryParams.put("productClassify", "RA.A");
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(dynamicColumnParam)
                .queryParam(queryParams).build();
        // 当前用户权限物料
        List<NewProductStockPointVO> newProductStockPointVOList = newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam);
        if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(newProductStockPointVOList)) {
            throw new BusinessException("用户没有物料权限");
        }
        Map<String, Object> params = new HashMap<>();
        if (StringUtils.isNotEmpty(dto.getProductCode())) {
            if (dto.getProductCode().contains("*")) {
                params.put("productCode", dto.getProductCode());
            } else {
                List<String> ypProductCodes = newMdsFeign.getYpProductCodes(scenario);
                Map<String, List<String>> substitutionProductGroup = GlassMrpUtil.getMixProductCodeGroup(ypProductCodes);
                params.put("productCode", GlassMrpUtil.getNewProductCode(dto.getProductCode(), substitutionProductGroup));
            }

        }
        params.put("stockPointType", dto.getStockPointType());
        params.put("productColor", dto.getProductColor());
        params.put("productThickness", dto.getProductThickness());
        params.put("warningRemindStart", dto.getWarningRemindStart());
        params.put("warningRemindEnd", dto.getWarningRemindEnd());

        dto.setPageSize(10);
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        List<GlassInventoryShiftDataVO> glassInventoryShiftDataVOList = glassInventoryShiftDataDao.selectVOByParams(params);
        PageInfo<GlassInventoryShiftDataVO> pageInfoResult1 = new PageInfo<>(glassInventoryShiftDataVOList);
        Map<String, GlassInventoryShiftDataVO> glassInventoryShiftDataVOMap = glassInventoryShiftDataVOList.stream()
                .collect(Collectors.toMap(GlassInventoryShiftDataVO::getId, Function.identity()));

        // 收集并分割本厂编码
        List<String> productFactoryCodes = glassInventoryShiftDataVOList.stream()
                .map(GlassInventoryShiftDataVO::getProductFactoryCode)
                .filter(StringUtils::isNotEmpty)
                .map(data -> data.split(","))
                .map(Arrays::asList)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        // 镀膜物料（本厂编码）
        List<String> coatingCodeList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(productFactoryCodes)) {
            // 查询对应物料id
            FeignDynamicParam feignDynamicParam02 = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id"))
                    .queryParam(ImmutableMap.of("productCodeList", productFactoryCodes))
                    .build();
            List<NewProductStockPointVO> newProductStockPointVOS =
                    newMdsFeign.selectProductListByParamOnDynamicColumns(scenario, feignDynamicParam02);
            List<String> productFactoryIds = newProductStockPointVOS.stream()
                    .map(NewProductStockPointVO::getId).collect(Collectors.toList());

            // 查询工艺路径数据
            FeignDynamicParam routingParam = FeignDynamicParam.builder()
                    .dynamicColumnParam(Lists.newArrayList("id","routing_code","product_code"))
                    .queryParam(ImmutableMap.of("productIds", productFactoryIds)).build();
            List<RoutingVO> routingVOList = newMdsFeign.selectRoutingByParamOnDynamicColumns(scenario, routingParam);
            List<String> routingIds = routingVOList.stream().map(BaseVO::getId).collect(Collectors.toList());

            // 查询工艺路径步骤
            List<RoutingStepVO> routingStepVOList = newMdsFeign.getRoutingStepByRoutingIds(scenario, routingIds);
            List<String> coatingRoutingIdList = routingStepVOList.stream()
                    .filter(data -> data.getStandardStepName().equals("镀膜"))
                    .map(RoutingStepBasicVO::getRoutingId)
                    .collect(Collectors.toList());

            // 收集镀膜物料
            coatingCodeList = routingVOList.stream()
                    .filter(data -> coatingRoutingIdList.contains(data.getId()))
                    .map(RoutingBasicVO::getProductCode)
                    .distinct()
                    .collect(Collectors.toList());
        }

        List<String> productCodes = glassInventoryShiftDataVOList.stream()
                .map(GlassInventoryShiftDataVO::getProductCode)
                .distinct().collect(Collectors.toList());
        // 处理*号
        productCodes = handleSpecialProductCodeList(productCodes);

        // 查询本厂库存
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = dfpFeign.selectInventoryBatchDetailByParams(scenario,ImmutableMap.of("productCodes", productCodes));
        Map<String, List<String>> lotNumberMap = inventoryBatchDetailVOList.stream()
                .filter(data -> StringUtils.isNotEmpty(data.getBatch()))
                .collect(Collectors.groupingBy(
                        InventoryBatchDetailVO::getProductCode,
                        Collectors.mapping(
                                InventoryBatchDetailVO::getBatch,
                                Collectors.toList()
                        )
                ));

        List<String> inventoryShiftDataIds = glassInventoryShiftDataVOList.stream()
                .map(BaseVO::getId)
                .collect(Collectors.toList());
        Map<String, List<GlassInventoryShiftDetailVO>> glassInventoryShiftDetailGroup = glassInventoryShiftDetailDao
                .selectVOByParams(ImmutableMap.of("inventoryShiftDataIds", inventoryShiftDataIds))
                .stream().collect(Collectors.groupingBy(GlassInventoryShiftDetailVO::getInventoryShiftDataId));

        List<GlassInventoryShiftPageVO> glassInventoryShiftPageVOList = new ArrayList<>();
        for (List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOList : glassInventoryShiftDetailGroup.values()) {
            Map<String, List<GlassInventoryShiftDetailVO>> collect = glassInventoryShiftDetailVOList.stream()
                    .collect(Collectors.groupingBy(t -> String.join("&&", t.getStockPointCode(), t.getStockPointType())));
            List<String> stockPointCodes = glassInventoryShiftDetailVOList.stream()
                    .map(GlassInventoryShiftDetailVO::getStockPointCode)
                    .distinct()
                    .collect(Collectors.toList());
            for (String stockPointType : Lists.newArrayList("BC", "MT", "BCMT", "FF")) {
                for (String stockPointCode : stockPointCodes) {
                    String key = String.join("&&", stockPointCode, stockPointType);
                    List<GlassInventoryShiftDetailVO> glassInventoryShiftDetailVOS = collect.get(key);
                    if (CollectionUtils.isEmpty(glassInventoryShiftDetailVOS)) {
                        continue;
                    }
                    String inventoryShiftDataId = glassInventoryShiftDetailVOS.get(0).getInventoryShiftDataId();
                    GlassInventoryShiftPageVO glassInventoryShiftPageVO = new GlassInventoryShiftPageVO();
                    glassInventoryShiftPageVO.setProductCode(glassInventoryShiftDataVOMap.get(inventoryShiftDataId).getProductCode());
                    glassInventoryShiftPageVO.setProductFactoryCode(glassInventoryShiftDataVOMap.get(inventoryShiftDataId).getProductFactoryCode());
                    glassInventoryShiftPageVO.setProductThickness(glassInventoryShiftDataVOMap.get(inventoryShiftDataId).getProductThickness());
                    glassInventoryShiftPageVO.setProductColor(glassInventoryShiftDataVOMap.get(inventoryShiftDataId).getProductColor());
                    glassInventoryShiftPageVO.setVehicleModelCode(glassInventoryShiftDataVOMap.get(inventoryShiftDataId).getVehicleModelCode());
                    glassInventoryShiftPageVO.setStockPointCode(glassInventoryShiftDetailVOS.get(0).getStockPointCode());
                    glassInventoryShiftPageVO.setStockPointType(stockPointType);

                    Map<Date, GlassInventoryShiftDetailVO> inventoryDateMap = glassInventoryShiftDetailVOS.stream()
                            .filter(data -> null != data.getInventoryDate())
                            .collect(Collectors.toMap(data -> DateUtils.formatDate(data.getInventoryDate(), "yyyy-MM-dd"), Function.identity()));
                    List<GlassInventoryShiftDetailVO> list = new ArrayList<>();
                    list.addAll(getDayMaterialPlanInventoryShiftList(inventoryDateMap, dto.getStartDate(),
                            glassInventoryShiftPageVO, coatingCodeList, lotNumberMap, dto.getCoatingWarningDays()));
                    list.addAll(getWeekMaterialPlanInventoryShiftList(glassInventoryShiftDetailVOS, list, dto.getEndDate()));

                    glassInventoryShiftPageVO.setGlassInventoryShiftDetailList(list);
                    glassInventoryShiftPageVOList.add(glassInventoryShiftPageVO);
                }
            }
        }

        PageInfo<GlassInventoryShiftPageVO> glassInventoryShiftPageVOPageInfo = new PageInfo<>(glassInventoryShiftPageVOList);
        glassInventoryShiftPageVOPageInfo.setTotal(pageInfoResult1.getTotal());
        glassInventoryShiftPageVOPageInfo.setPageSize(pageInfoResult1.getPageSize());
        return glassInventoryShiftPageVOPageInfo;
    }

    private List<GlassInventoryShiftDetailVO> getWeekMaterialPlanInventoryShiftList(List<GlassInventoryShiftDetailVO> inventoryShiftList,
                                                                                     List<GlassInventoryShiftDetailVO> list,
                                                                                     Date customEndDate) {

        List<GlassInventoryShiftDetailVO> result = new ArrayList<>();

        // 获取推移天后的最后一个日期
        Date inventoryDate = list.get(list.size() - 1).getInventoryDate();
        Date customStartDate = DateUtils.moveCalendar(inventoryDate, Calendar.DATE, 1);

        // 获取周维度
        List<String> weekRangeGroups = getWeekRangeGroups(customStartDate, customEndDate);
        for (String weekRangeGroup : weekRangeGroups) {
            // 解析 weekRangeGroup 获取开始日期和结束日期
            String[] dates = weekRangeGroup.split("~");
            Date startDate = DateUtils.stringToDate(dates[0]);
            Date endDate = DateUtils.stringToDate(dates[1]);

            // 汇总推移
            GlassInventoryShiftDetailVO vo = summarizeInventoryShift(inventoryShiftList, startDate, endDate);
            vo.setInventoryDateDimension(weekRangeGroup);
            result.add(vo);
        }

        return result;
    }
    public static GlassInventoryShiftDetailVO summarizeInventoryShift(List<GlassInventoryShiftDetailVO> inventoryShiftList,
                                                                       Date startDate,
                                                                       Date endDate) {
        if (inventoryShiftList == null || inventoryShiftList.isEmpty()) {
            return new GlassInventoryShiftDetailVO();
        }

        List<GlassInventoryShiftDetailVO> filteredList = inventoryShiftList.stream()
                .filter(data -> data.getInventoryDate() != null)
                .filter(data -> !data.getInventoryDate().before(startDate) && !data.getInventoryDate().after(endDate))
                .sorted(Comparator.comparing(GlassInventoryShiftDetailVO::getInventoryDate))
                .collect(Collectors.toList());

        if (filteredList.isEmpty()) {
            return new GlassInventoryShiftDetailVO();
        }

        GlassInventoryShiftDetailVO result = new GlassInventoryShiftDetailVO();
        result.setId(filteredList.get(0).getId());
        result.setOpeningInventory(filteredList.get(0).getOpeningInventory());
        result.setEndingInventory(filteredList.get(filteredList.size() - 1).getEndingInventory());

        // 计算安全库存平均值
        result.setSafetyStockLevelMin(calculateAverage(filteredList, GlassInventoryShiftDetailVO::getSafetyStockLevelMin));
        result.setSafetyStockLevelStandard(calculateAverage(filteredList, GlassInventoryShiftDetailVO::getSafetyStockLevelStandard));
        result.setSafetyStockLevelMax(calculateAverage(filteredList, GlassInventoryShiftDetailVO::getSafetyStockLevelMax));

        // 累加其他属性
        result.setDemandQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getDemandQuantity));
        result.setUsedAsReplaceQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getUsedAsReplaceQuantity));
        result.setUseReplaceQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getUseReplaceQuantity));
        result.setAdjustQuantityFromPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getAdjustQuantityFromPort));
        result.setInputQuantity(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getInputQuantity));
        result.setOutputQuantityToBc(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getOutputQuantityToBc));
        result.setTransitQuantityFromPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getTransitQuantityFromPort));
        result.setSafetyStockGap(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getSafetyStockGap));
        result.setAdjustQuantityFromFloat(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getAdjustQuantityFromFloat));
        result.setDecisionOutputQuantityToBc(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getDecisionOutputQuantityToBc));
        result.setDecisionOutputQuantityToPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getDecisionOutputQuantityToPort));
        result.setTransitQuantityFromFloat(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getTransitQuantityFromFloat));
        result.setOutputQuantityToPort(sumBigDecimals(filteredList, GlassInventoryShiftDetailVO::getOutputQuantityToPort));
        return result;
    }

    private static BigDecimal sumBigDecimals(List<GlassInventoryShiftDetailVO> list, Function<GlassInventoryShiftDetailVO, BigDecimal> mapper) {
        return list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private static BigDecimal calculateAverage(List<GlassInventoryShiftDetailVO> list,
                                               Function<GlassInventoryShiftDetailVO, BigDecimal> mapper) {
        // 检查列表是否为空，如果为空则无需计算，直接返回 0
        if (list.isEmpty()) {
            return BigDecimal.ZERO;
        }
        BigDecimal sum = list.stream()
                .map(mapper)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 将累加的总和除以列表的大小，得到平均值
        // 使用 divide 方法时，指定保留两位小数，并采用四舍五入的方式进行处理
        return sum.divide(BigDecimal.valueOf(list.size()), 2, RoundingMode.HALF_UP);
    }

    public static List<String> getWeekRangeGroups(Date customStartDate, Date customEndDate) {
        List<String> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Calendar calendar = Calendar.getInstance();

        // 处理开始日期
        Date startDate = (customStartDate != null) ? customStartDate : calendar.getTime();

        // 处理结束日期
        if (customEndDate == null) {
            calendar.setTime(startDate);
            calendar.add(Calendar.YEAR, 1);
        }
        Date endDate = (customEndDate != null) ? customEndDate : calendar.getTime();

        Calendar currentGroupStart = Calendar.getInstance();
        currentGroupStart.setTime(startDate);
        Calendar currentGroupEnd = Calendar.getInstance();

        while (currentGroupStart.getTime().before(endDate)) {
            currentGroupEnd.setTime(currentGroupStart.getTime());
            // 计算每组的结束日期，加 6 天保证每组 7 天
            currentGroupEnd.add(Calendar.DAY_OF_MONTH, 6);

            if (currentGroupEnd.getTime().after(endDate)) {
                currentGroupEnd.setTime(endDate);
            }

            result.add(sdf.format(currentGroupStart.getTime()) + "~" + sdf.format(currentGroupEnd.getTime()));

            // 移动到下一组的开始日期
            currentGroupStart.setTime(currentGroupEnd.getTime());
            currentGroupStart.add(Calendar.DAY_OF_MONTH, 1);
        }

        return result;
    }


    private List<GlassInventoryShiftDetailVO> getDayMaterialPlanInventoryShiftList(Map<Date, GlassInventoryShiftDetailVO> inventoryDateMap,
                                                                                   Date startDate,
                                                                                   GlassInventoryShiftPageVO glassInventoryShiftPageVO,
                                                                                   List<String> coatingCodeList,
                                                                                   Map<String, List<String>> lotNumberMap,
                                                                                   Integer coatingWarningDays) {
        List<GlassInventoryShiftDetailVO> result = new ArrayList<>();
        // 默认当前日期
        if (startDate == null) {
            startDate = new Date();
        }

        // 获取所需筛选时间
        List<Date> nextNDaysList = DateUtils.getIntervalDates(startDate, DateUtils.moveDay(startDate, 29));

        // 遍历接下来的日期，处理库存数据
        for (Date date : nextNDaysList) {
            GlassInventoryShiftDetailVO vo = new GlassInventoryShiftDetailVO();
            GlassInventoryShiftDetailVO materialPlanInventoryShiftVO = inventoryDateMap.get(date);
            if (Objects.nonNull(materialPlanInventoryShiftVO)) {
                vo = materialPlanInventoryShiftVO;
            }

            vo.setInventoryDate(date);
            vo.setInventoryDateDimension(DateUtils.dateToString(date));

            setWarning(vo, glassInventoryShiftPageVO, coatingCodeList, lotNumberMap, coatingWarningDays);
            result.add(vo);
        }
        return result;
    }

    private void setWarning(GlassInventoryShiftDetailVO vo,
                            GlassInventoryShiftPageVO glassInventoryShiftPageVO,
                            List<String> coatingCodeList,
                            Map<String, List<String>> lotNumberMap,
                            Integer coatingWarningDays) {
        // 已发布调拨-汽运为 空 或 等于0 直接跳过
        if (null == vo.getInputQuantity() || vo.getInputQuantity().compareTo(BigDecimal.ZERO) == 0) return;

        boolean isWarning = true;
        // 是否满足镀膜需求预警
        if ("BC".equals(glassInventoryShiftPageVO.getStockPointType()) || "FF".equals(glassInventoryShiftPageVO.getStockPointType())) {
            if (null != glassInventoryShiftPageVO.getProductFactoryCode()) {
                List<String> productFactoryCodes = Arrays.stream(glassInventoryShiftPageVO.getProductFactoryCode().split(","))
                        .collect(Collectors.toList());

                // 是镀膜工序，并且有需求
                if (CollectionUtils.isNotEmpty(coatingCodeList) && !Collections.disjoint(coatingCodeList, productFactoryCodes)) {
                    if ((null != vo.getDemandQuantity() && vo.getDemandQuantity().compareTo(BigDecimal.ZERO) > 0) ||
                            (null != vo.getUsedAsReplaceQuantity() && vo.getUsedAsReplaceQuantity().compareTo(BigDecimal.ZERO) > 0)) {
                        isWarning = false;
                        vo.setCoatingWarning(true);
                        vo.setCoatingWarningReason(CoatingWarningReasonEnum.COATING_ROUTING.getDesc());
                    }
                }
            }

            if (isWarning) {
                // 处理*号
                List<String> productCodeList = handleSpecialProductCodeList(Collections.singletonList(glassInventoryShiftPageVO.getProductCode()));

                for (String productCode : productCodeList) {
                    // 获取批次号
                    List<String> lotNumberList = lotNumberMap.get(productCode);

                    if (CollectionUtils.isEmpty(lotNumberList)) continue;

                    // 截取批次号
                    List<Date> lotNumberDateList = lotNumberList.stream()
                            .filter(lot -> lot.length() > 10)
                            .map(lot -> lot.substring(2, 10))
                            .map(dateStr -> {
                                Date intermediateDate;
                                try {
                                    intermediateDate = new SimpleDateFormat("yyyyMMdd").parse(dateStr);
                                } catch (ParseException e) {
                                    return null;
                                }
                                return DateUtils.formatDate(intermediateDate, "yyyy-MM-dd");
                            })
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());

                    // 需求日期 - 生产日期 < 预警天数 （多个批次号有一个满足即可）
                    for (Date lotNumberDate : lotNumberDateList) {
                        if (daysBetween(vo.getInventoryDate(), lotNumberDate) < coatingWarningDays) {
                            vo.setCoatingWarning(true);
                            vo.setCoatingWarningReason(CoatingWarningReasonEnum.COATING_DATE.getDesc());
                            break;
                        }
                    }
                }
            }
        }
    }

    @Override
    public List<GlassInventoryShiftDataVO> selectByParams(Map<String, Object> params) {
        List<GlassInventoryShiftDataPO> list = glassInventoryShiftDataDao.selectByParams(params);
        return GlassInventoryShiftDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<GlassInventoryShiftDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {

        return "";
//        return ObjectTypeEnum.GLASS_INVENTORY_SHIFT_DATA.getCode();
    }

    @Override
    public List<GlassInventoryShiftDataVO> invocation(List<GlassInventoryShiftDataVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public List<GlassInventoryShiftDataVO> selectForDemandCalculation(Map<String, Object> params) {
        return glassInventoryShiftDataDao.selectForDemandCalculation(params);
    }

    @Override
    public Date getLastCreateTime(String userId) {
        // 查询该用户的物料权限
        FeignDynamicParam feignDynamicParam = FeignDynamicParam.builder()
                .dynamicColumnParam(Lists.newArrayList("product_code"))
                .queryParam(ImmutableMap.of("materialPlanner", userId))
                .build();
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductListByParamOnDynamicColumns(SystemHolder.getScenario(), feignDynamicParam);
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            List<Date> dateList = new ArrayList<>();
            List<String> productCodeList = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
            List<List<String>> partition = Lists.partition(productCodeList, 1000);
            for (List<String> productCodes : partition) {
                Date date = glassInventoryShiftDataDao.getLastCreateTime(productCodes);
                if (null != date) {
                    dateList.add(date);
                }
            }
            if (CollectionUtils.isNotEmpty(dateList)) {
                return Collections.max(dateList);
            }
        }
        return null;
    }

    public static List<String> handleSpecialProductCodeList(List<String> productCodes) {
        List<String> newProductCodes = new ArrayList<>();
        for (String productCode : productCodes) {
            if (productCode.charAt(2) == '*') {
                StringBuilder sb = new StringBuilder(productCode);
                sb.setCharAt(2, 'B');
                newProductCodes.add(sb.toString());
                sb.setCharAt(2, 'T');
                newProductCodes.add(sb.toString());
            } else {
                newProductCodes.add(productCode);
            }
        }
        return newProductCodes;
    }

    /**
     * 计算两个日期之间的天数差（忽略时间部分，仅比较日期）
     *
     * @param dateA 起始日期
     * @param dateB 结束日期
     * @return 天数差（dateB - dateA），如果dateA在dateB之后返回负数
     */
    public static long daysBetween(Date dateA, Date dateB) {
        LocalDate localDateA = dateA.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        LocalDate localDateB = dateB.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        return localDateB.toEpochDay() - localDateA.toEpochDay();
    }
}
