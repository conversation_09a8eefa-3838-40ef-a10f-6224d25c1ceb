package com.yhl.scp.mds.box.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>BoxInfoDTO</code>
 * <p>
 * 箱体信息DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:13:42
 */
@ApiModel(value = "箱体信息DTO")
@Data
public class BoxInfoDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 746398715512420101L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 箱子类型：铁箱/纸箱
     */
    @ApiModelProperty(value = "箱子类型：铁箱/纸箱")
    private String boxType;
    /**
     * 箱子编码
     */
    @ApiModelProperty(value = "箱子编码")
    private String boxCode;
    /**
     * 单箱片数
     */
    @ApiModelProperty(value = "单箱片数")
    private Integer piecePerBox;
    /**
     * 单排箱数
     */
    @ApiModelProperty(value = "单排箱数")
    private Integer boxPerRow;
    /**
     * 单垛数量
     */
    @ApiModelProperty(value = "单垛数量")
    private Integer perStackQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    private Integer boxQuantity;
    /**
     * 箱长
     */
    @ApiModelProperty(value = "箱长")
    private BigDecimal boxLength;
    /**
     * 箱宽
     */
    @ApiModelProperty(value = "箱宽")
    private BigDecimal boxWidth;
    /**
     * 箱重
     */
    @ApiModelProperty(value = "箱重")
    private BigDecimal boxWeight;
    /**
     * 箱高
     */
    @ApiModelProperty(value = "箱高")
    private BigDecimal boxHeight;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否生效（生效/失效）
     */
    @ApiModelProperty(value = "是否生效（生效/失效）")
    private String enabled;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    private String kid;
    /**
     * 计划区域
     */
    @ApiModelProperty(value = "计划区域")
    private String planArea;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String creator;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String modifier;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;


}
