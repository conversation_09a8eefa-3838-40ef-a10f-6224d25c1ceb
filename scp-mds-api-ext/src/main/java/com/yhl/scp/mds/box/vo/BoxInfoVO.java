package com.yhl.scp.mds.box.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>BoxInfoVO</code>
 * <p>
 * 箱体信息VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 10:13:42
 */
@ApiModel(value = "箱体信息VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class BoxInfoVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -83860887731626748L;

    /**
     * 箱子类型：铁箱/纸箱
     */
    @ApiModelProperty(value = "箱子类型：铁箱/纸箱")
    @FieldInterpretation(value = "箱子类型：铁箱/纸箱")
    private String boxType;
    /**
     * 箱子编码
     */
    @ApiModelProperty(value = "箱子编码")
    @FieldInterpretation(value = "箱子编码")
    private String boxCode;
    /**
     * 单箱片数
     */
    @ApiModelProperty(value = "单箱片数")
    @FieldInterpretation(value = "单箱片数")
    private Integer piecePerBox;
    /**
     * 单排箱数
     */
    @ApiModelProperty(value = "单排箱数")
    @FieldInterpretation(value = "单排箱数")
    private Integer boxPerRow;
    /**
     * 单垛数量
     */
    @ApiModelProperty(value = "单垛数量")
    @FieldInterpretation(value = "单垛数量")
    private Integer perStackQuantity;
    /**
     * 箱数
     */
    @ApiModelProperty(value = "箱数")
    @FieldInterpretation(value = "箱数")
    private Integer boxQuantity;
    /**
     * 箱长
     */
    @ApiModelProperty(value = "箱长")
    @FieldInterpretation(value = "箱长")
    private BigDecimal boxLength;
    /**
     * 箱宽
     */
    @ApiModelProperty(value = "箱宽")
    @FieldInterpretation(value = "箱宽")
    private BigDecimal boxWidth;
    /**
     * 箱重
     */
    @ApiModelProperty(value = "箱重")
    @FieldInterpretation(value = "箱重")
    private BigDecimal boxWeight;
    /**
     * 箱高
     */
    @ApiModelProperty(value = "箱高")
    @FieldInterpretation(value = "箱高")
    private BigDecimal boxHeight;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    @FieldInterpretation(value = "库存点ID")
    private String stockPointId;

    /**
     * 库存点编码
     */
    @ApiModelProperty(value = "库存点编码")
    @FieldInterpretation(value = "库存点编码")
    private String stockPointCode;

    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @FieldInterpretation(value = "库存点名称")
    private String stockPointName;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date lastUpdateTime;
    /**
     * kid
     */
    @ApiModelProperty(value = "kid")
    private String kid;
    /**
     * 计划区域
     */
    @ApiModelProperty(value = "计划区域")
    private String planArea;

    @Override
    public void clean() {

    }

}
