package com.yhl.scp.mds.routing.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepResourceVO;
import com.yhl.scp.mds.routing.dto.NewRoutingStepResourceDTO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO;
import com.yhl.scp.mds.routing.vo.NewRoutingStepVO;
import com.yhl.scp.mds.routing.vo.NewRoutingVO;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;

/**
 * <code>NewRoutingStepResourceService</code>
 * <p>
 * 新-生产路径步骤资源应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-20 14:57:33
 */
public interface NewRoutingStepResourceService extends BaseService<NewRoutingStepResourceDTO, NewRoutingStepResourceVO> {

    /**
     * 查询所有
     *
     * @return list {@link RoutingStepResourceVO}
     */
    List<NewRoutingStepResourceVO> selectAll();

	void doLogicDeleteBatchByRoutingIds(List<String> routingIds);

	void doLogicDeleteBatchByRoutingStepIds(List<String> routingStepIds);

	void doTransitionRoutingStepResource(List<String> routingStepResourceIds, List<NewRoutingVO> routingList,
										 List<NewRoutingStepVO> newRoutingSteps, List<NewStockPointVO> stockPoints,
										 String scenario);
	
	void doCreateBatchWithPrimaryKey(List<NewRoutingStepResourceDTO> list);
    
    void doUpdateBatchSelective(List<NewRoutingStepResourceDTO> list);

	List<String> selectAllRoutingStepIds();

	Map<String, String> selectMainProcessByProductCodes(List<String> productCodes);
	List<NewRoutingStepResourceVO> selectRoutingStepResoueceBase(List<String> routingIds);

}
