package com.yhl.scp.mds.productroutestepbase.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <code>MdsProductStockPointBaseExportDTO</code>
 * <p>
 * 产品工艺基础数据导出DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-19 14:28:43
 */
@ApiModel(value = "产品工艺基础数据导出DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class MdsProductStockPointBaseExportDTO implements Serializable {

    private static final long serialVersionUID = 177985572270395976L;

    /**
     * 库存点代码
     */
    @ApiModelProperty(value = "库存点代码")
    @ExcelProperty(value = "库存点代码")
    private String stockPointCode;
    /**
     * 库存点名称
     */
    @ApiModelProperty(value = "库存点名称")
    @ExcelProperty(value = "库存点名称")
    private String stockPointName;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @ExcelProperty(value = "本厂编码")
    private String productCode;
    /**
     * 编码名称
     */
    @ApiModelProperty(value = "编码名称")
    @ExcelProperty(value = "编码名称")
    private String productName;
    /**
     * 长
     */
    @ApiModelProperty(value = "长")
    @ExcelProperty(value = "长")
    private BigDecimal productLength;
    /**
     * 宽
     */
    @ApiModelProperty(value = "宽")
    @ExcelProperty(value = "宽")
    private BigDecimal productWidth;
    /**
     * 厚
     */
    @ApiModelProperty(value = "厚")
    @ExcelProperty(value = "厚")
    private BigDecimal productThickness;
    /**
     * 装车位置
     */
    @ApiModelProperty(value = "装车位置")
    @ExcelProperty(value = "装车位置")
    private String loadPosition;
    /**
     * 颜色
     */
    @ApiModelProperty(value = "颜色")
    @ExcelProperty(value = "颜色")
    private String productColor;
    /**
     * 玻璃颜色
     */
    @ApiModelProperty(value = "玻璃颜色")
    @ExcelProperty(value = "玻璃颜色")
    private String glassColor;
    /**
     * 产品类型
     */
    @ApiModelProperty(value = "产品类型")
    @ExcelProperty(value = "产品类型")
    private String productType;
    /**
     * 难度等级
     */
    @ApiModelProperty(value = "难度等级")
    @ExcelProperty(value = "难度等级")
    private String difficultyLevel;
    /**
     * 风栅类型
     */
    @ApiModelProperty(value = "风栅类型")
    @ExcelProperty(value = "风栅类型")
    private String gridType;
    /**
     * 生产模式
     */
    @ApiModelProperty(value = "生产模式")
    @ExcelProperty(value = "生产模式")
    private String productionModel;
    /**
     * 钢化类型
     */
    @ApiModelProperty(value = "钢化类型")
    @ExcelProperty(value = "钢化类型")
    private String tougheningType;
    /**
     * 膜系
     */
    @ApiModelProperty(value = "膜系")
    @ExcelProperty(value = "膜系")
    private String membraneSystem;
    /**
     * HUD
     */
    @ApiModelProperty(value = "HUD")
    @ExcelProperty(value = "HUD")
    private String hud;
    /**
     * 夹丝类型
     */
    @ApiModelProperty(value = "夹丝类型")
    @ExcelProperty(value = "夹丝类型")
    private String clampType;
    /**
     * 印边
     */
    @ApiModelProperty(value = "印边")
    @ExcelProperty(value = "印边")
    private String sealEdge;
    /**
     * 面积
     */
    @ApiModelProperty(value = "面积")
    @ExcelProperty(value = "面积")
    private String productArea;
    /**
     * 曲率
     */
    @ApiModelProperty(value = "曲率")
    @ExcelProperty(value = "曲率")
    private String curvature;
    /**
     * 编码目录号
     */
    @ApiModelProperty(value = "编码目录号")
    private String dirNum;
    /**
     * 工艺类型
     */
    @ApiModelProperty(value = "工艺类型")
    private String itemType;
    /***
     * 除膜工艺
     */
    @ApiModelProperty(value = "除膜工艺")
    private String attr1;
    /***
     * 物料标识
     */
    @ApiModelProperty(value = "物料标识")
    private String itemFlag;
    /***
     * 生产线组
     */
    @ApiModelProperty(value = "生产线组")
    private String lineGroup;
    /***
     * 工装大类
     */
    @ApiModelProperty(value = "工装大类")
    private String standardResourceId;
    /***
     * 零件号
     */
    @ApiModelProperty(value = "零件号")
    private String partNum;
}
