package com.yhl.scp.mds.routing.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>ProductRoutingStepVO</code>
 * <p>
 * 物品工艺路径步骤VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-10 17:52:40
 */
@ApiModel(value = "物品工艺路径步骤VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductRoutingStepVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 867313501763200858L;

    /**
     * 路径ID
     */
    @ApiModelProperty(value = "路径ID")
    @FieldInterpretation(value = "路径ID")
    private String routingId;
    /**
     * 工艺行ID
     */
    @ApiModelProperty(value = "工艺行ID")
    @FieldInterpretation(value = "工艺行ID")
    private String operationSequenceId;
    /**
     * 顺序号
     */
    @ApiModelProperty(value = "顺序号")
    @FieldInterpretation(value = "顺序号")
    private Integer sequenceNo;
    /**
     * 前工序顺序号
     */
    @ApiModelProperty(value = "前工序顺序号")
    @FieldInterpretation(value = "前工序顺序号")
    private String preRoutingStepSequenceNo;
    /**
     * 后工序顺序号
     */
    @ApiModelProperty(value = "后工序顺序号")
    @FieldInterpretation(value = "后工序顺序号")
    private String nextRoutingStepSequenceNo;
    /**
     * 成品率
     */
    @ApiModelProperty(value = "成品率")
    @FieldInterpretation(value = "成品率")
    private BigDecimal yield;
    /**
     * 损耗策略
     */
    @ApiModelProperty(value = "损耗策略")
    @FieldInterpretation(value = "损耗策略")
    private String scrapStrategy;
    /**
     * 百分比损耗率
     */
    @ApiModelProperty(value = "百分比损耗率")
    @FieldInterpretation(value = "百分比损耗率")
    private BigDecimal percentageScrapRate;
    /**
     * 固定损耗数
     */
    @ApiModelProperty(value = "固定损耗数")
    @FieldInterpretation(value = "固定损耗数")
    private BigDecimal scrap;
    /**
     * 与前工序数量比
     */
    @ApiModelProperty(value = "与前工序数量比")
    @FieldInterpretation(value = "与前工序数量比")
    private BigDecimal preProcessRatio;
    /**
     * 加工方式
     */
    @ApiModelProperty(value = "加工方式")
    @FieldInterpretation(value = "加工方式")
    private String processingType;
    /**
     * 接续任务
     */
    @ApiModelProperty(value = "接续任务")
    @FieldInterpretation(value = "接续任务")
    private String connectionTask;
    /**
     * 接续方式
     */
    @ApiModelProperty(value = "接续方式")
    @FieldInterpretation(value = "接续方式")
    private String connectionType;
    /**
     * 最大接续时间
     */
    @ApiModelProperty(value = "最大接续时间")
    @FieldInterpretation(value = "最大接续时间")
    private Integer maxConnectionDuration;
    /**
     * 最小接续时间
     */
    @ApiModelProperty(value = "最小接续时间")
    @FieldInterpretation(value = "最小接续时间")
    private Integer minConnectionDuration;
    /**
     * 标准工艺ID
     */
    @ApiModelProperty(value = "标准工艺ID")
    @FieldInterpretation(value = "标准工艺ID")
    private String standardStepId;
    /**
     * 计数单位ID
     */
    @ApiModelProperty(value = "计数单位ID")
    @FieldInterpretation(value = "计数单位ID")
    private String countingUnitId;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    @FieldInterpretation(value = "是否有效")
    private String effective;
    /**
     * 失效原因
     */
    @ApiModelProperty(value = "失效原因")
    @FieldInterpretation(value = "失效原因")
    private String expireReason;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    @FieldInterpretation(value = "最后更新时间")
    private Date lastUpdateDate;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;
    
    /**
     * 路径代码
     */
    @ApiModelProperty(value = "路径代码")
    @FieldInterpretation(value = "路径代码")
    private String routingCode;
    
    /**
     * 标准工艺代码
     */
    @ApiModelProperty(value = "标准工艺代码")
    @FieldInterpretation(value = "标准工艺代码")
    private String standardStepCode;
    
    /**
     * 标准工艺名称
     */
    @ApiModelProperty(value = "标准工艺名称")
    @FieldInterpretation(value = "标准工艺名称")
    private String standardStepName;
    /**
     * 有效期起
     */
    @ApiModelProperty(value = "有效期起")
    @FieldInterpretation(value = "有效期起")
    private Date startDate;
    /**
     * 有效期止
     */
    @ApiModelProperty(value = "有效期止")
    @FieldInterpretation(value = "有效期止")
    private Date disableDate;
    /**
     * 标志
     */
    @ApiModelProperty(value = "标志")
    @FieldInterpretation(value = "标志")
    private String flag;

    @Override
    public void clean() {

    }

    private String stockPointCode;

}
