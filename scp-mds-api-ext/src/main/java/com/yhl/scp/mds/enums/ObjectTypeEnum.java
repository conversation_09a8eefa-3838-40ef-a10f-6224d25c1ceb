package com.yhl.scp.mds.enums;

import com.yhl.platform.common.enums.CommonEnum2;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ObjectTypeEnum</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 16:17:02
 */
public enum ObjectTypeEnum implements CommonEnum2 {
    /**
     * 场景
     */
    ANNUAL_FORECAST_SUBMISSION("annual_forecast_submission", "年度预测提报", "com.yhl.scp.dfp.annual.vo.AnnualForecastSubmissionVO"),
    BOX_INFO("box_info", "箱体信息", "com.yhl.scp.dfp.annual.vo.BoxInfoVO"),
    CLEAN_ALGORITHM_DATA("clean_algorithm_data", "预测算法数据", "com.yhl.scp.dfp.annual.vo.CleanAlgorithmDataVO"),
    CLEAN_DEMAND_DATA("clean_demand_data", "日需求数据", "com.yhl.scp.dfp.annual.vo.CleanDemandDataVO"),
    CLEAN_DEMAND_DATA_DETAIL("clean_demand_data_detail", "日需求数据明细", "com.yhl.scp.dfp.annual.vo.CleanDemandDataDetailVO"),
    CLEAN_FORECAST_DATA("clean_forecast_data", "滚动预测数据", "com.yhl.scp.dfp.annual.vo.CleanForecastDataVO"),
    CLEAN_FORECAST_DATA_DETAIL("clean_forecast_data_detail", "滚动预测数据明细", "com.yhl.scp.dfp.annual.vo.CleanForecastDataDetailVO"),
    CONSISTENCE_DEMAND_FORECAST_DATA("consistence_demand_forecast_data", "一致性业务预测数据", "com.yhl.scp.dfp.annual.vo.ConsistenceDemandForecastDataVO"),
    CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL("consistence_demand_forecast_data_detail", "一致性业务预测数据明细", "com.yhl.scp.dfp.annual.vo.ConsistenceDemandForecastDataDetailVO"),
    CONSISTENCE_DEMAND_FORECAST_VERSION("consistence_demand_forecast_version", "一致性业务预测版本", "com.yhl.scp.dfp.annual.vo.ConsistenceDemandForecastVersionVO"),
    DELIVERY_PLAN_LOCK_CONFIG("delivery_plan", "发货计划表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanLockConfigVO"),
    DELIVERY_PLAN_REPLENISH_CONFIG("delivery_plan", "发货计划表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanReplenishConfigVO"),
    DELIVERY_PLAN("delivery_plan", "发货计划表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO"),
    DELIVERY_PLAN_DETAIL("delivery_plan_detail", "发货计划明细表", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanDetailVO"),
    DELIVERY_PLAN_VERSION("delivery_plan_version", "发货计划版本", "com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO"),
    DEMAND_FORECAST_VERSION("demand_forecast_version", "业务预测版本", "com.yhl.scp.dfp.annual.vo.DemandForecastVersionVO"),
    DEMAND_VERSION("demand_version", "需求版本", "com.yhl.scp.dfp.annual.vo.DemandVersionVO"),
    GLOBAL_CAR_SALE("global_car_sale", "全球汽车销量", "com.yhl.scp.dfp.annual.vo.GlobalCarSaleVO"),
    GLOBAL_CAR_SALE_DETAIL("global_car_sale_detail", "全球汽车销量详情", "com.yhl.scp.dfp.annual.vo.GlobalCarSaleDetailVO"),
    CAR_PRICE_LIST("car_price_list", "汽车价格表", "com.yhl.scp.dfp.car.vo.CarPriceListVO"),
    CAR_SALE_LIST("car_sale_list", "汽车销量表", "com.yhl.scp.dfp.car.vo.CarSaleListVO"),
    LOADING_DEMAND_SUBMISSION("loading_demand_submission", "装车需求提报", "com.yhl.scp.dfp.annual.vo.LoadingDemandSubmissionVO"),
    LOADING_DEMAND_SUBMISSION_DETAIL("loading_demand_submission_detail", "装车需求提报详情", "com.yhl.scp.dfp.annual.vo.LoadingDemandSubmissionDetailVO"),
    MARKET_INFORMATION("market_information", "市场信息", "com.yhl.scp.dfp.market.vo.MarketInformationVO"),
    POLICY_INFORMATION("policy_information", "政策信息", "com.yhl.scp.dfp.policy.vo.PolicyInformationVO"),
    MARKET_SHARE("market_share", "市场占有率", "com.yhl.scp.dfp.market.vo.MarketShareVO"),
    MARKET_SHARE_DETAIL("market_share_detail", "市场占有率详情", "com.yhl.scp.dfp.market.vo.MarketShareDetailVO"),
    MATERIAL_RISK_LEVEL("material_risk_level", "零件风险等级", "com.yhl.scp.dfp.annual.vo.MaterialRiskLevelVO"),
    MATERIAL_RISK_LEVEL_DETAIL("material_risk_level_detail", "零件风险等级详情", "com.yhl.scp.dfp.annual.vo.MaterialRiskLevelDetailVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION("new_product_trial_submission", "新品试制提报", "com.yhl.scp.dfp.annual.vo.NewProductTrialSubmissionVO"),
    NEW_PRODUCT_TRIAL_SUBMISSION_DETAIL("new_product_trial_submission_detail", "新品试制提报详情", "com.yhl.scp.dfp.annual.vo.NewProductTrialSubmissionDetailVO"),
    NEW_PROJECT_SUBMISSION("new_project_submission", "新项目提报", "com.yhl.scp.dfp.annual.vo.NewProjectSubmissionVO"),
    NEW_PROJECT_SUBMISSION_DETAIL("new_project_submission_detail", "新项目提报详情", "com.yhl.scp.dfp.annual.vo.NewProjectSubmissionDetailVO"),
    OEM_RISK_LEVEL("oem_risk_level", "主机厂风险等级", "com.yhl.scp.dfp.annual.vo.OemRiskLevelVO"),
    ORIGIN_DEMAND_VERSION("origin_demand_version", "原始需求版本", "com.yhl.scp.dfp.annual.vo.OriginDemandVersionVO"),
    PART_RELATION_MAP("part_relation_map", "零件映射关系", "com.yhl.scp.dfp.annual.vo.PartRelationMapVO"),
    PASSENGER_CAR_SALE("passenger_car_sale", "乘用车市场信息", "com.yhl.scp.dfp.annual.vo.PassengerCarSaleVO"),
    PRODUCT_BOX_RELATION("product_box_relation", "产品与成品箱关系", "com.yhl.scp.mds.annual.vo.ProductBoxRelationVO"),
    QUEUE_PLAN("queue_plan", "排车计划", "com.yhl.scp.dfp.annual.vo.QueuePlanVO"),
    QUEUE_PLAN_BATCH("queue_plan_batch", "排车计划批次", "com.yhl.scp.dfp.annual.vo.QueuePlanBatchVO"),
    RISK_LEVEL_RULE("risk_level_rule", "风险等级规则", "com.yhl.scp.dfp.annual.vo.RiskLevelRuleVO"),
    RISK_LEVEL_RULE_DETAIL("risk_level_rule_detail", "风险等级规则详情", "com.yhl.scp.dfp.annual.vo.RiskLevelRuleDetailVO"),
    SAFETY_STOCK_LEVEL("safety_stock_level", "安全库存水位", "com.yhl.scp.dfp.annual.vo.SafetyStockLevelVO"),
    VEHICLE_CONFIGURATION("vehicle_configuration", "车型配置信息", "com.yhl.scp.dfp.annual.vo.VehicleConfigurationVO"),
    VEHICLE_CONFIGURATION_DETAIL("vehicle_configuration_detail", "车型配置信息详情", "com.yhl.scp.dfp.annual.vo.VehicleConfigurationDetailVO"),
    OEM("oem", "主机厂档案", "com.yhl.scp.dfp.annual.vo.OemVO"),
    OEM_PRODUCT_LINE("oem_product_line", "主机厂产线资源", "com.yhl.scp.dfp.annual.vo.OemProductLineVO"),
    OEM_PRODUCT_LINE_MAP("oem_product_line_map", "产线映射关系", "com.yhl.scp.dfp.annual.vo.OemProductLineMapVO"),
    OEM_STOCK_POINT_MAP("oem_stock_point_map", "主机厂库存点关联关系", "com.yhl.scp.dfp.annual.vo.OemStockPointMapVO"),
    OEM_VEHICLE_MODEL("oem_vehicle_model", "主机厂车型信息", "com.yhl.scp.dfp.annual.vo.OemVehicleModelVO"),
    OEM_VEHICLE_MODEL_MAP("oem_vehicle_model_map", "主机厂车型映射关系", "com.yhl.scp.dfp.annual.vo.OemVehicleModelMapVO"),
    PRODUCT_STOCK_POINT("product_stock_point", "物品", "com.yhl.scp.dfp.annual.vo.ProductStockPointVO"),
    PRODUCTION_ORGANIZE("production_organize", "生产组织", "com.yhl.scp.mds.production.vo.ProductionOrganizeVO"),
    SALE_ORGANIZE("sale_organize", "销售组织", "com.yhl.scp.mds.sale.vo.SaleOrganizeVO"),
    STOCK_POINT("stock_point", "库存点", "com.yhl.scp.mds.stock.vo.NewStockPointVO"),
    SUPPLIER_SUBMISSION("stock_point", "车型全供应商提报", "com.yhl.scp.dfp.supplier.vo.SupplierSubmissionVO"),
    SUPPLIER_SUBMISSION_DETAIL("stock_point", "车型全供应商提报明细", "com.yhl.scp.dfp.supplier.vo.SupplierSubmissionDetailVO"),
    INVENTORY_REAL_TIME_DATA("inventory_real_time_data", "库存实时数据", "com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO"),
    CURRENT_BATCH_QUANTITY("current_batch_quantity", "批次现有量数据", "com.yhl.scp.dfp.stock.vo.InventoryRealTimeDataVO"),
    ORIGINAL_FILM_CURRENT_BATCH_QUANTITY("original_film_current_batch_quantity", "原片库存批次明细数据", "com.yhl.scp.dfp.stock.vo.OriginalFilmCurrentBatchQuantityVO"),
    ORIGINAL_FILM_IN_TRANSIT("original_film_in_transit", "原片在途数据", "com.yhl.scp.dfp.stock.vo.OriginalFilmInTransitVO"),
    EXTERNAL_BUSINESS_DEMAND_SUBMIT("external_business_demand_submit", "外事业部需求提报", "com.yhl.scp.dfp.externalBusiness.vo.ExternalBusinessDemandSubmitVO"),
    EXTERNAL_BUSINESS_DEMAND_SUBMIT_DETAIL("external_business_demand_submit_detail", "外事业部需求提报明细", "com.yhl.scp.dfp.externalBusiness.vo.ExternalBusinessDemandSubmitDetailVO"),
    INVENTORY_SHIFT("inventory_shift", "库存推移表", "com.yhl.scp.dfp.stock.vo.InventoryShiftVO"),
    MASTER_PLAN_DETAIL("master_plan_detail","主生产计划明细","com.yhl.scp.mps.plan.vo.MasterPlanDetailVO"),
    DFP_DEMAND_FORECAST_ATTACHMENTS("dfp_demand_forecast_attachments","源文件管理","com.yhl.scp.dfp.loading.vo.DfpDemandForecastAttachmentsVO"),

    DFP_SWITCH_RELATION_BETWEEN_PRODUCT("dfp_switch_relation_between_product","新旧产品工程变更关系"
            ,"com.yhl.scp.dfp.loading.vo.DfpSwitchRelationBetweenProductVO"),


    RESOURCE_GROUP("resource_group", "资源组", "com.yhl.scp.dfp.resource.vo.ResourceGroupVO"),
    DEMAND_FORECAST_ESTABLISHMENT("demand_forecast_establishment", "业务预测编制", "com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO"),
    RESOURCE("resource", "资源", "com.yhl.scp.dfp.resource.vo.ResourceVO"),
    CALENDAR_RULE("calendar_rule", "日历规则", "com.yhl.scp.mps.calendar.vo.CalendarRuleVO"),
    SHIFT("shift", "班次", "com.yhl.scp.mps.calendar.vo.ShiftVO"),
    TRANSPORT_RESOURCE("transport_resource", "运输资源", "com.yhl.scp.dfp.transport.vo.TransportResourceVO"),
    TRANSPORT_ROUTING("transport_routing", "运输路径", "com.yhl.scp.dfp.transport.vo.TransportRoutingVO"),
    TRANSPORT_ROUTING_INTERFACE_LOG("transport_routing_interface_log", "运输路径MES接口日志", "com.yhl.scp.dfp.transport.vo" +
            ".TransportRoutingInterfaceLogVO"),
    TRANSPORT_ROUTING_DETAIL("transport_routing_detail", "运输线路", "com.yhl.scp.dfp.transport.vo.TransportRoutingDetailVO"),
    TRANSPORT_ROUTING_RESOURCE("transport_routing_resource", "运输路径资源关系", "com.yhl.scp.dfp.transport.vo.TransportRoutingResourceVO"),
    ERP_BOM("erp_bom", "erp同步bom主数据", "com.yhl.scp.mds.routing.vo.ErpBomVO"),
    PRODUCT_STOCK_POINT_BASE("mds_product_stock_point_base","产品工艺基础数据","com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO"),
    OVER_DEADLINE_DAYS("mds_over_deadline_days","超期界定天数","com.yhl.scp.mds.overdeadlineday.vo.MdsOverDeadlineDaysVO"),
    VEHICLE_MARKET_INVENTORY("vehicle_market_inventory","车型市场保有量","com.yhl.scp.dfp.vehicle.vo.VehicleMarketInventoryVO"),
    INVENTORY_BATCH_DETAIL("inventory_batch_detail","库存批次明细" ,"com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO" ),
    PRODUCT_CANDIDATE_RESOURCE_TIME("product_candidate_resource_time", "产品资源生产关系时段优先级", "com.yhl.scp.mps.product.vo.ProductCandidateResourceTimeVO"),
    PRODUCT_SUBSTITUTION_RELATIONSHIP("product_substitution_relationship", "物料替换关系", "com.yhl.scp.mps.product.vo.ProductSubstitutionRelationshipVO"),
    DELIVERY_DOCKING_ORDER_DETAIL("delivery_docking_order_detail","发货对接单详情" ,"com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO" ),
    DELIVERY_DOCKING_ORDER("delivery_docking_order","发货对接单" ,"com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO" ),
    MATERIAL_INVENTORY_OUR_FACTORY_DETAIL("inventory_our_factory_detail","原片本厂库存批次明细","com.yhl.scp.mrp.inventory.vo.InventoryOurFactoryDetailVO"),
    MATERIAL_INVENTORY_QUAY_DETAIL("inventory_quay_detail","原片码头库存批次明细","com.yhl.scp.mrp.inventory.vo.InventoryQuayDetailVO"),
    MATERIAL_INVENTORY_FLOAT_GLASS_DETAIL("inventory_float_glass_detail","原片浮法库存批次明细","com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassDetailVO"),
    MATERIAL_INVENTORY_FLOAT_GLASS_SHIPPED_DETAIL("inventory_float_glass_shipped_detail","原片浮法已发运库存批次明细","com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO"),
    MATERIAL_INVENTORY_ALTERNATIVE_RELATIONSHIP("inventory_alternative_relationship","原片库存替代关系","com.yhl.scp.mrp.inventory.vo.InventoryAlternativeRelationshipVO"),
    OEM_INVENTORY_SUBMISSION("oem_inventory_submission","主机厂库存提报" ,"com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO"),
    FULFILLMENT_MANUAL("fulfillment_manual","手动分配关系","com.yhl.scp.mrp.inventory.vo.FulfillmentManualVO"),
    WAREHOUSE_RELEASE_RECORD_LOG("WAREHOUSE_RELEASE_RECORD_LOG", "DFP库存发布记录日志", "com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordLogVO"),
    OEM_TRANSPORT_TIME("oem_transport_time","主机厂运输时间" ,"com.yhl.scp.dfp.oem.vo.OemTransportTimeVO" ),
    OEM_ADDRESS_INVENTORY_LOG("oem_address_inventory_log","主机厂地址" ,"com.yhl.scp.dfp.oem.vo.OemAddressInventoryLogVO" ),
    FDP_ORIGIN_DEMAND_INTERFACE_LOG("FDP_ORIGIN_DEMAND_INTERFACE_LOG","EDI系统需求数据接口中间表", "com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO"),
    FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG("FDP_ORIGIN_DEMAND_FORECAST_INTERFACE_LOG","EDI系统预测数据中间表", "com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO"),
    PRO_FREE_STORAGE("pro_free_storage","免堆期" ,"com.yhl.scp.mrp.freestorage.vo.ProFreeStorageVO" ),
    INVENTORY_OCEAN_FREIGHT("inventory_ocean_freight","浮法海运" ,"com.yhl.scp.mps.product.vo.InventoryOceanFreightVO" ),
    CHAIN_LINE("CHAIN_LINE","链式生产线" ,"com.yhl.scp.mrp.inventory.vo.ChainLineInventoryLogVO" ),
    HISTORY_FORECAST_DATA("history_forecast_data","历史预测数据表" ,"com.yhl.scp.dfp.forecast.vo.HistoryForecastDataVO" ),
	NEW_ROUTING("NEW_ROUTING","生产路径" ,"com.yhl.scp.mds.routing.vo.NewRoutingVO" ),
	NEW_ROUTING_STEP("NEW_ROUTING_STEP","生产路径步骤" ,"com.yhl.scp.mds.routing.vo.NewRoutingStepVO" ),
	NEW_ROUTING_STEP_INPUT("NEW_ROUTING_STEP_INPUT","生产路径步骤输入物品" ,"com.yhl.scp.mds.routing.vo.NewRoutingStepInputVO" ),
	NEW_ROUTING_STEP_OUTPUT("NEW_ROUTING_STEP_OUTPUT","生产路径步骤输出物品" ,"com.yhl.scp.mds.routing.vo.NewRoutingStepOutputVO" ),
	NEW_ROUTING_STEP_RESOURCE("NEW_ROUTING_STEP_RESOURCE","生产路径步骤资源" ,"com.yhl.scp.mds.routing.vo.NewRoutingStepResourceVO" ),
    SUPPLIER_ADDRESS("SUPPLIER_ADDRESS","供应商地址" ,"com.yhl.scp.mds.supplier.vo.SupplierAddressVO" ),
    CUSTOMER("CUSTOMER","客户" ,"com.yhl.scp.mds.customer.vo.CustomerVO" ),
    PRODUCTION_INTERVAL("production_interval","MPS结果-生产批量结果应用实现","com.yhl.scp.mps.plan.vo.ProductionIntervalVO" ),
    OEM_CUSTOMER_ADDRESS_INVENTORY_LOG("oem_customer_address_inventory_log", "ERP客户地址表","com.yhl.scp.mds.customer.vo.OemCustomerAddressInventoryLogVO"),
    PRODUCT_FIXTURE_RELATION("product_fixture_relation","产品与工装关系" ,"com.yhl.scp.mps.fixtureRelation.vo" + ".ProductFixtureRelationVO" ),
    WAREHOUSE_HALF_SUBINVENTORY("warehouse_half_subinventory","中转库半品辅料" , "com.yhl.scp.mrp.halfsubinventory.vo.WarehouseHalfSubinventoryVO"),
    GLASS_SAFETY_INVENTORY("glass_safety_inventory","原片安全库存天数" ,"com.yhl.scp.mrp.inventory.vo.GlassSafetyInventoryVO" ),
    MOLD_TOOLING("mold_tooling","模具工装族与工装编号关系" ,"com.yhl.scp.mds.mold.vo.MoldToolingVO" ),
    MOLD_TOOLING_GROUP("mold_tooling_group","模具工装族" ,"com.yhl.scp.mds.mold.vo.MoldToolingGroupVO" ),
    PRODUCT_CANDIDATE_RESOURCE("PRODUCT_CANDIDATE_RESOURCE","物品候选资源","com.yhl.scp.mds.candidateResource.vo" +
                                       ".ProductCandidateResourceVO" ),
    DELIVERY_PLAN_PUBLISHED("delivery_plan_published","发货计划发布" ,"com.yhl.scp.mps.delivery.vo.DeliveryPlanPublishedVO" ),
    MASTER_PLAN_PUBLISHED("master_plan_published","生产计划已发布" ,"com.yhl.scp.mps.published.vo.MasterPlanPublishedVO" ),
    MATERIAL_RETURNED_PURCHASE("material_returned_purchase","采购退货" ,"com.yhl.scp.mrp.material.purchase.vo.MaterialReturnedPurchaseVO"),
    MATERIAL_TRANSACTIONS("material_transactions","物料事务处理查询" ,"com.yhl.scp.mrp.material.transactions.vo.MaterialTransactionsVO"),
    SDS_ORD_WORK_ORDER_SUPPLEMENTARY_PUBLISH_LOG("sds_ord_work_order_supplementary_publish_log","临时补单" , "com.yhl.scp.mps.operationPublished.vo.SdsOrdWorkOrderSupplementaryPublishLogVO");
	ObjectTypeEnum(String code, String desc, String mappingValue) {
        this.code = code;
        this.desc = desc;
        this.mappingValue = mappingValue;
    }

    /**
     * 表名 / 视图名
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    /**
     * 映射类
     */
    private String mappingValue;

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    void setCode(String code) {
        this.code = code;
    }

    void setDesc(String desc) {
        this.desc = desc;
    }

    @Override
    public String getMappingValue() {
        return mappingValue;
    }

    void setMappingValue(String mappingValue) {
        this.mappingValue = mappingValue;
    }

    /**
     * 对象类型表名映射
     */
    public static final Map<String, String> OBJECT_TABLE_MAP = Arrays.stream(ObjectTypeEnum.values()).sequential()
            .collect(Collectors.toMap(ObjectTypeEnum::name, ObjectTypeEnum::getCode, (t1, t2) -> t2));

}
