package com.fuyaogroup.bpim.das.agent.service.impl;

import cn.hutool.json.JSONUtil;
import com.fuyaogroup.bpim.das.agent.config.AlgorithmConfig;
import com.fuyaogroup.bpim.das.agent.dto.ComputeParam;
import com.fuyaogroup.bpim.das.agent.service.IAlgorithmService;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;

/**
 * <code>算法求解服务实现</code>
 * <p>
 * AlgorithmServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-15 13:41:06
 */
@Slf4j
@Service
public class AlgorithmServiceImpl implements IAlgorithmService {

    @Resource
    private AlgorithmConfig algorithmProperties;

    /**
     * 校验计算参数数据有效性
     *
     * @param computeParam 计算参数
     * @return {@link AlgorithmConfig.Task}
     */
    private AlgorithmConfig.Task validateParam(ComputeParam computeParam) {
        if (StringUtils.isEmpty(computeParam.getType())) {
            throw new BusinessException("计算类型[type]不允许为空!");
        }
        if (StringUtils.isEmpty(computeParam.getDirectory())) {
            throw new BusinessException("计算目录[dirPath]不允许为空!");
        }
        AlgorithmConfig.Task config = algorithmProperties.getTask(computeParam.getType());
        if (config == null) {
            throw new BusinessException(String.format("计算类型[%s]对应的配置不允许为空!", computeParam.getType()));
        }
        return config;
    }

    /**
     * 异步计算
     *
     * @param computeParam 计算参数
     * @return java.lang.String
     */
    @Override
    public boolean compute(ComputeParam computeParam) {
        log.info("开始调用算法求解器计算,参数:{}", JacksonUtils.toJson(computeParam));
        long start = System.currentTimeMillis();
        AlgorithmConfig.Task config = validateParam(computeParam);
        String dirPath = IOUtils.cleanseDir(false, config.getWorkspace(), computeParam.getExecutionNumber());
        String logFile = StringUtils.isNotBlank(computeParam.getProcessLogPath())
                ? computeParam.getProcessLogPath() : "log";
        log.info("命令:{},目录路径:{},日志路径:{}", config.getCmd(), dirPath, logFile);
        boolean success = callSolver(config.getCmd(), dirPath, logFile);
        if (!success) {
            log.error("调用算法求解器失败:命令:{},工作空间:{},日志路径:{}!", config.getCmd(), dirPath, logFile);
            return false;
        }
        log.info("调用算法求解器成功，总耗时:{}ms!", (System.currentTimeMillis() - start));
        return true;
    }

    /**
     * 同步计算
     *
     * @param computeParam 计算参数
     * @return java.lang.String
     */
    public String syncCompute(ComputeParam computeParam) {
        log.info("开始调用算法求解器计算并获取结果,参数:{}", JSONUtil.toJsonStr(computeParam));
        long start = System.currentTimeMillis();
        AlgorithmConfig.Task config = validateParam(computeParam);
        String dirPath = IOUtils.cleanseDir(false, config.getWorkspace(), computeParam.getExecutionNumber());
        String logFile = StringUtils.isNotBlank(computeParam.getProcessLogPath())
                ? computeParam.getProcessLogPath() : "log";
        log.info("命令:{},目录路径:{},日志路径:{}", config.getCmd(), dirPath, logFile);
        boolean success = callSolver(config.getCmd(), dirPath, logFile);
        if (!success) {
            String errorMsg = String.format("调用算法求解器失败:命令:%s,工作空间:%s,日志路径:%s!", config.getCmd(), dirPath, logFile);
            log.error(errorMsg);
            throw new BusinessException(errorMsg);
        }
        String result = getComputeResult(config, computeParam);
        log.info("调用算法求解器计算并获取结果成功,结果:{},总耗时:{}ms!", result, (System.currentTimeMillis() - start));
        return result;
    }

    /**
     * 开启计算进程
     *
     * @param cmd       命令行
     * @param workspace 工作空间
     * @param logPath   日志路径
     * @return boolean
     */
    private boolean callSolver(String cmd, String workspace, String logPath) {
        log.info("开始调用算法求解器计算,命令:{},工作空间:{}，日志路径:{}", cmd, workspace, logPath);
        long start = System.currentTimeMillis();
        try {
            String fullLogPath = IOUtils.cleanseDir(true, workspace, "stderr.log");
            Process process = new ProcessBuilder(cmd).directory(new File(workspace)).redirectErrorStream(true)
                    .redirectOutput(ProcessBuilder.Redirect.to(new File(fullLogPath))).start();
            try (BufferedReader br = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String str;
                while ((str = br.readLine()) != null) {
                    log.info("计算出现错误: {}", str);
                }
            }
            if (process.waitFor() == 0) {
                log.info("调用算法求解器计算成功,耗时:{}ms!", (System.currentTimeMillis() - start));
                return true;
            } else {
                log.error("Command \"{}\" run failed. Please check log in {}.", cmd, logPath);
                return false;
            }
        } catch (Exception e) {
            log.error("调用算法求解器计算过程出现异常: {}", e.getMessage(), e);
            throw new BusinessException("调用算法求解器计算过程出现异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取计算结果
     *
     * @param computeParam 计算参数
     * @return java.lang.String
     */
    @Override
    public String getComputeResult(ComputeParam computeParam) {
        AlgorithmConfig.Task task = validateParam(computeParam);
        return this.getComputeResult(task, computeParam);
    }

    /**
     * 获取算法求解器计算结果
     *
     * @param task         任务对象
     * @param computeParam 计算参数
     * @return java.lang.String
     */
    private String getComputeResult(AlgorithmConfig.Task task, ComputeParam computeParam) {
        String outputDirPath = IOUtils.cleanseDir(false, task.getWorkspace(), computeParam.getOutputPath());
        File outputDir = new File(outputDirPath);
        if (!outputDir.exists()) {
            log.error("算法运行失败,未生成结果文件");
            throw new BusinessException("算法运行失败,未生成结果文件");
        }
        String resultFile = outputDirPath + File.separator + "result_file.json";
        log.info("读取结果路径：{}", resultFile);
        return IOUtils.readFile2String(resultFile);
    }

}