package com.yhl.scp.mps.dynamicDeliveryTracking.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DynamicDeliveryTrackingSubTaskVO</code>
 * <p>
 * 动态交付跟踪工序任务明细表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-279 9:51:15
 */
@ApiModel(value = "动态交付跟踪工序任务明细表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DynamicDeliveryTrackingSubTaskVXToVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = 248343500825581846L;

    /**
     * 动态交付跟踪工序任务ID
     */
    @ApiModelProperty(value = "动态交付跟踪工序任务ID")
    @FieldInterpretation(value = "动态交付跟踪工序任务ID")
    private String id;
    /**
     * 动态交付跟踪工序任务ID
     */
    @ApiModelProperty(value = "动态交付跟踪工序任务ID")
    private String taskId;
    /**
     * 本厂编码
     */
    @ApiModelProperty(value = "本厂编码")
    @FieldInterpretation(value = "本厂编码")
    private String productCode;
    /**
     * 本厂ID
     */
    @ApiModelProperty(value = "本厂ID")
    @FieldInterpretation(value = "本厂ID")
    private String productId;
    /**
     * 车型编码
     */
    @ApiModelProperty(value = "车型编码")
    @FieldInterpretation(value = "车型编码")
    private String vehicleModelCode;
    /**
     * 工序
     */
    @ApiModelProperty(value = "工序")
    @FieldInterpretation(value = "工序")
    private String sequenceCode;
    /**
     * 已报工数量
     */
    @ApiModelProperty(value = "已报工数量")
    @FieldInterpretation(value = "已报工数量")
    private Integer historyQuantity;
    /**
     * 完工数量
     */
    @ApiModelProperty(value = "完工数量")
    @FieldInterpretation(value = "完工数量")
    private Integer finishedQuantity;
    /**
     * 生产开始时间
     */
    @ApiModelProperty(value = "生产开始时间")
    @FieldInterpretation(value = "生产开始时间")
    private Date startTime;
    /**
     * 生产结束时间
     */
    @ApiModelProperty(value = "生产结束时间")
    @FieldInterpretation(value = "生产结束时间")
    private Date endTime;
    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String userName;
    /**
     * 产线
     */
    @ApiModelProperty(value = "产线")
    private String physicalResourceCode;
    /**
     * 计划员
     */
    @ApiModelProperty(value = "计划员")
    private String productionPlanner;

    private String scenario;

    private String physicalResourceId;

    @Override
    public void clean() {

    }

    public String getPeriodKey() {
        return DateUtils.dateToString(this.startTime, DateUtils.COMMON_TIME_STR1)
                + "-" + DateUtils.dateToString(this.endTime, DateUtils.COMMON_TIME_STR1);
    }

}
