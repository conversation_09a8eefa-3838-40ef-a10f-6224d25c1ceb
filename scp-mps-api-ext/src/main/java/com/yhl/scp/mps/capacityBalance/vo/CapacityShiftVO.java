package com.yhl.scp.mps.capacityBalance.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;

import java.util.Date;

/**
 * <code>CapacityShiftVO</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 15:57:29
 */
@Data
@HeadRowHeight(value = 40)
@ExcelIgnoreUnannotated
public class CapacityShiftVO {

    @ExcelProperty(value = "产线")
    @ColumnWidth(value = 20)
    private String resourceCode;

    @ExcelProperty(value = "可增开班次时间")
    @ColumnWidth(value = 50)
    private String openTimePeriod;

    @ExcelProperty(value = "可增加产能(片)")
    @ColumnWidth(value = 20)
    private String count;

}
