package com.yhl.scp.mps.capacityBalance.service;

import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceVersionDTO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO4;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO2;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <code>CapacityBalanceVersionService</code>
 * <p>
 * 产能平衡版本应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 14:15:27
 */
public interface CapacityBalanceVersionService extends BaseService<CapacityBalanceVersionDTO, CapacityBalanceVersionVO> {

    /**
     * 查询所有
     *
     * @return list {@link CapacityBalanceVersionVO}
     */
    List<CapacityBalanceVersionVO> selectAll();

    /**
     * 版本发布
     */
    void publishVersion();

    /**
     * 版本发布-带分布式锁
     */
    BaseResponse<String> publishVersionLock();

    /**
     * 查询最新版本
     *
     * @return
     */
    CapacityBalanceVersionVO selectLatestVersionCode();

    /**
     * 【产能平衡-负荷】 版本数据对比
     *
     * @param versionIds        版本ids
     * @param plantCode         工厂编码
     * @param operationCode     工序代码
     * @param operationName     工序名称
     * @param resourceGroupCode 资源组编码
     * @param resourceCode      资源编码
     * @return 对比结果
     */
    List<CapacityLoadVO4> contrastCapacityLoad(List<String> versionIds, String plantCode, String operationCode, String operationName, String resourceGroupCode, String resourceCode);

    /**
     * 【产能平衡-产能明细】版本数据对比
     *
     * @param versionIds      版本ids
     * @param operationCode   工序代码
     * @param operationName   工序名称
     * @param supplyTimeStart 供应时间-开始时间
     * @param supplyTimeEnd   供应时间-结束时间
     * @param productCode     本厂编码
     * @return 对比结果
     */
    List<CapacitySupplyRelationshipVO2> contrastCapacitySupplyRelationship(List<String> versionIds, String operationCode, String operationName, String supplyTimeStart, String supplyTimeEnd, String productCode);

    String selectWeekMaxVersionTime();

    /**
     * 版本下拉查询
     *
     * @return
     */
    List<LabelValue<String>> versionDropdown();

    /**
     * 根据版本号查询对应的计算月份，需求预测版本
     *
     * @param versionCode
     * @return
     */
    CapacityBalanceVersionVO selectVersionInfoByVersionCode(String versionCode);

    /**
     * 根据月份找到第一版的产能平衡版本资源当月合计数量
     *
     * @param planPeriod
     * @return
     */
    Map<String, BigDecimal> getFirstVersionResourceQuantityByPlanPeriod(String planPeriod);
}
