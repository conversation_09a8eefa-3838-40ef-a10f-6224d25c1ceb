package com.yhl.scp.mps.dynamicDeliveryTracking.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingSubTaskDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVXToVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;

import java.util.List;

/**
 * <code>DynamicDeliveryTrackingSubTaskService</code>
 * <p>
 * 动态交付跟踪工序任务明细表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:47:15
 */
public interface DynamicDeliveryTrackingSubTaskService extends BaseService<DynamicDeliveryTrackingSubTaskDTO, DynamicDeliveryTrackingSubTaskVO> {

    /**
     * 查询所有
     *
     * @return list {@link DynamicDeliveryTrackingSubTaskVO}
     */
    List<DynamicDeliveryTrackingSubTaskVO> selectAll();

    /**
     * 交付动态跟踪平台-明细查询
     *
     * @param taskId 任务ID
     * @param limitFlag 限制标志位
     * @return java.util.List<com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO>
     */
    List<DynamicDeliveryTrackingSubTaskVO> selectSubTaskByTaskId(String taskId, Boolean limitFlag);

    /**
     * 构建动态交付跟踪工序任务明细数据
     *
     * @param taskList 任务列表
     * @return java.util.List<com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO>
     */
    List<DynamicDeliveryTrackingSubTaskVO> buildSubTaskByTask(List<DynamicDeliveryTrackingTaskVO> taskList);

    List<DynamicDeliveryTrackingSubTaskVO> generateSubTask(DynamicDeliveryTrackingTaskVO taskVO);

    BaseResponse<DynamicDeliveryTrackingSubTaskVXToVO> search(String id);

    BaseResponse<Void> saveVXData(DynamicDeliveryTrackingSubTaskVXToVO vxToVO);

    List<DynamicDeliveryTrackingSubTaskVO> selectSubTaskByTaskIdAndReport(String taskId, Boolean limitFlag);

}
