package com.yhl.scp.mps.operationPublished.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>OperationPublishedVO</code>
 * <p>
 * 工序发布信息表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:31:08
 */
@ApiModel(value = "工序发布信息表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class OperationPublishedVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -33286783288927405L;

    private String newId;


    @ApiModelProperty(value = "源数据id")
    @FieldInterpretation(value = "源数据id")
    private String id;

        /**
     * 工序代码
     */
        @ApiModelProperty(value = "工序代码")
    @FieldInterpretation(value = "工序代码")
    private String operationCode;
        /**
     * 制造订单ID
     */
        @ApiModelProperty(value = "制造订单ID")
    @FieldInterpretation(value = "制造订单ID")
    private String orderId;
        /**
     * 计划批次ID
     */
        @ApiModelProperty(value = "计划批次ID")
    @FieldInterpretation(value = "计划批次ID")
    private String planUnitId;
        /**
     * 路径步骤ID
     */
        @ApiModelProperty(value = "路径步骤ID")
    @FieldInterpretation(value = "路径步骤ID")
    private String routingStepId;
        /**
     * 路径步骤顺序号
     */
        @ApiModelProperty(value = "路径步骤顺序号")
    @FieldInterpretation(value = "路径步骤顺序号")
    private Integer routingStepSequenceNo;
        /**
     * 前工序顺序号
     */
        @ApiModelProperty(value = "前工序顺序号")
    @FieldInterpretation(value = "前工序顺序号")
    private String preRoutingStepSequenceNo;
        /**
     * 后工序顺序号
     */
        @ApiModelProperty(value = "后工序顺序号")
    @FieldInterpretation(value = "后工序顺序号")
    private String nextRoutingStepSequenceNo;
        /**
     * 库存点物品ID
     */
        @ApiModelProperty(value = "库存点物品ID")
    @FieldInterpretation(value = "库存点物品ID")
    private String productStockPointId;
        /**
     * 物品ID
     */
        @ApiModelProperty(value = "物品ID")
    @FieldInterpretation(value = "物品ID")
    private String productId;
        /**
     * 库存点ID
     */
        @ApiModelProperty(value = "库存点ID")
    @FieldInterpretation(value = "库存点ID")
    private String stockPointId;
        /**
     * 数量
     */
        @ApiModelProperty(value = "数量")
    @FieldInterpretation(value = "数量")
    private BigDecimal quantity;
        /**
     * 计划资源ID
     */
        @ApiModelProperty(value = "计划资源ID")
    @FieldInterpretation(value = "计划资源ID")
    private String plannedResourceId;
        /**
     * 是否冻结
     */
        @ApiModelProperty(value = "是否冻结")
    @FieldInterpretation(value = "是否冻结")
    private String frozen;
        /**
     * 计划状态
     */
        @ApiModelProperty(value = "计划状态")
    @FieldInterpretation(value = "计划状态")
    private String planStatus;
        /**
     * 订单类型
     */
        @ApiModelProperty(value = "订单类型")
    @FieldInterpretation(value = "订单类型")
    private String orderType;
        /**
     * 齐套状态
     */
        @ApiModelProperty(value = "齐套状态")
    @FieldInterpretation(value = "齐套状态")
    private String kitStatus;
        /**
     * 处理时间
     */
        @ApiModelProperty(value = "处理时间")
    @FieldInterpretation(value = "处理时间")
    private Integer processingTime;
        /**
     * 开始时间
     */
        @ApiModelProperty(value = "开始时间")
    @FieldInterpretation(value = "开始时间")
    private Date startTime;
        /**
     * 结束时间
     */
        @ApiModelProperty(value = "结束时间")
    @FieldInterpretation(value = "结束时间")
    private Date endTime;
        /**
     * 最早开始时间
     */
        @ApiModelProperty(value = "最早开始时间")
    @FieldInterpretation(value = "最早开始时间")
    private Date earliestStartTime;
        /**
     * 最晚结束时间
     */
        @ApiModelProperty(value = "最晚结束时间")
    @FieldInterpretation(value = "最晚结束时间")
    private Date latestEndTime;
        /**
     * 计算的最早开始时间
     */
        @ApiModelProperty(value = "计算的最早开始时间")
    @FieldInterpretation(value = "计算的最早开始时间")
    private Date calcEarliestStartTime;
        /**
     * 计算的最晚结束时间
     */
        @ApiModelProperty(value = "计算的最晚结束时间")
    @FieldInterpretation(value = "计算的最晚结束时间")
    private Date calcLatestEndTime;
        /**
     * 接续任务
     */
        @ApiModelProperty(value = "接续任务")
    @FieldInterpretation(value = "接续任务")
    private String connectionTask;
        /**
     * 接续方式
     */
        @ApiModelProperty(value = "接续方式")
    @FieldInterpretation(value = "接续方式")
    private String connectionType;
        /**
     * 最大接续时间
     */
        @ApiModelProperty(value = "最大接续时间")
    @FieldInterpretation(value = "最大接续时间")
    private Integer maxConnectionDuration;
        /**
     * 最小接续时间
     */
        @ApiModelProperty(value = "最小接续时间")
    @FieldInterpretation(value = "最小接续时间")
    private Integer minConnectionDuration;
        /**
     * 分割类别
     */
        @ApiModelProperty(value = "分割类别")
    @FieldInterpretation(value = "分割类别")
    private String partitionType;
        /**
     * 资源固定标志
     */
        @ApiModelProperty(value = "资源固定标志")
    @FieldInterpretation(value = "资源固定标志")
    private String resourceFixed;
        /**
     * 时间固定标志
     */
        @ApiModelProperty(value = "时间固定标志")
    @FieldInterpretation(value = "时间固定标志")
    private String timeFixed;
        /**
     * 锁定状态
     */
        @ApiModelProperty(value = "锁定状态")
    @FieldInterpretation(value = "锁定状态")
    private String lockStatus;
        /**
     * 延期原因
     */
        @ApiModelProperty(value = "延期原因")
    @FieldInterpretation(value = "延期原因")
    private String delayReason;
        /**
     * 父工序ID
     */
        @ApiModelProperty(value = "父工序ID")
    @FieldInterpretation(value = "父工序ID")
    private String parentId;
        /**
     * 工序顺序
     */
        @ApiModelProperty(value = "工序顺序")
    @FieldInterpretation(value = "工序顺序")
    private Integer operationIndex;
        /**
     * 未排程原因
     */
        @ApiModelProperty(value = "未排程原因")
    @FieldInterpretation(value = "未排程原因")
    private String unscheduledReason;
        /**
     * 是否上次插单
     */
        @ApiModelProperty(value = "是否上次插单")
    @FieldInterpretation(value = "是否上次插单")
    private String lastInsertion;
        /**
     * 标准工艺ID
     */
        @ApiModelProperty(value = "标准工艺ID")
    @FieldInterpretation(value = "标准工艺ID")
    private String standardStepId;
    /**
     * 工单号
     */
    @ApiModelProperty(value = "工单号")
    @FieldInterpretation(value = "工单号")
    private String orderNo;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料编码")
    @FieldInterpretation(value = "物料编码")
    private String productCode;
    /**
     * 物料编码
     */
    @ApiModelProperty(value = "物料Id")
    @FieldInterpretation(value = "物料Id")
    private String inventoryItemId;
    /**
     * 制造订单交期
     */
    @ApiModelProperty(value = "制造订单交期")
    @FieldInterpretation(value = "制造订单交期")
    private Date dueDate;
    /**
     * 订单类型代码
     */
    @ApiModelProperty(value = "订单类型代码")
    @FieldInterpretation(value = "订单类型代码")
    private String orderTypeCode;
    /**
     * 试制单号
     */
    @ApiModelProperty(value = "试制单号")
    @FieldInterpretation(value = "试制单号")
    private String testOrderNumber;
    /**
     * 试制单号
     */
    @ApiModelProperty(value = "标准工艺类型")
    @FieldInterpretation(value = "标准工艺类型")
    private String standardStepType;

    /**
     * 主生产计划发布日志id
     */
    @ApiModelProperty(value = "主生产计划发布日志id")
    @FieldInterpretation(value = "主生产计划发布日志id")
    private String publishedLogId;

    @Override
    public void clean() {

    }

}
