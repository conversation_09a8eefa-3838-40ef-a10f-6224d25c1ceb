package com.yhl.scp.mps.operationPublished.service;

import java.util.List;
import java.util.Map;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.scp.mps.operationPublished.dto.WorkOrderPublishedDTO;
import com.yhl.scp.mps.operationPublished.vo.CopyPublishedStatusVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;

/**
 * <code>WorkOrderPublishedService</code>
 * <p>
 * 制造订单发布信息表应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:28:17
 */
public interface WorkOrderPublishedService extends BaseService<WorkOrderPublishedDTO, WorkOrderPublishedVO> {

    /**
     * 查询所有
     *
     * @return list {@link WorkOrderPublishedVO}
     */
    List<WorkOrderPublishedVO> selectAll();

    Boolean disposeSnapshotData(String oldPublishedLogId, String bindPublishedLogId, String publishedTime, Boolean copyFlag);
    List<WorkOrderPublishedVO>  selectVOByParams(Map<String, Object> params);

	CopyPublishedStatusVO doSnapshotDataForLineGroup(List<String> productIds, List<String> publishedLogIds, Map<String, String> productLineGroupMap);

}
