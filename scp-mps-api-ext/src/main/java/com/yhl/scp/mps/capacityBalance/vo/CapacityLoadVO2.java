package com.yhl.scp.mps.capacityBalance.vo;

import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <code>CapacityLoadVO2</code>
 * <p>
 * 产能负荷VO-页面展示-每月产能数据详情
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-19 11:09:03
 */
@ApiModel(value = "产能负荷VO2")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CapacityLoadVO2 implements Serializable {


    private static final long serialVersionUID = 7563442396537720344L;

    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求时间")
    @FieldInterpretation(value = "需求时间")
    private Date forecastTime;
    /**
     * 需求时间
     */
    @ApiModelProperty(value = "需求周次")
    @FieldInterpretation(value = "需求周次")
    private String forecastWeekTime;

    /**
     * 需求数量
     */
    @ApiModelProperty(value = "需求数量")
    @FieldInterpretation(value = "需求数量")
    private BigDecimal demandQuantity;



    /**
     * 可用产能
     */
    @ApiModelProperty(value = "可用产能（秒）")
    @FieldInterpretation(value = "可用产能（秒）")
    private BigDecimal availableCapacity;
    /**
     * 占用产能
     */
    @ApiModelProperty(value = "占用产能（秒）")
    @FieldInterpretation(value = "占用产能（秒）")
    private BigDecimal productionCapacity;
    /**
     * 产能负荷率
     */
    @ApiModelProperty(value = "产能负荷率")
    @FieldInterpretation(value = "产能负荷率")
    private BigDecimal capacityUtilization;

    /**
     * 产能负荷率
     */
    @ApiModelProperty(value = "当月平均节拍")
    @FieldInterpretation(value = "当月平均节拍")
    private BigDecimal averageBeat;

    /**
     * 当月总产能（片） = availableCapacity/averageBeat
     */
    @ApiModelProperty(value = "当月总产能（片）")
    @FieldInterpretation(value = "当月总产能（片）")
    private BigDecimal totalCapacity;

    @ApiModelProperty(value = "颜色")
    @FieldInterpretation(value = "颜色")
    private String color;

    @ApiModelProperty(value = "总发货计划量")
    @FieldInterpretation(value = "总发货计划量")
    private Integer totalProductCount;

}
