package com.yhl.scp.mps.operationPublished.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>WorkOrderPublishedDTO</code>
 * <p>
 * 制造订单发布信息表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:28:17
 */
@ApiModel(value = "制造订单发布信息表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class WorkOrderPublishedDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 448322985371166316L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String newId;

    /**
     * 源数据id
     */
    @ApiModelProperty(value = "源数据id")
    private String id;

    /**
     * 制造订单号
     */
    @ApiModelProperty(value = "制造订单号")
    private String orderNo;
    /**
     * 库存点物品ID
     */
    @ApiModelProperty(value = "库存点物品ID")
    private String productStockPointId;
    /**
     * 库存点ID
     */
    @ApiModelProperty(value = "库存点ID")
    private String stockPointId;
    /**
     * 物品ID
     */
    @ApiModelProperty(value = "物品ID")
    private String productId;
    /**
     * 生产组织ID
     */
    @ApiModelProperty(value = "生产组织ID")
    private String organizationId;
    /**
     * 父订单ID
     */
    @ApiModelProperty(value = "父订单ID")
    private String parentId;
    /**
     * BOM版本ID
     */
    @ApiModelProperty(value = "BOM版本ID")
    private String bomVersionId;
    /**
     * 上层订单ID
     */
    @ApiModelProperty(value = "上层订单ID")
    private String upperOrderId;
    /**
     * 顶层订单ID
     */
    @ApiModelProperty(value = "顶层订单ID")
    private String topOrderId;
    /**
     * 下层订单ID
     */
    @ApiModelProperty(value = "下层订单ID")
    private String lowerOrderId;
    /**
     * 底层订单ID
     */
    @ApiModelProperty(value = "底层订单ID")
    private String bottomOrderId;
    /**
     * 是否拆批
     */
    @ApiModelProperty(value = "是否拆批")
    private String batchSplit;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 下单时间
     */
    @ApiModelProperty(value = "下单时间")
    private Date orderTime;
    /**
     * 交期
     */
    @ApiModelProperty(value = "交期")
    private Date dueDate;
    /**
     * 延期惩罚
     */
    @ApiModelProperty(value = "延期惩罚")
    private BigDecimal delayPenalty;
    /**
     * 订单状态
     */
    @ApiModelProperty(value = "订单状态")
    private String orderStatus;
    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priority;
    /**
     * 是否延期
     */
    @ApiModelProperty(value = "是否延期")
    private String delayStatus;
    /**
     * 是否固定
     */
    @ApiModelProperty(value = "是否固定")
    private String fixed;
    /**
     * 是否反馈
     */
    @ApiModelProperty(value = "是否反馈")
    private String feedbackStatus;
    /**
     * 是否超产
     */
    @ApiModelProperty(value = "是否超产")
    private String overProduction;
    /**
     * 是否允许重新同步
     */
    @ApiModelProperty(value = "是否允许重新同步")
    private String resyncAllowed;
    /**
     * 计划状态
     */
    @ApiModelProperty(value = "计划状态")
    private String planStatus;
    /**
     * 齐套状态
     */
    @ApiModelProperty(value = "齐套状态")
    private String kitStatus;
    /**
     * 同步状态
     */
    @ApiModelProperty(value = "同步状态")
    private String syncStatus;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
    /**
     * 最早开始时间
     */
    @ApiModelProperty(value = "最早开始时间")
    private Date earliestStartTime;
    /**
     * 计算的最早开始时间
     */
    @ApiModelProperty(value = "计算的最早开始时间")
    private Date calcEarliestStartTime;
    /**
     * 计算的最晚结束时间
     */
    @ApiModelProperty(value = "计算的最晚结束时间")
    private Date calcLatestEndTime;
    /**
     * 是否可排
     */
    @ApiModelProperty(value = "是否可排")
    private String plannable;
    /**
     * 是否指定可排
     */
    @ApiModelProperty(value = "是否指定可排")
    private String appointPlannable;
    /**
     * 路径ID
     */
    @ApiModelProperty(value = "路径ID")
    private String routingId;
    /**
     * 指定路径ID
     */
    @ApiModelProperty(value = "指定路径ID")
    private String appointRoutingId;
    /**
     * 路径类型
     */
    @ApiModelProperty(value = "路径类型")
    private String routingType;
    /**
     * 提前期
     */
    @ApiModelProperty(value = "提前期")
    private Integer leadTime;
    /**
     * 指定提前期
     */
    @ApiModelProperty(value = "指定提前期")
    private Integer appointLeadTime;
    /**
     * 指定客户ID
     */
    @ApiModelProperty(value = "指定客户ID")
    private String appointCustomerId;
    /**
     * 指定需求类型
     */
    @ApiModelProperty(value = "指定需求类型")
    private String appointDemandType;
    /**
     * 指定需求订单ID
     */
    @ApiModelProperty(value = "指定需求订单ID")
    private String appointDemandOrderId;
    /**
     * 指定客户订单
     */
    @ApiModelProperty(value = "指定客户订单")
    private String appointCustomerOrderId;
    /**
     * 指定父制造订单
     */
    @ApiModelProperty(value = "指定父制造订单")
    private String appointParentWorkOrderId;
    /**
     * 同步失败原因
     */
    @ApiModelProperty(value = "同步失败原因")
    private String syncFailureReason;
    /**
     * 入库数量
     */
    @ApiModelProperty(value = "入库数量")
    private BigDecimal stockingQuantity;
    /**
     * 计划投产日期
     */
    @ApiModelProperty(value = "计划投产日期")
    private Date plannedProductionDate;
    /**
     * 计划产出日期
     */
    @ApiModelProperty(value = "计划产出日期")
    private Date plannedOutputDate;
    /**
     * 已分配量
     */
    @ApiModelProperty(value = "已分配量")
    private BigDecimal fulfilledQuantity;
    /**
     * 分配信息
     */
    @ApiModelProperty(value = "分配信息")
    private String fulfillmentInfo;
    /**
     * 可分配量
     */
    @ApiModelProperty(value = "可分配量")
    private BigDecimal unfulfilledQuantity;
    /**
     * 分配状态
     */
    @ApiModelProperty(value = "分配状态")
    private String fulfillmentStatus;
    /**
     * 计数单位ID
     */
    @ApiModelProperty(value = "计数单位ID")
    private String countingUnitId;
    /**
     * 货币单位ID
     */
    @ApiModelProperty(value = "货币单位ID")
    private String currencyUnitId;
    /**
     * 是否参与排程
     */
    @ApiModelProperty(value = "是否参与排程")
    private String participateScheduling;
    /**
     * 固定数量状态
     */
    @ApiModelProperty(value = "固定数量状态")
    private String fixedQuantityStatus;
    /**
     * 与子订单数量是否一致
     */
    @ApiModelProperty(value = "与子订单数量是否一致")
    private String quantityConsistent;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 是否工序冲突
     */
    @ApiModelProperty(value = "是否工序冲突")
    private String operationConflict;
    @ApiModelProperty(value = "${column.comment}")
    private String bomType;
    @ApiModelProperty(value = "${column.comment}")
    private String plannedProductionPlaceId;
    @ApiModelProperty(value = "${column.comment}")
    private Date plannedTime;
    @ApiModelProperty(value = "${column.comment}")
    private String dispatchPlaceId;
    /**
     * 期末库存最小安全库存差
     */
    @ApiModelProperty(value = "期末库存最小安全库存差")
    private Integer endingInventoryMinSafeDiff;
    /**
     * 需求类型
     */
    @ApiModelProperty(value = "需求类型")
    private String demandCategory;

    /**
     * 主生产计划发布日志id
     */
    @ApiModelProperty(value = "主生产计划发布日志id")
    private String publishedLogId;
    /**
     * 订单类型
     */
    @ApiModelProperty("订单类型")
    private String orderType;
    /**
     * 试制单号
     */
    @ApiModelProperty("试制单号")
    private String testOrderNumber;
}
