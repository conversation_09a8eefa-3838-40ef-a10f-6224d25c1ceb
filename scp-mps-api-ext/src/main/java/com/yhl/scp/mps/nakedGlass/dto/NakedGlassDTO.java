package com.yhl.scp.mps.nakedGlass.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>NakedGlassDTO</code>
 * <p>
 * 国内裸玻映射DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-28 18:42:50
 */
@ApiModel(value = "国内裸玻映射DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NakedGlassDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 243664638923727633L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 裸玻编码ID
     */
    @ApiModelProperty(value = "裸玻编码ID")
    private String rawItemId;
    /**
     * 裸玻编码
     */
    @ApiModelProperty(value = "裸玻编码")
    private String rawMaterial;
    /**
     * 供应商ID，组织ID
     */
    @ApiModelProperty(value = "供应商ID，组织ID")
    private String supplierOu;
    /**
     * 供应商组织
     */
    @ApiModelProperty(value = "供应商组织")
    private String supplierCode;
    /**
     * 中转库名称
     */
    @ApiModelProperty(value = "中转库名称")
    @JSONField(name = "zzk")
    private String transitWarehouse;
    /**
     * 中转库半品编码
     */
    @ApiModelProperty(value = "中转库半品编码")
    @JSONField(name = "zzkSa")
    private String semiFinishedProduct;
    /**
     * 中转库成品编码
     */
    @ApiModelProperty(value = "中转库成品编码")
    @JSONField(name = "zzkFg")
    private String finishedProduct;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    private String workType;
    /**
     * 销售组织id
     */
    @ApiModelProperty(value = "销售组织id")
    private String salId;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private Integer versionValue;
}
