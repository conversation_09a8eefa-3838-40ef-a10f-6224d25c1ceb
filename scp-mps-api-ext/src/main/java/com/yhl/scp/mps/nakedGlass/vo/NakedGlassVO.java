package com.yhl.scp.mps.nakedGlass.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>NakedGlassVO</code>
 * <p>
 * 国内裸玻映射VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-28 18:42:53
 */
@ApiModel(value = "国内裸玻映射VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NakedGlassVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -25614686157597677L;

    /**
     * 裸玻编码ID
     */
    @ApiModelProperty(value = "裸玻编码ID")
    @FieldInterpretation(value = "裸玻编码ID")
    private String rawItemId;
    /**
     * 裸玻编码
     */
    @ApiModelProperty(value = "裸玻编码")
    @FieldInterpretation(value = "裸玻编码")
    private String rawMaterial;
    /**
     * 供应商ID，组织ID
     */
    @ApiModelProperty(value = "供应商ID，组织ID")
    @FieldInterpretation(value = "供应商ID，组织ID")
    private String supplierOu;
    /**
     * 供应商组织
     */
    @ApiModelProperty(value = "供应商组织")
    @FieldInterpretation(value = "供应商组织")
    private String supplierCode;
    /**
     * 中转库名称
     */
    @ApiModelProperty(value = "中转库名称")
    @FieldInterpretation(value = "中转库名称")
    private String transitWarehouse;
    /**
     * 中转库半品编码
     */
    @ApiModelProperty(value = "中转库半品编码")
    @FieldInterpretation(value = "中转库半品编码")
    private String semiFinishedProduct;
    /**
     * 中转库成品编码
     */
    @ApiModelProperty(value = "中转库成品编码")
    @FieldInterpretation(value = "中转库成品编码")
    private String finishedProduct;
    /**
     * 加工类型
     */
    @ApiModelProperty(value = "加工类型")
    @FieldInterpretation(value = "加工类型")
    private String workType;    /**
     * 销售组织id
     */
    @ApiModelProperty(value = "销售组织id")
    @FieldInterpretation(value = "销售组织id")
    private String salId;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    @FieldInterpretation(value = "版本")
    private Integer versionValue;

    @Override
    public void clean() {

    }

}
