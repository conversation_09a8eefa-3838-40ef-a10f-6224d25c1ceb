package com.yhl.scp.mps.dynamicDeliveryTracking.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DynamicDeliveryTrackingTaskDTO</code>
 * <p>
 * 动态交付跟踪工序任务表DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:46:11
 */
@ApiModel(value = "动态交付跟踪工序任务表DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class DynamicDeliveryTrackingTaskDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 494912018140245239L;

    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    private String id;
    /**
     * 动态交付跟踪ID
     */
    @ApiModelProperty(value = "动态交付跟踪ID")
    private String dynamicDeliveryTrackingId;
    /**
     * 标准工艺ID
     */
    @ApiModelProperty(value = "标准工艺ID")
    private String standardStepId;
    /**
     * 物理资源ID
     */
    @ApiModelProperty(value = "物理资源ID")
    private String physicalResourceId;
    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    private BigDecimal quantity;
    /**
     * 生产开始时间
     */
    @ApiModelProperty(value = "生产开始时间")
    private Date startTime;
    /**
     * 生产结束时间
     */
    @ApiModelProperty(value = "生产结束时间")
    private Date endTime;
    /**
     * 流水线时间
     */
    private BigDecimal pipelineTime;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

}
