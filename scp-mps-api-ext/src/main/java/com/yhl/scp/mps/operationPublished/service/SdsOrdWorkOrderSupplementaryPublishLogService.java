package com.yhl.scp.mps.operationPublished.service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.operationPublished.dto.SdsOrdWorkOrderSupplementaryPublishLogDTO;
import com.yhl.scp.mps.operationPublished.vo.SdsOrdWorkOrderSupplementaryPublishLogVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.dto.MasterPlanWorkOrderBodyDTO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderVO;

import java.util.List;
import java.util.Map;

/**
 * <code>SdsOrdWorkOrderSupplementaryPublishLogService</code>
 * <p>
 * 应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-25 09:19:15
 */
public interface SdsOrdWorkOrderSupplementaryPublishLogService extends BaseService<SdsOrdWorkOrderSupplementaryPublishLogDTO, SdsOrdWorkOrderSupplementaryPublishLogVO> {

    /**
     * 查询所有
     *
     * @return list {@link SdsOrdWorkOrderSupplementaryPublishLogVO}
     */
    List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectAll();
    List<SdsOrdWorkOrderSupplementaryPublishLogVO>  selectVOByParams(Map<String, Object> params);

    BaseResponse<Void> publish(List<String> ids);
    BaseResponse<Void> generate();
    PageInfo<SdsOrdWorkOrderSupplementaryPublishLogVO> masterPlanWorkOrder(Pagination pagination, MasterPlanReq masterPlanReq, boolean status, List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOS,List<SdsOrdWorkOrderSupplementaryPublishLogVO> sdsOrdWorkOrderSupplementaryPublishLogVOS);
}
