package com.yhl.scp.mps.feedback.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFeedBack;
import com.yhl.scp.mps.feedback.dto.MesFeedbackDTO;
import com.yhl.scp.mps.feedback.vo.MesFeedbackVO;

import java.util.List;

/**
 * <code>MesFeedbackService</code>
 * <p>
 * MesFeedbackService
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17 14:46:33
 */
public interface MesFeedbackService extends BaseService<MesFeedbackDTO, MesFeedbackVO> {

    /**
     * 查询所有
     *
     * @return list {@link MesFeedbackVO}
     */
    List<MesFeedbackVO> selectAll();

    void doHandleMesFeedBack(List<MesFeedBack> mesFeedBacks);

    void doConvert(List<MesFeedbackDTO> mesFeedbackDTOS);

    BaseResponse<Void> syncData(String dataBaseName, String tenantId);

}
