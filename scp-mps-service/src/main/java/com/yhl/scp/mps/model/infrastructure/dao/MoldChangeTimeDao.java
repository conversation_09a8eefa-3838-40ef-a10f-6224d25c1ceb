package com.yhl.scp.mps.model.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.mps.model.infrastructure.po.MoldChangeTimePO;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;

import java.util.List;
import java.util.Map;


/**
 * <code>MoldChangeTimeDao</code>
 * <p>
 * 换模换型时间DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:20:41
 */
public interface MoldChangeTimeDao extends BaseDao<MoldChangeTimePO, MoldChangeTimeVO> {

    List<MoldChangeTimePO> selectByKidOrOperationNameOrResource(Map<String, Object> params);

    List<MoldChangeTimePO> selectTime();

}
