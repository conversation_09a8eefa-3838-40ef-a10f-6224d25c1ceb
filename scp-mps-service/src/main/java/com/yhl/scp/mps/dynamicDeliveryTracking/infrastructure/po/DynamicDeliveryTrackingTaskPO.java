package com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po;

import com.yhl.platform.common.ddd.BasePO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DynamicDeliveryTrackingTaskPO</code>
 * <p>
 * 动态交付跟踪工序任务表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:46:30
 */
public class DynamicDeliveryTrackingTaskPO extends BasePO implements Serializable {

    private static final long serialVersionUID = -11001656861555006L;

    /**
     * 动态交付跟踪ID
     */
    private String dynamicDeliveryTrackingId;
    /**
     * 标准工艺ID
     */
    private String standardStepId;
    /**
     * 物理资源ID
     */
    private String physicalResourceId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 生产开始时间
     */
    private Date startTime;
    /**
     * 生产结束时间
     */
    private Date endTime;

    /**
     * 流水线时间
     */
    private BigDecimal pipelineTime;

    public BigDecimal getPipelineTime() {
        return pipelineTime;
    }

    public void setPipelineTime(BigDecimal pipelineTime) {
        this.pipelineTime = pipelineTime;
    }

    public String getDynamicDeliveryTrackingId() {
        return dynamicDeliveryTrackingId;
    }

    public void setDynamicDeliveryTrackingId(String dynamicDeliveryTrackingId) {
        this.dynamicDeliveryTrackingId = dynamicDeliveryTrackingId;
    }

    public String getStandardStepId() {
        return standardStepId;
    }

    public void setStandardStepId(String standardStepId) {
        this.standardStepId = standardStepId;
    }

    public String getPhysicalResourceId() {
        return physicalResourceId;
    }

    public void setPhysicalResourceId(String physicalResourceId) {
        this.physicalResourceId = physicalResourceId;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

}
