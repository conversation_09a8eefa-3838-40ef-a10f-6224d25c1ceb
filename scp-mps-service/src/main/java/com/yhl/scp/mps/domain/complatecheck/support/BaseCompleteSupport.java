package com.yhl.scp.mps.domain.complatecheck.support;


import cn.hutool.core.util.StrUtil;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mps.domain.complatecheck.model.context.CompleteDataContext;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mrp.MrpFeign.MrpFeign;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.FulfillmentVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import com.yhl.scp.sds.pegging.service.DemandService;
import com.yhl.scp.sds.pegging.service.FulfillmentService;
import com.yhl.scp.sds.pegging.service.SupplyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 通用的策略数据服务支撑
 */
@Slf4j
public class BaseCompleteSupport {

    protected static final String COMPLETE_CHECK_KEY = "COMPLETE_CHECK_KEY";

    @Resource
    protected RedisUtil redisUtil;
    @Resource
    protected WorkOrderService workOrderService;
    @Resource
    protected OperationService operationService;
    @Resource
    protected DemandService demandService;
    @Resource
    protected SupplyService supplyService;
    @Resource
    protected FulfillmentService fulfillmentService;
    @Resource
    protected DemandDao demandDao;
    @Resource
    protected SupplyDao supplyDao;
    @Resource
    protected FulfillmentDao fulfillmentDao;
    @Resource
    protected WorkOrderDao workOrderDao;
    @Resource
    protected MasterPlanExtDao masterPlanExtDao;
    @Resource
    protected NewMdsFeign mdsFeign;
    @Resource
    private MrpFeign mrpFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    protected IAmsSchedule amsSchedule;


    protected CompleteDataContext initContext() {
        cacheLog("开始查询流程支撑数据");
        // 制造订单
        List<WorkOrderVO> workOrderVOS = workOrderService.selectAll();
        Map<String, WorkOrderVO> workOrderMap = workOrderVOS.stream()
                .collect(Collectors.toMap(WorkOrderVO::getId, Function.identity()));
        // 全工序
        List<OperationVO> operationVOS = operationService.selectAll();
        Map<String, List<OperationVO>> operationMap = operationVOS.stream()
                .collect(Collectors.groupingBy(OperationVO::getOrderId));
        // 需求
        List<DemandVO> demandVOS = demandService.selectAll();
        // 供应
        List<SupplyVO> supplyVOS = supplyService.selectAll()
                .stream().filter(p -> StrUtil.isNotEmpty(p.getProductId())).collect(Collectors.toList());
        check(demandVOS, supplyVOS);
        // 物料
        List<String> demandProductIds = demandVOS.stream().map(DemandVO::getProductId).distinct().collect(Collectors.toList());
        List<String> supplyProductIds = supplyVOS.stream().map(SupplyVO::getProductId).distinct().collect(Collectors.toList());
        List<String> productIds = new ArrayList<>();
        productIds.addAll(demandProductIds);
        productIds.addAll(supplyProductIds);
        List<NewProductStockPointVO> productStockPointVOS = mdsFeign
                .selectProductStockPointByIds(null, productIds.stream().distinct().collect(Collectors.toList()));
        Map<String, NewProductStockPointVO> productStockPointVOMap = productStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getId, Function.identity()));
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "PURCHASE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        // 多个物品编码筛选唯一库存点=SJG
        Map<String, NewProductStockPointVO> productStockPointVOCodeMap = productStockPointVOS.stream()
                .filter(p -> p.getStockPointCode().equals(rangeData))
                .collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity()));
        // 供需关系
        List<FulfillmentVO> fulfillmentVOS = fulfillmentService.selectByParams(new HashMap<>());
        CompleteDataContext completeDataContext = CompleteDataContext.builder()
                .productStockPointVOS(productStockPointVOS)
                .productStockPointMapOnProductId(productStockPointVOMap)
                .productStockPointMapOnProductCode(productStockPointVOCodeMap)
                .workOrderVOS(workOrderVOS)
                .workOrderMap(workOrderMap)
                .operationVOS(operationVOS)
                .operationMapOnOrderId(operationMap)
                .demandVOS(demandVOS)
                .supplyVOS(supplyVOS)
                .fulfillmentAllList(fulfillmentVOS)
                .build();
        cacheLog("结束查询流程支撑数据");
        return completeDataContext;
    }

    private void check(List<DemandVO> demandVOS, List<SupplyVO> supplyVOS) {
        log.info("齐套检查查询需求数量：{}，供应数量：{}", demandVOS.size(), supplyVOS.size());
        if (CollectionUtils.isEmpty(demandVOS)) {
            throw new BusinessException("无需求可分配，齐套检查终止");
        }
        if (CollectionUtils.isEmpty(supplyVOS)) {
            throw new BusinessException("无供应可分配，齐套检查终止");
        }
    }

    private String getMdsScenario() {
        BaseResponse<String> mdsScenario = ipsNewFeign.getDefaultScenario(SystemModuleEnum.MRP.getCode(),
                TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(mdsScenario.getSuccess(), mdsScenario.getMsg());
        return mdsScenario.getData();
    }


    /**
     * 写入redis-hash表中，记录执行日志
     */
    protected void cacheLog(String msg) {
        log.info(msg);
        redisUtil.hset(COMPLETE_CHECK_KEY, msg, DateUtils.dateToString(new Date(), DateUtils.COMMON_DATE_STR1));
    }

}
