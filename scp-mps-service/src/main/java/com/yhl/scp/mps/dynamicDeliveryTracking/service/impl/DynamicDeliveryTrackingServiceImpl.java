package com.yhl.scp.mps.dynamicDeliveryTracking.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingSubTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingTaskConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.entity.DynamicDeliveryTrackingDO;
import com.yhl.scp.mps.dynamicDeliveryTracking.domain.service.DynamicDeliveryTrackingDomainService;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingVXDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingSubTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingTaskDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingSubTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingService;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingTaskService;
import com.yhl.scp.mps.dynamicDeliveryTracking.support.DynamicDeliveryTrackingExcelSupport;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingSubTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.TrackingStatusEnum;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryTrackingServiceImpl</code>
 * <p>
 * 动态交付跟踪表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:45:10
 */
@Slf4j
@Service
public class DynamicDeliveryTrackingServiceImpl extends AbstractService implements DynamicDeliveryTrackingService {

    @Resource
    private DynamicDeliveryTrackingDao dynamicDeliveryTrackingDao;

    @Resource
    private DynamicDeliveryTrackingDomainService dynamicDeliveryTrackingDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DynamicDeliveryTrackingTaskService dynamicDeliveryTrackingTaskService;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @Resource
    private DynamicDeliveryTrackingTaskDao dynamicDeliveryTrackingTaskDao;

    @Resource
    private DynamicDeliveryTrackingSubTaskDao dynamicDeliveryTrackingSubTaskDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;
    @Autowired
    private MdsFeign mdsFeign;
    @Resource
    private DynamicDeliveryTrackingExcelSupport dynamicDeliveryTrackingExcelSupport;
    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(DynamicDeliveryTrackingDTO dynamicDeliveryTrackingDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingDO dynamicDeliveryTrackingDO = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingDTO);
        DynamicDeliveryTrackingPO dynamicDeliveryTrackingPO = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dynamicDeliveryTrackingDomainService.validation(dynamicDeliveryTrackingDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(dynamicDeliveryTrackingPO);
        dynamicDeliveryTrackingDao.insert(dynamicDeliveryTrackingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(DynamicDeliveryTrackingDTO dynamicDeliveryTrackingDTO) {
        // 0.数据转换
        DynamicDeliveryTrackingDO dynamicDeliveryTrackingDO = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Do(dynamicDeliveryTrackingDTO);
        DynamicDeliveryTrackingPO dynamicDeliveryTrackingPO = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Po(dynamicDeliveryTrackingDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        dynamicDeliveryTrackingDomainService.validation(dynamicDeliveryTrackingDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(dynamicDeliveryTrackingPO);
        dynamicDeliveryTrackingDao.update(dynamicDeliveryTrackingPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DynamicDeliveryTrackingDTO> list) {
        List<DynamicDeliveryTrackingPO> newList = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        dynamicDeliveryTrackingDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<DynamicDeliveryTrackingDTO> list) {
        List<DynamicDeliveryTrackingPO> newList = DynamicDeliveryTrackingConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        dynamicDeliveryTrackingDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return dynamicDeliveryTrackingDao.deleteBatch(idList);
        }
        return dynamicDeliveryTrackingDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DynamicDeliveryTrackingVO selectByPrimaryKey(String id) {
        DynamicDeliveryTrackingPO po = dynamicDeliveryTrackingDao.selectByPrimaryKey(id);
        return DynamicDeliveryTrackingConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING")
    public List<DynamicDeliveryTrackingVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "DYNAMIC_DELIVERY_TRACKING")
    public List<DynamicDeliveryTrackingVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DynamicDeliveryTrackingVO> dataList = dynamicDeliveryTrackingDao.selectByCondition(sortParam, queryCriteriaParam);
        DynamicDeliveryTrackingServiceImpl target = springBeanUtils.getBean(DynamicDeliveryTrackingServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DynamicDeliveryTrackingVO> selectByParams(Map<String, Object> params) {
        List<DynamicDeliveryTrackingPO> list = dynamicDeliveryTrackingDao.selectByParams(params);
        return DynamicDeliveryTrackingConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DynamicDeliveryTrackingVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DYNAMIC_DELIVERY_TRACKING.getCode();
    }

    @Override
    public List<DynamicDeliveryTrackingVO> invocation(List<DynamicDeliveryTrackingVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }


    @Override
    public List<DynamicDeliveryTrackingVO> buildTrackingByProductCodeList(List<String> productCodeList) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Collections.emptyList();
        }
        List<DynamicDeliveryTrackingVO> result = new ArrayList<>();
        List<DynamicDeliveryTrackingPO> dynamicDeliveryTrackingPOS = dynamicDeliveryTrackingDao.selectByParams(ImmutableMap.of(
                "productCodeList", productCodeList, "trackingStatus", TrackingStatusEnum.PUBLISHED.getCode()));
        List<DynamicDeliveryTrackingVO> dynamicDeliveryTrackingVOS = DynamicDeliveryTrackingConvertor.INSTANCE.po2Vos(dynamicDeliveryTrackingPOS);
        Map<String, List<DynamicDeliveryTrackingVO>> trackMap = dynamicDeliveryTrackingVOS.stream().collect(Collectors.groupingBy(DynamicDeliveryTrackingVO::getProductCode));
        List<String> trackingIds = dynamicDeliveryTrackingPOS.stream().map(DynamicDeliveryTrackingPO::getId).collect(Collectors.toList());
        List<DynamicDeliveryTrackingTaskVO> dynamicDeliveryTrackingTaskVOS = dynamicDeliveryTrackingTaskService.selectVOByParams(ImmutableMap.of("trackingIdList", trackingIds));
        // 查询已有的追踪数据
        Map<String, List<DynamicDeliveryTrackingTaskVO>> taskMap = dynamicDeliveryTrackingTaskVOS.stream().collect(Collectors.groupingBy(DynamicDeliveryTrackingTaskVO::getDynamicDeliveryTrackingId));
        for (String productCode : productCodeList) {
            List<DynamicDeliveryTrackingVO> trackingVOS = trackMap.get(productCode);
            if (CollectionUtils.isNotEmpty(trackingVOS)) {
                result.addAll(trackingVOS);
            } else {
                DynamicDeliveryTrackingVO trackingVO = new DynamicDeliveryTrackingVO();
                trackingVO.setProductCode(productCode);
                result.add(trackingVO);
            }
        }

        List<DynamicDeliveryTrackingTaskVO> buildTaskVOList = dynamicDeliveryTrackingTaskService.buildTaskByProductCodeList(productCodeList);
        Map<String, List<DynamicDeliveryTrackingTaskVO>> bulidTaskMap = buildTaskVOList.stream().collect(Collectors.groupingBy(DynamicDeliveryTrackingTaskVO::getProductCode));
        for (DynamicDeliveryTrackingVO trackingVO : result) {
            String productCode = trackingVO.getProductCode();
            String trackingId = trackingVO.getId();
            List<DynamicDeliveryTrackingTaskVO> productOfbuildTaskVOList = bulidTaskMap.get(productCode);
            if (CollectionUtils.isNotEmpty(productOfbuildTaskVOList)) {
                Map<String, Integer> orderMap = new HashMap<>();
                // 用于排序，确保返回的数据始终按工艺路径的顺序返回
                for (int i = 0; i < productOfbuildTaskVOList.size(); i++) {
                    orderMap.put(productOfbuildTaskVOList.get(i).getStandardStepId(), i);
                }
                String productId = productOfbuildTaskVOList.get(0).getProductId();
                trackingVO.setProductId(productId);
                // 原来存在追踪数据
                if (StringUtils.isNotEmpty(trackingId) && taskMap.containsKey(trackingId)) {
                    List<DynamicDeliveryTrackingTaskVO> taskVOList = new ArrayList<>();
                    List<DynamicDeliveryTrackingTaskVO> taskVOS = taskMap.get(trackingId);
                    Map<String, DynamicDeliveryTrackingTaskVO> taskVOMap = taskVOS.stream().collect(Collectors.toMap(t ->
                            String.join("-", t.getProductId(), t.getStandardStepId()), Function.identity()));
                    // 下挂的完整工艺路径
                    for (DynamicDeliveryTrackingTaskVO buildTaskVO : productOfbuildTaskVOList) {
                        String key = String.join("-", buildTaskVO.getProductId(), buildTaskVO.getStandardStepId());
                        if (taskVOMap.containsKey(key)) {
                            DynamicDeliveryTrackingTaskVO taskVO = taskVOMap.get(key);
                            taskVO.setProductId(buildTaskVO.getProductId());
                            taskVO.setProductCode(buildTaskVO.getProductCode());
                            taskVO.setStandardStepCode(buildTaskVO.getStandardStepCode());
                            taskVO.setStandardStepName(buildTaskVO.getStandardStepName());
                            taskVO.setRoutingStepResourceDOList(buildTaskVO.getRoutingStepResourceDOList());
                            taskVOList.add(taskVO);
                        } else {
                            taskVOList.add(buildTaskVO);
                        }
                    }
                    taskVOList.sort(Comparator.comparingInt(taskVO ->
                            Optional.ofNullable(orderMap.get(taskVO.getStandardStepId())).orElse(Integer.MAX_VALUE)
                    ));
                    trackingVO.setTaskVOS(taskVOList);
                } else {
                    productOfbuildTaskVOList.sort(Comparator.comparingInt(taskVO ->
                            Optional.ofNullable(orderMap.get(taskVO.getStandardStepId())).orElse(Integer.MAX_VALUE)
                    ));
                    trackingVO.setTaskVOS(productOfbuildTaskVOList);
                }
            }
        }
        result.sort(Comparator.comparing(DynamicDeliveryTrackingVO::getDeliveryTime, Comparator.nullsLast(Comparator.naturalOrder())));
        return result;
    }


    @Override
    public List<LabelValue<String>> org() {
        List<NewStockPointVO> newStockPointVOS = operationTaskExtDao.selectBCStockPoint();
        return newStockPointVOS.stream()
                .map(item -> new LabelValue<>(item.getStockPointCode(), item.getId()))
                .sorted(Comparator.comparing(LabelValue::getLabel)).collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> operationStep() {
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(SystemHolder.getScenario());
        return standardStepVOS.stream()
                .sorted(Comparator.comparing(StandardStepVO::getStockPointCode)
                        .thenComparing(StandardStepVO::getStandardStepCode))
                .map(standardStepVO -> {
                    String key = String.join("-", standardStepVO.getStockPointCode(),
                            standardStepVO.getStandardStepCode(), standardStepVO.getStandardStepName());
                    return new LabelValue<>(key, standardStepVO.getId());
                })
                .collect(Collectors.toList());
    }

    @Override
    public void doPublish(List<DynamicDeliveryTrackingVO> dynamicDeliveryTrackingVOList) {
        if (CollectionUtils.isEmpty(dynamicDeliveryTrackingVOList)) {
            throw new BusinessException("发布数据不能为空");
        }
        // 进仓工序
        String warehousedOperation = dynamicDeliveryTrackingExcelSupport.getWarehousedOperation();
        // 进仓工序资源
        String warehousedOperationResource = dynamicDeliveryTrackingExcelSupport.getWarehousedOperationResource();
        // 发货工序
        String deliveryOperation = dynamicDeliveryTrackingExcelSupport.getDeliveryOperation();
        // 发货产线
        String deliveryResource = dynamicDeliveryTrackingExcelSupport.getDeliveryResource();
        List<String> trackingIds = StreamUtils.columnToList(dynamicDeliveryTrackingVOList, DynamicDeliveryTrackingVO::getId);
        List<DynamicDeliveryTrackingTaskVO> dynamicDeliveryTrackingTaskVOS = dynamicDeliveryTrackingTaskDao.selectVOByParams(ImmutableMap.of("trackingIdList", trackingIds));
        List<String> taskIds = StreamUtils.columnToList(dynamicDeliveryTrackingTaskVOS, DynamicDeliveryTrackingTaskVO::getId);
        Map<String, List<DynamicDeliveryTrackingTaskVO>> trackingDeliveryTaskMap = StreamUtils.mapListByColumn(dynamicDeliveryTrackingTaskVOS,
                DynamicDeliveryTrackingTaskVO::getDynamicDeliveryTrackingId);
        List<DynamicDeliveryTrackingSubTaskPO> subTaskPOList = dynamicDeliveryTrackingSubTaskDao.selectByParams(ImmutableMap.of("taskIdList", taskIds));
        Map<String, List<DynamicDeliveryTrackingSubTaskPO>> subTaskMap = StreamUtils.mapListByColumn(subTaskPOList, DynamicDeliveryTrackingSubTaskPO::getTaskId);
        List<DynamicDeliveryTrackingPO> insertTrackingList = new ArrayList<>();
        List<DynamicDeliveryTrackingPO> updateTrackingList = new ArrayList<>();
        List<DynamicDeliveryTrackingTaskPO> insertTrackingTaskList = new ArrayList<>();
        List<DynamicDeliveryTrackingTaskPO> updateTrackingTaskList = new ArrayList<>();
        List<DynamicDeliveryTrackingSubTaskPO> insertTrackingSubTaskList = new ArrayList<>();
        List<DynamicDeliveryTrackingSubTaskPO> updateTrackingSubTaskList = new ArrayList<>();
        List<String> deleteTaskIdList = new ArrayList<>();
        for (DynamicDeliveryTrackingVO trackingVO : dynamicDeliveryTrackingVOList) {
            // 参数校验
            paramsCheck(trackingVO);
            DynamicDeliveryTrackingPO trackingPO = DynamicDeliveryTrackingConvertor.INSTANCE.vo2Po(trackingVO);
            trackingPO.setTrackingStatus(TrackingStatusEnum.PUBLISHED.getCode());
            String trackingId = trackingPO.getId();
            if (StringUtils.isEmpty(trackingId)) {
                trackingId = UUIDUtil.getUUID();
                trackingPO.setId(trackingId);
                trackingVO.setId(trackingId);
                insertTrackingList.add(trackingPO);
            } else {
                updateTrackingList.add(trackingPO);
            }
            List<DynamicDeliveryTrackingTaskVO> taskVOS = trackingVO.getTaskVOS();
            if (trackingDeliveryTaskMap.containsKey(trackingId)) {
                // 补充进仓,发货工序
                supplyOperation(trackingDeliveryTaskMap, trackingId, warehousedOperation, deleteTaskIdList, taskVOS, subTaskMap);
                supplyOperation(trackingDeliveryTaskMap, trackingId, deliveryOperation, deleteTaskIdList, taskVOS, subTaskMap);
            }
            for (DynamicDeliveryTrackingTaskVO taskVO : taskVOS) {
                String standardStepId = taskVO.getStandardStepId();
                if (StrUtil.isNotEmpty(warehousedOperation) && standardStepId.equals(warehousedOperation)) {
                    continue;
                }
                // 追踪工序任务参数校验
                DynamicDeliveryTrackingTaskServiceImpl.paramsCheck(taskVO);
                DynamicDeliveryTrackingTaskPO taskPO = DynamicDeliveryTrackingTaskConvertor.INSTANCE.vo2Po(taskVO);
                String taskId = taskPO.getId();
                taskPO.setDynamicDeliveryTrackingId(trackingId);
                if (StringUtils.isEmpty(taskId)) {
                    taskId = UUIDUtil.getUUID();
                    taskPO.setId(taskId);
                    insertTrackingTaskList.add(taskPO);
                } else {
                    updateTrackingTaskList.add(taskPO);
                }
                //这里需要subTask全部做成插入，原来的数据删掉
                List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = taskVO.getSubTaskVOS();
                if (CollectionUtils.isNotEmpty(subTaskVOS)) {
                    deleteTaskIdList.add(taskId);
                    //  数量和时间校验
                    qtyAndTimeCheck(taskVO, subTaskVOS);
                    for (DynamicDeliveryTrackingSubTaskVO subTaskVO : subTaskVOS) {
                        DynamicDeliveryTrackingSubTaskServiceImpl.paramsCheck(subTaskVO);
                        DynamicDeliveryTrackingSubTaskPO subTaskPO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.vo2Po(subTaskVO);
                        String subTaskId = subTaskPO.getId();
                        subTaskPO.setTaskId(taskId);
                        if (StringUtils.isEmpty(subTaskId)) {
                            subTaskId = UUIDUtil.getUUID();
                            subTaskPO.setId(subTaskId);
                            insertTrackingSubTaskList.add(subTaskPO);
                        } else {
                            updateTrackingSubTaskList.add(subTaskPO);
                        }
                    }
                    if (taskPO.getEndTime() == null) {
                        subTaskVOS.stream().map(DynamicDeliveryTrackingSubTaskVO::getEndTime)
                                .max(Comparator.comparing(Date::getTime)).ifPresent(taskPO::setEndTime);
                        taskVO.setEndTime(taskPO.getEndTime());
                    }
                }

            }
            // 后处理进仓工序
            afterHandleWarehouseOperation(taskVOS, warehousedOperation, warehousedOperationResource, insertTrackingTaskList,
                    updateTrackingTaskList, insertTrackingSubTaskList, updateTrackingSubTaskList, trackingVO,deliveryOperation,deliveryResource);
        }
        if (CollectionUtils.isNotEmpty(deleteTaskIdList)) {
            dynamicDeliveryTrackingSubTaskDao.deleteByTaskIds(deleteTaskIdList);
        }
        if (CollectionUtils.isNotEmpty(insertTrackingList)) {
            BasePOUtils.insertBatchFiller(insertTrackingList);
            dynamicDeliveryTrackingDao.insertBatch(insertTrackingList);
        }
        if (CollectionUtils.isNotEmpty(updateTrackingList)) {
            BasePOUtils.updateBatchFiller(updateTrackingList);
            dynamicDeliveryTrackingDao.updateBatch(updateTrackingList);
        }
        if (CollectionUtils.isNotEmpty(insertTrackingTaskList)) {
            BasePOUtils.insertBatchFiller(insertTrackingTaskList);
            dynamicDeliveryTrackingTaskDao.insertBatch(insertTrackingTaskList);
        }
        if (CollectionUtils.isNotEmpty(updateTrackingTaskList)) {
            BasePOUtils.updateBatchFiller(updateTrackingTaskList);
            dynamicDeliveryTrackingTaskDao.updateBatch(updateTrackingTaskList);
        }
        if (CollectionUtils.isNotEmpty(insertTrackingSubTaskList)) {
            BasePOUtils.insertBatchFiller(insertTrackingSubTaskList);
            dynamicDeliveryTrackingSubTaskDao.insertBatch(insertTrackingSubTaskList);
        }
        if (CollectionUtils.isNotEmpty(updateTrackingSubTaskList)) {
            BasePOUtils.updateBatchFiller(updateTrackingSubTaskList);
            dynamicDeliveryTrackingSubTaskDao.insertBatch(updateTrackingSubTaskList);
        }
    }

    private void supplyOperation(Map<String, List<DynamicDeliveryTrackingTaskVO>> trackingDeliveryTaskMap,
                                 String trackingId, String supplyOperation,
                                 List<String> deleteTaskIdList,
                                 List<DynamicDeliveryTrackingTaskVO> taskVOS,
                                 Map<String, List<DynamicDeliveryTrackingSubTaskPO>> subTaskMap) {
        DynamicDeliveryTrackingTaskVO supplyOperationTask = trackingDeliveryTaskMap.get(trackingId)
                .stream().filter(p -> p.getStandardStepId().equals(supplyOperation))
                .findFirst().orElse(null);
        if (Objects.nonNull(supplyOperationTask)) {
            String id = supplyOperationTask.getId();
            List<DynamicDeliveryTrackingSubTaskPO> subTaskOnTask = subTaskMap.get(id);
            if (CollectionUtils.isNotEmpty(subTaskOnTask)) {
                supplyOperationTask.setSubTaskVOS(DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.po2Vos(subTaskOnTask));
                deleteTaskIdList.add(supplyOperationTask.getId());
            }
            // 添加工序，前端不会传
            taskVOS.add(supplyOperationTask);
        }
    }

    public static void main(String[] args) {
        DynamicDeliveryTrackingTaskVO q = new DynamicDeliveryTrackingTaskVO();
        q.setStandardStepCode("1");
        q.setStartTime(DateUtils.stringToDate("2025-01-01 00:01:00", DateUtils.COMMON_DATE_STR1));
        DynamicDeliveryTrackingTaskVO w = new DynamicDeliveryTrackingTaskVO();
        w.setStandardStepCode("2");
        w.setStartTime(DateUtils.stringToDate("2025-01-01 00:01:00", DateUtils.COMMON_DATE_STR1));
        DynamicDeliveryTrackingTaskVO e = new DynamicDeliveryTrackingTaskVO();
        e.setStandardStepCode("3");
        e.setStartTime(DateUtils.stringToDate("2025-01-01 00:02:00", DateUtils.COMMON_DATE_STR1));
        DynamicDeliveryTrackingTaskVO r = new DynamicDeliveryTrackingTaskVO();
        r.setStandardStepCode("3");
        r.setStartTime(DateUtils.stringToDate("2025-01-01 00:01:00", DateUtils.COMMON_DATE_STR1));
        List<DynamicDeliveryTrackingTaskVO> dynamicDeliveryTrackingTaskVOS = new ArrayList<>(ListUtil.of(q, w, e, r));
        dynamicDeliveryTrackingTaskVOS.sort(Comparator.comparing(DynamicDeliveryTrackingTaskVO::getStandardStepCode).reversed()
                .thenComparing(DynamicDeliveryTrackingTaskVO::getStartTime));
        DynamicDeliveryTrackingTaskVO taskVO = dynamicDeliveryTrackingTaskVOS.get(0);
        System.out.println(taskVO);

    }

    public static void afterHandleWarehouseOperation(List<DynamicDeliveryTrackingTaskVO> taskVOS,
                                                     String warehousedOperation,
                                                     String warehousedOperationResource,
                                                     List<DynamicDeliveryTrackingTaskPO> insertTrackingTaskList,
                                                     List<DynamicDeliveryTrackingTaskPO> updateTrackingTaskList,
                                                     List<DynamicDeliveryTrackingSubTaskPO> insertTrackingSubTaskList,
                                                     List<DynamicDeliveryTrackingSubTaskPO> updateTrackingSubTaskList,
                                                     DynamicDeliveryTrackingVO trackingVO, String deliveryOperation, String deliveryResource) {
        boolean warehousedOperationFlag = StrUtil.isNotBlank(warehousedOperation)
                && taskVOS.stream().anyMatch(p -> p.getStandardStepId().equals(warehousedOperation));
        taskVOS.sort(Comparator.comparing(DynamicDeliveryTrackingTaskVO::getStandardStepCode).reversed()
                .thenComparing(DynamicDeliveryTrackingTaskVO::getStartTime));
        if (!warehousedOperationFlag) {
            DynamicDeliveryTrackingTaskVO taskVO = taskVOS.get(0);
            // 补充进仓工序
            DynamicDeliveryTrackingTaskPO supplyWarehousedOperation = supplyWarehousedOperation(insertTrackingTaskList, trackingVO, taskVO,
                    warehousedOperation, warehousedOperationResource);
            // 补充进仓工序任务
            supplyWarehousedOperationTask(supplyWarehousedOperation, trackingVO, insertTrackingSubTaskList);
        } else {
            DynamicDeliveryTrackingTaskVO taskVO = taskVOS.stream()
                    .filter(p -> p.getStandardStepId().equals(warehousedOperation)).findFirst().get();
            DynamicDeliveryTrackingTaskVO preTask = taskVOS.get(0);
            // 修改进仓工序
            DynamicDeliveryTrackingTaskPO taskPO = DynamicDeliveryTrackingTaskConvertor.INSTANCE.vo2Po(taskVO);
            taskPO.setStartTime(preTask.getStartTime());
            taskPO.setEndTime(trackingVO.getInWarehousedTime());
            taskPO.setQuantity(trackingVO.getInWarehousedQuantity());
            taskPO.setPhysicalResourceId(warehousedOperationResource);
            updateTrackingTaskList.add(taskPO);
            // 修改进仓工序任务
            List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = taskVO.getSubTaskVOS();
            if (CollectionUtils.isNotEmpty(subTaskVOS)) {
                DynamicDeliveryTrackingSubTaskVO subTaskVO = subTaskVOS.get(0);
                DynamicDeliveryTrackingSubTaskPO subTaskPO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.vo2Po(subTaskVO);
                subTaskPO.setPlannedQuantity(taskPO.getQuantity());
                subTaskPO.setStartTime(taskPO.getStartTime());
                subTaskPO.setEndTime(taskPO.getEndTime());
                updateTrackingSubTaskList.add(subTaskPO);
            }
        }
        boolean deliveryOperationFlag = StrUtil.isNotBlank(deliveryOperation)
                && taskVOS.stream().anyMatch(p -> p.getStandardStepId().equals(deliveryOperation));
        if (!deliveryOperationFlag) {
            // 补充发货工序
            DynamicDeliveryTrackingTaskPO supplyDeliveryOperation = supplyDeliveryOperation(insertTrackingTaskList, trackingVO,
                    deliveryOperation, deliveryResource);
            // 补充发货工序任务
            supplyDeliveryOperationTask(supplyDeliveryOperation, trackingVO, insertTrackingSubTaskList);
        } else {
            DynamicDeliveryTrackingTaskVO taskVO = taskVOS.stream()
                    .filter(p -> p.getStandardStepId().equals(deliveryOperation)).findFirst().get();
            // 修改发货工序
            DynamicDeliveryTrackingTaskPO taskPO = DynamicDeliveryTrackingTaskConvertor.INSTANCE.vo2Po(taskVO);
            taskPO.setStartTime(trackingVO.getInWarehousedTime());
            taskPO.setEndTime(trackingVO.getDeliveryTime());
            taskPO.setQuantity(trackingVO.getInWarehousedQuantity());
            taskPO.setPhysicalResourceId(warehousedOperationResource);
            updateTrackingTaskList.add(taskPO);
            // 修改发货工序任务
            List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS = taskVO.getSubTaskVOS();
            if (CollectionUtils.isNotEmpty(subTaskVOS)) {
                DynamicDeliveryTrackingSubTaskVO subTaskVO = subTaskVOS.get(0);
                DynamicDeliveryTrackingSubTaskPO subTaskPO = DynamicDeliveryTrackingSubTaskConvertor.INSTANCE.vo2Po(subTaskVO);
                subTaskPO.setPlannedQuantity(taskPO.getQuantity());
                subTaskPO.setStartTime(taskPO.getStartTime());
                subTaskPO.setEndTime(taskPO.getEndTime());
                updateTrackingSubTaskList.add(subTaskPO);
            }
        }
    }

    private static void supplyDeliveryOperationTask(DynamicDeliveryTrackingTaskPO supplyDeliveryOperation,
                                                    DynamicDeliveryTrackingVO trackingVO, List<DynamicDeliveryTrackingSubTaskPO> insertTrackingSubTaskList) {
        DynamicDeliveryTrackingSubTaskPO dynamicDeliveryTrackingSubTaskPO = new DynamicDeliveryTrackingSubTaskPO();
        dynamicDeliveryTrackingSubTaskPO.setId(UUIDUtil.getUUID());
        dynamicDeliveryTrackingSubTaskPO.setTaskId(supplyDeliveryOperation.getId());
        dynamicDeliveryTrackingSubTaskPO.setPlannedQuantity(trackingVO.getInWarehousedQuantity());
        dynamicDeliveryTrackingSubTaskPO.setFinishedQuantity(BigDecimal.ZERO);
        dynamicDeliveryTrackingSubTaskPO.setStartTime(supplyDeliveryOperation.getStartTime());
        dynamicDeliveryTrackingSubTaskPO.setEndTime(supplyDeliveryOperation.getEndTime());
        dynamicDeliveryTrackingSubTaskPO.setRemark("补充发货工序任务");
        insertTrackingSubTaskList.add(dynamicDeliveryTrackingSubTaskPO);
    }

    private static DynamicDeliveryTrackingTaskPO supplyDeliveryOperation(List<DynamicDeliveryTrackingTaskPO> insertTrackingTaskList,
                                                                         DynamicDeliveryTrackingVO trackingVO, String warehousedOperation,
                                                                         String warehousedOperationResource) {
        DynamicDeliveryTrackingTaskPO dynamicDeliveryTrackingTaskPO = new DynamicDeliveryTrackingTaskPO();
        dynamicDeliveryTrackingTaskPO.setId(UUIDUtil.getUUID());
        dynamicDeliveryTrackingTaskPO.setDynamicDeliveryTrackingId(trackingVO.getId());
        dynamicDeliveryTrackingTaskPO.setStandardStepId(warehousedOperation);
        dynamicDeliveryTrackingTaskPO.setPhysicalResourceId(warehousedOperationResource);
        dynamicDeliveryTrackingTaskPO.setQuantity(trackingVO.getInWarehousedQuantity());
        // 开始时间=进仓时间为发货任务的开始时间，结束时间=发货时间作为结束时间
        dynamicDeliveryTrackingTaskPO.setStartTime(trackingVO.getInWarehousedTime());
        dynamicDeliveryTrackingTaskPO.setEndTime(trackingVO.getDeliveryTime());
        dynamicDeliveryTrackingTaskPO.setRemark("补充发货工序");
        insertTrackingTaskList.add(dynamicDeliveryTrackingTaskPO);
        return dynamicDeliveryTrackingTaskPO;
    }

    public static void supplyWarehousedOperationTask(DynamicDeliveryTrackingTaskPO supplyWarehousedOperation,
                                                     DynamicDeliveryTrackingVO trackingVO,
                                                     List<DynamicDeliveryTrackingSubTaskPO> insertTrackingSubTaskList) {
        DynamicDeliveryTrackingSubTaskPO dynamicDeliveryTrackingSubTaskPO = new DynamicDeliveryTrackingSubTaskPO();
        dynamicDeliveryTrackingSubTaskPO.setId(UUIDUtil.getUUID());
        dynamicDeliveryTrackingSubTaskPO.setTaskId(supplyWarehousedOperation.getId());
        dynamicDeliveryTrackingSubTaskPO.setPlannedQuantity(trackingVO.getInWarehousedQuantity());
        dynamicDeliveryTrackingSubTaskPO.setFinishedQuantity(BigDecimal.ZERO);
        dynamicDeliveryTrackingSubTaskPO.setStartTime(supplyWarehousedOperation.getStartTime());
        dynamicDeliveryTrackingSubTaskPO.setEndTime(supplyWarehousedOperation.getEndTime());
        dynamicDeliveryTrackingSubTaskPO.setRemark("补充进仓工序任务");
        insertTrackingSubTaskList.add(dynamicDeliveryTrackingSubTaskPO);
    }

    public static DynamicDeliveryTrackingTaskPO supplyWarehousedOperation(List<DynamicDeliveryTrackingTaskPO> insertTrackingTaskList,
                                                                          DynamicDeliveryTrackingVO trackingVO,
                                                                          DynamicDeliveryTrackingTaskVO taskVO,
                                                                          String warehousedOperation,
                                                                          String warehousedOperationResource) {
        DynamicDeliveryTrackingTaskPO dynamicDeliveryTrackingTaskPO = new DynamicDeliveryTrackingTaskPO();
        dynamicDeliveryTrackingTaskPO.setId(UUIDUtil.getUUID());
        dynamicDeliveryTrackingTaskPO.setDynamicDeliveryTrackingId(trackingVO.getId());
        dynamicDeliveryTrackingTaskPO.setStandardStepId(warehousedOperation);
        dynamicDeliveryTrackingTaskPO.setPhysicalResourceId(warehousedOperationResource);
        dynamicDeliveryTrackingTaskPO.setQuantity(trackingVO.getInWarehousedQuantity());
        // 开始时间=前工序的开始时间，结束时间=进仓时间
        dynamicDeliveryTrackingTaskPO.setStartTime(taskVO.getStartTime());
        dynamicDeliveryTrackingTaskPO.setEndTime(trackingVO.getInWarehousedTime());
        dynamicDeliveryTrackingTaskPO.setRemark("补充进仓工序");
        insertTrackingTaskList.add(dynamicDeliveryTrackingTaskPO);
        return dynamicDeliveryTrackingTaskPO;
    }


    @Override
    public void doUnPublish(List<String> trackingIds) {
        if (CollectionUtils.isEmpty(trackingIds)) {
            throw new BusinessException("取消发布数据不能为空");
        }
        trackingIds = trackingIds.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<DynamicDeliveryTrackingPO> trackingPOList = dynamicDeliveryTrackingDao.selectByPrimaryKeys(trackingIds);
        trackingPOList.forEach(item -> {
            item.setTrackingStatus(TrackingStatusEnum.CANCELED.getCode());
        });
        if (CollectionUtils.isNotEmpty(trackingPOList)) {
            BasePOUtils.updateBatchFiller(trackingPOList);
            dynamicDeliveryTrackingDao.updateBatch(trackingPOList);
        }
    }

    /**
     * 获取状态下拉
     *
     * @return
     */
    @Override
    public List<LabelValue<String>> statusOption() {
        TrackingStatusEnum[] values = TrackingStatusEnum.values();
        List<LabelValue<String>> labelValueS = new ArrayList<>();
        Arrays.stream(values).forEach(item -> {
            labelValueS.add(new LabelValue<>(item.getDesc(), item.getCode()));
        });
        return labelValueS;
    }

    @Override
    public BaseResponse<Void> cancelPublish(DynamicDeliveryTrackingVXDTO dto) {
        List<String> trackingIds = dto.getTrackIds();
        if (CollectionUtils.isEmpty(trackingIds)) {
            return BaseResponse.error("取消发布数据不能为空");
        }
        trackingIds = trackingIds.stream().filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        List<DynamicDeliveryTrackingPO> trackingPOList = dynamicDeliveryTrackingDao.selectByPrimaryKeys(trackingIds);
        if (CollectionUtils.isNotEmpty(trackingPOList)) {
            trackingPOList.forEach(item -> {
                item.setTrackingStatus(TrackingStatusEnum.CANCELED.getCode());
            });
            User user = ipsNewFeign.getUserByUserName(dto.getEmployeeCode());
            if (Objects.isNull(user)) {
                log.error("BPIM系统中匹配不到工号为{}的用户数据", dto.getEmployeeCode());
                return BaseResponse.error(String.join("BPIM系统中匹配不到工号为", dto.getEmployeeCode(), "的用户数据"));
            }
            Date date = new Date();
            String userId = user.getId();
            trackingPOList.parallelStream().forEach(item -> {
                item.setModifier(userId);
                item.setModifyTime(date);
            });
            dynamicDeliveryTrackingDao.updateBatch(trackingPOList);
            return BaseResponse.success();
        }
        log.error("BPIM系统中匹配不到该任务，请联系管理员,任务id为{}", trackingIds);
        return BaseResponse.error("BPIM系统中匹配不到该任务，请联系管理员");
    }

    /**
     * 参数校验
     *
     * @param trackingVO
     */
    private static void paramsCheck(DynamicDeliveryTrackingVO trackingVO) {
        if (StringUtils.isEmpty(trackingVO.getProductId())) {
            throw new BusinessException("产品Id不能为空");
        }
        if (StringUtils.isEmpty(trackingVO.getProductCode())) {
            throw new BusinessException("产品编码不能为空");
        }
        if (trackingVO.getDeliveryTime() == null) {
            throw new BusinessException(trackingVO.getProductCode() + "发货时间不能为空");
        }
        if (trackingVO.getInWarehousedQuantity() == null || trackingVO.getInWarehousedQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(trackingVO.getProductCode() + "进仓数量必须大于0");
        }
        if (trackingVO.getInWarehousedTime() == null) {
            throw new BusinessException(trackingVO.getProductCode() + "要求进仓时间不能为空");
        }
        if (CollectionUtils.isEmpty(trackingVO.getTaskVOS())) {
            throw new BusinessException(trackingVO.getProductCode() + "没有设置追踪工序任务");
        }
    }

    private static void qtyAndTimeCheck(DynamicDeliveryTrackingTaskVO taskVO, List<DynamicDeliveryTrackingSubTaskVO> subTaskVOS) {
        BigDecimal sum = subTaskVOS.stream().map(DynamicDeliveryTrackingSubTaskVO::getPlannedQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
        Date minStartTime = subTaskVOS.stream().map(DynamicDeliveryTrackingSubTaskVO::getStartTime).min(Date::compareTo).orElse(null);
        if (taskVO.getQuantity().compareTo(sum) != 0) {
            throw new BusinessException("时段汇总数量与工序任务数量不一致！");
        }
        if (minStartTime == null || taskVO.getStartTime().compareTo(minStartTime) != 0) {
            throw new BusinessException("时段汇总开始时间与工序任务开始时间不一致！");
        }
    }
}
