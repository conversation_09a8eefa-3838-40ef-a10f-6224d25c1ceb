<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO">
        <!--@Table mps_sub_cargo_location_information-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="corporation_code" jdbcType="VARCHAR" property="corporationCode"/>
        <result column="factory_code" jdbcType="VARCHAR" property="factoryCode"/>
        <result column="factory_name" jdbcType="VARCHAR" property="factoryName"/>
        <result column="stash_code" jdbcType="VARCHAR" property="stashCode"/>
        <result column="stash_name" jdbcType="VARCHAR" property="stashName"/>
        <result column="freight_space_code" jdbcType="VARCHAR" property="freightSpaceCode"/>
        <result column="freight_space_name" jdbcType="VARCHAR" property="freightSpaceName"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="valid" jdbcType="VARCHAR" property="valid"/>
        <result column="erp_freight_space_code" jdbcType="VARCHAR" property="erpFreightSpaceCode"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO">
        <result column="stock_point_type" jdbcType="VARCHAR" property="stockPointType"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,corporation_code,factory_code,factory_name,stash_code,stash_name,freight_space_code,freight_space_name,source,valid,erp_freight_space_code,update_time,enabled,creator,create_time,modifier,modify_time,version_value,remark
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.corporationCode != null and params.corporationCode != ''">
                and corporation_code = #{params.corporationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointCodes != null and params.stockPointCodes.size() > 0">
                and factory_code in
                <foreach collection="params.stockPointCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.factoryCode != null and params.factoryCode != ''">
                and factory_code = #{params.factoryCode,jdbcType=VARCHAR}
            </if>
            <if test="params.factoryName != null and params.factoryName != ''">
                and factory_name = #{params.factoryName,jdbcType=VARCHAR}
            </if>
            <if test="params.stashCode != null and params.stashCode != ''">
                and stash_code = #{params.stashCode,jdbcType=VARCHAR}
            </if>
            <if test="params.stashName != null and params.stashName != ''">
                and stash_name = #{params.stashName,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpaceCode != null and params.freightSpaceCode != ''">
                and freight_space_code = #{params.freightSpaceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.freightSpaceCodes != null and params.freightSpaceCodes.size > 0">
                and freight_space_code in
                <foreach collection="params.freightSpaceCodes" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="params.freightSpaceName != null and params.freightSpaceName != ''">
                and freight_space_name = #{params.freightSpaceName,jdbcType=VARCHAR}
            </if>
            <if test="params.source != null and params.source != ''">
                and source = #{params.source,jdbcType=VARCHAR}
            </if>
            <if test="params.valid != null and params.valid != ''">
                and valid = #{params.valid,jdbcType=VARCHAR}
            </if>
            <if test="params.erpFreightSpaceCode != null and params.erpFreightSpaceCode != ''">
                and erp_freight_space_code = #{params.erpFreightSpaceCode,jdbcType=VARCHAR}
            </if>
            <if test="params.updateTime != null">
                and update_time = #{params.updateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_sub_cargo_location_information
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_sub_cargo_location_information
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mps_sub_cargo_location_information
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_sub_cargo_location_information
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 条件查询 -->
    <select id="selectByCorporationOrFactoryOrStashOrFreightSpace" parameterType="map" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_sub_cargo_location_information
        <where>
            1=0
            <if test="corporationCodes != null and !corporationCodes.isEmpty()">
                OR corporation_code IN
                <foreach collection="corporationCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="factoryCodes != null and !factoryCodes.isEmpty()">
                OR factory_code IN
                <foreach collection="factoryCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="stashCodes != null and !stashCodes.isEmpty()">
                OR stash_code IN
                <foreach collection="stashCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="freightSpaceCodes != null and !freightSpaceCodes.isEmpty()">
                OR freight_space_code IN
                <foreach collection="freightSpaceCodes" item="item" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_sub_cargo_location_information(
        id,
        corporation_code,
        factory_code,
        factory_name,
        stash_code,
        stash_name,
        freight_space_code,
        freight_space_name,
        source,
        valid,
        erp_freight_space_code,
        update_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{corporationCode,jdbcType=VARCHAR},
        #{factoryCode,jdbcType=VARCHAR},
        #{factoryName,jdbcType=VARCHAR},
        #{stashCode,jdbcType=VARCHAR},
        #{stashName,jdbcType=VARCHAR},
        #{freightSpaceCode,jdbcType=VARCHAR},
        #{freightSpaceName,jdbcType=VARCHAR},
        #{source,jdbcType=VARCHAR},
        #{valid,jdbcType=VARCHAR},
        #{erpFreightSpaceCode,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO">
        insert into mps_sub_cargo_location_information(
        id,
        corporation_code,
        factory_code,
        factory_name,
        stash_code,
        stash_name,
        freight_space_code,
        freight_space_name,
        source,
        valid,
        erp_freight_space_code,
        update_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark)
        values (
        #{id,jdbcType=VARCHAR},
        #{corporationCode,jdbcType=VARCHAR},
        #{factoryCode,jdbcType=VARCHAR},
        #{factoryName,jdbcType=VARCHAR},
        #{stashCode,jdbcType=VARCHAR},
        #{stashName,jdbcType=VARCHAR},
        #{freightSpaceCode,jdbcType=VARCHAR},
        #{freightSpaceName,jdbcType=VARCHAR},
        #{source,jdbcType=VARCHAR},
        #{valid,jdbcType=VARCHAR},
        #{erpFreightSpaceCode,jdbcType=VARCHAR},
        #{updateTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_sub_cargo_location_information(
        id,
        corporation_code,
        factory_code,
        factory_name,
        stash_code,
        stash_name,
        freight_space_code,
        freight_space_name,
        source,
        valid,
        erp_freight_space_code,
        update_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.corporationCode,jdbcType=VARCHAR},
        #{entity.factoryCode,jdbcType=VARCHAR},
        #{entity.factoryName,jdbcType=VARCHAR},
        #{entity.stashCode,jdbcType=VARCHAR},
        #{entity.stashName,jdbcType=VARCHAR},
        #{entity.freightSpaceCode,jdbcType=VARCHAR},
        #{entity.freightSpaceName,jdbcType=VARCHAR},
        #{entity.source,jdbcType=VARCHAR},
        #{entity.valid,jdbcType=VARCHAR},
        #{entity.erpFreightSpaceCode,jdbcType=VARCHAR},
        #{entity.updateTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_sub_cargo_location_information(
        id,
        corporation_code,
        factory_code,
        factory_name,
        stash_code,
        stash_name,
        freight_space_code,
        freight_space_name,
        source,
        valid,
        erp_freight_space_code,
        update_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        remark)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.corporationCode,jdbcType=VARCHAR},
        #{entity.factoryCode,jdbcType=VARCHAR},
        #{entity.factoryName,jdbcType=VARCHAR},
        #{entity.stashCode,jdbcType=VARCHAR},
        #{entity.stashName,jdbcType=VARCHAR},
        #{entity.freightSpaceCode,jdbcType=VARCHAR},
        #{entity.freightSpaceName,jdbcType=VARCHAR},
        #{entity.source,jdbcType=VARCHAR},
        #{entity.valid,jdbcType=VARCHAR},
        #{entity.erpFreightSpaceCode,jdbcType=VARCHAR},
        #{entity.updateTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.versionValue,jdbcType=INTEGER},
        #{entity.remark,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO">
        update mps_sub_cargo_location_information set
        corporation_code = #{corporationCode,jdbcType=VARCHAR},
        factory_code = #{factoryCode,jdbcType=VARCHAR},
        factory_name = #{factoryName,jdbcType=VARCHAR},
        stash_code = #{stashCode,jdbcType=VARCHAR},
        stash_name = #{stashName,jdbcType=VARCHAR},
        freight_space_code = #{freightSpaceCode,jdbcType=VARCHAR},
        freight_space_name = #{freightSpaceName,jdbcType=VARCHAR},
        source = #{source,jdbcType=VARCHAR},
        valid = #{valid,jdbcType=VARCHAR},
        erp_freight_space_code = #{erpFreightSpaceCode,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        version_value = #{versionValue,jdbcType=INTEGER},
        remark = #{remark,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO">
        update mps_sub_cargo_location_information
        <set>
            <if test="item.corporationCode != null and item.corporationCode != ''">
                corporation_code = #{item.corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryCode != null and item.factoryCode != ''">
                factory_code = #{item.factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryName != null and item.factoryName != ''">
                factory_name = #{item.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="item.stashCode != null and item.stashCode != ''">
                stash_code = #{item.stashCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stashName != null and item.stashName != ''">
                stash_name = #{item.stashName,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceCode != null and item.freightSpaceCode != ''">
                freight_space_code = #{item.freightSpaceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceName != null and item.freightSpaceName != ''">
                freight_space_name = #{item.freightSpaceName,jdbcType=VARCHAR},
            </if>
            <if test="item.source != null and item.source != ''">
                source = #{item.source,jdbcType=VARCHAR},
            </if>
            <if test="item.valid != null and item.valid != ''">
                valid = #{item.valid,jdbcType=VARCHAR},
            </if>
            <if test="item.erpFreightSpaceCode != null and item.erpFreightSpaceCode != ''">
                erp_freight_space_code = #{item.erpFreightSpaceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.updateTime != null">
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_sub_cargo_location_information
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="corporation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.corporationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="factory_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.factoryCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="factory_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.factoryName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stash_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stashCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stash_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.stashName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpaceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="freight_space_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.freightSpaceName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.source,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="valid = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.valid,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="erp_freight_space_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.erpFreightSpaceCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.updateTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_sub_cargo_location_information
        <set>
            <if test="item.corporationCode != null and item.corporationCode != ''">
                corporation_code = #{item.corporationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryCode != null and item.factoryCode != ''">
                factory_code = #{item.factoryCode,jdbcType=VARCHAR},
            </if>
            <if test="item.factoryName != null and item.factoryName != ''">
                factory_name = #{item.factoryName,jdbcType=VARCHAR},
            </if>
            <if test="item.stashCode != null and item.stashCode != ''">
                stash_code = #{item.stashCode,jdbcType=VARCHAR},
            </if>
            <if test="item.stashName != null and item.stashName != ''">
                stash_name = #{item.stashName,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceCode != null and item.freightSpaceCode != ''">
                freight_space_code = #{item.freightSpaceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.freightSpaceName != null and item.freightSpaceName != ''">
                freight_space_name = #{item.freightSpaceName,jdbcType=VARCHAR},
            </if>
            <if test="item.source != null and item.source != ''">
                source = #{item.source,jdbcType=VARCHAR},
            </if>
            <if test="item.valid != null and item.valid != ''">
                valid = #{item.valid,jdbcType=VARCHAR},
            </if>
            <if test="item.erpFreightSpaceCode != null and item.erpFreightSpaceCode != ''">
                erp_freight_space_code = #{item.erpFreightSpaceCode,jdbcType=VARCHAR},
            </if>
            <if test="item.updateTime != null">
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_sub_cargo_location_information where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_sub_cargo_location_information where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectByBatchCodeAndStockType" resultMap="VOResultMap">
        select distinct
            ms.factory_code       as corporation_code,
            ms.freight_space_code as freight_space_code,
            ms.enabled            as enabled,
            mp.stock_point_type   as stock_point_type
        from mps_sub_cargo_location_information ms
                 LEFT JOIN mds_stock_point mp on ms.factory_code = mp.stock_point_code
        where mp.stock_point_type = #{stockPointType}
          and ms.enabled = 'YES'
          and ms.freight_space_code in
        <foreach collection="spaceCodeList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    
    <select id="selectOmeLocation" resultType="java.lang.String">
        SELECT
			DISTINCT t1.freight_space_code
		FROM
			mps_sub_cargo_location_information t1 
		WHERE
			t1.factory_code = #{factoryCode,jdbcType=VARCHAR}
            AND t1.valid = 'YES'
			AND t1.corporation_code NOT IN (
			SELECT
				t2.stock_point_code 
			FROM
				mds_stock_point t2 
			WHERE
			t2.organize_type IN ( 'PURCHASE_ORGANIZATION', 'PRODUCT_ORGANIZATION' ))
    </select>

</mapper>
