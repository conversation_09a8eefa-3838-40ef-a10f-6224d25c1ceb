package com.yhl.scp.mps.manualAdjust.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.service.impl.ManualAdjustService;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.annotation.Resource;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * <code>ManualAdjustController</code>
 *
 * <p>手工调整控制器
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags = "手工调整控制器")
@RestController
@RequestMapping("manualAdjust")
public class ManualAdjustController extends BaseController {

    @Resource
    private ManualAdjustService manualAdjustService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;

    @PostMapping("adjust")
    @ApiOperation("手工调整")
    public BaseResponse<Void> adjust(@RequestBody ManualAdjustParam param) {
        return manualAdjustService.adjust(param);
    }

    @ApiOperation("调用算法")
    @PostMapping("callAlgorithm")
    @BusinessMonitorLog(businessCode = "生产计划编制", moduleCode = "MPS", businessFrequency = "DAY")
    public DeferredResult<BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>>> callAlgorithm(
            @RequestBody ManualAdjustParam param) {
        operationTaskExtDao.updateWorkOrderStatus(param.getOperationIds());
        return manualAdjustService.callAlgorithm(getPagination(), param);
    }


    @ApiOperation("获取主计划动态列部分")
    @PostMapping("getAdditionalInfo")
    public BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>> getAdditionalInfo(
            @RequestBody ManualAdjustParam param) {
        return manualAdjustService.getAdditionalInfo(getPagination(), param);
    }
}
