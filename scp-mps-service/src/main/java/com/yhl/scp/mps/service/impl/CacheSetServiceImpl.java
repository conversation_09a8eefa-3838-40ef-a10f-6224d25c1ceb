package com.yhl.scp.mps.service.impl;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.biz.common.constants.CacheConstants;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import com.yhl.scp.sds.order.infrastructure.dao.WorkOrderDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.CachePut;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CacheSetServiceImpl</code>
 * <p>
 * CacheSetServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-22 23:42:31
 */
@Service
@Slf4j
public class CacheSetServiceImpl implements CacheSetService {

    @Resource
    private WorkOrderDao workOrderDao;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsFeign ipsFeign;

    @Resource
    @SuppressWarnings("unused")
    private CacheManager cacheManager;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Override
    public void refreshWorkOrderCache(String scenario) {
        CacheSetService bean = SpringBeanUtils.getBean(CacheSetService.class);
        Map<String, WorkOrderPO> workOrderMap = bean.setWorkOrderMap(scenario);
        List<WorkOrderPO> workOrders = new ArrayList<>(workOrderMap.values());
        bean.setOrderId2ComprehensiveYieldMap(workOrders, scenario);
        bean.fuzzyEvict(CacheConstants.OPERATION_MAP, "^" + CacheConstants.OPERATION_MAP + "::" + scenario + "_.*");
    }

    public Set<String> getCacheKeys(String cacheName) {
        // 构造Redis中缓存键的前缀，通常Spring Cache存储时会有缓存名称作为前缀
        String pattern = cacheName + "*";
        // 使用RedisTemplate执行keys命令获取匹配的键，这里使用的是Redis的模糊匹配功能
        return redisTemplate.keys(pattern);
    }

    @Override
    public void fuzzyEvict(String cacheName, String pattern) {
        Set<String> keys = getCacheKeys(cacheName);
        if (CollectionUtils.isEmpty(keys)) {
            return;
        }
        for (String key : keys) {
            if (key.matches(pattern)) {
                log.info("FUZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZZY key = {}", key);
                Objects.requireNonNull(cacheManager.getCache(cacheName)).evict(key);
                Cache.ValueWrapper valueWrapper = Objects.requireNonNull(cacheManager.getCache(cacheName)).get(key);
                if (valueWrapper != null) {
                    Object o = valueWrapper.get();
                    System.out.println("Cache value: " + JacksonUtils.toJson(o));
                } else {
                    System.out.println("Cache value not found.");
                }
            }
        }
    }

    @Override
    @CachePut(value = CacheConstants.WORK_ORDER_MAP, key = "#scenario")
    public Map<String, WorkOrderPO> setWorkOrderMap(String scenario) {
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<WorkOrderPO> workOrders = workOrderDao.selectByParams(new HashMap<>());
        DynamicDataSourceContextHolder.clearDataSource();
        return workOrders.stream().collect(Collectors.toMap(WorkOrderPO::getId, Function.identity(),
                (v1, v2) -> v1));
    }

    @Override
    @CachePut(value = CacheConstants.COMPREHENSIVE_YIELD_MAP, key = "#scenario")
    public Map<String, BigDecimal> setOrderId2ComprehensiveYieldMap(List<WorkOrderPO> workOrders, String scenario) {
        List<String> routingIds = workOrders.stream().map(WorkOrderPO::getRoutingId)
                .filter(routingId -> !StringUtils.isBlank(routingId)).distinct().collect(Collectors.toList());
        List<RoutingStepVO> routingStepByRoutingIds = newMdsFeign.getRoutingStepByRoutingIds(scenario, routingIds);
        Map<String, List<RoutingStepVO>> routingStepMap = routingStepByRoutingIds.stream()
                .collect(Collectors.groupingBy(RoutingStepVO::getRoutingId));
        return workOrders.stream().filter(workOrderPO ->
                        !StringUtils.isBlank(workOrderPO.getRoutingId()))
                .collect(Collectors.toMap(WorkOrderPO::getId,
                        x -> routingStepMap.getOrDefault(x.getRoutingId(), new ArrayList<>())
                                .stream().map(RoutingStepVO::getYield).reduce(BigDecimal.ONE, BigDecimal::multiply)
                                .setScale(4, RoundingMode.HALF_UP), (v1, v2) -> v2));
    }

    @Override
    @Cacheable(value = CacheConstants.HW_LIMIT_STAND_RESOURCE_CODE, key = "#scenario")
    public String setHwLimitStandResourceCode(String scenario) {
        String collectionCode = "HW_LIMIT_STAND_RESOURCE_CODE";
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<CollectionValueVO> collections = ipsFeign.getByCollectionCode(collectionCode);
        DynamicDataSourceContextHolder.clearDataSource();
        return Optional.ofNullable(collections).filter(CollectionUtils::isNotEmpty)
                .map(hwResource -> hwResource.get(0).getCollectionValue()).orElse("");
    }

    @Override
    @Cacheable(value = CacheConstants.TOOL_RESOURCE_MAP, key = "#scenario")
    public Map<String, PhysicalResourceVO> setToolResourceMap(String scenario) {
        return newMdsFeign.selectPhysicalResourceByParams(scenario,
                ImmutableMap.of("resourceCategory", "TOOL")).stream().collect(Collectors
                .toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.STANDARD_STEP_MAP, key = "#scenario")
    public Map<String, String> setStandardStepMap(String scenario) {
        return newMdsFeign.selectStandardStepAll(scenario).stream().collect(Collectors
                .toMap(p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
    }

    @Override
    @Cacheable(value = CacheConstants.NEW_STOCK_POINT_LIST, key = "#scenario")
    public List<NewStockPointVO> setNewStockPoints(String scenario) {
        return newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of("stockPointType", StockPointTypeEnum.BC.getCode()));
    }

}