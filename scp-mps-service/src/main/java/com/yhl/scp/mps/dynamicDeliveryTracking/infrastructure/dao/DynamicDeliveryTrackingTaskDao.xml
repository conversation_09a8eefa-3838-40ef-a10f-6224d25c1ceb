<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingTaskDao">
    <resultMap id="BaseResultMap"
               type="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO">
        <!--@Table mps_dynamic_delivery_tracking_task-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="dynamic_delivery_tracking_Id" jdbcType="VARCHAR" property="dynamicDeliveryTrackingId"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="physical_resource_id" jdbcType="VARCHAR" property="physicalResourceId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="pipeline_time" jdbcType="DECIMAL" property="pipelineTime"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO">
        <!-- TODO -->
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="physical_resource_code" jdbcType="VARCHAR" property="physicalResourceCode"/>
        <result column="finished_quantity" jdbcType="VARCHAR" property="finishedQuantity"/>
        <result column="planned_quantity" jdbcType="VARCHAR" property="plannedQuantity"/>
        <result column="tracking_Id" jdbcType="VARCHAR" property="trackingId"/>
        <result column="task_id" jdbcType="VARCHAR" property="taskId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,dynamic_delivery_tracking_Id,standard_step_id,physical_resource_id,quantity,start_time,end_time,pipeline_time,remark,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>
        ,product_id,product_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.dynamicDeliveryTrackingId != null and params.dynamicDeliveryTrackingId != ''">
                and dynamic_delivery_tracking_Id = #{params.dynamicDeliveryTrackingId,jdbcType=VARCHAR}
            </if>
            <if test="params.trackingIdList != null and params.trackingIdList.size() > 0">
                and dynamic_delivery_tracking_Id in
                <foreach collection="params.trackingIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and standard_step_id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceId != null and params.physicalResourceId != ''">
                and physical_resource_id = #{params.physicalResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.startTime != null">
                and start_time = #{params.startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_dynamic_delivery_tracking_task
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_dynamic_delivery_tracking_task
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from mps_dynamic_delivery_tracking_task
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_dynamic_delivery_tracking_task
        <include refid="Base_Where_Condition"/>
    </select>

    <select id="selectVOByParams" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List"/>
        from v_mps_dynamic_delivery_tracking_task
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert"
            parameterType="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_dynamic_delivery_tracking_task(
        id,
        dynamic_delivery_tracking_Id,
        standard_step_id,
        physical_resource_id,
        quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        pipeline_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{dynamicDeliveryTrackingId,jdbcType=VARCHAR},
        #{standardStepId,jdbcType=VARCHAR},
        #{physicalResourceId,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{pipelineTime,jdbcType=DECIMAL})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO">
        insert into mps_dynamic_delivery_tracking_task(id,
                                                       dynamic_delivery_tracking_Id,
                                                       standard_step_id,
                                                       physical_resource_id,
                                                       quantity,
                                                       start_time,
                                                       end_time,
                                                       remark,
                                                       enabled,
                                                       creator,
                                                       create_time,
                                                       modifier,
                                                       modify_time,pipeline_time)
        values (#{id,jdbcType=VARCHAR},
                #{dynamicDeliveryTrackingId,jdbcType=VARCHAR},
                #{standardStepId,jdbcType=VARCHAR},
                #{physicalResourceId,jdbcType=VARCHAR},
                #{quantity,jdbcType=VARCHAR},
                #{startTime,jdbcType=TIMESTAMP},
                #{endTime,jdbcType=TIMESTAMP},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},#{pipelineTime,jdbcType=DECIMAL})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_dynamic_delivery_tracking_task(
        id,
        dynamic_delivery_tracking_Id,
        standard_step_id,
        physical_resource_id,
        quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        pipeline_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.id,jdbcType=VARCHAR},
            #{entity.dynamicDeliveryTrackingId,jdbcType=VARCHAR},
            #{entity.standardStepId,jdbcType=VARCHAR},
            #{entity.physicalResourceId,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.startTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},#{entity.pipelineTime,jdbcType=DECIMAL})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_dynamic_delivery_tracking_task(
        id,
        dynamic_delivery_tracking_Id,
        standard_step_id,
        physical_resource_id,
        quantity,
        start_time,
        end_time,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        pipeline_time)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.dynamicDeliveryTrackingId,jdbcType=VARCHAR},
            #{entity.standardStepId,jdbcType=VARCHAR},
            #{entity.physicalResourceId,jdbcType=VARCHAR},
            #{entity.quantity,jdbcType=VARCHAR},
            #{entity.startTime,jdbcType=TIMESTAMP},
            #{entity.endTime,jdbcType=TIMESTAMP},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},#{entity.pipelineTime,jdbcType=DECIMAL})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update"
            parameterType="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO">
        update mps_dynamic_delivery_tracking_task
        set dynamic_delivery_tracking_Id = #{dynamicDeliveryTrackingId,jdbcType=VARCHAR},
            standard_step_id             = #{standardStepId,jdbcType=VARCHAR},
            physical_resource_id         = #{physicalResourceId,jdbcType=VARCHAR},
            quantity                     = #{quantity,jdbcType=VARCHAR},
            start_time                   = #{startTime,jdbcType=TIMESTAMP},
            end_time                     = #{endTime,jdbcType=TIMESTAMP},
            remark                       = #{remark,jdbcType=VARCHAR},
            enabled                      = #{enabled,jdbcType=VARCHAR},
            modifier                     = #{modifier,jdbcType=VARCHAR},
            modify_time                  = #{modifyTime,jdbcType=TIMESTAMP},
            pipeline_time                  = #{pipelineTime,jdbcType=DECIMAL}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO">
        update mps_dynamic_delivery_tracking_task
        <set>
            <if test="item.dynamicDeliveryTrackingId != null and item.dynamicDeliveryTrackingId != ''">
                dynamic_delivery_tracking_Id = #{item.dynamicDeliveryTrackingId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.pipelineTime != null">
                pipeline_time = #{item.pipelineTime,jdbcType=DECIMAL},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_dynamic_delivery_tracking_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="dynamic_delivery_tracking_Id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.dynamicDeliveryTrackingId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="pipeline_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.pipelineTime,jdbcType=DECIMAL}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update mps_dynamic_delivery_tracking_task
            <set>
                <if test="item.dynamicDeliveryTrackingId != null and item.dynamicDeliveryTrackingId != ''">
                    dynamic_delivery_tracking_Id = #{item.dynamicDeliveryTrackingId,jdbcType=VARCHAR},
                </if>
                <if test="item.standardStepId != null and item.standardStepId != ''">
                    standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
                </if>
                <if test="item.physicalResourceId != null and item.physicalResourceId != ''">
                    physical_resource_id = #{item.physicalResourceId,jdbcType=VARCHAR},
                </if>
                <if test="item.quantity != null">
                    quantity = #{item.quantity,jdbcType=VARCHAR},
                </if>
                <if test="item.startTime != null">
                    start_time = #{item.startTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.endTime != null">
                    end_time = #{item.endTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.pipelineTime != null">
                    pipeline_time = #{item.pipelineTime,jdbcType=DECIMAL},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from mps_dynamic_delivery_tracking_task
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_dynamic_delivery_tracking_task where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectTaskByPhysicalResourceId" resultMap="VOResultMap">
        SELECT
        mpsp.vehicle_model_code,
        mddt.product_code,
        mddt.product_id,
        mddtt.physical_resource_id,
        mrpr.physical_resource_code,
        min( mddtst.start_time ) AS start_time,
        max( mddtst.end_time ) AS end_time,
        mddtt.quantity,
        sum( mddtst.finished_quantity ) AS finished_quantity,
        mddtt.id as id,
        mddt.id as tracking_Id
        FROM
        mps_dynamic_delivery_tracking mddt,
        mps_dynamic_delivery_tracking_task mddtt,
        mps_dynamic_delivery_tracking_sub_task mddtst,
        mds_res_physical_resource mrpr,
        mds_product_stock_point mpsp
        WHERE
        mddtst.task_id = mddtt.id
        AND mddt.id = mddtt.dynamic_delivery_tracking_Id
        AND mddtt.physical_resource_id = mrpr.id
        AND mddt.tracking_status in ('PUBLISHED','IN_EXECUTION')
        AND mddt.product_id = mpsp.id
        <if test="physicalResourceId != null and physicalResourceId != ''">
            AND mddtt.physical_resource_id = #{physicalResourceId,jdbcType=VARCHAR}
        </if>
        GROUP BY
        mpsp.vehicle_model_code,
        mddt.product_id,
        mddt.product_code,
        mddtt.physical_resource_id,
        mddtt.quantity,
        mrpr.physical_resource_code,
        mddtt.id
        having mddtt.quantity > finished_quantity
        order by start_time
        <if test="limitFlag != null and limitFlag == true">
            limit 3
        </if>
    </select>

    <resultMap id="dynamicMap" type="com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryPolymerizationVO">
        <id column="tracking_id" jdbcType="VARCHAR" property="trackingId"/>
        <id column="product_id" jdbcType="VARCHAR" property="productId"/>
        <id column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <id column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <id column="warehouse_number" jdbcType="DECIMAL" property="warehouseNumber"/>
        <id column="warehoused_time" jdbcType="TIMESTAMP" property="warehousedTime"/>
        <id column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <id column="task_id" jdbcType="VARCHAR" property="taskId"/>
        <id column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <id column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <id column="quantity" jdbcType="DECIMAL" property="quantity"/>
        <id column="finished_quantity" jdbcType="DECIMAL" property="finishedQuantity"/>
        <id column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <id column="standard_step_name" jdbcType="VARCHAR" property="standardStepName"/>
        <id column="stock_point_code" jdbcType="VARCHAR" property="stockPointCode"/>
        <id column="stock_point_name" jdbcType="VARCHAR" property="stockPointName"/>
        <id column="step_id" jdbcType="VARCHAR" property="stepId"/>
        <id column="tracking_status" jdbcType="VARCHAR" property="trackingStatus"/>
        <id column="standard_step_code" jdbcType="VARCHAR" property="standardStepCode"/>
    </resultMap>

    <select id="selectDynamicDeliveryPolymerization" resultMap="dynamicMap">
        SELECT
        ddr.id as tracking_id,
        ddr.product_id as product_id,
        ddr.in_warehoused_quantity as warehouse_number,
        ddr.in_warehoused_time as warehoused_time,
        ddr.delivery_time as delivery_time,
        mpsp.product_code as product_code,
        mpsp.vehicle_model_code as vehicle_model_code,
        mpsp.stock_point_code as stock_point_code,
        msp.stock_point_name as stock_point_name,
        ddtt.id as task_id,
        ddtt.quantity as quantity,
        ddts.finished_quantity as finished_quantity,
        ddtt.start_time as start_time,
        ddtt.end_time as end_time,
        rss.standard_step_name as standard_step_name,
        rss.id as standard_step_id,
        ddtt.standard_step_id as step_id,
        ddr.tracking_status as tracking_status,
        COALESCE(rss.standard_step_code,ddtt.standard_step_id) as standard_step_code
        from
        mps_dynamic_delivery_tracking ddr
        LEFT JOIN mps_dynamic_delivery_tracking_task ddtt on ddr.id = ddtt.dynamic_delivery_tracking_Id
        left join mds_product_stock_point mpsp on mpsp.id = ddr.product_id
        left join mds_stock_point msp on mpsp.stock_point_code = msp.stock_point_code
        LEFT JOIN mps_dynamic_delivery_tracking_sub_task ddts on ddts.task_id = ddtt.id
        LEFT JOIN mds_rou_routing_step rrs on rrs.id = ddtt.standard_step_id
        LEFT JOIN mds_rou_standard_step rss on rss.id = rrs.standard_step_id
        where 1=1
        <if test="deliveryTrackingViewReq.trackingIds != null and deliveryTrackingViewReq.trackingIds.size() > 0">
            and ddtt.dynamic_delivery_tracking_Id in
            <foreach collection="deliveryTrackingViewReq.trackingIds" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="deliveryTrackingViewReq.productCodeList != null and deliveryTrackingViewReq.productCodeList.size() > 0">
            and ddr.product_code in
            <foreach collection="deliveryTrackingViewReq.productCodeList" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="deliveryTrackingViewReq.status != null and deliveryTrackingViewReq.status .size() > 0">
            and ddr.tracking_status in
            <foreach collection="deliveryTrackingViewReq.status" item="item" separator="," open="(" close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="deliveryTrackingViewReq.productCode != null and deliveryTrackingViewReq.productCode != ''">
            and ddr.product_code = #{deliveryTrackingViewReq.productCode,jdbcType=VARCHAR}
        </if>
        <if test="deliveryTrackingViewReq.stockPointCode != null and deliveryTrackingViewReq.stockPointCode != ''">
            and mpsp.stock_point_code = #{deliveryTrackingViewReq.stockPointCode,jdbcType=VARCHAR}
        </if>
        <if test="deliveryTrackingViewReq.deliveryTime != null and deliveryTrackingViewReq.deliveryTime != ''">
            AND DATE(ddr.delivery_time) = #{deliveryTrackingViewReq.deliveryTime,jdbcType=VARCHAR}
        </if>
    </select>

    <select id="selectTrackingIds" resultType="java.lang.String">
        select distinct zddt.dynamic_delivery_tracking_Id
        from mps_dynamic_delivery_tracking_task zddt
                 LEFT JOIN
             mds_rou_routing_step zrrs on zddt.standard_step_id = zrrs.id
                 LEFT JOIN mds_rou_standard_step zmrs on zrrs.standard_step_id = zmrs.id
        where zmrs.id = #{standardOperation,jdbcType=VARCHAR}
    </select>
    <select id="selectTaskGroupByPhysicalResourceId" resultMap="VOResultMap">
        WITH grouped_data AS (
        SELECT
        mddtt.id,
        mddtt.physical_resource_id,
        mddtt.quantity,
        mddt.product_id,
        mddt.product_code,
        sum(mddtst.finished_quantity) AS finished_quantity,
        min(mddtst.start_time) AS start_time,
        max(mddtst.end_time) AS end_time,
        ROW_NUMBER() OVER (
        PARTITION BY mddtt.id, mddtt.physical_resource_id, mddtt.quantity, mddt.product_id, mddt.product_code
        ORDER BY min(mddtst.start_time)
        ) AS rn
        FROM
        mps_dynamic_delivery_tracking mddt,
        mps_dynamic_delivery_tracking_task mddtt,
        mps_dynamic_delivery_tracking_sub_task mddtst
        WHERE
        mddtst.task_id = mddtt.id
        AND mddt.id = mddtt.dynamic_delivery_tracking_Id
        AND mddt.tracking_status IN ('PUBLISHED', 'IN_EXECUTION')
        GROUP BY
        mddtt.id,
        mddtt.physical_resource_id,
        mddtt.quantity,
        mddt.product_id,
        mddt.product_code
        HAVING
        mddtt.quantity > finished_quantity
        AND (NOW() BETWEEN min(mddtst.start_time) AND max(mddtst.end_time) OR now() > max(mddtst.end_time))
        )
        SELECT
        id,
        physical_resource_id,
        quantity,
        product_id,
        product_code,
        finished_quantity,
        start_time,
        end_time
        FROM grouped_data
        WHERE rn = 1
        <if test="physicalResourceId != null and physicalResourceId != ''">
            and physical_resource_id=#{physicalResourceId,jdbcType=VARCHAR}
        </if>
        ORDER BY start_time
    </select>
    <select id="selectSubTaskByPhysicalResourceId" resultMap="VOResultMap">
        SELECT
        mddtst.id AS id,
        mddtt.physical_resource_id,
        mddtst.start_time AS start_time,
        mddtst.end_time AS end_time,
        mddtst.finished_quantity AS finished_quantity,
        mddtst.planned_quantity AS planned_quantity,
        mddtt.quantity,
        mddt.product_id,
        mddt.product_code,
        mddtst.task_id
        FROM mps_dynamic_delivery_tracking mddt,
        mps_dynamic_delivery_tracking_task mddtt,
        mps_dynamic_delivery_tracking_sub_task mddtst
        WHERE mddtst.task_id = mddtt.id
        AND mddt.id = mddtt.dynamic_delivery_tracking_Id
        AND mddt.tracking_status IN ('PUBLISHED', 'IN_EXECUTION')
        <if test="physicalResourceId != null and physicalResourceId != ''">
            AND mddtt.physical_resource_id = #{physicalResourceId,jdbcType=VARCHAR}
        </if>
        <if test="taskId != null and taskId != ''">
            AND mddtst.task_id = #{taskId,jdbcType=VARCHAR}
        </if>
        ORDER BY start_time
    </select>

    <delete id="deleteByTrackingIds" parameterType="java.util.List">
        delete from mps_dynamic_delivery_tracking_task where dynamic_delivery_tracking_Id in
        <foreach collection="trackingIds" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <select id="selectGroupTask" resultType="map">
        SELECT py.physical_resource_code AS physicalResourceCode,
               py.id                     AS physicalResourceId,
               MAX(CASE
                       WHEN mddt.tracking_status IN ('PUBLISHED', 'IN_EXECUTION') THEN 'true'
                       ELSE 'false'
                   END)                  AS hasTask
        FROM mds_res_physical_resource py
                 LEFT JOIN mps_dynamic_delivery_tracking_task mddtt ON py.id = mddtt.physical_resource_id
                 LEFT JOIN mps_dynamic_delivery_tracking mddt ON mddt.id = mddtt.dynamic_delivery_tracking_id
        WHERE py.standard_resource_id = #{standardResourceId}
        GROUP BY py.physical_resource_code,
                 py.id
    </select>

</mapper>
