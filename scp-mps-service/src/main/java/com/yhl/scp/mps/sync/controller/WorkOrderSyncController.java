package com.yhl.scp.mps.sync.controller;

import cn.hutool.core.util.StrUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mps.dispatch.output.RzzMpsAlgorithmOutput;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.sync.IWorkOrderSync;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>WorkOrderSyncController</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-12 10:02:36
 */
@Api(tags = "MPS-制造订单展开控制器")
@RestController
@RequestMapping("/workOrderSync")
@Slf4j
public class WorkOrderSyncController extends BaseController {

    @Resource
    private IWorkOrderSync workOrderSync;
    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationService operationService;
    @Resource
    IAmsSchedule amsSchedule;

    @ApiOperation(value = "制造订单展开-执行")
    @GetMapping(value = "/execute")
    @Transactional
    public BaseResponse execute(@RequestParam(value = "remark") String remark,
                                @RequestParam(value = "userId") String userId) {
        // TODO 查询制造订单
        List<WorkOrderVO> workOrderVOS = workOrderService.selectAll();
        List<WorkOrderVO> syncWorkOrder = workOrderVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getRemark()) && p.getRemark().equals(remark)).collect(Collectors.toList());
        List<String> ids = syncWorkOrder.stream().map(WorkOrderVO::getId).collect(Collectors.toList());
        RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        rzzMpsAlgorithmOutput.setWorkOrderIds(ids);
        workOrderSync.doSyncOrder(rzzMpsAlgorithmOutput);
        // 自动排程
        MpsAnalysisContext mpsAnalysisContext = new MpsAnalysisContext();
        mpsAnalysisContext.setWorkOrderIds(ids);
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId("hands");
        algorithmLog.setCreator(userId);
        mpsAnalysisContext.setAlgorithmLog(algorithmLog);
        mpsAnalysisContext.setAlgorithmStepLogDTOList(new ArrayList<>());
        amsSchedule.doAmsSchedule(algorithmLog, mpsAnalysisContext);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "制造订单展开-按制造订单id")
    @PostMapping(value = "/executeByWorkOrderIds")
    @Transactional
    public BaseResponse executeByWorkOrderIds(@RequestBody List<String> workOrderIds) {

        RzzMpsAlgorithmOutput rzzMpsAlgorithmOutput = new RzzMpsAlgorithmOutput();
        rzzMpsAlgorithmOutput.setWorkOrderIds(workOrderIds);
        workOrderSync.doSyncOrder(rzzMpsAlgorithmOutput);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

}
