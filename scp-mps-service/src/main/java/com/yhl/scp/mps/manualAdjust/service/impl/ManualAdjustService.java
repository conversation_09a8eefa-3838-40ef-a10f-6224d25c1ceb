package com.yhl.scp.mps.manualAdjust.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.support.ManualAdjustHandler;
import com.yhl.scp.mps.manualAdjust.support.ManualAdjustSupport;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.async.DeferredResult;

/**
 * 手动调整服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ManualAdjustService {

  @Resource private ManualAdjustHandler manualAdjustHandler;

  @Resource private RedisUtil redisUtil;

  @Resource private MdsFeign mdsFeign;

  @Resource private ObjectMapper objectMapper;

  /**
   * 手工进行调整
   *
   * @param param
   * @return
   */
  public BaseResponse<Void> adjust(ManualAdjustParam param) {
    return manualAdjustHandler.handleBackEnd(param);
  }

  /**
   * 调用算法
   *
   * @param pagination
   * @param param
   * @return
   */
  public DeferredResult<BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>>> callAlgorithm(
      Pagination pagination, ManualAdjustParam param) {
    DeferredResult<BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>>> deferredResult =
        new DeferredResult<>(2 * 60 * 1000L);
    String taskId = UUID.randomUUID().toString();
    // 算法互斥锁
    String algorithmLockKey =
        String.format(
            ManualAdjustSupport.ALGORITHM_RUNNING_LOCK_KEY_PREFIX, SystemHolder.getUserId());
    // 请求次数
    String requestQuantityKey =
        String.format(
            ManualAdjustSupport.REQUEST_QUANTITY_LOCK_KEY_PREFIX, SystemHolder.getUserId());
    Object quantityObj = redisUtil.get(requestQuantityKey);
    int requestQuantity = 0;
    if (Objects.isNull(quantityObj)) {
      redisUtil.set(requestQuantityKey, requestQuantity);
    } else {
      requestQuantity = (int) quantityObj + 1;
      redisUtil.set(requestQuantityKey, requestQuantity);
    }
    // 请求阻塞锁
    String requestDeferredKey =
        String.format(ManualAdjustSupport.REQUEST_DEFERRED_MAP_KEY, SystemHolder.getUserId());
    PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
    // 暂存合并参数
    List<ManualAdjustParam> manualAdjustParams = this.mergeParams(param);
    if (!redisUtil.hasKey(algorithmLockKey)) {
      // 不存在算法执行锁，直接执行
      // 参数已取出待执行算法，清空参数
      this.clearParams();
      manualAdjustHandler.handleAlgorithm(
          pagination, planningHorizon, manualAdjustParams, deferredResult, requestQuantity);
    } else {
      // 存在算法执行锁，阻塞
      redisUtil.set(requestDeferredKey, taskId, 2 * 60);
      while (true) {
        try {
          Thread.sleep(200);
          Object o = redisUtil.get(requestDeferredKey);
          String newTaskId = Objects.isNull(o) ? "" : o.toString();
          if (!taskId.equals(newTaskId)) {
            deferredResult.setResult(BaseResponse.success("系统正在执行算法，请等待"));
            break;
          } else {
            if (!redisUtil.hasKey(algorithmLockKey)) {
              // 不存在算法执行锁，直接执行
              // 参数已取出待执行算法，清空参数
              this.clearParams();
              // 加锁，位置待优化
              manualAdjustHandler.resetResourceLock(Lists.newArrayList(), manualAdjustParams);
              manualAdjustHandler.handleAlgorithm(
                  pagination, planningHorizon, manualAdjustParams, deferredResult, requestQuantity);
              break;
            }
          }
        } catch (Exception ex) {
          log.error(ex.getMessage());
        }
      }
    }
    return deferredResult;
  }

  private void clearParams() {
    String algorithmParamKey =
        String.format(
            ManualAdjustSupport.ALGORITHM_RUNNING_PARAM_KEY_PREFIX, SystemHolder.getUserId());
    redisUtil.delete(algorithmParamKey);
  }

  /**
   * 合并参数
   *
   * @param param
   */
  private List<ManualAdjustParam> mergeParams(ManualAdjustParam param) {
    String algorithmParamKey =
        String.format(
            ManualAdjustSupport.ALGORITHM_RUNNING_PARAM_KEY_PREFIX, SystemHolder.getUserId());
    try {
      Object o = redisUtil.get(algorithmParamKey);
      List<ManualAdjustParam> oldValues;
      if (Objects.isNull(o)) {
        oldValues = Lists.newArrayList();
      } else if (o instanceof List) {
        // 如果 RedisUtil 已经反序列化为 List<Map> 或 List<LinkedHashMap>
        oldValues = objectMapper.convertValue(o, new TypeReference<List<ManualAdjustParam>>() {});
      } else if (o instanceof String) {
        // 如果仍然需要手动反序列化字符串
        oldValues =
            objectMapper.readValue(
                (String) o,
                objectMapper
                    .getTypeFactory()
                    .constructCollectionType(List.class, ManualAdjustParam.class));
      } else {
        throw new IllegalStateException("不支持的数据类型: " + o.getClass());
      }
      oldValues.add(param);
      // 序列化后写回 Redis
      String updatedJson = objectMapper.writeValueAsString(oldValues);
      redisUtil.set(algorithmParamKey, updatedJson);

      return oldValues;
    } catch (Exception e) {
      log.error("合并参数失败", e);
      throw new RuntimeException("合并参数失败", e);
    }
  }

  /**
   * 获取主计划动态列部分
   *
   * @param pagination
   * @param param
   * @return
   */
  public BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>> getAdditionalInfo(
      Pagination pagination, ManualAdjustParam param) {
    return null;
  }
}
