package com.yhl.scp.mps.plan.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderQuery;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.plan.convertor.MasterPlanRelationConvertor;
import com.yhl.scp.mps.plan.domain.entity.MasterPlanRelationDO;
import com.yhl.scp.mps.plan.domain.service.MasterPlanRelationDomainService;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanRelationDao;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanRelationPO;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MasterPlanRelationServiceImpl</code>
 * <p>
 * 计划单关联表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-04 15:33:42
 */
@Slf4j
@Service
public class MasterPlanRelationServiceImpl extends AbstractService implements MasterPlanRelationService {

    @Resource
    private MasterPlanRelationDao masterPlanRelationDao;

    @Resource
    private MasterPlanRelationDomainService masterPlanRelationDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(MasterPlanRelationDTO masterPlanRelationDTO) {
        // 0.数据转换
        MasterPlanRelationDO masterPlanRelationDO = MasterPlanRelationConvertor.INSTANCE.dto2Do(masterPlanRelationDTO);
        MasterPlanRelationPO masterPlanRelationPO = MasterPlanRelationConvertor.INSTANCE.dto2Po(masterPlanRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        masterPlanRelationDomainService.validation(masterPlanRelationDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(masterPlanRelationPO);
        masterPlanRelationDao.insert(masterPlanRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(MasterPlanRelationDTO masterPlanRelationDTO) {
        // 0.数据转换
        MasterPlanRelationDO masterPlanRelationDO = MasterPlanRelationConvertor.INSTANCE.dto2Do(masterPlanRelationDTO);
        MasterPlanRelationPO masterPlanRelationPO = MasterPlanRelationConvertor.INSTANCE.dto2Po(masterPlanRelationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        masterPlanRelationDomainService.validation(masterPlanRelationDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(masterPlanRelationPO);
        masterPlanRelationDao.update(masterPlanRelationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MasterPlanRelationDTO> list) {
        List<MasterPlanRelationPO> newList = MasterPlanRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        masterPlanRelationDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MasterPlanRelationDTO> list) {
        List<MasterPlanRelationPO> newList = MasterPlanRelationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        masterPlanRelationDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return masterPlanRelationDao.deleteBatch(idList);
        }
        return masterPlanRelationDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MasterPlanRelationVO selectByPrimaryKey(String id) {
        MasterPlanRelationPO po = masterPlanRelationDao.selectByPrimaryKey(id);
        return MasterPlanRelationConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "MASTER_PLAN_RELATION")
    public List<MasterPlanRelationVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "MASTER_PLAN_RELATION")
    public List<MasterPlanRelationVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MasterPlanRelationVO> dataList = masterPlanRelationDao.selectByCondition(sortParam, queryCriteriaParam);
        MasterPlanRelationServiceImpl target = springBeanUtils.getBean(MasterPlanRelationServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MasterPlanRelationVO> selectByParams(Map<String, Object> params) {
        List<MasterPlanRelationPO> list = masterPlanRelationDao.selectByParams(params);
        return MasterPlanRelationConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MasterPlanRelationVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationService operationService;
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private NewMdsFeign mdsFeign;

    @Override
    public BaseResponse<Void> sync(String scenario, List<ErpPlanOrderQuery> list) {
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.error("接口计划单数据为空");
        }
        List<String> planNos = list.stream().map(ErpPlanOrderQuery::getReqNumber).distinct().collect(Collectors.toList());
        List<String> inventoryItemIds = list.stream().map(ErpPlanOrderQuery::getInventoryItemId).distinct().collect(Collectors.toList());

        Map stockParams = MapUtil.builder()
                .put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
                .put("stockPointType", StockPointTypeEnum.BC.getCode())
                .put("enabled", YesOrNoEnum.YES.getCode()).build();
        List<NewStockPointVO> stockPointVOS = mdsFeign.selectStockPointByParams(scenario, stockParams);
        if(CollectionUtils.isEmpty(stockPointVOS)){
            return BaseResponse.error("库存点信息为空");
        }
        String saleOrgId = stockPointVOS.get(0).getOrganizeId();
        String saleStockPointCode = stockPointVOS.get(0).getStockPointCode();
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of("inventoryItemIds", inventoryItemIds));

        if(CollectionUtils.isEmpty(newProductStockPointVOS)){
            return BaseResponse.error("系统异常，找不到物料");
        }
        Map<String, String> productStockPointVOMap = newProductStockPointVOS.stream()
                .collect(Collectors.toMap(NewProductStockPointVO::getInventoryItemId, NewProductStockPointVO::getProductCode, (v1, v2) -> v1));
        List<MasterPlanRelationDTO> insertMasterPlanRelationDTOS = Lists.newArrayList();
        List<MasterPlanRelationDTO> updateMasterPlanRelationDTOS = Lists.newArrayList();
        List<MasterPlanRelationPO> masterPlanRelationVOS = masterPlanRelationDao.selectByParams(ImmutableMap.of("planNos", planNos));
        if (CollectionUtils.isEmpty(masterPlanRelationVOS)) {
            log.info("没有找到对应的计划单关系数据");
        }
        Map<String, MasterPlanRelationPO> relationVOMap =CollectionUtils.isEmpty(masterPlanRelationVOS)?new HashMap<>():
                masterPlanRelationVOS.stream().collect(Collectors.toMap(t -> t.getPlanNo() + "|" + t.getLineNo(),
                        Function.identity(), (v1, v2) -> v1));
        List<String> closeOrder = new ArrayList<>();
        List<String> closeStatus = ListUtil.of("50", "60");
        for (ErpPlanOrderQuery erpPlanOrderQuery : list) {
            String key = erpPlanOrderQuery.getReqNumber() + "|" + erpPlanOrderQuery.getLineNum();
            log.info("计划单数据key:{}", key);
            if (relationVOMap.containsKey(key)) {
                MasterPlanRelationPO masterPlanRelationPO = relationVOMap.get(key);
                MasterPlanRelationDTO masterPlanRelationDTO = MasterPlanRelationConvertor.INSTANCE.po2Dto(masterPlanRelationPO);
                masterPlanRelationDTO.setPlanStatus(erpPlanOrderQuery.getStatus());
                String reqQty = erpPlanOrderQuery.getReqQuantity();
                String deliveryQty = erpPlanOrderQuery.getDeliveryQuantity();

                masterPlanRelationDTO.setOrderQuantity(StringUtils.isNotEmpty(reqQty) ? new BigDecimal(reqQty) : BigDecimal.ZERO);
                masterPlanRelationDTO.setDeliveryQuantity(StringUtils.isNotEmpty(deliveryQty) ? new BigDecimal(deliveryQty) : BigDecimal.ZERO);

                updateMasterPlanRelationDTOS.add(masterPlanRelationDTO);
                String planStatus = masterPlanRelationDTO.getPlanStatus();
                if (StrUtil.isNotEmpty(planStatus) && closeStatus.contains(planStatus)) {
                    closeOrder.add(masterPlanRelationDTO.getPlanNo());
                }
            }else{
                MasterPlanRelationDTO masterPlanRelationDTO = new MasterPlanRelationDTO();
                masterPlanRelationDTO.setPlanStatus(erpPlanOrderQuery.getStatus());
                String reqQty = erpPlanOrderQuery.getReqQuantity();
                String deliveryQty = erpPlanOrderQuery.getDeliveryQuantity();

                masterPlanRelationDTO.setOrderQuantity(StringUtils.isNotEmpty(reqQty) ? new BigDecimal(reqQty) : BigDecimal.ZERO);
                masterPlanRelationDTO.setDeliveryQuantity(StringUtils.isNotEmpty(deliveryQty) ? new BigDecimal(deliveryQty) : BigDecimal.ZERO);
                masterPlanRelationDTO.setPlanNo(erpPlanOrderQuery.getReqNumber());
                masterPlanRelationDTO.setLineNo(String.valueOf(erpPlanOrderQuery.getLineNum()));
                masterPlanRelationDTO.setPlanStatus(erpPlanOrderQuery.getStatus());
                masterPlanRelationDTO.setOrderNo(erpPlanOrderQuery.getReqNumber());
                if(productStockPointVOMap.containsKey(erpPlanOrderQuery.getInventoryItemId())){
                    masterPlanRelationDTO.setProductCode(productStockPointVOMap.get(erpPlanOrderQuery.getInventoryItemId()));
                }else{
                    log.error("没有找到对应的物料信息，接口物料编码id：{}", erpPlanOrderQuery.getInventoryItemId());
                    continue;
                }
                masterPlanRelationDTO.setStockPointCode(saleStockPointCode);
                masterPlanRelationDTO.setEnabled(YesOrNoEnum.YES.getCode());
                masterPlanRelationDTO.setOrgId(saleOrgId);

                insertMasterPlanRelationDTOS.add(masterPlanRelationDTO);

            }
        }
        if (CollectionUtils.isNotEmpty(insertMasterPlanRelationDTOS)) {
            this.doCreateBatch(insertMasterPlanRelationDTOS);
            log.info("新增计划单关联表成功,数量：{}", insertMasterPlanRelationDTOS.size());
        }
        if (CollectionUtils.isNotEmpty(updateMasterPlanRelationDTOS)) {
            this.doUpdateBatch(updateMasterPlanRelationDTOS);
            log.info("更新计划单关联表成功,更新计划单关联表条数：{}", updateMasterPlanRelationDTOS.size());
        }
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(closeOrder)) {
            List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("orderNos", closeOrder));
            if(CollectionUtils.isNotEmpty(workOrderVOS)){
                List<String> parentIds = StreamUtils.columnToList(workOrderVOS.stream().filter(
                                p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.toList())
                        , WorkOrderVO::getParentId);
                // 查询制造订单下的半品制造订单
                if (CollectionUtils.isNotEmpty(parentIds)) {
                    List<WorkOrderVO> childWorkOrderVOS = workOrderService.selectByParams(ImmutableMap.of("ids", parentIds));
                    workOrderVOS.addAll(childWorkOrderVOS);
                }
                // 关闭计划单
                List<String> orderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
                if(CollectionUtils.isNotEmpty(orderIds)){
                    List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("orderIds", orderIds));
                    if(CollectionUtils.isNotEmpty(operationVOS)){
                        List<String> operationIds = StreamUtils.columnToList(operationVOS, OperationVO::getId);
//                        masterPlanService.doBatchClose(operationIds);
                    }
                }
            }
        }
        return BaseResponse.success();
    }

    @Override
    public BaseResponse<Void> syncPlanOrder(String tenantId) {
        Map stockParams = MapUtil.builder().put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode()).put("stockPointType", StockPointTypeEnum.BC.getCode())
                .put("enabled", YesOrNoEnum.YES.getCode()).build();
        BaseResponse<String> scenario = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode(), tenantId);
        List<NewStockPointVO> newStockPointVOS = newMdsFeign.selectStockPointByParams(scenario.getData(), stockParams);
        Map<String, Object> params = MapUtil.newHashMap();

        if (CollectionUtils.isEmpty(newStockPointVOS)) {
            return BaseResponse.error("库存点信息为空");
        }
        List<String> orgIds = newStockPointVOS.stream().
                filter(t -> StringUtils.isNotEmpty(t.getOrganizeId()) && StringUtils.isNotEmpty(t.getInterfaceFlag())).
                filter(t -> t.getInterfaceFlag().contains(ApiCategoryEnum.PLAN_ORDER_QUERY.getCode())).
                map(NewStockPointVO::getOrganizeId).collect(Collectors.toList());

        for (String orgId : orgIds) {
            log.info("开始同步组织:{}的ERP计划单记录：", orgId);
            params.put("orgId", Integer.valueOf(orgId));
            newDcpFeign.callExternalApi(tenantId, ApiSourceEnum.ERP.getCode(),
                    ApiCategoryEnum.PLAN_ORDER_QUERY.getCode(), params);
        }

        return BaseResponse.success("同步成功");
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MASTER_PLAN_RELATION.getCode();
    }

    @Override
    public List<MasterPlanRelationVO> invocation(List<MasterPlanRelationVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
