package com.yhl.scp.mps.operationPublished.infrastructure.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.yhl.platform.common.ddd.BasePO;

/**
 * <code>WorkOrderPublishedPO</code>
 * <p>
 * 制造订单发布信息表PO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:29:38
 */
public class WorkOrderPublishedPO extends BasePO implements Serializable {

    private static final long serialVersionUID = 662426644157244375L;

        /**
     * 制造订单号
     */
        private String orderNo;
        /**
     * 库存点物品ID
     */
        private String productStockPointId;
        /**
     * 库存点ID
     */
        private String stockPointId;
        /**
     * 物品ID
     */
        private String productId;
        /**
     * 生产组织ID
     */
        private String organizationId;
        /**
     * 父订单ID
     */
        private String parentId;
        /**
     * BOM版本ID
     */
        private String bomVersionId;
        /**
     * 上层订单ID
     */
        private String upperOrderId;
        /**
     * 顶层订单ID
     */
        private String topOrderId;
        /**
     * 下层订单ID
     */
        private String lowerOrderId;
        /**
     * 底层订单ID
     */
        private String bottomOrderId;
        /**
     * 是否拆批
     */
        private String batchSplit;
        /**
     * 数量
     */
        private BigDecimal quantity;
        /**
     * 金额
     */
        private BigDecimal amount;
        /**
     * 下单时间
     */
        private Date orderTime;
        /**
     * 交期
     */
        private Date dueDate;
        /**
     * 延期惩罚
     */
        private BigDecimal delayPenalty;
        /**
     * 订单状态
     */
        private String orderStatus;
        /**
     * 优先级
     */
        private Integer priority;
        /**
     * 是否延期
     */
        private String delayStatus;
        /**
     * 是否固定
     */
        private String fixed;
        /**
     * 是否反馈
     */
        private String feedbackStatus;
        /**
     * 是否超产
     */
        private String overProduction;
        /**
     * 是否允许重新同步
     */
        private String resyncAllowed;
        /**
     * 计划状态
     */
        private String planStatus;
        /**
     * 齐套状态
     */
        private String kitStatus;
        /**
     * 同步状态
     */
        private String syncStatus;
        /**
     * 开始时间
     */
        private Date startTime;
        /**
     * 结束时间
     */
        private Date endTime;
        /**
     * 最早开始时间
     */
        private Date earliestStartTime;
        /**
     * 计算的最早开始时间
     */
        private Date calcEarliestStartTime;
        /**
     * 计算的最晚结束时间
     */
        private Date calcLatestEndTime;
        /**
     * 是否可排
     */
        private String plannable;
        /**
     * 是否指定可排
     */
        private String appointPlannable;
        /**
     * 路径ID
     */
        private String routingId;
        /**
     * 指定路径ID
     */
        private String appointRoutingId;
        /**
     * 路径类型
     */
        private String routingType;
        /**
     * 提前期
     */
        private Integer leadTime;
        /**
     * 指定提前期
     */
        private Integer appointLeadTime;
        /**
     * 指定客户ID
     */
        private String appointCustomerId;
        /**
     * 指定需求类型
     */
        private String appointDemandType;
        /**
     * 指定需求订单ID
     */
        private String appointDemandOrderId;
        /**
     * 指定客户订单
     */
        private String appointCustomerOrderId;
        /**
     * 指定父制造订单
     */
        private String appointParentWorkOrderId;
        /**
     * 同步失败原因
     */
        private String syncFailureReason;
        /**
     * 入库数量
     */
        private BigDecimal stockingQuantity;
        /**
     * 计划投产日期
     */
        private Date plannedProductionDate;
        /**
     * 计划产出日期
     */
        private Date plannedOutputDate;
        /**
     * 已分配量
     */
        private BigDecimal fulfilledQuantity;
        /**
     * 分配信息
     */
        private String fulfillmentInfo;
        /**
     * 可分配量
     */
        private BigDecimal unfulfilledQuantity;
        /**
     * 分配状态
     */
        private String fulfillmentStatus;
        /**
     * 计数单位ID
     */
        private String countingUnitId;
        /**
     * 货币单位ID
     */
        private String currencyUnitId;
        /**
     * 是否参与排程
     */
        private String participateScheduling;
        /**
     * 固定数量状态
     */
        private String fixedQuantityStatus;
        /**
     * 与子订单数量是否一致
     */
        private String quantityConsistent;
        /**
     * 是否工序冲突
     */
        private String operationConflict;
        private String bomType;
        private String plannedProductionPlaceId;
        private Date plannedTime;
        private String dispatchPlaceId;
        /**
     * 期末库存最小安全库存差
     */
        private Integer endingInventoryMinSafeDiff;
        /**
     * 需求类型
     */
        private String demandCategory;

    /**
     * 主生产计划发布日志id
     */
    private String publishedLogId;

    /**
     * 源数据id
     */
    private String id;

    /**
     * 源数据id
     */
    private String newId;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 试制单号
     */
    private String testOrderNumber;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getProductStockPointId() {
        return productStockPointId;
    }

    public void setProductStockPointId(String productStockPointId) {
        this.productStockPointId = productStockPointId;
    }

    public String getStockPointId() {
        return stockPointId;
    }

    public void setStockPointId(String stockPointId) {
        this.stockPointId = stockPointId;
    }

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOrganizationId() {
        return organizationId;
    }

    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    public String getParentId() {
        return parentId;
    }

    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    public String getBomVersionId() {
        return bomVersionId;
    }

    public void setBomVersionId(String bomVersionId) {
        this.bomVersionId = bomVersionId;
    }

    public String getUpperOrderId() {
        return upperOrderId;
    }

    public void setUpperOrderId(String upperOrderId) {
        this.upperOrderId = upperOrderId;
    }

    public String getTopOrderId() {
        return topOrderId;
    }

    public void setTopOrderId(String topOrderId) {
        this.topOrderId = topOrderId;
    }

    public String getLowerOrderId() {
        return lowerOrderId;
    }

    public void setLowerOrderId(String lowerOrderId) {
        this.lowerOrderId = lowerOrderId;
    }

    public String getBottomOrderId() {
        return bottomOrderId;
    }

    public void setBottomOrderId(String bottomOrderId) {
        this.bottomOrderId = bottomOrderId;
    }

    public String getBatchSplit() {
        return batchSplit;
    }

    public void setBatchSplit(String batchSplit) {
        this.batchSplit = batchSplit;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Date getDueDate() {
        return dueDate;
    }

    public void setDueDate(Date dueDate) {
        this.dueDate = dueDate;
    }

    public BigDecimal getDelayPenalty() {
        return delayPenalty;
    }

    public void setDelayPenalty(BigDecimal delayPenalty) {
        this.delayPenalty = delayPenalty;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public String getDelayStatus() {
        return delayStatus;
    }

    public void setDelayStatus(String delayStatus) {
        this.delayStatus = delayStatus;
    }

    public String getFixed() {
        return fixed;
    }

    public void setFixed(String fixed) {
        this.fixed = fixed;
    }

    public String getFeedbackStatus() {
        return feedbackStatus;
    }

    public void setFeedbackStatus(String feedbackStatus) {
        this.feedbackStatus = feedbackStatus;
    }

    public String getOverProduction() {
        return overProduction;
    }

    public void setOverProduction(String overProduction) {
        this.overProduction = overProduction;
    }

    public String getResyncAllowed() {
        return resyncAllowed;
    }

    public void setResyncAllowed(String resyncAllowed) {
        this.resyncAllowed = resyncAllowed;
    }

    public String getPlanStatus() {
        return planStatus;
    }

    public void setPlanStatus(String planStatus) {
        this.planStatus = planStatus;
    }

    public String getKitStatus() {
        return kitStatus;
    }

    public void setKitStatus(String kitStatus) {
        this.kitStatus = kitStatus;
    }

    public String getSyncStatus() {
        return syncStatus;
    }

    public void setSyncStatus(String syncStatus) {
        this.syncStatus = syncStatus;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Date getEarliestStartTime() {
        return earliestStartTime;
    }

    public void setEarliestStartTime(Date earliestStartTime) {
        this.earliestStartTime = earliestStartTime;
    }

    public Date getCalcEarliestStartTime() {
        return calcEarliestStartTime;
    }

    public void setCalcEarliestStartTime(Date calcEarliestStartTime) {
        this.calcEarliestStartTime = calcEarliestStartTime;
    }

    public Date getCalcLatestEndTime() {
        return calcLatestEndTime;
    }

    public void setCalcLatestEndTime(Date calcLatestEndTime) {
        this.calcLatestEndTime = calcLatestEndTime;
    }

    public String getPlannable() {
        return plannable;
    }

    public void setPlannable(String plannable) {
        this.plannable = plannable;
    }

    public String getAppointPlannable() {
        return appointPlannable;
    }

    public void setAppointPlannable(String appointPlannable) {
        this.appointPlannable = appointPlannable;
    }

    public String getRoutingId() {
        return routingId;
    }

    public void setRoutingId(String routingId) {
        this.routingId = routingId;
    }

    public String getAppointRoutingId() {
        return appointRoutingId;
    }

    public void setAppointRoutingId(String appointRoutingId) {
        this.appointRoutingId = appointRoutingId;
    }

    public String getRoutingType() {
        return routingType;
    }

    public void setRoutingType(String routingType) {
        this.routingType = routingType;
    }

    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    public Integer getAppointLeadTime() {
        return appointLeadTime;
    }

    public void setAppointLeadTime(Integer appointLeadTime) {
        this.appointLeadTime = appointLeadTime;
    }

    public String getAppointCustomerId() {
        return appointCustomerId;
    }

    public void setAppointCustomerId(String appointCustomerId) {
        this.appointCustomerId = appointCustomerId;
    }

    public String getAppointDemandType() {
        return appointDemandType;
    }

    public void setAppointDemandType(String appointDemandType) {
        this.appointDemandType = appointDemandType;
    }

    public String getAppointDemandOrderId() {
        return appointDemandOrderId;
    }

    public void setAppointDemandOrderId(String appointDemandOrderId) {
        this.appointDemandOrderId = appointDemandOrderId;
    }

    public String getAppointCustomerOrderId() {
        return appointCustomerOrderId;
    }

    public void setAppointCustomerOrderId(String appointCustomerOrderId) {
        this.appointCustomerOrderId = appointCustomerOrderId;
    }

    public String getAppointParentWorkOrderId() {
        return appointParentWorkOrderId;
    }

    public void setAppointParentWorkOrderId(String appointParentWorkOrderId) {
        this.appointParentWorkOrderId = appointParentWorkOrderId;
    }

    public String getSyncFailureReason() {
        return syncFailureReason;
    }

    public void setSyncFailureReason(String syncFailureReason) {
        this.syncFailureReason = syncFailureReason;
    }

    public BigDecimal getStockingQuantity() {
        return stockingQuantity;
    }

    public void setStockingQuantity(BigDecimal stockingQuantity) {
        this.stockingQuantity = stockingQuantity;
    }

    public Date getPlannedProductionDate() {
        return plannedProductionDate;
    }

    public void setPlannedProductionDate(Date plannedProductionDate) {
        this.plannedProductionDate = plannedProductionDate;
    }

    public Date getPlannedOutputDate() {
        return plannedOutputDate;
    }

    public void setPlannedOutputDate(Date plannedOutputDate) {
        this.plannedOutputDate = plannedOutputDate;
    }

    public BigDecimal getFulfilledQuantity() {
        return fulfilledQuantity;
    }

    public void setFulfilledQuantity(BigDecimal fulfilledQuantity) {
        this.fulfilledQuantity = fulfilledQuantity;
    }

    public String getFulfillmentInfo() {
        return fulfillmentInfo;
    }

    public void setFulfillmentInfo(String fulfillmentInfo) {
        this.fulfillmentInfo = fulfillmentInfo;
    }

    public BigDecimal getUnfulfilledQuantity() {
        return unfulfilledQuantity;
    }

    public void setUnfulfilledQuantity(BigDecimal unfulfilledQuantity) {
        this.unfulfilledQuantity = unfulfilledQuantity;
    }

    public String getFulfillmentStatus() {
        return fulfillmentStatus;
    }

    public void setFulfillmentStatus(String fulfillmentStatus) {
        this.fulfillmentStatus = fulfillmentStatus;
    }

    public String getCountingUnitId() {
        return countingUnitId;
    }

    public void setCountingUnitId(String countingUnitId) {
        this.countingUnitId = countingUnitId;
    }

    public String getCurrencyUnitId() {
        return currencyUnitId;
    }

    public void setCurrencyUnitId(String currencyUnitId) {
        this.currencyUnitId = currencyUnitId;
    }

    public String getParticipateScheduling() {
        return participateScheduling;
    }

    public void setParticipateScheduling(String participateScheduling) {
        this.participateScheduling = participateScheduling;
    }

    public String getFixedQuantityStatus() {
        return fixedQuantityStatus;
    }

    public void setFixedQuantityStatus(String fixedQuantityStatus) {
        this.fixedQuantityStatus = fixedQuantityStatus;
    }

    public String getQuantityConsistent() {
        return quantityConsistent;
    }

    public void setQuantityConsistent(String quantityConsistent) {
        this.quantityConsistent = quantityConsistent;
    }

    public String getOperationConflict() {
        return operationConflict;
    }

    public void setOperationConflict(String operationConflict) {
        this.operationConflict = operationConflict;
    }

    public String getBomType() {
        return bomType;
    }

    public void setBomType(String bomType) {
        this.bomType = bomType;
    }

    public String getPlannedProductionPlaceId() {
        return plannedProductionPlaceId;
    }

    public void setPlannedProductionPlaceId(String plannedProductionPlaceId) {
        this.plannedProductionPlaceId = plannedProductionPlaceId;
    }

    public Date getPlannedTime() {
        return plannedTime;
    }

    public void setPlannedTime(Date plannedTime) {
        this.plannedTime = plannedTime;
    }

    public String getDispatchPlaceId() {
        return dispatchPlaceId;
    }

    public void setDispatchPlaceId(String dispatchPlaceId) {
        this.dispatchPlaceId = dispatchPlaceId;
    }

    public Integer getEndingInventoryMinSafeDiff() {
        return endingInventoryMinSafeDiff;
    }

    public void setEndingInventoryMinSafeDiff(Integer endingInventoryMinSafeDiff) {
        this.endingInventoryMinSafeDiff = endingInventoryMinSafeDiff;
    }

    public String getDemandCategory() {
        return demandCategory;
    }

    public void setDemandCategory(String demandCategory) {
        this.demandCategory = demandCategory;
    }

	public String getPublishedLogId() {
		return publishedLogId;
	}

	public void setPublishedLogId(String publishedLogId) {
		this.publishedLogId = publishedLogId;
	}

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public String getNewId() {
		return newId;
	}

	public void setNewId(String newId) {
		this.newId = newId;
	}

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public String getTestOrderNumber() {
        return testOrderNumber;
    }

    public void setTestOrderNumber(String testOrderNumber) {
        this.testOrderNumber = testOrderNumber;
    }
}
