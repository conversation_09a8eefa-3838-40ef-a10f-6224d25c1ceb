package com.yhl.scp.mps.dispatch.controller;


import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.ImmutableMap;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.api.runner.APSRunner;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.topic.MessageConstants;
import com.yhl.scp.biz.common.topic.RedissonPubSubUtil;
import com.yhl.scp.biz.common.util.FileZipUtils;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataService;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "测试算法控制器")
@RestController
@RequestMapping("/testAlgo")
@Slf4j
public class TestAlgoController extends BaseController {


    @Resource
    private RzzBaseAlgorithmDataService rzzBaseAlgorithmDataService;


    @Resource
    private IAmsSchedule amsSchedule;

    @Resource
    private WorkOrderService workOrderService;
    @Resource
    private OperationService operationService;
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;

    @Resource
    private RedissonPubSubUtil redissonPubSubUtil;

    @GetMapping("downloadZip")
    @ApiOperation(value = "下载文件")
    public ResponseEntity<Object> downloadZip(@RequestParam("directoryPath") String directoryPath,
                                              @RequestParam("logId") String logId) {
        List<String> checkPath = ListUtil.of("D:\\testFile\\MPS", "D:\\testFile\\DFP", "D:\\testFile\\AMS");
        List<String> fileTypes = ListUtil.of("txt", "log", "xlsx","csv");
        return FileZipUtils.downloadZip(directoryPath, logId, checkPath, fileTypes);
    }

    @ApiOperation(value = "测试pub")
    @PostMapping(value = "/testPub")
    public BaseResponse testPub(String chanel, String msg) {
        String jsonString = JSON.toJSONString("");
        redissonPubSubUtil.sendMessage(MessageConstants.CAPACITY_OVERFLOW, jsonString);
        return BaseResponse.success();
    }

    @ApiOperation(value = "测试完工")
    @PostMapping(value = "/testClose")
    @Transactional
    public BaseResponse testClose() {
        mpsProReportingFeedbackService.doProcessStartOperation();
        return BaseResponse.success();
    }

    @ApiOperation(value = "测试计划单关闭")
    @PostMapping(value = "/close")
    @Transactional
    public BaseResponse<Void> sync(@RequestBody List<String> list) {
        if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(list)) {
            List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("orderNos", list));
            List<String> parentIds = StreamUtils.columnToList(workOrderVOS.stream().filter(p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.toList())
                    , WorkOrderVO::getParentId);
            // 查询制造订单下的半品制造订单
            if (CollectionUtils.isNotEmpty(parentIds)) {
                List<WorkOrderVO> childWorkOrderVOS = workOrderService.selectByParams(ImmutableMap.of("ids", parentIds));
                workOrderVOS.addAll(childWorkOrderVOS);
            }
            // 关闭计划单
            List<String> orderIds = StreamUtils.columnToList(workOrderVOS, WorkOrderVO::getId);
            List<OperationVO> operationVOS = operationService.selectByParams(ImmutableMap.of("orderIds", orderIds));
            List<String> operationIds = StreamUtils.columnToList(operationVOS, OperationVO::getId);
            masterPlanService.doBatchClose(operationIds);
        }
        return BaseResponse.success();
    }

    @ApiOperation(value = "测试计划调整")
    @PostMapping(value = "/adjust")
    @Transactional
    public BaseResponse adjust(@RequestBody List<String> workOrderIds) {
        amsSchedule.doAdjustPlan(workOrderIds);
        return BaseResponse.success();
    }

    @ApiOperation(value = "测试包装工序时间")
    @GetMapping(value = "/testTime")
    public BaseResponse testTime(String id) {
        amsSchedule.test(id);
        return BaseResponse.success();
    }

    @ApiOperation(value = "生成AMS算法输入测试")
    @GetMapping(value = "/amsInput")
    public BaseResponse testAmsInput() {
        APSInput apsInput = rzzBaseAlgorithmDataService.test();
        APSRunner apsRunner = new APSRunner();
        // 调用算法
        APSOutput apsOutput = apsRunner.run(apsInput);
        return BaseResponse.success();
    }


}
