package com.yhl.scp.mps.schedule.service.impl;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.*;
import com.yhl.platform.component.custom.Expression;
import com.yhl.platform.component.gantt.Gantt;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.mds.basic.enums.AssignQtyTypeEnum;
import com.yhl.scp.mds.basic.enums.SubTaskTypeEnum;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.coating.service.CoatingMaintenanceSettingsService;
import com.yhl.scp.mps.coating.vo.CoatingMaintenanceSettingsVO;
import com.yhl.scp.mps.schedule.service.AmsResourceLoadService;
import com.yhl.scp.mps.schedule.vo.GanttReq;
import com.yhl.scp.mps.util.DatePairUtil;
import com.yhl.scp.sds.basic.order.vo.OperationSubTaskBasicVO;
import com.yhl.scp.sds.extension.order.vo.OperationSubTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.resourceload.vo.ResourceLoadGanttVO;
import com.yhl.scp.sds.feign.SdsFeign;
import com.yhl.scp.sds.feign.SdsScheduleFeign;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.OperationSubTaskService;
import com.yhl.scp.sds.resourceload.gantt.Block;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ResourceLoadServiceImpl</code>
 * <p>
 * 资源负荷
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-14 15:44:32
 */
@Service
@Slf4j
public class AmsResourceLoadServiceImpl implements AmsResourceLoadService {

    @Resource
    SpringBeanUtils springBeanUtils;

    @Resource
    MdsFeign mdsFeign;

    @Resource
    NewMdsFeign newMdsFeign;

    @Resource
    IpsNewFeign ipsNewFeign;

    @Resource
    SdsFeign sdsFeign;

    @Resource
    private SdsScheduleFeign sdsScheduleFeign;

    @Resource
    private OperationService operationService;

    @Resource
    private OperationSubTaskService operationSubTaskService;

    @Resource
    private CoatingMaintenanceSettingsService coatingMaintenanceSettingsService;


    @Override
    public Gantt getGanttResourceLoadInfo(List<String> properties, List<String> physicalResourceIds, GanttReq ganttSortParam) {
        AmsResourceLoadServiceImpl target = springBeanUtils.getBean(AmsResourceLoadServiceImpl.class);
        List<ResourceLoadGanttVO> resourceLoadGanttVOS = target.getGantt(properties, physicalResourceIds, ganttSortParam);
        Map<String, List<ResourceLoadGanttVO>> keyToResourceLoadGanttsMaps =
                resourceLoadGanttVOS.stream().collect(Collectors.groupingBy(ResourceLoadGanttVO::getKey));
        List<com.yhl.platform.component.gantt.Process> componentProcesses = new ArrayList<>();
        Date startDate = null;
        Date endDate = null;
        for (Map.Entry<String, List<ResourceLoadGanttVO>> entry : keyToResourceLoadGanttsMaps.entrySet()) {
            String k = entry.getKey();
            List<ResourceLoadGanttVO> v = entry.getValue();
            com.yhl.platform.component.gantt.Process process1 = new com.yhl.platform.component.gantt.Process();
            process1.setKey(k);
            List<com.yhl.platform.component.gantt.Block> componentBlocks = new ArrayList<>();
            for (ResourceLoadGanttVO resourceLoadGanttVO : v) {
                com.yhl.platform.component.gantt.Block block1 = new com.yhl.platform.component.gantt.Block();
                block1.setKey(resourceLoadGanttVO.getKey());
                Date periodStart = resourceLoadGanttVO.getPeriodStart();
                startDate = startDate == null ? periodStart : getMinDate(startDate, periodStart);
                Date periodEnd = resourceLoadGanttVO.getPeriodEnd();
                endDate = endDate == null ? periodEnd : getMaxDate(endDate, periodEnd);
                block1.setStartTime(periodStart);
                block1.setEndTime(periodEnd);
                Map<String, Object> customMap = resourceLoadGanttVO.getCustomMap();
                customMap.put("load", resourceLoadGanttVO.getPercent());
                customMap.put("availableTime", resourceLoadGanttVO.getAvailableTime());
                customMap.put("workTime", resourceLoadGanttVO.getWorkTime());
                customMap.put("operationTaskIds", resourceLoadGanttVO.getOperationTaskIds());
                customMap.put("coatingMaintenanceSettingId", resourceLoadGanttVO.getCoatingMaintenanceSettingId());
                customMap.put("coatingStartTime", resourceLoadGanttVO.getStartTime());
                customMap.put("coatingEndTime", resourceLoadGanttVO.getEndTime());
                customMap.put("coatFlag", resourceLoadGanttVO.getCoatFlag());
                block1.setCustomMap(customMap);
                componentBlocks.add(block1);
            }
            process1.setBlocks(componentBlocks);
            componentProcesses.add(process1);
        }

        Gantt resultGantt = new Gantt();
        resultGantt.setStartDate(startDate);
        resultGantt.setEndDate(endDate);
        resultGantt.setProcesses(componentProcesses);
        return resultGantt;
    }

    private Date getMinDate(Date date1, Date date2) {
        return date1.before(date2) ? date1 : date2;
    }

    private Date getMaxDate(Date date1, Date date2) {
        return date1.before(date2) ? date2 : date1;
    }


    @Expression("RESOURCE_LOAD_GANTT_VO")
    @Override
    public List<ResourceLoadGanttVO> getGantt(List<String> properties, List<String> physicalResourceIds, GanttReq ganttSortParam) {
        if (CollectionUtils.isEmpty(physicalResourceIds)) {
            return new ArrayList<>();
        }

        StopWatch stopWatch = new StopWatch("获取甘特图数据");
        // 物理资源
        stopWatch.start("查询物理资源");
        List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.getPhysicalResourcesByParams(ImmutableMap.of("ids",
                physicalResourceIds));
        stopWatch.stop();
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            return new ArrayList<>();
        }
        // 查询资源对应的工序生产任务
        List<OperationSubTaskVO> operationSubTaskVOS = operationSubTaskService.selectByParams(ImmutableMap.of(
                "physicalResourceIds", physicalResourceIds));

        String mdsScenario = getMdsScenario();
        // 生产任务列表
        List<OperationSubTaskVO> productionTaskList =
                operationSubTaskVOS.stream().filter(vo -> SubTaskTypeEnum.WORK.getCode().equals(vo.getTaskType())).collect(Collectors.toList());
        Date planGanttStartTime = ganttSortParam.getStartTime();
        Date planGanttEndTime = ganttSortParam.getEndTime();
        if (null != planGanttStartTime && null != planGanttEndTime) {
            productionTaskList = productionTaskList.stream().filter(p -> p.getStartTime().getTime() >= planGanttStartTime.getTime()
                    && p.getEndTime().getTime() <= planGanttEndTime.getTime()).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(productionTaskList)) {
            return new ArrayList<>();
        }
        // 物理资源到生产任务映射
        Map<String, List<OperationSubTaskVO>> physicalResourceIdToWorkTaskMaps =
                productionTaskList.stream().collect(Collectors.groupingBy(OperationSubTaskVO::getPhysicalResourceId));
        // 根据资源ID列表获取对应的资源日历信息
        List<ResourceCalendarVO> calendarList = newMdsFeign.selectByPhysicalResourceIds(mdsScenario,
                physicalResourceIds);
        // 资源日历按照资源分组
        Map<String, List<ResourceCalendarVO>> resourceIdToCalendarMap =
                calendarList.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getPhysicalResourceId));
        // 查询工序信息
        List<String> operationIds =
                productionTaskList.stream().map(OperationSubTaskVO::getOperationId).collect(Collectors.toList());
        // 获取工序信息
        List<OperationVO> operationVOList = operationService.getVOListByIds(operationIds);
        Map<String, OperationVO> operationVOMap = operationVOList.stream().collect(Collectors
                .toMap(OperationVO::getId, Function.identity(),(v1, v2) -> v2));
        // 获取工序任务到工序id的映射关系
        Map<String, OperationVO> taskIdToOperationMaps =
                operationSubTaskVOS.stream().collect(Collectors.toMap(OperationSubTaskBasicVO::getTaskId,
                        v -> operationVOMap.get(v.getOperationId()), (v1, v2) -> v1));

        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();
        List<Date> intervalDates = DateUtils.getIntervalDates(historyRetrospectStartTime, planEndTime);

        // 获取镀膜维保信息
        Map<String, Object> params = new HashMap<>();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("physicalResourceIds", physicalResourceIds);
        List<CoatingMaintenanceSettingsVO> coatingMaintenanceSettingsVOS =
                coatingMaintenanceSettingsService.selectByParams(params);
        Map<String, List<CoatingMaintenanceSettingsVO>> coatMap =
                coatingMaintenanceSettingsVOS.stream().collect(Collectors.groupingBy(CoatingMaintenanceSettingsVO::getPhysicalResourceId));

        // 存放物理资源每天工作时间段数据
        stopWatch.start("获取任务资源对应的可用工作时间");
        Map<String, List<Pair<Date, Date>>> physicalResourceIdToSplitDatePairMap = new HashMap<>();
        for (String physicalResourceId : resourceIdToCalendarMap.keySet()) {
            List<ResourceCalendarVO> resourceCalendars = resourceIdToCalendarMap.get(physicalResourceId);
            List<ResourceCalendarVO> normalList =
                    resourceCalendars.stream().filter(vo -> "NORMAL".equals(vo.getCalendarType())).collect(Collectors.toList());
            List<Pair<Date, Date>> dateAllList = new ArrayList<>();
            for (ResourceCalendarVO resourceCalendarVO : normalList) {
                Date startTime = resourceCalendarVO.getStartTime();
                Date endTime = resourceCalendarVO.getEndTime();
                dateAllList.addAll(DatePairUtil.splitTimeByDay(startTime, endTime));
            }
            physicalResourceIdToSplitDatePairMap.put(physicalResourceId, dateAllList);
        }
        stopWatch.stop();

        // 任务对应工作时间
        // key为工序任务id,注意不是工序子任务
        // value为该工序任务在时间轴单元格内的工作时间，比如一个工序任务可能跨三个时间轴单元格生产，那么value就是每个单元格中的生产开始结束时间
        // 注意：value中的时间是剔除了异常时间的
        Map<String, List<Pair<Date, Date>>> taskListMap = new HashMap<>();
        stopWatch.start("获取任务对应的工作时间");
        for (OperationSubTaskVO operationSubTaskVO : productionTaskList) {
            Date startTime = operationSubTaskVO.getStartTime();
            Date endTime = operationSubTaskVO.getEndTime();
            List<Pair<Date, Date>> dateList = new ArrayList<>();
            dateList.addAll(DatePairUtil.splitTimeByDay(startTime, endTime));
            // 根据查询维度对时间进行拆分
            List<Pair<Date, Date>> pairs = taskListMap.get(operationSubTaskVO.getTaskId());
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(pairs)) {
                taskListMap.put(operationSubTaskVO.getTaskId(), dateList);
            } else {
                pairs.addAll(dateList);
                taskListMap.put(operationSubTaskVO.getTaskId(), pairs);
            }
        }
        stopWatch.stop();

        Map<String, PhysicalResourceVO> processMap = physicalResourceVOS.stream().collect(Collectors
            .toMap(k -> getProcessKey(k, properties), Function.identity()));
        stopWatch.start("gantt数据组装");
        List<ResourceLoadGanttVO> resourceLoadGanttVOS = new ArrayList<>();
        for (Map.Entry<String, PhysicalResourceVO> entry : processMap.entrySet()) {
            String processKey = entry.getKey();
            PhysicalResourceVO physicalResourceVO = entry.getValue();
            com.yhl.scp.sds.resourceload.gantt.Process process = new com.yhl.scp.sds.resourceload.gantt.Process();
            process.setProcessId(processKey);
            List<OperationSubTaskVO> operationSubTasks =
                    physicalResourceIdToWorkTaskMaps.get(physicalResourceVO.getId());
            List<Block> blocks = getBlocks(physicalResourceVO, intervalDates,
                    physicalResourceIdToSplitDatePairMap.get(physicalResourceVO.getId()),
                    taskListMap, operationSubTasks, taskIdToOperationMaps, coatMap);
            for (Block block : blocks) {
                ResourceLoadGanttVO resourceLoadGantt = block.getResourceLoadGantt();
                resourceLoadGantt.setKey(processKey);
                resourceLoadGantt.setPeriodStart(block.getStartTime());
                resourceLoadGantt.setPeriodEnd(block.getEndTime());
                resourceLoadGanttVOS.add(resourceLoadGantt);
            }
        }
        stopWatch.stop();
        log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));

        return resourceLoadGanttVOS;
    }

    /**
     * TODO
     *
     * @param physicalResourceVO
     * @param intervalDates
     * @param resourceCalendars
     * @param operationTaskIdToWorkTimePairs 工序任务总工作时间（分段）
     * @param operationSubTasks
     * @param taskIdToOperationMaps
     * @param coatMap
     * @return java.util.List<com.yhl.scp.sds.resourceload.gantt.Block>
     */
    @Expression("RESOURCE_LOAD_GANTT_VO")
    private List<Block> getBlocks(PhysicalResourceVO physicalResourceVO, List<Date> intervalDates,
                                  List<Pair<Date, Date>> resourceCalendars,
                                  Map<String, List<Pair<Date, Date>>> operationTaskIdToWorkTimePairs,
                                  List<OperationSubTaskVO> operationSubTasks,
                                  Map<String, OperationVO> taskIdToOperationMaps,
                                  Map<String, List<CoatingMaintenanceSettingsVO>> coatMap) {
        List<Block> blocks = new ArrayList<>();
        if (CollectionUtils.isEmpty(resourceCalendars)) {
            return blocks;
        }
        List<CoatingMaintenanceSettingsVO> coatingMaintenanceSettingsVOS = coatMap.get(physicalResourceVO.getId());
        // 循环日期
        for (Date date : intervalDates) {
            Map<String, Object> properties = new HashedMap<>();
            Block block = new Block();
            block.setStartTime(DateUtils.getDayFirstTime(date));
            block.setEndTime(DateUtils.getDayFirstTime(DateUtils.moveDay(date, 1)));
            List<CoatingMaintenanceSettingsVO> coatList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(coatingMaintenanceSettingsVOS)) {
                // 过滤镀膜维保时间遍历日期有交集的
                coatList =
                        coatingMaintenanceSettingsVOS.stream().filter(k -> !(k.getStartTime().after(block.getEndTime()) || k.getEndTime().before(block.getStartTime()))).collect(Collectors.toList());
            }

            // 筛选工序子任务与遍历日期有交集的
            if (CollectionUtils.isEmpty(operationSubTasks) && CollectionUtils.isEmpty(coatList)) {
                continue;
            }
            if (CollectionUtils.isEmpty(operationSubTasks)) {
                operationSubTasks = new ArrayList<>();
            }
            List<OperationSubTaskVO> filterOperationSubTasks =
                    operationSubTasks.stream().filter(k -> !(k.getStartTime().after(block.getEndTime()) || k.getEndTime().before(block.getStartTime()))).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filterOperationSubTasks) && CollectionUtils.isEmpty(coatList)) {
                continue;
            }
            // 单天改物理资源总可用时长
            List<Pair<Date, Date>> filterCalendarPair =
                    resourceCalendars.stream().filter(k -> DateUtils.isSameDay(k.getKey(), date)).collect(Collectors.toList());
            long unitDayAvailableTime = 0L;
            long unitDayWorkTime = 0L;
            if (CollectionUtils.isNotEmpty(filterCalendarPair)) {
                unitDayAvailableTime =
                        filterCalendarPair.stream().map(k -> k.getValue().getTime() - k.getKey().getTime()).reduce(Long::sum).get();
            }
            // 单天内的工序任务集合
            List<String> operationTaskIds = new ArrayList<>();
            BigDecimal load = BigDecimal.ZERO;
            for (OperationSubTaskVO operationSubTaskVO : filterOperationSubTasks) {
                operationTaskIds.add(operationSubTaskVO.getTaskId());
                // workTime为该子工序对应工序任务实际工作时长
                long operationWorkTime = 0;
                List<Pair<Date, Date>> operationWorkTimePairs =
                        operationTaskIdToWorkTimePairs.get(operationSubTaskVO.getTaskId());
                if (CollectionUtils.isNotEmpty(operationWorkTimePairs)) {
                    operationWorkTime =
                            operationWorkTimePairs.stream().map(k -> k.getValue().getTime() - k.getKey().getTime()).reduce(Long::sum).get();
                }
                // 单天内工序子任务工作时长
                long unitDaySubTaskWorkTime = 0L;
                // 对应日期维度生产时间列表
                List<Pair<Date, Date>> unitDayWorkTimePairs = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(operationWorkTimePairs)) {
                    for (Pair<Date, Date> workTimePair : operationWorkTimePairs) {
                        // 资源对应工序任务生产时间列表（包含异常）和资源对应工序任务可用时间列表的交集可得工序任务的实际生产时间
                        // dateMap 工序子任务对应的开始时间->结束时间
                        // resourceMapList 物理资源的可用时间列表
                        List<Pair<Date, Date>> intersection = DatePairUtil.intersection(workTimePair,
                                filterCalendarPair);
                        unitDayWorkTimePairs.addAll(intersection);
                    }
                }
                // listMap存放 key为单天 value为该时间轴内的工作时间
                Map<String, List<Pair<Date, Date>>> listMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(unitDayWorkTimePairs)) {
                    listMap.put(DateUtils.dateToString(date), unitDayWorkTimePairs);
                    unitDaySubTaskWorkTime =
                            unitDayWorkTimePairs.stream().map(k -> k.getValue().getTime() - k.getKey().getTime()).reduce(Long::sum).get();
                }
                // 汇总单天总工作时长
                unitDayWorkTime = unitDayWorkTime + unitDaySubTaskWorkTime;
                OperationVO operationVO = taskIdToOperationMaps.get(operationSubTaskVO.getTaskId());
                // 获取对应物品信息 暂时用不到
                // ProductStockPointVO productStockPointVO = mainKeyToProductMaps.get(operationVO.getStockPointId() +
                // "&" + operationVO.getProductId());
                // NewProductStockPointVO newProductStockPointVO = mainKeyToProductMaps.get(operationVO.getProductId());

/*                Map<String, BigDecimal> columnToQuantityMap = calculateColumnToQuantityMap(operationVO.getQuantity
(), listMap ,operationWorkTime,
                        productStockPointVO.getUnitProductionQuantity() == null ? BigDecimal.ONE :
                        productStockPointVO.getUnitProductionQuantity());*/
                Map<String, BigDecimal> columnToQuantityMap = calculateColumnToQuantityMap(operationVO.getQuantity(),
                        listMap, operationWorkTime, BigDecimal.ONE);
                load = load.add(calculateResourceLoad(physicalResourceVO, unitDayAvailableTime,
                        unitDaySubTaskWorkTime, columnToQuantityMap.get(DateUtils.dateToString(date))));
            }

            ResourceLoadGanttVO resourceLoadGanttVO = new ResourceLoadGanttVO();
            resourceLoadGanttVO.setOperationTaskIds(operationTaskIds.stream().distinct().collect(Collectors.toList()));
            resourceLoadGanttVO.setPercent(Double.valueOf(load.toString()));
            resourceLoadGanttVO.setAvailableTime(unitDayAvailableTime);
            resourceLoadGanttVO.setWorkTime(unitDayWorkTime);
            if (CollectionUtils.isNotEmpty(coatList)) {
                resourceLoadGanttVO.setCoatingMaintenanceSettingId(coatList.get(0).getId());
                resourceLoadGanttVO.setStartTime(coatList.get(0).getStartTime());
                resourceLoadGanttVO.setEndTime(coatList.get(0).getEndTime());
                resourceLoadGanttVO.setCoatFlag(Boolean.TRUE);
            }

            block.setProperties(properties);
            block.setResourceLoadGantt(resourceLoadGanttVO);
            blocks.add(block);
        }
        return blocks;
    }


    private String getProcessKey(Object object, List<String> properties) {
        String processKey = "";
        for (String property : properties) {
            if (StringUtils.isEmpty(processKey)) {
                processKey = String.valueOf(ReflectUtils.getFieldValueByName(property, object));
            } else {
                processKey += "-" + ReflectUtils.getFieldValueByName(property, object);
            }

        }
        return processKey;
    }

    /**
     * 计算单个工序任务在每个时间轴单元格的数量
     *
     * @param quantity
     * @param listMap
     * @param workTime
     * @param unitProductionQuantity
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    public Map<String, BigDecimal> calculateColumnToQuantityMap(BigDecimal quantity, Map<String, List<Pair<Date,
            Date>>> listMap, long workTime, BigDecimal unitProductionQuantity) {
        Map<String, BigDecimal> result = new HashMap<>();
        List<String> sortedColumn =
                listMap.keySet().stream().sorted(Comparator.comparing(k -> k)).collect(Collectors.toList());
        BigDecimal count = BigDecimal.ZERO;
        for (int i = 0; i < sortedColumn.size(); i++) {
            String column = sortedColumn.get(i);
            long workTimeOfTimeLine = 0;
            if (i == sortedColumn.size() - 1) {
                result.put(column, quantity.subtract(count));
            } else {
                List<Pair<Date, Date>> pairs = listMap.get(column);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(pairs)) {
                    for (Pair<Date, Date> datePair : pairs) {
                        Date start = datePair.getKey();
                        Date end = datePair.getValue();
                        workTimeOfTimeLine += end.getTime() - start.getTime();
                    }
                }
                BigDecimal divide = BigDecimal.valueOf(workTimeOfTimeLine).divide(BigDecimal.valueOf(workTime == 0 ?
                        1 : workTime), 6, RoundingMode.HALF_UP);
                // 根据单位制造量保留位数
                BigDecimal multiply = quantity.multiply(divide).divide(unitProductionQuantity, 0,
                        RoundingMode.HALF_UP).multiply(unitProductionQuantity);
                result.put(column, multiply);
                count = count.add(result.get(column));
            }
        }
        return result;
    }

    /**
     * 计算资源负荷
     *
     * @param physicalResourceVO
     * @param resWorkTime
     * @param workTimeOfTimeLine
     * @param quantity
     * @return java.math.BigDecimal
     */
    public BigDecimal calculateResourceLoad(PhysicalResourceVO physicalResourceVO, long resWorkTime,
                                            long workTimeOfTimeLine, BigDecimal quantity) {
        BigDecimal load = BigDecimal.ZERO;
        // 统计资源产能利用率=实际占用时间/资源可用时间
        // 物理资源的堆叠限制类型为：限制同时加工任务个数(TASK_NUM_LIMIT)
        // •  实际占用时间=时间轴颗粒度下工序制造任务时间之和
        // •  资源可用时间=时间轴颗粒度下资源非异常时间*资源量系数（当分母为0时，产能利用率按0处理）
        // 物理资源的堆叠限制类型为：限制同时加工数量总和(SUM_LIMIT)
        // •  实际占用时间=时间轴颗粒度下Σ工序制造数量*工序制造时间
        // ·  资源可用时间=时间轴颗粒度下资源非异常时间*资源量系数（当分母为0时，产能利用率按0处理）
        if (resWorkTime == 0) {
            return load;
        }
        // 分母
        BigDecimal denominator =
                BigDecimal.valueOf(resWorkTime).multiply(new BigDecimal(physicalResourceVO.getResourceQuantityCoefficient()));

        if (AssignQtyTypeEnum.TASK_NUM_LIMIT.getCode().equals(physicalResourceVO.getAssignQuantityType())) {
            load = BigDecimal.valueOf(workTimeOfTimeLine).divide(denominator, 6, RoundingMode.HALF_UP);
        }
        if (AssignQtyTypeEnum.SUM_LIMIT.getCode().equals(physicalResourceVO.getAssignQuantityType()) && quantity != null) {
            load = BigDecimal.valueOf(workTimeOfTimeLine).multiply(quantity).divide(denominator, 6,
                    RoundingMode.HALF_UP);
        }
        return load;

    }

    private String getMdsScenario() {
        BaseResponse<String> scenarioMds = ipsNewFeign.getScenarioByTenantCode(SystemModuleEnum.MDS.getCode()
                , TenantCodeEnum.FYQB.getCode());
        Assert.isTrue(scenarioMds.getSuccess(), scenarioMds.getMsg());
        return scenarioMds.getData();
    }
}
