package com.yhl.scp.mps.capacityBalance.service.impl;

import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.das.core.feign.NewDasFeign;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.risk.enums.RiskLevelEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingStepVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mps.capacityBalance.convertor.CapacityLoadConvertor;
import com.yhl.scp.mps.capacityBalance.convertor.CapacitySupplyRelationshipConvertor;
import com.yhl.scp.mps.capacityBalance.convertor.CapacitySupplyRelationshipExceptionConvertor;
import com.yhl.scp.mps.capacityBalance.dto.*;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceRule;
import com.yhl.scp.mps.capacityBalance.enums.CapacityBalanceTypeEnum;
import com.yhl.scp.mps.capacityBalance.enums.LockStatusEnum;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacityLoadDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.dao.CapacitySupplyRelationshipExceptionDao;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacityLoadPO;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipExceptionPO;
import com.yhl.scp.mps.capacityBalance.infrastructure.po.CapacitySupplyRelationshipPO;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmDataService;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceAlgorithmExecuteService;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceAlgorithmDataService;
import com.yhl.scp.mps.capacityBalance.service.CapacityWeekBalanceService;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO;
import com.yhl.scp.mps.enums.SupplyModelEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>CapacityBalanceAlgorithmDataServiceImpl</code>
 * <p>
 * 产能平衡算法参数服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-20 14:30:11
 */
@Slf4j
@Service
public class CapacityBalanceAlgorithmExecuteServiceImpl implements CapacityBalanceAlgorithmExecuteService {

    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private CapacitySupplyRelationshipDao capacitySupplyRelationshipDao;
    @Resource
    private CapacityLoadDao capacityLoadDao;
    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private NewDasFeign newDasFeign;
    @Resource(name = "CapacityDayBalanceAlgorithmDataServiceImpl")
    private CapacityBalanceAlgorithmDataService capacityDayBalanceAlgorithmDataService;
    @Resource
    private CapacityWeekBalanceAlgorithmDataService capacityWeekBalanceAlgorithmDataService;
    @Resource
    private CapacitySupplyRelationshipExceptionDao capacitySupplyRelationshipExceptionDao;
    @Resource
    private CapacityWeekBalanceService capacityWeekBalanceService;

    private static final String VIRTUAL_RESOURCE = "VIRTUAL_RESOURCE";


    private static final String KEY_PREFIX = "CAPACITY_BALANCE";
    private static final String KEY_WEEK_PREFIX = "CAPACITY_WEEK_BALANCE";


    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessMonitorLog(businessCode = "月度产能均衡", moduleCode = "MPS", businessFrequency = "MONTH")
    public void capacityBalanceExecuteLock(String capacityPeriod) {
        String key = KEY_PREFIX + capacityPeriod;
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(key, key, 30, TimeUnit.MINUTES);

        String logId = UUIDUtil.getUUID();
        String errorMsg = null;
        AlgorithmLog capacityAlgoLog = new AlgorithmLog();

        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("变更同步或产能平衡计算中, 请勿重复执行");
        }
        List<String> logList = new ArrayList<>();
        try {
            // 创建写入日志方便查看
            AlgorithmLog algorithmLog = new AlgorithmLog();
            algorithmLog.setFailMsg("产能平衡计算中");
            algorithmLog.setId(logId);
            algorithmLog.setStatus((AlgorithmLogStatusEnum.RUNNING.getCode()));
            capacityAlgoLog = createCapacityAlgoLog(algorithmLog);
            log.info("产能平衡计算开始");

            // 执行计算
            capacityBalanceExecute(capacityPeriod, logList);
            log.info("产能平衡计算结束");
            capacityAlgoLog.setStatus((AlgorithmLogStatusEnum.SUCCESS.getCode()));
        }catch (Exception e){
            errorMsg = e.getMessage();
            log.error("执行报错",e);
            capacityAlgoLog.setFailMsg("产能平衡计算失败，" + errorMsg);
            capacityAlgoLog.setStatus((AlgorithmLogStatusEnum.FAIL.getCode()));
            throw new BusinessException("执行报错："+e.getMessage());
        }finally {
            redisTemplate.delete(key);
            logList.add("产能平衡日志");
            String filePath = "/usr/local/aps/workspace/capacity";
            String fileName = capacityAlgoLog.getId();
            // 修改日志并补充日志内容
            capacityAlgoLog.setFilePath(filePath + "/" + fileName);
            if (capacityAlgoLog.getFailMsg().length() > 1024){
                capacityAlgoLog.setFailMsg(capacityAlgoLog.getFailMsg().substring(0, 1024));
            }
            AlgorithmLog finalCapacityAlgoLog = capacityAlgoLog;
            CompletableFuture.runAsync(() -> {
                updateCapacityAlgoLog(finalCapacityAlgoLog);
                //生成文件
                newDasFeign.createTxtFile(logList, finalCapacityAlgoLog.getFilePath(), fileName);
                log.info("产能平衡计算日志已记录");
            });

        }
    }

    @Override
    public void deleteRedisKey(String key) {
        redisTemplate.delete(key);
    }

    @Override
    public void capacityBalanceExecute(String capacityPeriod, List<String> logList) {
        if (StringUtils.isEmpty(capacityPeriod)){
            throw new BusinessException("请选择展望期");
        }

        //获取算法数据
        CapacityBalanceAlgorithmDataDTO capacityBalanceAlgorithmDataDTO = capacityDayBalanceAlgorithmDataService.capacityBalanceAlgorithmData(capacityPeriod, false, logList);

        //执行算法
        CapacityBalanceResultDTO capacityBalanceResultDTO = capacityBalanceExecuteCommon(capacityBalanceAlgorithmDataDTO);

        //数据处理
        commonDataUpdate(capacityBalanceResultDTO);
    }

    @Override
    public void capacityDayBalanceExecute(String capacityPeriod) {
        if (StringUtils.isEmpty(capacityPeriod)) {
            throw new BusinessException("请选择展望期");
        }
        //获取算法数据
        CapacityBalanceAlgorithmDataDTO capacityBalanceAlgorithmDataDTO = capacityDayBalanceAlgorithmDataService.capacityBalanceAlgorithmData(capacityPeriod, false, null);
        //执行算法
        CapacityBalanceResultDTO capacityBalanceResultDTO = capacityBalanceExecuteCommon(capacityBalanceAlgorithmDataDTO);
        //数据处理
        // dayCommonDataUpdate(capacityBalanceResultDTO);
        commonDataUpdate(capacityBalanceResultDTO);
    }

    private CapacityBalanceResultDTO capacityBalanceExecuteCommon(CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {

        CapacityBalanceResultDTO resultDTO = new CapacityBalanceResultDTO();

        List<String> monthList = algorithmDataDTO.getMonthList();
        List<String> dayList = algorithmDataDTO.getDayList();
        Map<String, Integer> operationQty = algorithmDataDTO.getOperationQty();
        Map<String, Integer> primitiveOperationQty = algorithmDataDTO.getPrimitiveOperationQty();
        Map<String, Set<String>> operationOfProductMap = algorithmDataDTO.getOperationOfProductMap();
        List<String> productCodeList = algorithmDataDTO.getProductCodeList();
        Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap = algorithmDataDTO.getProductAndOperationOfResourceMap();
        Map<String, List<CapacitySupplyRelationshipVO>> lockByMonthDataMap = algorithmDataDTO.getLockData();
        Map<String, PartRiskLevelVO> materialRiskLevelMap = algorithmDataDTO.getMaterialRiskLevelMap();
        Map<String, Integer> capacityMap = algorithmDataDTO.getResourceCapacityMap();
        Map<String, ProductCandidateResourceTimeVO> resourceOperationMap = algorithmDataDTO.getResourceOperationMap();
        Map<String, PhysicalResourceVO> physicalResourceMap = algorithmDataDTO.getPhysicalresourceMap();
        Map<String, OemVO> oemVOMap = algorithmDataDTO.getOemVOMap();
        Map<String, List<String>> productOfOemMap = algorithmDataDTO.getProductOfOemMap();
        Map<String, List<String>> productOfSourceProductListMap = algorithmDataDTO.getProductOfSourceProductListMap();
        //规则1，每月的结果，后面每个规则都维护这个map
        Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap = new HashMap<>();
        Map<String, NewProductStockPointVO> productMap = algorithmDataDTO.getProductMap();
        String pattern = CapacityBalanceTypeEnum.MONTH.getCode().equals(algorithmDataDTO.getType()) ? "yyyyMM" : "yyyy-MM-dd";
        String type = algorithmDataDTO.getType();
        if (CapacityBalanceTypeEnum.WEEK.getCode().equals(type)){
            monthList = algorithmDataDTO.getDayList();
        }
        List<CapacitySupplyRelationshipExceptionDTO> exceptionPOList = algorithmDataDTO.getExceptionPOList();
        Map<String, RoutingStepVO> routingStepVOMap = algorithmDataDTO.getRoutingStepVOMap();

        //产能负荷数据
        Map<String, Map<String, CapacityLoadVO>> resourceCapacityMonthMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(monthList)) {
            List<String> rules = algorithmDataDTO.getRule();


            for (String rule : rules) {
                switch (rule) {
                    case "highestPriority":
                        //规则1:最高优先级设备
                        highestPriority(dayList, lockByMonthDataMap, productCodeList, operationOfProductMap, operationQty,
                                productAndOperationOfResourceMap, productMap, monthResultMap, capacityMap, resourceCapacityMonthMap,
                                resourceOperationMap, physicalResourceMap, primitiveOperationQty, exceptionPOList, pattern, routingStepVOMap);
                        break;
                    case "equipmentBalance":
                        //规则2:设备均衡
                        resourceCapacityMonthMap = equipmentBalanceNew(monthList, monthResultMap, productAndOperationOfResourceMap, capacityMap, resourceCapacityMonthMap,
                                physicalResourceMap, pattern, type);
                        break;
                    case "stockProduction":
                        //规则3:备库生产，需要在两个月份直接调整，所以要汇总每个月的设备负荷
                        stockProductionNew(monthList, resourceCapacityMonthMap, monthResultMap, capacityMap, materialRiskLevelMap,
                                productAndOperationOfResourceMap, physicalResourceMap, oemVOMap, productOfOemMap, pattern, type, productOfSourceProductListMap);
                        break;
                    case "manualModification":
                        break;
                    default:
                        break;
                }
            }

            //最后的关系里有供应数量为0的，把它过滤掉
            List<CapacitySupplyRelationshipDTO> capacitySupplyRelationshipDTOList = getCapacitySupplyRelationshipPOList(monthResultMap, algorithmDataDTO);
            List<CapacityLoadDTO> capacityLoadDTOList = getCapacityLoadDTOList(resourceCapacityMonthMap, algorithmDataDTO);
            List<CapacitySupplyRelationshipExceptionDTO> exceptionDTOS = getExceptionPOList(exceptionPOList);
            resultDTO.setCapacitySupplyRelationshipDTOList(capacitySupplyRelationshipDTOList);
            resultDTO.setCapacityLoadDTOList(capacityLoadDTOList);
            resultDTO.setAddResourceTimeList(algorithmDataDTO.getAddResourceTimeList());
            resultDTO.setExceptionPOList(exceptionDTOS);
        }
        return resultDTO;
    }

    /**
     * 去除重复数据
     * @param exceptionPOList
     * @return
     */
    private List<CapacitySupplyRelationshipExceptionDTO> getExceptionPOList(List<CapacitySupplyRelationshipExceptionDTO> exceptionPOList) {
        List<CapacitySupplyRelationshipExceptionDTO> result = new ArrayList<>();
        Map<String, List<CapacitySupplyRelationshipExceptionDTO>> collect = exceptionPOList.stream()
                .filter(t->StringUtils.isNotEmpty(t.getProductCode()) && StringUtils.isNotEmpty(t.getOperationCode()) && StringUtils.isNotEmpty(t.getForecastMonth()))
                .collect(Collectors.groupingBy(t -> String.join("#", t.getProductCode(), t.getOperationCode())));
        //没有候选资源的逻辑
        for (Map.Entry<String, List<CapacitySupplyRelationshipExceptionDTO>> entry : collect.entrySet()) {
            BigDecimal reduce = entry.getValue().stream().map(CapacitySupplyRelationshipExceptionDTO::getDemandQuantity).reduce(BigDecimal.ZERO, BigDecimal::add);
            CapacitySupplyRelationshipExceptionDTO dto = new CapacitySupplyRelationshipExceptionDTO();
            BeanUtils.copyProperties(entry.getValue().get(0), dto);
            dto.setDemandQuantity(reduce);
            dto.setForecastMonth(null);
            result.add(dto);
        }
        //没有工艺路径的异常数据
        List<CapacitySupplyRelationshipExceptionDTO> exceptionDTOS = exceptionPOList.stream()
                .filter(t -> StringUtils.isNotEmpty(t.getProductCode()) && StringUtils.isEmpty(t.getOperationCode()) && StringUtils.isEmpty(t.getForecastMonth()))
                .collect(Collectors.toList());
        result.addAll(exceptionDTOS);
        result.forEach(t->{
            t.setId(UUIDUtil.getUUID());
        });
        return result;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void changeSynchronizationLock(String capacityPeriod) {
        String key = KEY_PREFIX + capacityPeriod;
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(key, key, 30, TimeUnit.MINUTES);

        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("变更同步或产能平衡计算中, 请勿重复执行");
        }
        try {
            changeSynchronization(capacityPeriod);
        }catch (Exception e){
            log.error("执行报错",e);
            throw new BusinessException("执行报错："+e.getMessage());
        }finally {
            redisTemplate.delete(key);
        }
    }

    @Override
    public void changeSynchronization(String capacityPeriod) {
        if (StringUtils.isEmpty(capacityPeriod)){
            throw new BusinessException("请选择展望期");
        }
        String pattern = "yyyy-MM-dd";
        CapacityBalanceAlgorithmDataDTO algorithmDataDTO = capacityDayBalanceAlgorithmDataService.capacityBalanceAlgorithmData(capacityPeriod, true, null);
        //新版本数据拆解得到的原始工序需求数量
        Map<String, Integer> primitiveOperationQty = algorithmDataDTO.getPrimitiveOperationQty();
        if (CollectionUtils.isEmpty(primitiveOperationQty)){
            throw new BusinessException("原始工序需求数量为空");
        }
        //获取最新的产能平衡数据
        List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = capacitySupplyRelationshipDao.selectLatestData(null);
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = CapacitySupplyRelationshipConvertor.INSTANCE.po2Vos(capacitySupplyRelationshipPOList);
        if (CollectionUtils.isEmpty(capacitySupplyRelationshipVOList)){
            throw new BusinessException("产能平衡数据为空");
        }
        //过滤掉特殊工艺对应的供应关系 委外数据也过滤掉，重新生成
        List<CollectionValueVO> specialTechnology = ipsFeign.getByCollectionCode("SPECIAL_TECHNOLOGY");
        List<String> specialTechnologyList = specialTechnology.stream().map(CollectionValueVO::getValueMeaning).collect(Collectors.toList());
        capacitySupplyRelationshipVOList = capacitySupplyRelationshipVOList.stream().filter(t -> !specialTechnologyList.contains(t.getOperationCode())
                && !SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel())).collect(Collectors.toList());
        Map<String, List<CapacitySupplyRelationshipVO>> supplyMap = capacitySupplyRelationshipVOList.stream().collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), pattern) + "-" + t.getProductCode() + "-" + t.getOperationCode()));
        List<CapacitySupplyRelationshipVO> retentionData = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : primitiveOperationQty.entrySet()) {
            String key = entry.getKey();
            BigDecimal newDemandQuantity = BigDecimal.valueOf(entry.getValue());
            //新版本旧版本 月份-产品-工序都有需求数据
            if (supplyMap.containsKey(key)){
                //这里无论有几条数据，需求量是一样的 key:day+"-"+productCode+"-"+sequenceNo;
                List<CapacitySupplyRelationshipVO> voList = supplyMap.get(key);
                BigDecimal oldDemandQuantity = voList.get(0).getDemandQuantity();

                //需求量减少，首先扣减非锁定供应时间最晚的数据
                if (oldDemandQuantity.compareTo(newDemandQuantity) > 0){
                    BigDecimal subtract = oldDemandQuantity.subtract(newDemandQuantity);
                    List<CapacitySupplyRelationshipVO> unLockList = voList.stream().filter(t -> LockStatusEnum.UNLOCKED.getCode().equals(t.getLockStatus())).sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getSupplyTime).reversed()).collect(Collectors.toList());
                    //重新设置供应数量
                    subtractQty(subtract, unLockList);
                    //如果非锁定数据不够，再减锁定数据
                    if (subtract.compareTo(BigDecimal.ZERO) > 0){
                        List<CapacitySupplyRelationshipVO> lockList = voList.stream().filter(t -> LockStatusEnum.LOCKED.getCode().equals(t.getLockStatus())).sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getSupplyTime).reversed()).collect(Collectors.toList());
                        subtractQty(subtract, lockList);
                    }
                    //过滤出供应数量不为0的供应关系，修改需求数量
                    List<CapacitySupplyRelationshipVO> list = voList.stream().filter(t -> t.getSupplyQuantity().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                    list.forEach(t->t.setDemandQuantity(newDemandQuantity));
                    retentionData.addAll(list);
                }else if (oldDemandQuantity.compareTo(newDemandQuantity) < 0){
                    //需求数量增加，则直接更新需求数量
                    voList.forEach(t->t.setDemandQuantity(newDemandQuantity));
                    retentionData.addAll(voList);
                }else {
                    //数量没有发生变化，保留上一版供应关系数据
                    retentionData.addAll(voList);
                }
            }
        }
        retentionData = retentionData.stream().filter(t->LockStatusEnum.LOCKED.getCode().equals(t.getLockStatus())).collect(Collectors.toList());
        //保存数据原来的锁定状态
        Map<String, String> lockStatusMap = retentionData.stream().collect(Collectors.toMap(CapacitySupplyRelationshipVO::getId, CapacitySupplyRelationshipVO::getLockStatus));
        // retentionData.forEach(t -> t.setLockStatus(LockStatusEnum.LOCKED.getCode()));
        //这里面要计算工序净需求，减掉锁定和委外的数据，
        Map<String, Integer> operationQty = algorithmDataDTO.getOperationQty();
        //保留的数据当成锁定数据，在原始工序需求数量中减掉就得到了要重新计算的数据
        Map<String, BigDecimal> lockQtyData = capacityDayBalanceAlgorithmDataService.getLockQtyData(retentionData, algorithmDataDTO, null);
        for (Map.Entry<String, Integer> entry : primitiveOperationQty.entrySet()) {
            String key = entry.getKey();
            if (lockQtyData.containsKey(key)){
                operationQty.put(key, entry.getValue() - lockQtyData.get(key).intValue());
            }
        }
        algorithmDataDTO.setOperationQty(operationQty);
        CapacityBalanceResultDTO capacityBalanceResultDTO = capacityBalanceExecuteCommon(algorithmDataDTO);

        //数据处理
        List<CapacitySupplyRelationshipDTO> shipDtoList = capacityBalanceResultDTO.getCapacitySupplyRelationshipDTOList();
        shipDtoList.forEach(t->{
            if (lockStatusMap.containsKey(t.getId())){
                t.setLockStatus(lockStatusMap.get(t.getId()));
            }
        });
        commonDataUpdate(capacityBalanceResultDTO);
    }

    @Override
    public void capacityWeekBalanceExecute() {
        CapacityBalanceAlgorithmDataDTO algorithmData = capacityWeekBalanceAlgorithmDataService.getCapacityWeekBalanceAlgorithmData();
        //执行算法
        CapacityBalanceResultDTO capacityBalanceResultDTO = capacityBalanceExecuteCommon(algorithmData);
        //数据处理
        weekDataUpdate(capacityBalanceResultDTO);
        // 发送预警消息
        capacityWeekBalanceService.sendMessage(capacityBalanceResultDTO.getCapacityLoadDTOList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void capacityWeekBalanceExecuteLock() {
        String key = KEY_WEEK_PREFIX;
        Boolean setIfAbsent = redisTemplate.opsForValue().setIfAbsent(key, key, 30, TimeUnit.MINUTES);

        if (!Objects.nonNull(setIfAbsent) || Boolean.FALSE.equals(setIfAbsent)) {
            throw new BusinessException("周产能平衡计算中, 请勿重复执行");
        }
        try {
            log.info("周产能平衡计算开始");
            capacityWeekBalanceExecute();
            log.info("周产能平衡计算完成");
        }catch (Exception e){
            log.error("执行报错",e);
            throw new BusinessException("执行报错："+e.getMessage());
        }finally {
            redisTemplate.delete(key);
        }
    }

    private void subtractQty(BigDecimal subtract, List<CapacitySupplyRelationshipVO> lockList) {
        for (CapacitySupplyRelationshipVO vo : lockList) {
            if (subtract.compareTo(vo.getSupplyQuantity()) > 0){
                subtract = subtract.subtract(vo.getSupplyQuantity());
                vo.setSupplyQuantity(BigDecimal.ZERO);
            }else {
                vo.setSupplyQuantity(vo.getSupplyQuantity().subtract(subtract));
                subtract = BigDecimal.ZERO;
            }
            if (subtract.compareTo(BigDecimal.ZERO) == 0){
                break;
            }
        }
    }

    private void commonDataUpdate(CapacityBalanceResultDTO capacityBalanceResultDTO) {
        List<CapacitySupplyRelationshipDTO> shipDtoList = capacityBalanceResultDTO.getCapacitySupplyRelationshipDTOList();
        if (CollectionUtils.isNotEmpty(shipDtoList)){
            List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Pos(shipDtoList);
            //删除未发布的数据
            // capacitySupplyRelationshipDao.deleteUnpublishedBatch();
            List<String> deleteIds = capacitySupplyRelationshipDao.selectNeedDeleteIds(Collections.singletonList(CapacityBalanceTypeEnum.NEW.getCode()));
            log.info("删除供应关系数据条数：{}", deleteIds.size());
            for (List<String> deleteId : CollectionUtils.splitList(deleteIds, 2000)) {
                capacitySupplyRelationshipDao.deleteBatch(deleteId);
            }
            capacitySupplyRelationshipPOList.forEach(t->t.setVersionId(CapacityBalanceTypeEnum.NEW.getCode()));
            log.info("新增供应关系数据条数：{}", capacitySupplyRelationshipPOList.size());
            BasePOUtils.insertBatchFiller(capacitySupplyRelationshipPOList);
            for (List<CapacitySupplyRelationshipPO> pos : CollectionUtils.splitList(capacitySupplyRelationshipPOList, 2000)) {
                capacitySupplyRelationshipDao.insertBatch(pos);
            }
        }
        List<CapacityLoadDTO> loadDtoList = capacityBalanceResultDTO.getCapacityLoadDTOList();
        if (CollectionUtils.isNotEmpty(loadDtoList)){
            List<CapacityLoadPO> capacityLoadPOList = CapacityLoadConvertor.INSTANCE.dto2Pos(loadDtoList);
            //删除未发布的数据
            capacityLoadDao.deleteUnpublishedBatch();
            BasePOUtils.insertBatchFiller(capacityLoadPOList);
            log.info("新增负荷数据条数：{}", capacityLoadPOList.size());
            capacityLoadPOList.forEach(t->t.setVersionId(CapacityBalanceTypeEnum.NEW.getCode()));
            for (List<CapacityLoadPO> pos : CollectionUtils.splitList(capacityLoadPOList, 2000)) {
                capacityLoadDao.insertBatch(pos);
            }
        }
        saveExceptionInfo(capacityBalanceResultDTO);


        if (CollectionUtils.isNotEmpty(capacityBalanceResultDTO.getAddResourceTimeList())){
            //保存数据
            newMdsFeign.saveProductCandidateResourceTimeVO(null, capacityBalanceResultDTO.getAddResourceTimeList());
        }

    }

    private void weekDataUpdate(CapacityBalanceResultDTO capacityBalanceResultDTO) {
        List<CapacitySupplyRelationshipDTO> shipDtoList = capacityBalanceResultDTO.getCapacitySupplyRelationshipDTOList();
        if (CollectionUtils.isNotEmpty(shipDtoList)){
            log.info("新增供应关系周数据条数：{}", shipDtoList.size());
            List<CapacitySupplyRelationshipPO> capacitySupplyRelationshipPOList = CapacitySupplyRelationshipConvertor.INSTANCE.dto2Pos(shipDtoList);
            capacitySupplyRelationshipPOList.forEach(t->t.setVersionId(CapacityBalanceTypeEnum.WEEK.getCode()));
            //删除未发布的数据
            capacitySupplyRelationshipDao.deleteWeekData(CapacityBalanceTypeEnum.WEEK.getCode());
            BasePOUtils.insertBatchFiller(capacitySupplyRelationshipPOList);
            capacitySupplyRelationshipDao.insertBatch(capacitySupplyRelationshipPOList);
            log.info("新增供应关系周数据结束");
        }
        List<CapacityLoadDTO> loadDtoList = capacityBalanceResultDTO.getCapacityLoadDTOList();
        if (CollectionUtils.isNotEmpty(loadDtoList)){
            log.info("新增负荷周数据条数：{}", loadDtoList.size());
            List<CapacityLoadPO> capacityLoadPOList = CapacityLoadConvertor.INSTANCE.dto2Pos(loadDtoList);
            capacityLoadPOList.forEach(t->t.setVersionId(CapacityBalanceTypeEnum.WEEK.getCode()));
            //删除未发布的数据
            capacityLoadDao.deleteByVersion(Collections.singletonList(CapacityBalanceTypeEnum.WEEK.getCode()));
            BasePOUtils.insertBatchFiller(capacityLoadPOList);
            capacityLoadDao.insertBatch(capacityLoadPOList);
            log.info("新增负荷周数据结束");
        }
        saveExceptionInfo(capacityBalanceResultDTO);
    }

    private void saveExceptionInfo(CapacityBalanceResultDTO capacityBalanceResultDTO) {
        List<CapacitySupplyRelationshipExceptionDTO> exceptionPOList = capacityBalanceResultDTO.getExceptionPOList();
        if (CollectionUtils.isNotEmpty(exceptionPOList)){
            capacitySupplyRelationshipExceptionDao.deleteAll();
            List<CapacitySupplyRelationshipExceptionPO> capacitySupplyRelationshipExceptionPOS = CapacitySupplyRelationshipExceptionConvertor.INSTANCE.dto2Pos(exceptionPOList);
            BasePOUtils.insertBatchFiller(capacitySupplyRelationshipExceptionPOS);
            log.info("新增异常数据条数：{}", capacitySupplyRelationshipExceptionPOS.size());
            capacitySupplyRelationshipExceptionDao.insertBatch(capacitySupplyRelationshipExceptionPOS);
        }
    }


    private List<CapacityLoadDTO> getCapacityLoadDTOList(Map<String, Map<String, CapacityLoadVO>> resourceCapacityMonthMap,
                                                         CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        List<CapacityLoadVO>  capacityLoadVOList = new ArrayList<>();
        List<CapacityLoadDTO>  capacityLoadDTOList = new ArrayList<>();
        for (Map.Entry<String, Map<String, CapacityLoadVO>> entry : resourceCapacityMonthMap.entrySet()) {
            capacityLoadVOList.addAll(entry.getValue().values());
        }
        if (CollectionUtils.isNotEmpty(algorithmDataDTO.getSpecialOperationLoadData())){
            capacityLoadVOList.addAll(algorithmDataDTO.getSpecialOperationLoadData());
        }
        capacityLoadVOList.forEach(
                t->{
                    if(t.getDemandQuantity().compareTo(BigDecimal.ZERO) > 0){
                    CapacityLoadDTO dto = new CapacityLoadDTO();
                    BeanUtils.copyProperties(t, dto);
                    dto.setId(UUIDUtil.getUUID());
                    capacityLoadDTOList.add(dto);
                    }else {
                        log.info("该资源没有需求量，跳过：{}-{}",t.getResourceCode(), DateUtils.dateToString(t.getForecastTime(), DateUtils.COMMON_DATE_STR1));
                    }
                }
        );
        return capacityLoadDTOList;
    }

    private List<CapacitySupplyRelationshipDTO> getCapacitySupplyRelationshipPOList(Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap,
                                                                                    CapacityBalanceAlgorithmDataDTO algorithmDataDTO) {
        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOList = new ArrayList<>();
        for (Map.Entry<String, List<CapacitySupplyRelationshipVO>> entry : monthResultMap.entrySet()) {
            capacitySupplyRelationshipVOList.addAll(entry.getValue());
        }
        if (CollectionUtils.isNotEmpty(algorithmDataDTO.getSpecialOperationShipData())){
            capacitySupplyRelationshipVOList.addAll(algorithmDataDTO.getSpecialOperationShipData());
        }
        Map<String, String> operationMap = capacitySupplyRelationshipVOList.stream().collect(Collectors.toMap(t -> t.getProductCode() + "-" + t.getOperationCode(), CapacitySupplyRelationshipVO::getOperationName, (v1, v2) -> v1));
        if (CollectionUtils.isNotEmpty(algorithmDataDTO.getOutShipData())){
            List<CapacitySupplyRelationshipVO> outShipData = algorithmDataDTO.getOutShipData();
            for (CapacitySupplyRelationshipVO out : outShipData) {
                out.setOperationName(operationMap.get(out.getProductCode() + "-" + out.getOperationCode()));
            }
            capacitySupplyRelationshipVOList.addAll(outShipData);
        }

        Map<String, List<String>> productOfSourceProductListMap = algorithmDataDTO.getProductOfSourceProductListMap();

        // 获取工艺路径步骤
        Map<String, String> routingStepIdMap = algorithmDataDTO.getRoutingStepIdMap();

        List<CapacitySupplyRelationshipDTO> CapacitySupplyRelationshipDTOList = new ArrayList<>();
        capacitySupplyRelationshipVOList.forEach(
                t->{
                    if (t.getSupplyQuantity().compareTo(BigDecimal.ZERO)>0){
                        CapacitySupplyRelationshipDTO dto = new CapacitySupplyRelationshipDTO();
                        BeanUtils.copyProperties(t, dto);
                        dto.setId(UUIDUtil.getUUID());
                        dto.setPlanPeriod(algorithmDataDTO.getPlanPeriod());
                        dto.setVersionCode(algorithmDataDTO.getVersionCode());
                        if (CollectionUtils.isNotEmpty(productOfSourceProductListMap.get(t.getProductCode()))) {
                            dto.setSourceProductCode(productOfSourceProductListMap.get(t.getProductCode()).get(0));
                        }else {
                            log.info("该物料没有原产品编码：{}",t.getProductCode());
                        }

                        // 根据物料编码获取对应工艺路径步骤id并赋值
                        dto.setRoutingStepId(routingStepIdMap.get(dto.getProductCode() + "&" + dto.getOperationCode()));
                        dto.setForecastMonth(DateUtils.dateToString(dto.getForecastTime(), DateUtils.YEAR_MONTH));
                        dto.setSupplyMonth(DateUtils.dateToString(dto.getSupplyTime(), DateUtils.YEAR_MONTH));
                        CapacitySupplyRelationshipDTOList.add(dto);
                    }
                }
        );
        return CapacitySupplyRelationshipDTOList;
    }

    private void highestPriority(List<String> monthList,
                                 Map<String, List<CapacitySupplyRelationshipVO>> lockByMonthDataMap,
                                 List<String> productCodeList,
                                 Map<String, Set<String>> operationOfProductMap,
                                 Map<String, Integer> operationQty,
                                 Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap,
                                 Map<String, NewProductStockPointVO> productMap,
                                 Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap,
                                 Map<String, Integer> capacityMap,
                                 Map<String, Map<String, CapacityLoadVO>> resourceCapacityMonthMap,
                                 Map<String, ProductCandidateResourceTimeVO> resourceOperationMap,
                                 Map<String, PhysicalResourceVO> physicalResourceMap,
                                 Map<String, Integer> primitiveOperationQty,
                                 List<CapacitySupplyRelationshipExceptionDTO> exceptionDTOList,
                                 String pattern, Map<String, RoutingStepVO> routingStepVOMap) {
        for (String month : monthList) {
            //这里先把当月锁定数据添加到产能供应关系结果里
            List<CapacitySupplyRelationshipVO> monthData = new ArrayList<>();
            if (lockByMonthDataMap.containsKey(month)){
                monthData.addAll(lockByMonthDataMap.get(month));
            }
            //记录该设备的供应量
            Map<String, BigDecimal> resourceQtyMap = new HashMap<>();

            //记录第t月设备的已用产能
            Map<String, BigDecimal> resourceCapacityMap = new HashMap<>();
            for (String productCode : productCodeList) {
                Set<String> operationList = operationOfProductMap.get(productCode);
                if (CollectionUtils.isNotEmpty(operationList)){
                    for (String operationCode : operationList) {
                        String qtyKey = month+"#"+productCode+"-"+operationCode;
                        if (operationQty.containsKey(qtyKey)){
                            //该产品这个月在这道工序的需求量
                            int qty = operationQty.get(qtyKey);
                            if (qty == 0){
                                continue;
                            }
                            String key = productCode+"-"+operationCode;
                            RoutingStepVO routingStepVO = routingStepVOMap.get(key);
                            String[] split = month.split("-");
                            int primitiveQty = primitiveOperationQty.get(qtyKey);
                            //获取该工序候选资源
                            List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productAndOperationOfResourceMap.get(key);
                            if (CollectionUtils.isNotEmpty(productCandidateResourceTimeVOS)){
                                //存在候选资源则按照设备优先级排序（优先级越小小代表越高）
                                productCandidateResourceTimeVOS = productCandidateResourceTimeVOS.stream()
                                        .sorted(Comparator.comparing(ProductCandidateResourceTimeVO::getPriority)).collect(Collectors.toList());
                                ProductCandidateResourceTimeVO resourceTimeVO = productCandidateResourceTimeVOS.get(0);
                                //第一版只根据设备优先级进行排产
                                CapacitySupplyRelationshipVO supplyRelationshipVO = getCapacitySupply(CapacityBalanceRule.highestPriority.getCode(),
                                        month, productCode, qty, primitiveQty, productMap, resourceTimeVO, pattern);
                                //计算该这次分配的产能
                                Double beat = resourceTimeVO.getBeat();
                                BigDecimal productionCapacity = supplyRelationshipVO.getSupplyQuantity().multiply(new BigDecimal(beat.toString()));
                                if (resourceCapacityMap.containsKey(resourceTimeVO.getResourceCode())){
                                    BigDecimal capacity = resourceCapacityMap.get(resourceTimeVO.getResourceCode());
                                    capacity = capacity.add(productionCapacity);
                                    resourceCapacityMap.put(resourceTimeVO.getResourceCode(), capacity);
                                }else {
                                    resourceCapacityMap.put(resourceTimeVO.getResourceCode(), productionCapacity);
                                }
                                monthData.add(supplyRelationshipVO);
                                if (resourceQtyMap.containsKey(resourceTimeVO.getResourceCode())){
                                    BigDecimal resourceQty = resourceQtyMap.get(resourceTimeVO.getResourceCode());
                                    resourceQty = resourceQty.add(supplyRelationshipVO.getSupplyQuantity());
                                    resourceQtyMap.put(resourceTimeVO.getResourceCode(), resourceQty);
                                }else {
                                    resourceQtyMap.put(resourceTimeVO.getResourceCode(), supplyRelationshipVO.getSupplyQuantity());
                                }
                            }else {
                                log.info("{}没有候选资源,或缺失优先级数据{}，无法分配", qtyKey,qty);
                                CapacitySupplyRelationshipExceptionDTO exceptionDTO = new CapacitySupplyRelationshipExceptionDTO();
                                exceptionDTO.setProductCode(productCode);
                                exceptionDTO.setVehicleModelCode(productMap.get(productCode).getVehicleModelCode());
                                exceptionDTO.setProductName(productMap.get(productCode).getProductName());
                                exceptionDTO.setForecastMonth(split[0] + "-" + split[1]);
                                exceptionDTO.setOperationCode(operationCode);
                                exceptionDTO.setDemandQuantity(new BigDecimal(qty));
                                exceptionDTO.setExceptionReason("没有候选资源，或缺失优先级数据,无法分配");
                                exceptionDTOList.add(exceptionDTO);
                                // 创建一个虚拟的资源
                                CapacitySupplyRelationshipVO virtualResourceCapacitySupply = getVirtualResourceCapacitySupply(CapacityBalanceRule.highestPriority.getCode(), month,
                                        productCode, qty, primitiveQty, productMap, operationCode, routingStepVO.getStandardStepName(), pattern);
                                monthData.add(virtualResourceCapacitySupply);
                            }
                        }
                    }
                }else {
                    log.info("{}没有对应工序信息", productCode);
                }
            }
            monthResultMap.put(month, monthData);
            //计算锁定数据中这个月的每个设备的产能,下面的逻辑把每个月分配的产能，锁定数据的产能都包含在里面了
            if (lockByMonthDataMap.containsKey(month)){
                for (CapacitySupplyRelationshipVO supplyRelationshipVO : lockByMonthDataMap.get(month)) {
                    if (VIRTUAL_RESOURCE.equals(supplyRelationshipVO.getResourceCode())) {
                        continue;
                    }
                    //委外数据不扣产能，锁定，本厂的数据才扣
                    if (!SupplyModelEnum.OUTSOURCED.getCode().equals(supplyRelationshipVO.getSupplyModel())&&LockStatusEnum.LOCKED.getCode().equals(supplyRelationshipVO.getLockStatus())){
                        BigDecimal productionCapacity = supplyRelationshipVO.getSupplyQuantity().multiply(new BigDecimal(supplyRelationshipVO.getBeat()));
                        if (resourceCapacityMap.containsKey(supplyRelationshipVO.getResourceCode())){
                            BigDecimal capacity = resourceCapacityMap.get(supplyRelationshipVO.getResourceCode());
                            capacity = capacity.add(productionCapacity);
                            resourceCapacityMap.put(supplyRelationshipVO.getResourceCode(), capacity);
                        }else {
                            resourceCapacityMap.put(supplyRelationshipVO.getResourceCode(), productionCapacity);
                        }
                        if (resourceQtyMap.containsKey(supplyRelationshipVO.getResourceCode())){
                            BigDecimal resourceQty = resourceQtyMap.get(supplyRelationshipVO.getResourceCode());
                            resourceQty = resourceQty.add(supplyRelationshipVO.getSupplyQuantity());
                            resourceQtyMap.put(supplyRelationshipVO.getResourceCode(), resourceQty);
                        }else {
                            resourceQtyMap.put(supplyRelationshipVO.getResourceCode(), supplyRelationshipVO.getSupplyQuantity());
                        }
                    }

                }
            }
            Date date = DateUtils.stringToDate(month, pattern);
            List<CapacityLoadVO> resourceCapacityList = new ArrayList<>();
            for (Map.Entry<String, BigDecimal> decimalEntry : resourceCapacityMap.entrySet()) {
                String resourceCode = decimalEntry.getKey();
                //获取该设备当月的总产能 计算负荷率 = 该设备当月已用产能/该设备当天总产能
                Integer total = capacityMap.get(resourceCode+"-"+month);
                if (total == null || total == 0){
                    continue;
                }
                BigDecimal divide = decimalEntry.getValue().divide(new BigDecimal(total.toString()), 4, RoundingMode.HALF_UP);
                PhysicalResourceVO physicalResourceVO = physicalResourceMap.get(resourceCode) == null ? new PhysicalResourceVO() : physicalResourceMap.get(resourceCode);
                ProductCandidateResourceTimeVO resourceTimeVO = resourceOperationMap.get(resourceCode) == null ? new ProductCandidateResourceTimeVO() : resourceOperationMap.get(resourceCode);

                CapacityLoadVO capacityLoadVO = new CapacityLoadVO();
                capacityLoadVO.setForecastTime(date);
                capacityLoadVO.setResourceCode(resourceCode);
                capacityLoadVO.setResourceName(resourceTimeVO.getResourceName());
                capacityLoadVO.setOperationCode(resourceTimeVO.getOperationCode());
                capacityLoadVO.setOperationName(resourceTimeVO.getOperationName());
                capacityLoadVO.setPlantCode(physicalResourceVO.getProductionLine());
                capacityLoadVO.setResourceGroupCode(physicalResourceVO.getStandardResourceCode());
                capacityLoadVO.setResourceGroupName(physicalResourceVO.getStandardResourceName());
                capacityLoadVO.setProductionCapacity(decimalEntry.getValue());
                capacityLoadVO.setAvailableCapacity(BigDecimal.valueOf(total));
                capacityLoadVO.setCapacityUtilization(divide);
                capacityLoadVO.setDemandQuantity(resourceQtyMap.get(resourceCode));
                resourceCapacityList.add(capacityLoadVO);
            }
            Map<String, CapacityLoadVO> map = resourceCapacityList.stream().collect(Collectors.toMap(CapacityLoadVO::getResourceCode, Function.identity()));
            resourceCapacityMonthMap.put(month, map);
        }
    }




    private Map<String,Map<String, CapacityLoadVO>> equipmentBalanceNew(List<String> monthList,
                                                                        Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap,
                                                                        Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap,
                                                                        Map<String, Integer> capacityMap,
                                                                        Map<String,Map<String, CapacityLoadVO>> resourceCapacityMonthMap,
                                                                        Map<String, PhysicalResourceVO> physicalResourceMap,
                                                                        String pattern, String type) {
        String loadPattern;
        if (CapacityBalanceTypeEnum.WEEK.getCode().equals(type)){
            loadPattern = DateUtils.COMMON_DATE_STR3;
        }else {
            loadPattern = DateUtils.YEAR_MONTH;
        }
        Map<String,Map<String, CapacityLoadVO>> capacityLoadVOMap = new HashMap<>();
        for (String month : monthList) {
            //该月的供应关系
            List<CapacitySupplyRelationshipVO> monthData = getMonthSupply(monthResultMap, month);
            //存该月的设备均衡结果
            List<CapacitySupplyRelationshipVO> resultList = new ArrayList<>();
            Map<String, CapacityLoadVO> resourceCapacityMap;
            if (CapacityBalanceTypeEnum.WEEK.getCode().equals(type)){
                resourceCapacityMap = resourceCapacityMonthMap.get(month);
            }else {
                //该月的设备负荷
                List<CapacityLoadVO> monthCapacityLoad = getMonthCapacityLoad(resourceCapacityMonthMap,  month);
                resourceCapacityMap = getMonthCapacityLoadMap(monthCapacityLoad, capacityMap, month);
            }
            //该月的超负荷设备，按负荷率从大到小排序
            List<CapacityLoadVO> capacityLoadVOList = resourceCapacityMap.values().stream().filter(
                            capacityLoadVO -> capacityLoadVO.getCapacityUtilization().compareTo(BigDecimal.valueOf(1)) > 0)
                    .sorted(Comparator.comparing(CapacityLoadVO::getCapacityUtilization).reversed()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(capacityLoadVOList)){
                //第一次平衡（规则2:设备均衡）
                for (CapacityLoadVO capacityLoadVO : capacityLoadVOList) {
                    //当前超产设备
                    String resourceCode = capacityLoadVO.getResourceCode();
                    //获取该超产设备的产能供应关系数据,并按生产节拍从大到小排序，锁定数据不移动，跳过
                    List<CapacitySupplyRelationshipVO> data = monthData.stream().filter(t -> resourceCode.equals(t.getResourceCode())
                                    && !(LockStatusEnum.LOCKED.getCode().equals(t.getLockStatus()) || SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel())))
                            .sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getBeat).reversed()).collect(Collectors.toList());
                    List<String> moveProductCodes = data.stream().map(CapacitySupplyRelationshipVO::getProductCode).distinct().collect(Collectors.toList());
                    Map<String, List<CapacitySupplyRelationshipVO>> monthProductMap = data.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));
                    for (String moveProductCode : moveProductCodes) {
                        //设备平衡标识
                        boolean flag = false;
                        //移动该月当前全部产品所需要的产能
                        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = monthProductMap.get(moveProductCode);
                        BigDecimal totalSupplyQuantity = capacitySupplyRelationshipVOS.stream()
                                .map(CapacitySupplyRelationshipVO::getSupplyQuantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //本厂编码和资源相同，上面的节拍是一样的
                        CapacitySupplyRelationshipVO relationshipVO = capacitySupplyRelationshipVOS.get(0);
                        BigDecimal needCapacity = totalSupplyQuantity.multiply(new BigDecimal(relationshipVO.getBeat()));
                        String operationCode = capacityLoadVO.getOperationCode();
                        //获取产品的候选资源
                        String key = moveProductCode+"-"+operationCode;
                        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productAndOperationOfResourceMap.getOrDefault(key, new ArrayList<>());
                        if (CollectionUtils.isEmpty(productCandidateResourceTimeVOS)){
                            log.info("设备均衡获取产品资源生产关系数据为空，key{}", key);
                        }
                        //获取生产类型相同的设备(这里同类型的设备本身应该就在产品的候选资源里，所以直接看优先级就可以了) 按照候选资源的优先级进行平衡
                        List<ProductCandidateResourceTimeVO> resource = productCandidateResourceTimeVOS.stream()
                                .filter(t -> !resourceCode.equals(t.getResourceCode()))
                                .sorted(Comparator.comparing(ProductCandidateResourceTimeVO::getPriority)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(resource)){
                            //尝试全部转移
                            for (ProductCandidateResourceTimeVO resourceTimeVO : resource) {
                                //这里有可能get不到，todayResourceCapacityMap只存了排上的设备，低优先级设备可能就没排上
                                CapacityLoadVO capacityLoadVO1 = resourceCapacityMap.get(resourceTimeVO.getResourceCode());
                                //该设备可能对于其他产品来说优先级高，别的产品排上了它
                                capacityLoadVO1 = getCapacityLoad(capacityLoadVO1, resourceTimeVO, month, capacityMap, physicalResourceMap, loadPattern);
                                if (capacityLoadVO1.getCapacityUtilization().compareTo(BigDecimal.valueOf(1)) < 0){
                                    //计算该设备的剩余可用产能
                                    BigDecimal availableCapacity = capacityLoadVO1.getAvailableCapacity().subtract(capacityLoadVO1.getProductionCapacity());
                                    //可用产能对应的数量(可接收的最大数量)
                                    BigDecimal demandQty = availableCapacity.divide(new BigDecimal(resourceTimeVO.getBeat().toString()), 0, RoundingMode.DOWN);
                                    //如果该设备剩余可用产能对应的可接收的最大数量大于该产品所需数量，则该设备可以完全转移该产品
                                    if (demandQty.compareTo(totalSupplyQuantity) >= 0){
                                        //能完全转移更新一下资源代码就行了,更新一下名称
                                        capacitySupplyRelationshipVOS.forEach(
                                                supplyRelationshipVO-> {
                                                    supplyRelationshipVO.setResourceCode(resourceTimeVO.getResourceCode());
                                                    supplyRelationshipVO.setResourceName(resourceTimeVO.getResourceName());
                                                    supplyRelationshipVO.setRule(CapacityBalanceRule.equipmentBalance.getCode());
                                                    supplyRelationshipVO.setBeat(String.valueOf(resourceTimeVO.getBeat()));
                                                }
                                        );
                                        //换设备后，节拍不一样了，对应占用的产能对于原设备和新设备是不一样的
                                        BigDecimal add = totalSupplyQuantity.multiply(new BigDecimal(relationshipVO.getBeat()));
                                        //更新该设备产能，新设备增加占用产能, 全部移动的情况下，新设备增加的产能等于供应数量*新设备节拍
                                        updateCapacityLoadVO(capacityLoadVO1,"add", totalSupplyQuantity ,add, resourceCapacityMap, resourceTimeVO.getResourceCode());

                                        //原设备减少占用产能，全部移动的情况下，原设备减少的产能等于供应数量*原设备节拍
                                        CapacityLoadVO capacityLoadVO2 = resourceCapacityMap.get(resourceCode);
                                        updateCapacityLoadVO(capacityLoadVO2, "subtract", totalSupplyQuantity, needCapacity, resourceCapacityMap, resourceCode);
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            //flag不是true，说明所有候选资源都无法完全转移，则按照候选资源的优先级尝试部分转移
                            if (!flag){
                                for (ProductCandidateResourceTimeVO resourceTimeVO : resource) {
                                    CapacityLoadVO capacityLoadVO1 = resourceCapacityMap.get(resourceTimeVO.getResourceCode());
                                    capacityLoadVO1 = getCapacityLoad(capacityLoadVO1, resourceTimeVO, month, capacityMap, physicalResourceMap, loadPattern);
                                    //如果该设备没有超载，尝试部分转移
                                    if (capacityLoadVO1.getCapacityUtilization().compareTo(BigDecimal.valueOf(1))< 0){
                                        //计算该设备的剩余可用产能（可接收的产能） 这里现在转移了该设备所能接收的最大产能
                                        BigDecimal availableCapacity = capacityLoadVO1.getAvailableCapacity().subtract(capacityLoadVO1.getProductionCapacity());
                                        //可用产能对应的数量(可接收的最大数量)

                                        BigDecimal sumQty = availableCapacity.divide(new BigDecimal(resourceTimeVO.getBeat().toString()), 0, RoundingMode.DOWN);
                                        BigDecimal demandQty;
                                        //将供应数量全部移动到新设备（新设备不够把供应数量全部接收）
                                        for (CapacitySupplyRelationshipVO supplyRelationshipVO : capacitySupplyRelationshipVOS) {
                                            if (supplyRelationshipVO.getSupplyQuantity().compareTo(sumQty) <= 0){
                                                //也就是变化数量
                                                demandQty = supplyRelationshipVO.getSupplyQuantity();
                                            }else {
                                                //sumQty一直在减少
                                                demandQty = sumQty;
                                            }
                                            BigDecimal add = demandQty.multiply(new BigDecimal(resourceTimeVO.getBeat().toString()));
                                            BigDecimal subtract = demandQty.multiply(new BigDecimal(supplyRelationshipVO.getBeat()));

                                            //部分转移新建产能供应关系，并添加到结果集，原供应关系更新，减少供应量,设备均衡时在同一个月设备变化了，供应时间还是这个月
                                            CapacitySupplyRelationshipVO newSupplyRelationshipVO = new CapacitySupplyRelationshipVO();
                                            BeanUtils.copyProperties(supplyRelationshipVO, newSupplyRelationshipVO);
                                            newSupplyRelationshipVO.setSupplyQuantity(demandQty);
                                            newSupplyRelationshipVO.setResourceCode(resourceTimeVO.getResourceCode());
                                            newSupplyRelationshipVO.setResourceName(resourceTimeVO.getResourceName());
                                            newSupplyRelationshipVO.setBeat(String.valueOf(resourceTimeVO.getBeat()));
                                            newSupplyRelationshipVO.setRule(CapacityBalanceRule.equipmentBalance.getCode());
                                            resultList.add(newSupplyRelationshipVO);
                                            supplyRelationshipVO.setSupplyQuantity(supplyRelationshipVO.getSupplyQuantity().subtract(demandQty));
                                            //更新该设备产能，新设备增加占用产能
                                            updateCapacityLoadVO(capacityLoadVO1,"add", demandQty, add, resourceCapacityMap, resourceTimeVO.getResourceCode());
                                            //原设备减少占用产能，
                                            CapacityLoadVO capacityLoadVO2 = resourceCapacityMap.get(resourceCode);
                                            updateCapacityLoadVO(capacityLoadVO2, "subtract",demandQty, subtract, resourceCapacityMap, resourceCode);
                                            sumQty = sumQty.subtract(demandQty);
                                            if (sumQty.compareTo(BigDecimal.ZERO) == 0){
                                                flag = true;
                                                break;
                                            }
                                        }
                                    }
                                    if (flag){
                                        break;
                                    }
                                }
                            }
                        }
                        if (capacityLoadVO.getCapacityUtilization().compareTo(BigDecimal.valueOf(1))<=0){
                            break;
                        }
                    }
                }
                //更新该月的设备均衡结果
                if (CollectionUtils.isNotEmpty(resultList)){
                    if (!monthResultMap.containsKey(month)){
                        monthResultMap.put(month, resultList);
                    }else {
                        monthResultMap.get(month).addAll(resultList);
                    }
                }
            }
            capacityLoadVOMap.put(month, resourceCapacityMap);
        }
/*        List<CapacitySupplyRelationshipVO> collect = monthResultMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        Map<String, List<CapacitySupplyRelationshipVO>> map = collect.stream()
                .filter(t -> t.getSupplyModel().equals(SupplyModelEnum.LOCAL.getCode()))
                .collect(Collectors.groupingBy(t -> t.getResourceCode() + "-" + DateUtils.dateToString(t.getSupplyTime(), DateUtils.YEAR_MONTH)));
        List<CapacityLoadVO> capacityLoadVOS = capacityLoadVOMap.values().stream().flatMap(t -> t.values().stream()).collect(Collectors.toList());
        for (CapacityLoadVO capacityLoadDTO : capacityLoadVOS) {
            String string = DateUtils.dateToString(capacityLoadDTO.getForecastTime(), DateUtils.YEAR_MONTH);
            String key = capacityLoadDTO.getResourceCode() + "-" + string;
            int sum = map.get(key).stream().mapToInt(t -> t.getSupplyQuantity().intValue()).sum();
            if (sum != capacityLoadDTO.getDemandQuantity().intValue()){
                log.info("{}月{}供应关系量{},负荷量{}", string, capacityLoadDTO.getResourceCode(),sum, capacityLoadDTO.getDemandQuantity());
            }
        }*/
        return capacityLoadVOMap;
    }


    private void stockProductionNew(List<String> monthList,
                                    Map<String, Map<String, CapacityLoadVO>> resourceCapacityMonthMap,
                                    Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap,
                                    Map<String, Integer> capacityMap,
                                    Map<String, PartRiskLevelVO> materialRiskLevelMap,
                                    Map<String, List<ProductCandidateResourceTimeVO>> productAndOperationOfResourceMap,
                                    Map<String, PhysicalResourceVO> physicalResourceMap,
                                    Map<String, OemVO> oemVOMap,
                                    Map<String, List<String>> productOfOemMap,
                                    String pattern,
                                    String type,
                                    Map<String, List<String>> productOfSourceProductListMap) {
        String loadPattern;
        if (CapacityBalanceTypeEnum.WEEK.getCode().equals(type)){
            loadPattern = DateUtils.COMMON_DATE_STR3;
        }else {
            loadPattern = DateUtils.YEAR_MONTH;
        }
        Collections.sort(monthList);
        String beforeMonth = monthList.get(0);
        for (String month : monthList) {
            //第一个月跳过，从次月开始
            if (month.equals(monthList.get(0))){
                continue;
            }
            List<CapacitySupplyRelationshipVO> inventoryBalanceResult = new ArrayList<>();

            //过滤超产设备，并按负荷率排序
            Map<String, CapacityLoadVO> resourceCapacityMap = resourceCapacityMonthMap.get(month);
            List<CapacityLoadVO> capacityLoadVOS = new ArrayList<>(resourceCapacityMap.values());
            capacityLoadVOS = capacityLoadVOS.stream().filter(capacityLoadVO -> capacityLoadVO.getCapacityUtilization().compareTo(BigDecimal.valueOf(1)) > 0)
                    .sorted(Comparator.comparing(CapacityLoadVO::getCapacityUtilization).reversed()).collect(Collectors.toList());

            List<CapacitySupplyRelationshipVO> monthData = getMonthSupply(monthResultMap, month);


            if (CollectionUtils.isNotEmpty(capacityLoadVOS)){
                for (CapacityLoadVO capacityLoadVO : capacityLoadVOS) {
                    //当前超产设备
                    String resourceCode = capacityLoadVO.getResourceCode();
                    List<CapacitySupplyRelationshipVO> data = monthData.stream().filter(t -> resourceCode.equals(t.getResourceCode())
                                    && !(LockStatusEnum.LOCKED.getCode().equals(t.getLockStatus()) || SupplyModelEnum.OUTSOURCED.getCode().equals(t.getSupplyModel())))
                            .sorted(Comparator.comparing(CapacitySupplyRelationshipVO::getBeat).reversed()).collect(Collectors.toList());
                    List<String> moveProductCodes = data.stream().map(CapacitySupplyRelationshipVO::getProductCode).distinct().collect(Collectors.toList());
                    Map<String, List<CapacitySupplyRelationshipVO>> monthProductMap = data.stream().collect(Collectors.groupingBy(CapacitySupplyRelationshipVO::getProductCode));

                    for (String moveProductCode : moveProductCodes) {


                        //高风险数据不移动
                        List<String> oemCodeList = getOemCodeList(productOfOemMap, productOfSourceProductListMap, moveProductCode);
                        boolean riskLevel = false;
                        riskLevel = isRiskLevel(materialRiskLevelMap, moveProductCode, oemCodeList, riskLevel);
                        boolean inFlag = false;
                        inFlag = isInFlag(oemVOMap, oemCodeList, inFlag);
                        if (riskLevel && inFlag) {
                            continue;
                        }

                        //设备平衡标识
                        boolean flag = false;
                        //移动该月当前全部产品所需要的产能
                        List<CapacitySupplyRelationshipVO> capacitySupplyRelationshipVOS = monthProductMap.get(moveProductCode);
                        BigDecimal totalSupplyQuantity = capacitySupplyRelationshipVOS.stream()
                                .map(CapacitySupplyRelationshipVO::getSupplyQuantity)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);
                        //本厂编码和资源相同，上面的节拍是一样的
                        CapacitySupplyRelationshipVO relationshipVO = capacitySupplyRelationshipVOS.get(0);
                        BigDecimal needCapacity = totalSupplyQuantity.multiply(new BigDecimal(relationshipVO.getBeat()));
                        String operationCode = capacityLoadVO.getOperationCode();
                        //获取产品的前一个月的候选资源
                        String key = moveProductCode+"-"+operationCode;
                        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = productAndOperationOfResourceMap.getOrDefault(key, new ArrayList<>());
                        if (CollectionUtils.isEmpty(productCandidateResourceTimeVOS)){
                            log.info("备库生产获取产品资源生产关系数据为空，key{}", key);
                        }
                        List<ProductCandidateResourceTimeVO> resource = productCandidateResourceTimeVOS.stream()
                                // .filter(t -> !resourceCode.equals(t.getResourceCode()))
                                .sorted(Comparator.comparing(ProductCandidateResourceTimeVO::getPriority)).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(resource)){
                            for (ProductCandidateResourceTimeVO resourceTimeVO : resource) {
                                //查该资源前一个月的负荷率是否超过100%
                                CapacityLoadVO capacityLoadVO1 = resourceCapacityMonthMap.get(beforeMonth).get(resourceTimeVO.getResourceCode());
                                capacityLoadVO1 = getCapacityLoad(capacityLoadVO1, resourceTimeVO, beforeMonth, capacityMap,physicalResourceMap, loadPattern);
                                if (capacityLoadVO1.getCapacityUtilization().compareTo(BigDecimal.valueOf(1)) < 0) {
                                    //计算该设备的剩余可用产能
                                    BigDecimal availableCapacity = capacityLoadVO1.getAvailableCapacity().subtract(capacityLoadVO1.getProductionCapacity());
                                    //可用产能对应的数量(可接收的最大数量)
                                    BigDecimal demandQty = availableCapacity.divide(new BigDecimal(resourceTimeVO.getBeat().toString()), 0, RoundingMode.DOWN);
                                    //如果该设备剩余可用产能大于该产品所需产能，则该设备可以完全转移该产品
                                    if (demandQty.compareTo(totalSupplyQuantity) >= 0){
                                        String finalBeforeMonth = beforeMonth;
                                        capacitySupplyRelationshipVOS.forEach(
                                                supplyRelationshipVO->{
                                                    supplyRelationshipVO.setResourceCode(resourceTimeVO.getResourceCode());
                                                    supplyRelationshipVO.setRule(CapacityBalanceRule.stockProduction.getCode());
                                                    supplyRelationshipVO.setSupplyTime(moveMonthSupplyTime(finalBeforeMonth, supplyRelationshipVO.getSupplyTime(), pattern));
                                                    supplyRelationshipVO.setBeat(String.valueOf(resourceTimeVO.getBeat()));
                                                }
                                        );
                                        BigDecimal add = totalSupplyQuantity.multiply(new BigDecimal(relationshipVO.getBeat()));
                                        //更新该设备产能，新设备增加占用产能
                                        updateCapacityLoadVO(capacityLoadVO1,"add", totalSupplyQuantity,add, resourceCapacityMonthMap.get(beforeMonth), resourceTimeVO.getResourceCode());
                                        //原设备减少占用产能，
                                        CapacityLoadVO capacityLoadVO2 = resourceCapacityMonthMap.get(month).get(resourceCode);
                                        updateCapacityLoadVO(capacityLoadVO2, "subtract",totalSupplyQuantity, needCapacity, resourceCapacityMonthMap.get(month), resourceCode);
                                        flag = true;
                                        break;
                                    }
                                }
                            }
                            //flag不是true，说明所有候选资源都无法完全转移，则按照候选资源的优先级尝试部分转移
                            if (!flag){
                                for (ProductCandidateResourceTimeVO resourceTimeVO : resource) {
                                    CapacityLoadVO capacityLoadVO1 = resourceCapacityMonthMap.get(beforeMonth).get(resourceTimeVO.getResourceCode());
                                    capacityLoadVO1 = getCapacityLoad(capacityLoadVO1, resourceTimeVO, beforeMonth, capacityMap,physicalResourceMap, loadPattern);
                                    //如果该设备没有超载，尝试部分转移
                                    if (capacityLoadVO1.getCapacityUtilization().compareTo(BigDecimal.valueOf(1))< 0){
                                        //计算该设备的剩余可用产能（可接收的产能） 这里现在转移了该设备所能接收的最大产能
                                        BigDecimal availableCapacity = capacityLoadVO1.getAvailableCapacity().subtract(capacityLoadVO1.getProductionCapacity());
                                        //可用产能对应的数量(可接收的最大数量)
                                        BigDecimal sumQty = availableCapacity.divide(new BigDecimal(resourceTimeVO.getBeat().toString()), 0, RoundingMode.DOWN);
                                        BigDecimal demandQty;

                                        //将供应数量全部移动到新设备（新设备不够把供应数量全部接收）
                                        for (CapacitySupplyRelationshipVO supplyRelationshipVO : capacitySupplyRelationshipVOS) {
                                            if (supplyRelationshipVO.getSupplyQuantity().compareTo(sumQty) <= 0){
                                                //也就是变化数量
                                                demandQty = supplyRelationshipVO.getSupplyQuantity();
                                            }else {
                                                //sumQty一直在减少
                                                demandQty = sumQty;
                                            }
                                            BigDecimal add = demandQty.multiply(new BigDecimal(resourceTimeVO.getBeat().toString()));
                                            BigDecimal subtract = demandQty.multiply(new BigDecimal(supplyRelationshipVO.getBeat()));

                                            //部分转移新建产能供应关系，并添加到结果集，原供应关系更新，减少供应量，备库生产供应时间提前了一个月
                                            CapacitySupplyRelationshipVO newSupplyRelationshipVO = new CapacitySupplyRelationshipVO();
                                            BeanUtils.copyProperties(supplyRelationshipVO, newSupplyRelationshipVO);
                                            newSupplyRelationshipVO.setSupplyQuantity(demandQty);
                                            newSupplyRelationshipVO.setResourceCode(resourceTimeVO.getResourceCode());
                                            newSupplyRelationshipVO.setResourceName(resourceTimeVO.getResourceName());
                                            newSupplyRelationshipVO.setBeat(String.valueOf(resourceTimeVO.getBeat()));
                                            newSupplyRelationshipVO.setRule(CapacityBalanceRule.stockProduction.getCode());
                                            newSupplyRelationshipVO.setSupplyTime(moveMonthSupplyTime(beforeMonth, supplyRelationshipVO.getSupplyTime(), pattern));
                                            inventoryBalanceResult.add(newSupplyRelationshipVO);
                                            supplyRelationshipVO.setSupplyQuantity(supplyRelationshipVO.getSupplyQuantity().subtract(demandQty));
                                            //更新该设备产能，新设备增加占用产能
                                            updateCapacityLoadVO(capacityLoadVO1,"add", demandQty, add, resourceCapacityMonthMap.get(beforeMonth), resourceTimeVO.getResourceCode());
                                            //原设备减少占用产能，
                                            CapacityLoadVO capacityLoadVO2 = resourceCapacityMonthMap.get(month).get(resourceCode);
                                            updateCapacityLoadVO(capacityLoadVO2, "subtract",demandQty, subtract, resourceCapacityMonthMap.get(month), resourceCode);
                                            sumQty = sumQty.subtract(demandQty);
                                            if (sumQty.compareTo(BigDecimal.ZERO) == 0){
                                                flag = true;
                                                break;
                                            }
                                        }
                                    }
                                    if (flag){
                                        break;
                                    }
                                }
                            }
                        }
                        if (capacityLoadVO.getCapacityUtilization().compareTo(BigDecimal.valueOf(1))<=0){
                            break;
                        }
                    }
                }
                if (CollectionUtils.isNotEmpty(inventoryBalanceResult)){
                    if (!monthResultMap.containsKey(month)){
                        monthResultMap.put(month, inventoryBalanceResult);
                    }else {
                        monthResultMap.get(month).addAll(inventoryBalanceResult);
                    }
                }
            }
            beforeMonth = month;
        }
    }

    private static List<String> getOemCodeList(Map<String, List<String>> productOfOemMap, Map<String, List<String>> productOfSourceProductListMap, String moveProductCode) {
        List<String> oemCodeList = productOfOemMap.get(moveProductCode);
        if (CollectionUtils.isEmpty(oemCodeList)) {
            List<String> sourceProductList = productOfSourceProductListMap.get(moveProductCode);
            if (CollectionUtils.isNotEmpty(sourceProductList)) {
                for (String sourceProduct : sourceProductList) {
                    List<String> sourceOemCodeList = productOfOemMap.get(sourceProduct);
                    if (CollectionUtils.isNotEmpty(sourceOemCodeList)) {
                        oemCodeList = sourceOemCodeList;
                        break;
                    }
                }
            }
        }
        return oemCodeList;
    }

    private static boolean isInFlag(Map<String, OemVO> oemVOMap, List<String> oemCodeList, boolean inFlag) {
        if (CollectionUtils.isNotEmpty(oemCodeList)) {
            for (String oemCode : oemCodeList) {
                if (oemVOMap.containsKey(oemCode)) {
                    OemVO oemVO = oemVOMap.get(oemCode);
                    if (OemTradeTypeEnum.IN.getCode().equals(oemVO.getMarketType())) {
                        inFlag = true;
                        break;
                    }
                }
            }
        }

        return inFlag;
    }

    private static boolean isRiskLevel(Map<String, PartRiskLevelVO> materialRiskLevelMap, String moveProductCode, List<String> oemCodeList, boolean riskLevel) {
        if (CollectionUtils.isNotEmpty(oemCodeList)) {
            for (String oemCode : oemCodeList) {
                String riskKey = oemCode + "_" + moveProductCode;
                if (materialRiskLevelMap.containsKey(riskKey)){
                    PartRiskLevelVO partRiskLevelVO = materialRiskLevelMap.get(riskKey);
                    if (RiskLevelEnum.HIGH.getCode().equals(partRiskLevelVO.getMaterialRiskLevel())){
                        riskLevel = true;
                        break;
                    }
                }
            }
        }
        return riskLevel;
    }


    private Date moveMonthSupplyTime(String beforeMonth, Date supplyTime, String pattern){
        String string = DateUtils.dateToString(supplyTime, pattern);
        String day = string.split("-")[2];
        //上个月的最后一天 28，29，30，31
        int maxDay = DateUtils.getMonthDayCount(Integer.parseInt(beforeMonth.split("-")[0]), Integer.parseInt(beforeMonth.split("-")[1]));
        if (Integer.parseInt(day) > maxDay){
            //本来是要移动到上个月那一天，例如原来是2025-03-10，备库生产则移到2025-02-10，3月以后的31号，则移动到上个月最后一天，3-30，3-31则移动到2月最后一天
            day = String.valueOf(maxDay);
        }
        return DateUtils.stringToDate(beforeMonth + "-" + day, pattern);
    }

    private List<CapacitySupplyRelationshipVO> getMonthSupply(Map<String, List<CapacitySupplyRelationshipVO>> monthResultMap, String month){
        List<CapacitySupplyRelationshipVO> monthSupply = new ArrayList<>();
        //这里key是2025-02-01， month是2025-02
        monthResultMap.forEach((key, value) -> {
            if (key.contains(month)) {
                monthSupply.addAll(value);
            }
        });
        return monthSupply;
    }

    private List<CapacityLoadVO> getMonthCapacityLoad(Map<String, Map<String, CapacityLoadVO>> resourceCapacityMonthMap, String month){
        List<CapacityLoadVO> monthCapacityLoad = new ArrayList<>();
        resourceCapacityMonthMap.forEach((key, value) -> {
            if (key.contains(month)) {
                monthCapacityLoad.addAll(value.values());
            }
        });
        return monthCapacityLoad;
    }

    /**
     * 将当月原本按天的负荷数据汇总到按月
     * @param monthCapacityLoad
     * @param month
     * @return
     */
    private Map<String, CapacityLoadVO> getMonthCapacityLoadMap(List<CapacityLoadVO> monthCapacityLoad, Map<String, Integer> capacityMap, String month){
        Map<String, CapacityLoadVO> map = new HashMap<>();
        Date date = DateUtils.stringToDate(month, DateUtils.YEAR_MONTH);
        Map<String, List<CapacityLoadVO>> dayMap = monthCapacityLoad.stream()
                .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getForecastTime(), DateUtils.YEAR_MONTH) + "-" + t.getResourceCode()));
        for (Map.Entry<String, List<CapacityLoadVO>> entry : dayMap.entrySet()) {
            CapacityLoadVO vo = entry.getValue().get(0);
            Map<String, BigDecimal> demandQuantityMap = entry.getValue().stream().collect(Collectors.toMap(
                    CapacityLoadVO::getResourceCode,
                    CapacityLoadVO::getDemandQuantity,
                    BigDecimal::add));
            Map<String, BigDecimal> productionCapacityMap = entry.getValue().stream().collect(Collectors.toMap(
                    CapacityLoadVO::getResourceCode,
                    CapacityLoadVO::getProductionCapacity,
                    BigDecimal::add));
            CapacityLoadVO monthCapacityLoadVO = new CapacityLoadVO();
            BeanUtils.copyProperties(vo, monthCapacityLoadVO);
            monthCapacityLoadVO.setForecastTime(date);
            monthCapacityLoadVO.setDemandQuantity(demandQuantityMap.get(vo.getResourceCode()));
            int i = getCapacityNum(capacityMap, month, vo.getResourceCode());
            monthCapacityLoadVO.setAvailableCapacity(new BigDecimal(i));
            monthCapacityLoadVO.setProductionCapacity(productionCapacityMap.get(vo.getResourceCode()));
            monthCapacityLoadVO.setCapacityUtilization(monthCapacityLoadVO.getProductionCapacity().divide(monthCapacityLoadVO.getAvailableCapacity(), 2, RoundingMode.HALF_UP));
            map.put(vo.getResourceCode(), monthCapacityLoadVO);
        }
        return map;
    }

    /**
     * 如果获取不到该资源对应的负荷，新建一个vo
     * @param vo
     * @param resourceTimeVO
     * @param month
     * @param capacityMap
     */
    private CapacityLoadVO getCapacityLoad(CapacityLoadVO vo,
                                           ProductCandidateResourceTimeVO resourceTimeVO,
                                           String month,
                                           Map<String, Integer> capacityMap,
                                           Map<String, PhysicalResourceVO> physicalResourceMap,
                                           String pattern){
        if (vo == null){
            String resourceCode = resourceTimeVO.getResourceCode();
            int i = getCapacityNum(capacityMap, month, resourceCode);
            PhysicalResourceVO physicalResourceVO = physicalResourceMap.get(resourceCode);
            vo = new CapacityLoadVO();
            vo.setProductionCapacity(BigDecimal.ZERO);
            vo.setForecastTime(DateUtils.stringToDate(month, pattern));
            vo.setResourceCode(resourceCode);
            vo.setResourceName(resourceTimeVO.getResourceName());
            vo.setResourceGroupCode(physicalResourceVO.getStandardResourceCode());
            vo.setResourceGroupName(physicalResourceVO.getStandardResourceName());
            vo.setPlantCode(physicalResourceVO.getProductionLine());
            vo.setOperationCode(resourceTimeVO.getOperationCode());
            vo.setOperationName(resourceTimeVO.getOperationName());
            vo.setAvailableCapacity(BigDecimal.valueOf(i));
            vo.setCapacityUtilization(BigDecimal.ZERO);
            vo.setDemandQuantity(BigDecimal.ZERO);

        }
        return vo;
    }
    private int getCapacityNum(Map<String, Integer> capacityMap, String month, String resourceCode){
        List<Integer> nums = new ArrayList<>();
        //这里key是2025-02-01， month是2025-02
        String resourceKey = resourceCode+"-"+month;
        capacityMap.forEach((key, value) -> {
            if (key.contains(resourceKey)) {
                nums.add(value);
            }
        });
        int sum = nums.stream().mapToInt(t -> t).sum();
        return sum;
    }

    private void updateCapacityLoadVO(CapacityLoadVO capacityLoadVO, String type, BigDecimal qty,BigDecimal demandQuantity, Map<String, CapacityLoadVO> todayResourceCapacityMap, String resourceCode) {
        capacityLoadVO.setProductionCapacity("add".equals(type) ? capacityLoadVO.getProductionCapacity().add(demandQuantity): capacityLoadVO.getProductionCapacity().subtract(demandQuantity));
        capacityLoadVO.setDemandQuantity("add".equals(type) ? capacityLoadVO.getDemandQuantity().add(qty): capacityLoadVO.getDemandQuantity().subtract(qty));
        capacityLoadVO.setCapacityUtilization(capacityLoadVO.getProductionCapacity().divide(capacityLoadVO.getAvailableCapacity(), 4, RoundingMode.HALF_UP));
        todayResourceCapacityMap.put(resourceCode, capacityLoadVO);
    }

    private CapacitySupplyRelationshipVO getCapacitySupply(String rule,
                                                           String month,
                                                           String productCode,
                                                           int qty,
                                                           int primitiveQty,
                                                           Map<String, NewProductStockPointVO> productMap,
                                                           ProductCandidateResourceTimeVO resourceTimeVO,
                                                           String pattern) {
        Date date = DateUtils.stringToDate(month, pattern);
        CapacitySupplyRelationshipVO capacitySupplyRelationshipVO = new CapacitySupplyRelationshipVO();
        capacitySupplyRelationshipVO.setSupplyModel(SupplyModelEnum.LOCAL.getCode());
        capacitySupplyRelationshipVO.setLockStatus(LockStatusEnum.UNLOCKED.getCode());
        capacitySupplyRelationshipVO.setDemandQuantity(BigDecimal.valueOf(primitiveQty));
        capacitySupplyRelationshipVO.setForecastTime(date);
        capacitySupplyRelationshipVO.setSupplyQuantity(BigDecimal.valueOf(qty));
        capacitySupplyRelationshipVO.setSupplyTime(date);
        capacitySupplyRelationshipVO.setVehicleModelCode(productMap.get(productCode).getVehicleModelCode());
        capacitySupplyRelationshipVO.setProductCode(productCode);
        capacitySupplyRelationshipVO.setProductName(productMap.get(productCode).getProductName());
        capacitySupplyRelationshipVO.setResourceCode(resourceTimeVO.getResourceCode());
        capacitySupplyRelationshipVO.setResourceName(resourceTimeVO.getResourceName());
        capacitySupplyRelationshipVO.setBeat(String.valueOf(resourceTimeVO.getBeat()));
        capacitySupplyRelationshipVO.setRule(rule);
        capacitySupplyRelationshipVO.setProductType(productMap.get(productCode).getProductType());
        capacitySupplyRelationshipVO.setOperationCode(resourceTimeVO.getOperationCode());
        capacitySupplyRelationshipVO.setOperationName(resourceTimeVO.getOperationName());
        return capacitySupplyRelationshipVO;
    }

    private CapacitySupplyRelationshipVO getVirtualResourceCapacitySupply(String rule,
                                                                          String month,
                                                                          String productCode,
                                                                          int qty,
                                                                          int primitiveQty,
                                                                          Map<String, NewProductStockPointVO> productMap,
                                                                          String operationCode,
                                                                          String operationName,
                                                                          String pattern) {
        Date date = DateUtils.stringToDate(month, pattern);
        CapacitySupplyRelationshipVO capacitySupplyRelationshipVO = new CapacitySupplyRelationshipVO();
        capacitySupplyRelationshipVO.setSupplyModel(SupplyModelEnum.LOCAL.getCode());
        capacitySupplyRelationshipVO.setLockStatus(LockStatusEnum.UNLOCKED.getCode());
        capacitySupplyRelationshipVO.setDemandQuantity(BigDecimal.valueOf(primitiveQty));
        capacitySupplyRelationshipVO.setForecastTime(date);
        capacitySupplyRelationshipVO.setSupplyQuantity(BigDecimal.valueOf(qty));
        capacitySupplyRelationshipVO.setSupplyTime(date);
        capacitySupplyRelationshipVO.setVehicleModelCode(productMap.get(productCode).getVehicleModelCode());
        capacitySupplyRelationshipVO.setProductCode(productCode);
        capacitySupplyRelationshipVO.setProductName(productMap.get(productCode).getProductName());
        capacitySupplyRelationshipVO.setResourceCode(VIRTUAL_RESOURCE);
        capacitySupplyRelationshipVO.setResourceName("虚拟资源");
        capacitySupplyRelationshipVO.setBeat("0");
        capacitySupplyRelationshipVO.setRule(rule);
        capacitySupplyRelationshipVO.setProductType(productMap.get(productCode).getProductType());
        capacitySupplyRelationshipVO.setOperationCode(operationCode);
        capacitySupplyRelationshipVO.setOperationName(operationName);
        return capacitySupplyRelationshipVO;
    }

    private AlgorithmLog createCapacityAlgoLog(AlgorithmLog masterAlgorithmLog) {
        String parentId = masterAlgorithmLog.getId();
        String scenario =SystemHolder.getScenario();
        String userId = SystemHolder.getUserId();
        log.info("创建产能平衡计算日志，parentId：{}，场景scenario：{}，创建者：{}", parentId, scenario, userId);
        String logId = masterAlgorithmLog.getId();
        Date date = new Date();
        AlgorithmLog algorithmLog = new AlgorithmLog();
        algorithmLog.setId(logId);
        algorithmLog.setExecutionNumber(logId);
        algorithmLog.setModuleCode(ModuleCodeEnum.CAPACITY.getCode());
        algorithmLog.setFilePath(masterAlgorithmLog.getFilePath());
        algorithmLog.setIpAddress("bpim-das-service");
        algorithmLog.setStartTime(date);
        algorithmLog.setFailMsg(masterAlgorithmLog.getFailMsg());
        algorithmLog.setStatus(masterAlgorithmLog.getStatus());
        algorithmLog.setModifyTime(date);
        algorithmLog.setCreateTime(date);
        algorithmLog.setEndTime(date);
        algorithmLog.setScenario(scenario);
        algorithmLog.setCreator(userId);
        algorithmLog.setModifier(userId);
        ipsFeign.createAlgorithmLog(algorithmLog);
        log.info("产能平衡计算日志创建完成，logId：{}", logId);
        return algorithmLog;
    }

    private AlgorithmLog updateCapacityAlgoLog(AlgorithmLog masterAlgorithmLog) {
        masterAlgorithmLog.setEndTime(new Date());
        ipsFeign.updateAlgorithmLog(masterAlgorithmLog);
        log.info("产能平衡计算日志修改完成，logId：{}", masterAlgorithmLog.getId());
        return masterAlgorithmLog;
    }
}
