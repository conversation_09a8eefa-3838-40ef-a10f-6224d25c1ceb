package com.yhl.scp.mps.nakedGlass.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.nakedGlass.convertor.NakedGlassConvertor;
import com.yhl.scp.mps.nakedGlass.domain.entity.NakedGlassDO;
import com.yhl.scp.mps.nakedGlass.domain.service.NakedGlassDomainService;
import com.yhl.scp.mps.nakedGlass.dto.NakedGlassDTO;
import com.yhl.scp.mps.nakedGlass.infrastructure.dao.NakedGlassDao;
import com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO;
import com.yhl.scp.mps.nakedGlass.service.NakedGlassService;
import com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NakedGlassServiceImpl extends AbstractService implements NakedGlassService {

    @Resource
    private NakedGlassDao nakedGlassDao;

    @Resource
    private NakedGlassDomainService nakedGlassDomainService;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse<Void> doCreate(NakedGlassDTO nakedGlassDTO) {
        // 0.数据转换
        NakedGlassDO nakedGlassDO = NakedGlassConvertor.INSTANCE.dto2Do(nakedGlassDTO);
        NakedGlassPO nakedGlassPO = NakedGlassConvertor.INSTANCE.dto2Po(nakedGlassDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        nakedGlassDomainService.validation(nakedGlassDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(nakedGlassPO);
        nakedGlassDao.insert(nakedGlassPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(NakedGlassDTO nakedGlassDTO) {
        // 0.数据转换
        NakedGlassDO nakedGlassDO = NakedGlassConvertor.INSTANCE.dto2Do(nakedGlassDTO);
        NakedGlassPO nakedGlassPO = NakedGlassConvertor.INSTANCE.dto2Po(nakedGlassDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        nakedGlassDomainService.validation(nakedGlassDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(nakedGlassPO);
        nakedGlassDao.update(nakedGlassPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<NakedGlassDTO> list) {
        List<NakedGlassPO> newList = NakedGlassConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        nakedGlassDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<NakedGlassDTO> list) {
        List<NakedGlassPO> newList = NakedGlassConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        nakedGlassDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return nakedGlassDao.deleteBatch(idList);
        }
        return nakedGlassDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public NakedGlassVO selectByPrimaryKey(String id) {
        NakedGlassPO po = nakedGlassDao.selectByPrimaryKey(id);
        return NakedGlassConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "NAKED_GLASS")
    public List<NakedGlassVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "NAKED_GLASS")
    public List<NakedGlassVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<NakedGlassVO> dataList = nakedGlassDao.selectByCondition(sortParam, queryCriteriaParam);
        NakedGlassServiceImpl target = SpringBeanUtils.getBean(NakedGlassServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<NakedGlassVO> selectByParams(Map<String, Object> params) {
        List<NakedGlassPO> list = nakedGlassDao.selectByParams(params);
        return NakedGlassConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<NakedGlassVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse syncData(String tenantCode) {
        log.info("开始同步裸玻成品映射数据");
        try {
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MDM.getCode(),
                    ApiCategoryEnum.NAKED_GLASS.getCode(), MapUtil.newHashMap());
            return BaseResponse.success("同步数据完成");
        } catch (Exception e) {
            log.error("同步资源组数据报错，{}", e.getMessage());
            throw new BusinessException("同步资源组数据报错，{}", e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> syncNakedGlassData(List<NakedGlassDTO> list) {
        List<NakedGlassPO> nakedGlassPOS = nakedGlassDao.selectByParams(MapUtil.newHashMap());
        //全量新增，先删除旧数据
        List<String> ids = nakedGlassPOS.stream().map(NakedGlassPO::getId).collect(Collectors.toList());
        if (!ids.isEmpty()){
            this.doDelete(ids);
        }
        //将同步的数据插入表中
        doCreateNewBatch(list);

        return BaseResponse.success("数据同步成功");
    }

    @Override
    public void doCreateNewBatch(List<NakedGlassDTO> list) {
        List<NakedGlassPO> newList = NakedGlassConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        nakedGlassDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public List<NakedGlassVO> selectNakedGlassByFinishedProducts(List<String> productCodes) {
        if(CollectionUtils.isNotEmpty(productCodes)){
            return NakedGlassConvertor.INSTANCE.po2Vos(nakedGlassDao.selectNakedGlassByFinishedProducts(productCodes));
        }
        return Collections.emptyList();
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<NakedGlassVO> invocation(List<NakedGlassVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
