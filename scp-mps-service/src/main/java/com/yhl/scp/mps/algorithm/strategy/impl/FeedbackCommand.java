package com.yhl.scp.mps.algorithm.strategy.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.StopWatch;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.yhl.aps.api.runner.APSInput;
import com.yhl.aps.api.runner.APSOutput;
import com.yhl.aps.api.runner.APSRunner;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.ams.basic.enums.ScheduleTypeEnum;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mds.basic.resource.vo.PhysicalResourceBasicVO;
import com.yhl.scp.mds.enums.StandardStepEnum;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.adjust.support.AdjustPlanSupport;
import com.yhl.scp.mps.algorithm.enums.DemandTypeEnum;
import com.yhl.scp.mps.algorithm.schedule.input.RzzBaseAlgorithmData;
import com.yhl.scp.mps.algorithm.schedule.input.RzzOperationFeedbackVO;
import com.yhl.scp.mps.domain.algorithm.RzzAMSSupportService;
import com.yhl.scp.mps.domain.algorithm.RzzAlgorithmOutputService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataConfigService;
import com.yhl.scp.mps.domain.algorithm.RzzBaseAlgorithmDataService;
import com.yhl.scp.mps.enums.ReportingStatusEnum;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.vo.OperationDemandVO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.sds.basic.enums.ReportingTypeEnum;
import com.yhl.scp.sds.extension.feedback.dto.FeedbackProductionDTO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.pegging.dto.DemandDTO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.pegging.convertor.DemandConvertor;
import com.yhl.scp.sds.pegging.service.DemandService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: FeedbackCommand.java
 * @Description: 生产反馈命令
 * @Package: com.yhl.aps.algorithm.data.command
 * @Date: 2021年12月30日 下午5:13:09
 * @Copyright 悠桦林信息科技（上海）有限公司
 * @since JDK 1.8
 */
@Slf4j
@Component
public class FeedbackCommand extends RzzBaseAlgorithmDataConfigService {


    private static final String RESOURCE_LOCK_PREFIX = "FEEDBACK_EXECUTE_RESOURCE_LIST";
    private static final String FINISHED = "finished";
    @Resource
    protected IpsNewFeign ipsNewFeign;
    @Resource
    protected IpsFeign ipsFeign;
    @Resource
    AdjustPlanSupport adjustPlanSupport;
    @Autowired
    private RzzBaseAlgorithmDataService baseAlgorithmDataService;
    @Resource
    private RzzAlgorithmOutputService algorithmOutputService;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private OperationService operationService;
    @Resource
    private MasterPlanExtDao masterPlanExtDao;
    @Resource
    private RzzAMSSupportService rzzAMSSupportService;
    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private MoldChangeTimeService moldChangeTimeService;
    @Autowired
    private RedisUtil redisUtil;
    @Resource
    private DemandService demandService;

    public static List<String> convertStringToList(String jsonString) {
        // 解析 JSON 字符串为 JSONArray
        JSONArray jsonArray = JSON.parseArray(jsonString);
        // 将 JSONArray 转换为 List<String>
        return jsonArray.toJavaList(String.class);
    }

    /**
     * 根据权限执行生产反馈
     *
     * @param masterAlgorithmLog log
     */
    @Transactional(rollbackFor = Exception.class)
    public void executeCommandLock(AlgorithmLog masterAlgorithmLog) {
        List<PhysicalResourceVO> physicalResourceVOS;
        if (null == masterAlgorithmLog) {
            physicalResourceVOS = operationTaskExtDao.selectPhysicalResourceByPlanner(SystemHolder.getUserId());
            // 同时执行校验
            adjustPlanSupport.checkPermission();
        } else {
            physicalResourceVOS = operationTaskExtDao.selectPhysicalResourceByPlanner(masterAlgorithmLog.getCreator());
        }
        if (CollectionUtils.isEmpty(physicalResourceVOS)) {
            throw new BusinessException("当前用户没有有权限的资源执行生产反馈算法");
        }
        List<String> resourceIds = physicalResourceVOS.stream().map(BaseVO::getId).collect(Collectors.toList());
        if (isResourceLocked(resourceIds)) {
            try {
                // 无限能力资源ids
                List<String> infiniteCapacityResourceIds = operationTaskExtDao.selectInfiniteCapacityResourceIds();
                resourceIds.addAll(infiniteCapacityResourceIds);
                executeCommand(masterAlgorithmLog, resourceIds, physicalResourceVOS);
            } finally {
                deleteLock(resourceIds);
            }
        }
    }

    private boolean isResourceLocked(List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return false;
        }
        if (redisUtil.hasKey(RESOURCE_LOCK_PREFIX)) {
            List<Object> objects = redisUtil.lGet(RESOURCE_LOCK_PREFIX);
            List<String> list = (List<String>) objects.get(0);
            List<String> existingOperationIds = list;
            List<String> intersection = CollectionUtils.getInterSection(
                    resourceIds, existingOperationIds);

            if (CollectionUtils.isNotEmpty(intersection)) {
                throw new BusinessException("产线{}正在运行排产任务，请稍后再试！", intersection);
            }
            existingOperationIds.addAll(resourceIds);
            redisUtil.delete(RESOURCE_LOCK_PREFIX);
            redisUtil.lSet(RESOURCE_LOCK_PREFIX, existingOperationIds);
        } else {
            redisUtil.lSet(RESOURCE_LOCK_PREFIX, resourceIds);
        }
        return true;
    }

    /**
     * 从当前执行的feedback资源缓存中删掉相应的资源
     *
     * @param resourceIds
     */
    private void deleteLock(List<String> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return;
        }
        if (redisUtil.hasKey(RESOURCE_LOCK_PREFIX)) {
            List<Object> objects = redisUtil.lGet(RESOURCE_LOCK_PREFIX);
            List<String> list = (List<String>) objects.get(0);
            List<String> existingOperationIds = list;
            List<String> deleteList = (List<String>) CollectionUtils.getDiffSection(existingOperationIds, resourceIds);
            redisUtil.delete(RESOURCE_LOCK_PREFIX);
            redisUtil.lSet(RESOURCE_LOCK_PREFIX, deleteList);
        }
    }

    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;

    public void executeCommand(AlgorithmLog masterAlgorithmLog,List<String> permissionResourceIds,
                               List<PhysicalResourceVO> keyPhysicalResourceVOS) {
        AlgorithmLog amsAlgoLog = createAmsAlgoLog(masterAlgorithmLog, ModuleCodeEnum.FEEDBACK.getCode(), keyPhysicalResourceVOS);
        String logId = amsAlgoLog.getId();
        StopWatch stopWatch = new StopWatch("调用生产反馈算法");
        try {
            log.info("开始处理历史开始未完工工序");
            mpsProReportingFeedbackService.doProcessStartOperation();
            log.info("结束处理历史开始未完工工序");
            //查询生产反馈数据
            stopWatch.start("收集处理生产反馈数据");
            PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
            List<RzzOperationFeedbackVO> feedback = getFeedback(planningHorizon, permissionResourceIds);
            if (CollectionUtils.isEmpty(feedback)) {
                log.info("没有符合条件的生产反馈数据，生产反馈算法跳过");
                amsAlgoLog.setFailMsg("没有符合条件的生产反馈数据，生产反馈算法跳过");
                amsAlgoLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
                return;
            } else {
                log.info("生产反馈数据条数：{}", feedback.size());
            }
            stopWatch.stop();
            //组装算法参数
            stopWatch.start("组装算法参数");
            List<String> resourceIdAll = feedback.stream().map(RzzOperationFeedbackVO::getResourceId).distinct().collect(Collectors.toList());
            List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.getPhysicalResourcesByParams(new HashMap<>());
            // 获取资源ids
            List<String> resourceIds = getResourceIds(resourceIdAll, physicalResourceVOS);
            List<OperationVO> resourceOfOperationList = operationTaskExtDao.selectOperationByResourceIds(resourceIds);
            List<String> existOperation = resourceOfOperationList.stream().map(BaseVO::getId).collect(Collectors.toList());
            feedback = feedback.stream().filter(t -> existOperation.contains(t.getOperationId())).collect(Collectors.toList());

            List<MoldChangeTimeVO> moldChangeTimeVOS = moldChangeTimeService.selectTime();
            RzzBaseAlgorithmData baseAlgorithmData = baseAlgorithmDataService.getAlgorithmData(physicalResourceVOS, resourceOfOperationList,
                    new ArrayList<>(), ScheduleTypeEnum.HANDWORK.getCode(), planningHorizon, null,
                    null, null, null, moldChangeTimeVOS, null, null);
            baseAlgorithmData.setPlanningHorizon(planningHorizon);
            baseAlgorithmData.setFeedback(feedback);
            APSRunner apsRunner = new APSRunner();
            APSInput apsInput = baseAlgorithmDataService.getApsInput(baseAlgorithmData, getOs(logId));
            stopWatch.stop();
            stopWatch.start("生成算法配置文件");
            //生成算法配置文件
            writeConf(apsInput, baseAlgorithmData);
            stopWatch.stop();

            // 调用算法
            stopWatch.start("执行算法");
            APSOutput apsOutput = apsRunner.run(apsInput);
            stopWatch.stop();
            stopWatch.start("解析算法输出");
            algorithmOutputService.doAnalysisAlgorithmOutputDataCommon(apsOutput, new ArrayList<>(baseAlgorithmData.getWorkOrderVOMap().values()), baseAlgorithmData.getSourceOperations(), logId, planningHorizon, false,new HashMap<>());
            stopWatch.stop();

            // 更新ips算法日志调用状态
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            log.error("AMS-生产反馈算法运行失败：", e);
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            amsAlgoLog.setFailMsg(e.getMessage());
            throw new RuntimeException("AMS算法运行失败：" + e.getMessage());
        } finally {
            log.info(stopWatch.prettyPrint(TimeUnit.SECONDS));
            amsAlgoLog.setEndTime(new Date());
            CompletableFuture.runAsync(() -> ipsFeign.updateAlgorithmLog(amsAlgoLog));
            log.info("AMS生产反馈算法日志更新完成");
        }
    }

    public List<String> getResourceIds(List<String> resourceIds, List<PhysicalResourceVO> physicalResourceVOS){
        Map<String, PhysicalResourceVO> physicalResourceVOMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(PhysicalResourceBasicVO::getPhysicalResourceCode, Function.identity()));
        List<CollectionValueVO> yztResource = ipsFeign.getByCollectionCode("YZT_RESOURCE");
        // 需要搭配压制台的资源
        List<String> yztResourceList = yztResource.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        List<String> yztResourceIdList = yztResourceList.stream().map(t -> physicalResourceVOMap.get(t).getId()).collect(Collectors.toList());
        List<String> limitResourceIds = resourceIds.stream().filter(yztResourceIdList::contains).collect(Collectors.toList());
        // 涉及到压制炉的调整
        if (CollectionUtils.isNotEmpty(limitResourceIds)) {
            List<String> yztToolResourceIds = physicalResourceVOS.stream()
                    .filter(t -> "YZT".equals(t.getRemark()))
                    .map(BaseVO::getId)
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(yztToolResourceIds)) {
                resourceIds.addAll(yztToolResourceIds);
                // S1YZ01, S1YZ02
                resourceIds.addAll(yztResourceIdList);
            }
        }
        return resourceIds.stream().distinct().collect(Collectors.toList());
    }


    @Override
    protected void writeConf(APSInput apsInput, RzzBaseAlgorithmData baseAlgorithmData) {
        try {
            JSONObject conf = getConf();
            JSONArray modules = conf.getJSONArray("modules");
            //confLoader(排程参数加载模块)
            PlanningHorizonVO planningHorizon = baseAlgorithmData.getPlanningHorizon();
            JSONObject confLoaderObject = getConfLoader(planningHorizon.getPlanStartTime(), planningHorizon.getPlanEndTime());
            JSONObject confLoader = confLoaderObject.getJSONObject("confLoader");
            confLoader.put("operationAssignStrategy", "EarliestAvailable");
            confLoader.put("noSwitchIfSameProduct", true);
            //数据加载
            modules.add(confLoaderObject);
            //规则加载
            modules.add(getRuleLoader());
            //生产反馈
            modules.add(getFeedbackModule());
            modules.add(getResultDumper());
            conf.put("runTimeParameter", getRunTimeParameter(baseAlgorithmData));
            Gson gson = new Gson();
            JsonElement config2 = gson.toJsonTree(conf);
            apsInput.setConfig(config2);
        } catch (Exception e) {
            log.error("写入配置文件报错:" + e.getMessage(), e);
            throw new BusinessException("写入配置文件报错:" + e.getMessage());
        }
    }


    private JSONObject getFeedbackModule() {
        JSONObject jsonObject = new JSONObject();
        JSONObject assignStrategyForOperationObj = new JSONObject();
        JSONObject lastObj = new JSONObject();
        lastObj.put("last", new JSONObject());
        lastObj.put("autoAdjustOtherSchedules", true);
        assignStrategyForOperationObj.put("assignStrategyForOperation", lastObj);
        jsonObject.put("rzzFeedbackModule", assignStrategyForOperationObj);
        return jsonObject;
    }

    private JSONObject getRunTimeParameter(RzzBaseAlgorithmData baseAlgorithmData) {
        JSONObject jsonObject = new JSONObject();
        //供需关系
        jsonObject.put("fulfillment", baseAlgorithmData.getFulfillmentList());
        //反馈
        jsonObject.put("feedback", baseAlgorithmData.getFeedback());
        //镀膜切换时间-产品切换时间表
        jsonObject.put("switchDurForProduct", getSwitchDurForProductList(baseAlgorithmData));
        //镀膜切换时间-规格切换时间表
        jsonObject.put("switchDurForSpec", getSwitchDurForSpecList(baseAlgorithmData));
        //工具资源切换时间-清洗时间换型
//        jsonObject.put("switchDurForTool", getSwitchDurForToolList(baseAlgorithmData));
        return jsonObject;
    }

    public List<RzzOperationFeedbackVO> getFeedback(PlanningHorizonVO planningHorizon, List<String> permissionResourceIds) {
        //只处理锁定期的报工数据
        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        Date planEndTime = planningHorizon.getPlanEndTime();
        //获取锁定期内生产反馈信息，若反馈时间为空默认为计划时间
        List<FeedbackProductionVO> allFeedbackProduction = masterPlanExtDao.selectLockFeedbackList(historyRetrospectStartTime,
                planEndTime, permissionResourceIds);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        List<RzzOperationFeedbackVO> operationFeedbackVOS = new ArrayList<>();
        if (CollectionUtils.isEmpty(allFeedbackProduction)) {
            return operationFeedbackVOS;
        }
        List<FeedbackProductionDTO> list = new ArrayList<>();
        allFeedbackProduction.forEach(feedbackProductionVO -> {
            FeedbackProductionDTO feedbackProductionDTO = new FeedbackProductionDTO();
            BeanUtils.copyProperties(feedbackProductionVO, feedbackProductionDTO);
            list.add(feedbackProductionDTO);
        });
        // 计算工序及制造订单的状态
        rzzAMSSupportService.doBatchPlanFeedBackProductionProcess(list);
        validateFeedbackProduction(allFeedbackProduction);
        List<FeedbackProductionVO> feedbackProductionVOS = mergeFeedBack(allFeedbackProduction);

        List<String> standardStepList = ListUtil.of(StandardStepEnum.FORMING_PROCESS.getCode(), StandardStepEnum.COATING_PROCESS.getCode());
        List<FeedbackProductionVO> notKeyOperationList = new ArrayList<>();
        feedbackProductionVOS.forEach(feedbackOperationVO -> {
            // 仅关键工序进入生产反馈算法
            String standardStepType = feedbackOperationVO.getStandardStepCode();
            if (!standardStepList.contains(standardStepType)) {
                // 非关键工序，扣减demand数量
                notKeyOperationList.add(feedbackOperationVO);
                return;
            }
            RzzOperationFeedbackVO operationFeedbackVO = new RzzOperationFeedbackVO();
            //工序id
            operationFeedbackVO.setOperationId(feedbackOperationVO.getOperationId());
            //当前时间
            if (ObjectUtils.isNotEmpty(feedbackOperationVO.getReportingTime())) {
                String reportingTime = simpleDateFormat.format(feedbackOperationVO.getReportingTime());
                operationFeedbackVO.setCurrentTime(reportingTime);
            }
            //实际开始时间
            if (ObjectUtils.isNotEmpty(feedbackOperationVO.getStartTime())) {
                String startTime = simpleDateFormat.format(feedbackOperationVO.getStartTime());
                operationFeedbackVO.setProcessBeginTime(startTime);
            }
            if (ObjectUtils.isNotEmpty(feedbackOperationVO.getEndTime())) {
                //实际结束时间
                String endTime = simpleDateFormat.format(feedbackOperationVO.getEndTime());
                operationFeedbackVO.setProcessEndTime(endTime);
            }
            //物理资源id
            operationFeedbackVO.setResourceId(feedbackOperationVO.getPhysicalResourceId());
            //实际产量
            operationFeedbackVO.setQuantity(feedbackOperationVO.getReportingQuantity());
            //实际报废量
            operationFeedbackVO.setQuantityOfWaste(feedbackOperationVO.getReportingScrap());
            //实际进度
            BigDecimal reportingProgress = feedbackOperationVO.getReportingProgress();
            operationFeedbackVO.setOperationProcess(reportingProgress);
            //状态
            operationFeedbackVO.setStatus(feedbackOperationVO.getReportingStatus());
            operationFeedbackVOS.add(operationFeedbackVO);
        });
        afterProcessFeedBackDemand(notKeyOperationList);
        return operationFeedbackVOS;
    }

    private void afterProcessFeedBackDemand(List<FeedbackProductionVO> notKeyOperationList) {
        log.info("需要计算完工影响demand数量的工序行：{}", notKeyOperationList.size());
        if (CollectionUtils.isEmpty(notKeyOperationList)) {
            return;
        }
        List<String> childOperationId = StreamUtils.columnToList(notKeyOperationList, FeedbackProductionVO::getOperationId);
        List<OperationDemandVO> operationDemands = operationTaskExtDao.selectOperationDemandByOperationIds(childOperationId);
        if (CollectionUtils.isEmpty(operationDemands)) {
            return;
        }
        Map<String, FeedbackProductionVO> operationIdQuantityMap = StreamUtils.mapByColumn(notKeyOperationList, FeedbackProductionVO::getOperationId);
        List<String> demandIds = StreamUtils.columnToList(operationDemands, OperationDemandVO::getDemandId);
        List<DemandVO> demandVOS = demandService.selectByDemandIds(demandIds);
        Map<String, DemandVO> demandMap = StreamUtils.mapByColumn(demandVOS, DemandVO::getId);

        List<String> deleteDemandIds = new ArrayList<>();
        List<DemandDTO> updateDemandList = new ArrayList<>();
        DemandConvertor instance = DemandConvertor.INSTANCE;
        for (OperationDemandVO item : operationDemands) {
            String demandId = item.getDemandId();
            String operationId = item.getOperationId();
            DemandVO demandVO = demandMap.get(demandId);
            String demandType = demandVO.getDemandType();
            if (!demandType.equals(DemandTypeEnum.WORK_ORDER_DEMAND.getCode())) {
                continue;
            }
            FeedbackProductionVO feedbackProductionVO = operationIdQuantityMap.get(operationId);
            // 仅完工状态的工序计算扣减demand
            String reportingStatus = feedbackProductionVO.getReportingStatus();
            if (!reportingStatus.equals(FINISHED)) {
                continue;
            }
            BigDecimal reportingQuantity = feedbackProductionVO.getReportingQuantity();
            BigDecimal yield = Objects.isNull(item.getYield()) ? BigDecimal.ONE : item.getYield();
            BigDecimal inputFactor = Objects.isNull(item.getInputFactor()) ? BigDecimal.ONE : item.getInputFactor();
            BigDecimal newReportingQuantity = reportingQuantity.divide(yield, 4, RoundingMode.HALF_UP)
                    .multiply(inputFactor)
                    .setScale(2, RoundingMode.HALF_UP);
            BigDecimal originalQty = item.getQuantity();
            if (originalQty.compareTo(newReportingQuantity) <= 0) {
                deleteDemandIds.add(demandId);
            } else {
                BigDecimal value = originalQty.subtract(newReportingQuantity);
                demandVO.setQuantity(value);
                demandVO.setUnfulfilledQuantity(value);
                updateDemandList.add(instance.vo2Dto(demandVO));
            }
        }
        log.info("生产反馈修改demand数量：{}", updateDemandList.size());
        if (CollectionUtils.isNotEmpty(updateDemandList)) {
            Lists.partition(updateDemandList, 2000).forEach(p -> demandService.doUpdateBatch(p, false));
        }
        log.info("生产反馈删除demand数量：{}", deleteDemandIds.size());
        if (CollectionUtils.isNotEmpty(deleteDemandIds)) {
            demandService.doDelete(deleteDemandIds, false);
        }
    }

    /**
     * 校验数据
     *
     * @param allFeedbackProduction
     **/
    private void validateFeedbackProduction(List<FeedbackProductionVO> allFeedbackProduction) {
        if (CollectionUtils.isEmpty(allFeedbackProduction)) {
            return;
        }
        //反馈父工序
        List<FeedbackProductionVO> parentFeedbackOperations = allFeedbackProduction.stream().filter(k -> ReportingTypeEnum.PARENT.getCode().equals(k.getReportingType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(parentFeedbackOperations)) {
            return;
        }
        //查看父工序是否有任务
        List<String> parentOperationIds = parentFeedbackOperations.stream().map(FeedbackProductionVO::getOperationId).collect(Collectors.toList());
        List<OperationVO> parentOperations = operationService.getVOListByIds(parentOperationIds);
        List<OperationVO> subOperations = operationService.selectVOByParams(ImmutableMap.of("parentIds", parentOperationIds));
        if (CollectionUtils.isEmpty(subOperations)) {
            subOperations = new ArrayList<>();
        }
        Map<String, List<OperationVO>> parentId2SubOperationsMap = subOperations.stream().collect(Collectors.groupingBy(OperationVO::getParentId));
        List<String> noTaskParentOperationCodes = parentOperations.stream().filter(k -> {
            List<OperationVO> operationVOS = parentId2SubOperationsMap.get(k.getId());
            if (CollectionUtils.isEmpty(operationVOS)) {
                return true;
            }
            return false;
        }).map(OperationVO::getOperationCode).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(noTaskParentOperationCodes)) {
            throw new BusinessException("反馈中存在未计划的父工序，工序代码为：" + String.join(",", noTaskParentOperationCodes));
        }
    }

    private List<FeedbackProductionVO> mergeFeedBack(List<FeedbackProductionVO> feedBacks) {
        //按照父工序的工序代码和资源分组
        List<FeedbackProductionVO> parentFeedbackList = feedBacks.stream().filter(item -> StringUtils.isBlank(item.getParentOperationId())).collect(Collectors.toList());
        List<FeedbackProductionVO> subFeedbackList = feedBacks.stream().filter(item -> StringUtils.isNotBlank(item.getParentOperationId())).collect(Collectors.toList());
        List<FeedbackProductionVO> feedBacksResult = new ArrayList<>(subFeedbackList);
        Map<String, List<FeedbackProductionVO>> parentFeedbackMapOfJoinKey = parentFeedbackList.stream().collect(Collectors.groupingBy(item -> String.join("&", item.getOperationId(), item.getPhysicalResourceId())));
        for (Map.Entry<String, List<FeedbackProductionVO>> entry : parentFeedbackMapOfJoinKey.entrySet()) {
            List<Integer> indexAllList = new ArrayList<>();
            List<FeedbackProductionVO> value = entry.getValue();
            //按照实绩开始实绩排序
            List<FeedbackProductionVO> sortFeedbackList = value.stream().sorted(Comparator.comparing(FeedbackProductionVO::getStartTime)).collect(Collectors.toList());
            Map<Integer, List<Integer>> map = new HashMap<>(16);
            List<Integer> mergeIndex = new ArrayList<>();
            int groupKey = 1;
            for (int i = 0; i < sortFeedbackList.size() - 1; i++) {
                indexAllList.add(i);
                FeedbackProductionVO feedBack = sortFeedbackList.get(i);
                FeedbackProductionVO afterFeedback = sortFeedbackList.get(i + 1);
                long endTime = feedBack.getEndTime().getTime();
                long startTime = afterFeedback.getStartTime().getTime();
                //误差可以1秒
                if (endTime != startTime && startTime != (endTime + 1000)) {
                    //无缝衔接的反馈数据
                    groupKey++;
                    continue;
                }
                mergeIndex.add(i);
                mergeIndex.add(i + 1);
                map.put(groupKey, mergeIndex);
            }
            indexAllList.add(sortFeedbackList.size() - 1);
            if (MapUtils.isNotEmpty(map)) {
                List<Integer> mergeIndexList = new ArrayList<>();
                for (Map.Entry<Integer, List<Integer>> listEntry : map.entrySet()) {
                    FeedbackProductionVO mergeFeedBack = new FeedbackProductionVO();
                    //合并无缝衔接的反馈数据
                    TreeSet<Integer> indexTreeSet = new TreeSet<>(listEntry.getValue());
                    FeedbackProductionVO firstFeedBack = sortFeedbackList.get(indexTreeSet.first());
                    BeanUtils.copyProperties(firstFeedBack, mergeFeedBack);
                    FeedbackProductionVO lastFeedBack = sortFeedbackList.get(indexTreeSet.last());
                    mergeFeedBack.setEndTime(lastFeedBack.getEndTime());
                    //合并实绩数量和实绩进度
                    BigDecimal reportedQtySum = BigDecimal.ZERO;
                    BigDecimal reportedProgressSum = BigDecimal.ZERO;
                    for (Integer index : indexTreeSet) {
                        if (sortFeedbackList.get(index) != null) {
                            reportedQtySum = reportedQtySum.add(sortFeedbackList.get(index).getReportingQuantity()
                                    == null ? BigDecimal.ZERO : sortFeedbackList.get(index).getReportingQuantity());
                            reportedProgressSum = reportedProgressSum.add(sortFeedbackList.get(index).getReportingProgress()
                                    == null ? BigDecimal.ZERO : sortFeedbackList.get(index).getReportingProgress());
                            mergeIndexList.add(index);
                        } else {
                            log.info("反馈数据异常, index: {}, listEntry: {}", index, JSON.toJSONString(listEntry));
                        }
                    }

                    mergeFeedBack.setReportingQuantity(reportedQtySum);
                    mergeFeedBack.setReportingProgress(reportedProgressSum);
                    mergeFeedBack.setReportingStatus(lastFeedBack.getReportingStatus());
                    feedBacksResult.add(mergeFeedBack);
                }
                Collection<Integer> diffSectionIndex = CollectionUtils.getDiffSection(indexAllList, mergeIndexList);
                for (Integer index : diffSectionIndex) {
                    feedBacksResult.add(sortFeedbackList.get(index));
                }
            } else {
                feedBacksResult.addAll(value);
            }
        }

        for (FeedbackProductionVO feedbackProductionVO : feedBacksResult) {
            if (feedbackProductionVO.getReportingStatus().equals(ReportingStatusEnum.STARTED.getCode())) {
                //已开始状态的 即使有结束时间也不传,，已完工 开始时间、结束时间有值则都传
                feedbackProductionVO.setEndTime(null);
                feedbackProductionVO.setReportingStatus("processing");
            }
            if (feedbackProductionVO.getReportingStatus().equals(ReportingStatusEnum.FINISHED.getCode()) || feedbackProductionVO.getReportingStatus().equals(ReportingStatusEnum.PLAN_IDENTIFIED.getCode())) {
                feedbackProductionVO.setReportingStatus("finished");
            }
        }


        return feedBacksResult;
    }

}
