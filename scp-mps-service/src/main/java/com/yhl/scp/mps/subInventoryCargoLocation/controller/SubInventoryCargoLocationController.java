package com.yhl.scp.mps.subInventoryCargoLocation.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.subInventoryCargoLocation.dto.SubInventoryCargoLocationDTO;
import com.yhl.scp.mps.subInventoryCargoLocation.service.SubInventoryCargoLocationService;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;


@Slf4j
@Api(tags = "子库存货位信息控制器")
@RestController
@RequestMapping("subCargoLocation")
public class SubInventoryCargoLocationController extends BaseController {

    @Resource
    private SubInventoryCargoLocationService subInventoryCargoLocationService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<SubInventoryCargoLocationVO>> page() {
        List<SubInventoryCargoLocationVO> subInventoryCargoLocationVOS = subInventoryCargoLocationService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<SubInventoryCargoLocationVO> pageInfo = new PageInfo<>(subInventoryCargoLocationVOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody SubInventoryCargoLocationDTO subInventoryCargoLocationDTO) {
        return subInventoryCargoLocationService.doCreate(subInventoryCargoLocationDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody SubInventoryCargoLocationDTO subInventoryCargoLocationDTO) {
        return subInventoryCargoLocationService.doUpdate(subInventoryCargoLocationDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        subInventoryCargoLocationService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<SubInventoryCargoLocationVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, subInventoryCargoLocationService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "子库存货位信息同步")
    @PostMapping(value = "sync")
    public BaseResponse<SubInventoryCargoLocationVO> syncData() {
        return subInventoryCargoLocationService.syncData(null, null);
    }
    
    @ApiOperation(value = "目标货位下拉")
    @GetMapping(value = "targetStockLocationDropdown")
    public BaseResponse<List<LabelValue<String>>> selectTargetStockLocation(@RequestParam(value = "factoryCode") String factoryCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, subInventoryCargoLocationService.selectTargetStockLocation(factoryCode));
    }

}
