package com.yhl.scp.mps.feedback.service.impl;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.constants.StringConstants;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesFeedBack;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.enums.ReportingStatusEnum;
import com.yhl.scp.mps.feedback.convertor.MesFeedbackConvertor;
import com.yhl.scp.mps.feedback.domain.entity.MesFeedbackDO;
import com.yhl.scp.mps.feedback.domain.service.MesFeedbackDomainService;
import com.yhl.scp.mps.feedback.dto.FeedBackConvertDTO;
import com.yhl.scp.mps.feedback.dto.MesFeedbackDTO;
import com.yhl.scp.mps.feedback.infrastructure.dao.MesFeedbackDao;
import com.yhl.scp.mps.feedback.infrastructure.po.MesFeedbackPO;
import com.yhl.scp.mps.feedback.service.MesFeedbackService;
import com.yhl.scp.mps.feedback.vo.MesFeedbackVO;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.reportingFeedback.dto.MpsProReportingFeedbackDTO;
import com.yhl.scp.mps.reportingFeedback.service.MpsProReportingFeedbackService;
import com.yhl.scp.sds.basic.enums.ReportingTypeEnum;
import com.yhl.scp.sds.basic.enums.TaskTypeEnum;
import com.yhl.scp.sds.extension.feedback.dto.FeedbackProductionDTO;
import com.yhl.scp.sds.extension.feedback.vo.FeedbackProductionVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.feedback.infrastructure.dao.FeedbackProductionDao;
import com.yhl.scp.sds.feedback.service.FeedbackProductionService;
import com.yhl.scp.sds.order.service.OperationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <code>MesFeedbackServiceImpl</code>
 * <p>
 * MesFeedbackServiceImpl
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-17 14:46:54
 */
@Slf4j
@Service
public class MesFeedbackServiceImpl extends AbstractService implements MesFeedbackService {

    @Resource
    private MesFeedbackDao mesFeedbackDao;

    @Resource
    private MesFeedbackDomainService mesFeedbackDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OperationTaskExtDao operationTaskExtDao;
    @Resource
    private OperationService operationService;
    @Resource
    private MpsProReportingFeedbackService mpsProReportingFeedbackService;
    @Resource
    private FeedbackProductionService feedbackProductionService;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private NewDcpFeign newDcpFeign;

    @Override
    public BaseResponse<Void> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步生产报工反馈");
            if (StringUtils.isEmpty(scenario)) {
                scenario = SystemHolder.getScenario();
                tenantCode = SystemHolder.getTenantId();
            }
            HashMap<String, Object> map = MapUtil.newHashMap();
            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.MES_FEED_BACK.getCode(), map);
        } catch (Exception e) {
            log.error("同步生产报工反馈数据报错,{}", e.getMessage());
            throw new BusinessException("同步生产报工反馈数据报错", e.getMessage());
        }
        return BaseResponse.success("同步生产报工反馈操作完成");
    }

    @Override
    public BaseResponse<Void> doCreate(MesFeedbackDTO mesFeedbackDTO) {
        // 0.数据转换
        MesFeedbackDO mesFeedbackDO = MesFeedbackConvertor.INSTANCE.dto2Do(mesFeedbackDTO);
        MesFeedbackPO mesFeedbackPO = MesFeedbackConvertor.INSTANCE.dto2Po(mesFeedbackDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mesFeedbackDomainService.validation(mesFeedbackDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(mesFeedbackPO);
        mesFeedbackDao.insert(mesFeedbackPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MesFeedbackDTO mesFeedbackDTO) {
        // 0.数据转换
        MesFeedbackDO mesFeedbackDO = MesFeedbackConvertor.INSTANCE.dto2Do(mesFeedbackDTO);
        MesFeedbackPO mesFeedbackPO = MesFeedbackConvertor.INSTANCE.dto2Po(mesFeedbackDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        mesFeedbackDomainService.validation(mesFeedbackDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(mesFeedbackPO);
        mesFeedbackDao.update(mesFeedbackPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MesFeedbackDTO> list) {
        List<MesFeedbackPO> newList = MesFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        mesFeedbackDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<MesFeedbackDTO> list) {
        List<MesFeedbackPO> newList = MesFeedbackConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        mesFeedbackDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return mesFeedbackDao.deleteBatch(idList);
        }
        return mesFeedbackDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MesFeedbackVO selectByPrimaryKey(String id) {
        MesFeedbackPO po = mesFeedbackDao.selectByPrimaryKey(id);
        return MesFeedbackConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_mes_feedback")
    public List<MesFeedbackVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_mes_feedback")
    public List<MesFeedbackVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MesFeedbackVO> dataList = mesFeedbackDao.selectByCondition(sortParam, queryCriteriaParam);
        MesFeedbackServiceImpl target = SpringBeanUtils.getBean(MesFeedbackServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MesFeedbackVO> selectByParams(Map<String, Object> params) {
        List<MesFeedbackPO> list = mesFeedbackDao.selectByParams(params);
        return MesFeedbackConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MesFeedbackVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<MesFeedbackVO> invocation(List<MesFeedbackVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public void doHandleMesFeedBack(List<MesFeedBack> mesFeedBacks) {
        log.info("接口同步DCP生产反馈数据量：{}", mesFeedBacks.size());
        if (CollectionUtils.isEmpty(mesFeedBacks)) {
            return;
        }
        List<String> types = ListUtil.of("台班成品.其它", "转单");
        List<MesFeedbackDTO> mesFeedbackDTOS = new ArrayList<>();
        mesFeedBacks.removeIf(item ->
                StrUtil.isBlank(item.getPropertyValue())
                        || !types.contains(item.getPropertyValue())
                        || StrUtil.isBlank(item.getItemCode())
                        || Objects.isNull(item.getTrxQty())
                        || item.getTrxQty() == 0
        );
        log.info("接口同步校验处理DCP生产反馈数据量：{}", mesFeedBacks.size());
        if(CollectionUtils.isEmpty(mesFeedBacks)){
            return;
        }
        for (MesFeedBack mesFeedBack : mesFeedBacks) {
            MesFeedbackDTO build = MesFeedbackDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .itemCode(mesFeedBack.getItemCode())
                    .propertyValue(mesFeedBack.getPropertyValue())
                    .transactionCode(mesFeedBack.getTransactionCode())
                    .userName(mesFeedBack.getUserName())
                    .trxQty(BigDecimalUtils.toBigDecimal(mesFeedBack.getTrxQty()))
                    .businessUnit(mesFeedBack.getBusinessUnitt())
                    .transactionDesc(mesFeedBack.getTransactionDesc())
                    .transactionType(mesFeedBack.getTransactionType())
                    .wipEntityName(mesFeedBack.getWipEntityName())
                    .operationNum(mesFeedBack.getOperationNum())
                    .kid(mesFeedBack.getKid())
                    .shiftDate(mesFeedBack.getShiftdate())
                    .prodLineCode(mesFeedBack.getProdLineCode())
                    .eventTime(DateUtils.stringToDate(mesFeedBack.getEventTime()))
                    .plantCode(mesFeedBack.getPlantCode())
                    .cellOperationNum(mesFeedBack.getCellOperationNum())
                    .build();
            mesFeedbackDTOS.add(build);
        }
        Lists.partition(mesFeedbackDTOS, 1000).forEach(this::doCreateBatch);
        // 转换生产反馈数据
        doConvert(mesFeedbackDTOS);
    }

    @Override
    public void doConvert(List<MesFeedbackDTO> mesFeedbackDTOS) {
        List<String> itemCodes = StreamUtils.columnToList(mesFeedbackDTOS, MesFeedbackDTO::getItemCode);
        List<WorkOrderVO> workOrderPOS = operationTaskExtDao.selectByItemCodeAndPlantCode(itemCodes);
        List<String> orderIds = StreamUtils.columnToList(workOrderPOS, WorkOrderVO::getId);
        List<OperationVO> operationVOS = operationService.selectByWorkOrderIds(orderIds)
                .stream().filter(p -> !Objects.equals(p.getPlanStatus(), PlanStatusEnum.UNPLAN.getCode())).collect(Collectors.toList());
        List<FeedbackProductionVO> feedbackProductionVOS = feedbackProductionService
                .selectByParams(ImmutableMap.of("workOrderIds", orderIds));
        List<CollectionValueVO> hwLimitResult = ipsFeign.getByCollectionCode("HW_LIMIT_STAND_RESOURCE_CODE");
        List<String> hwLimitResultList = hwLimitResult.stream().map(CollectionValueVO::getCollectionValue).collect(Collectors.toList());
        List<String> resourceIds = StreamUtils.columnToList(operationVOS, OperationVO::getPlannedResourceId);
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectVOByPhysicalIds(resourceIds);
        List<FeedbackProductionDTO> feedbackProductionDTOS = feedbackProductionVOS.stream().map(p -> {
            FeedbackProductionDTO feedbackProductionDTO = new FeedbackProductionDTO();
            BeanUtils.copyProperties(p, feedbackProductionDTO);
            return feedbackProductionDTO;
        }).collect(Collectors.toList());
        // 分组数据
        Map<String, PhysicalResourceVO> physicalResourceVOMap = StreamUtils.mapByColumn(physicalResourceVOS, PhysicalResourceVO::getId);
        Map<String, List<WorkOrderVO>> productOrderMap = StreamUtils.mapListByColumn(workOrderPOS, WorkOrderVO::getProductCode);
        Map<String, WorkOrderVO> workOrderVOMap = StreamUtils.mapByColumn(workOrderPOS, WorkOrderVO::getId);
        Map<String, List<MesFeedbackDTO>> reportingFeedbackMap = StreamUtils.mapListByColumn(mesFeedbackDTOS,
                MesFeedbackDTO::getItemCode);
        Map<String, OperationVO> operationAllMap = StreamUtils.mapByColumn(operationVOS, OperationVO::getId);
        Map<String, List<OperationVO>> operationMap = StreamUtils.mapListByColumn(operationVOS,
                p -> StrUtil.join(StringConstants.SPLIT_STR_1, p.getOrderId(), p.getRoutingStepSequenceNo()));
        Map<String, List<OperationVO>> childOperation = operationVOS.stream()
                .filter(p -> StrUtil.isNotEmpty(p.getParentId())).collect(Collectors.groupingBy(OperationVO::getParentId));
        Map<String, FeedbackProductionDTO> feedbackProductionDTOMap = StreamUtils.mapByColumn(feedbackProductionDTOS, FeedbackProductionDTO::getOperationId);

        FeedBackConvertDTO feedBackConvertDTO = FeedBackConvertDTO.builder()
                .hwLimitResultList(hwLimitResultList)
                .operationMap(operationMap)
                .childOperationMap(childOperation)
                .resourceMap(physicalResourceVOMap)
                .workOrderMap(workOrderVOMap)
                .feedbackProductionPOMap(new HashMap<>())
                .updateFeedbackProductionMap(new HashMap<>())
                .feedbackProductionInsertValueMap(new HashMap<>())
                .updateFeedbackProductionMap(new HashMap<>())
                .exitsFeedbackProductionPOMap(feedbackProductionDTOMap)
                .build();
        /*
         * 报工数据匹配对应制造订单信息
         * 筛选制造订单下工序数据，找到报工工序，根据报工开始结束时间匹配计划时间，数量均分
         */
        for (Map.Entry<String, List<MesFeedbackDTO>> itemEntry : reportingFeedbackMap.entrySet()) {
            String itemCode = itemEntry.getKey();
            if (!productOrderMap.containsKey(itemCode)) {
                log.warn("物料：{}报工没有对应订单排产信息", itemCode);
                continue;
            }
            List<MesFeedbackDTO> subList = itemEntry.getValue();
            // 根据物料的工序分组
            Map<Integer, List<MesFeedbackDTO>> subMap = subList.stream().collect(Collectors.groupingBy(MesFeedbackDTO::getOperationNum));
            for (Map.Entry<Integer, List<MesFeedbackDTO>> subEntry : subMap.entrySet()) {
                List<MesFeedbackDTO> value = subEntry.getValue();
                value.sort(Comparator.comparing(MesFeedbackDTO::getEventTime));
                // 循环工序报工
                for (MesFeedbackDTO mesFeedbackDTO : value) {
                    // 工序代码
                    Integer operationNum = mesFeedbackDTO.getOperationNum();
                    List<WorkOrderVO> workOrderVOS = productOrderMap.get(itemCode);
                    // 构建父工序平铺时间轴，均分报工量，区分增改
                    Map<Date, OperationVO> parentOperationTimeMap = getParentOperationTimeMap(workOrderVOS, operationNum, feedBackConvertDTO);
                    // 根据结束时间匹配eventTime段
                    calculateFeedBackData(parentOperationTimeMap, mesFeedbackDTO, feedBackConvertDTO);
                }
            }
        }
        Collection<FeedbackProductionDTO> insertFeedBack = feedBackConvertDTO.getFeedbackProductionInsertValueMap().values();
        Collection<FeedbackProductionDTO> updateFeedBack = feedBackConvertDTO.getUpdateFeedbackProductionMap().values();
        List<FeedbackProductionDTO> buildFeedBackReportingList = new ArrayList<>();
        buildFeedBackReportingList.addAll(insertFeedBack);
        buildFeedBackReportingList.addAll(updateFeedBack);
        // 构建mps_pro_reporting_feedback
        buildFeedBackReporting(buildFeedBackReportingList, workOrderVOMap, operationAllMap, physicalResourceVOMap);
        log.info("insertFeedBack:{}", insertFeedBack.size());
        if (CollectionUtils.isNotEmpty(insertFeedBack)) {
            Lists.partition(new ArrayList<>(insertFeedBack), 1000).forEach(feedbackProductionService::doCreateBatch);
        }
        log.info("updateFeedBack:{}", updateFeedBack.size());
        if (CollectionUtils.isNotEmpty(updateFeedBack)) {
            Lists.partition(new ArrayList<>(updateFeedBack), 1000).forEach(feedbackProductionService::doUpdateBatch);
        }
    }

    private void buildFeedBackReporting(List<FeedbackProductionDTO> buildFeedBackReportingList,
                                        Map<String, WorkOrderVO> workOrderVOMap,
                                        Map<String, OperationVO> operationAllMap,
                                        Map<String, PhysicalResourceVO> physicalResourceVOMap) {
        List<MpsProReportingFeedbackDTO> mpsProReportingFeedbackDTOS = new ArrayList<>();
        for (FeedbackProductionDTO feedbackProductionDTO : buildFeedBackReportingList) {
            String workOrderId = feedbackProductionDTO.getWorkOrderId();
            WorkOrderVO workOrderVO = workOrderVOMap.get(workOrderId);
            String operationId = feedbackProductionDTO.getOperationId();
            OperationVO operationVO = operationAllMap.get(operationId);
            String plannedResourceId = operationVO.getPlannedResourceId();
            PhysicalResourceVO physicalResourceVO = physicalResourceVOMap.get(plannedResourceId);
            BigDecimal reportingQuantity = feedbackProductionDTO.getReportingQuantity();

            MpsProReportingFeedbackDTO proReportingFeedbackDTO = MpsProReportingFeedbackDTO.builder()
                    .id(UUIDUtil.getUUID())
                    .organizationCode(workOrderVO.getStockPointCode())
                    .orderCode(workOrderVO.getOrderNo())
                    .productCode(workOrderVO.getProductCode())
                    .productionLine(physicalResourceVO.getProductionLine())
                    .statusCode("R")
                    .statusDesc("已下达")
                    .planQuantity(operationVO.getQuantity().intValue())
                    .finishQuantity(reportingQuantity.intValue())
                    .scrapQuantity(0)
                    .sequenceCode(operationVO.getRoutingStepSequenceNo().toString())
                    .endUpdateTime(DateUtils.stringToDate(feedbackProductionDTO.getCountingUnitId()))
                    .remark(feedbackProductionDTO.getId())
                    .productionLine(physicalResourceVO.getPhysicalResourceCode())
                    .build();
            mpsProReportingFeedbackDTOS.add(proReportingFeedbackDTO);
        }
        Lists.partition(mpsProReportingFeedbackDTOS, 1000).forEach(mpsProReportingFeedbackService::doCreateBatch);
    }

    private void calculateFeedBackData(Map<Date, OperationVO> parentOperationTimeMap,
                                       MesFeedbackDTO mesFeedbackDTO, FeedBackConvertDTO feedBackConvertDTO) {
        // 报工数量
        BigDecimal trxQty = mesFeedbackDTO.getTrxQty();
        boolean flag = trxQty.compareTo(BigDecimal.ZERO) > 0;
        // 事件时间，前后增加一天查找‘已开始’，‘已计划’工序，大于0均摊算，小于零倒着算
        Date eventTime = mesFeedbackDTO.getEventTime();
        Date startEventTime = DateUtil.offsetDay(eventTime, -1);
        Date endEventTime = DateUtil.offsetDay(eventTime, 1);
        List<Date> key = parentOperationTimeMap.keySet()
                .stream().sorted(Comparator.comparing(Date::getTime)).collect(Collectors.toList());
        List<Date> eventRange = key.stream().filter(p -> p.getTime() >= startEventTime.getTime() && p.getTime() <= endEventTime.getTime()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(eventRange)) {
            return;
        }
        if (flag) {
            // 增量
            handleIncrementCover(eventRange, parentOperationTimeMap, feedBackConvertDTO, trxQty, mesFeedbackDTO);
        } else {
            eventRange.sort(Comparator.comparing(Date::getTime).reversed());
            // 减量
            handleReduceAmount(eventRange, parentOperationTimeMap, feedBackConvertDTO, trxQty, mesFeedbackDTO);
        }
    }

    private void handleReduceAmount(List<Date> eventRange, Map<Date, OperationVO> parentOperationTimeMap,
                                    FeedBackConvertDTO feedBackConvertDTO, BigDecimal quantity, MesFeedbackDTO mesFeedbackDTO) {
        quantity = quantity.abs();
        for (Date date : eventRange) {
            if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            OperationVO parentOperationVO = parentOperationTimeMap.get(date);
            String parentId = parentOperationVO.getId();
            String plannedResourceId = parentOperationVO.getPlannedResourceId();
            if (!feedBackConvertDTO.getResourceMap().containsKey(plannedResourceId)) {
                continue;
            }
            String standardResourceCode = feedBackConvertDTO.getResourceMap().get(plannedResourceId).getStandardResourceCode();
            List<OperationVO> childOperations = feedBackConvertDTO.getChildOperationMap().get(parentId);
            childOperations.sort(Comparator.comparing(OperationVO::getStartTime));
            // 根据结束时间从后往前扣减
            childOperations.sort(Comparator.comparing(OperationVO::getEndTime).reversed());
            if (childOperations.size() > 1) {
                // 烘弯炉
                if (feedBackConvertDTO.getHwLimitResultList().contains(standardResourceCode)) {
                    BigDecimal size = new BigDecimal(childOperations.size());
                    // 整除，减少循环次数
                    BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
                    // 取余
                    BigDecimal remainder = quantity.remainder(size);
                    //均分
                    for (int i = 0; i < childOperations.size(); i++) {
                        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                        OperationVO operationVO = childOperations.get(i);
                        boolean whetherFeedBack = whetherReduceAmount(operationVO.getId(), feedBackConvertDTO);
                        if (!whetherFeedBack) {
                            // 不存在历史及已有报工反馈
                            continue;
                        }
                        BigDecimal num;
                        // 尾差加到第一道工序
                        if (i == 0) {
                            num = quotient.add(remainder);
                        } else {
                            num = quotient;
                        }
                        if (num.compareTo(operationVO.getQuantity()) > 0) {
                            num = operationVO.getQuantity();
                        }
                        BigDecimal deductionFeedBackQty = deductionFeedBack(num, operationVO, feedBackConvertDTO, mesFeedbackDTO, quantity, true);
                        quantity = quantity.subtract(deductionFeedBackQty);
                        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                } else {
                    for (OperationVO operationVO : childOperations) {
                        boolean whetherFeedBack = whetherReduceAmount(operationVO.getId(), feedBackConvertDTO);
                        if (!whetherFeedBack) {
                            // 不存在历史及已有报工反馈
                            continue;
                        }
                        BigDecimal planQuantity = operationVO.getQuantity();
                        BigDecimal deductionFeedBackQty = deductionFeedBack(planQuantity, operationVO, feedBackConvertDTO, mesFeedbackDTO, quantity, false);
                        quantity = quantity.subtract(deductionFeedBackQty);
                        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                    }
                }
            } else {
                OperationVO operationVO = childOperations.get(0);
                boolean whetherFeedBack = whetherReduceAmount(operationVO.getId(), feedBackConvertDTO);
                if (!whetherFeedBack) {
                    // 不存在历史及已有报工反馈
                    continue;
                }
                BigDecimal planQuantity = operationVO.getQuantity();
                BigDecimal deductionFeedBackQty = deductionFeedBack(planQuantity, operationVO, feedBackConvertDTO, mesFeedbackDTO, quantity, false);
                quantity = quantity.subtract(deductionFeedBackQty);
            }
        }
    }


    /**
     * 增量覆盖报工，匹配到找到父工序对应得子工序，区分hw和连续，从前往后
     * 判断工序是否可以报工覆盖，已完工得不处理，已计划已开始得做覆盖
     */
    private void handleIncrementCover(List<Date> eventRange, Map<Date, OperationVO> parentOperationTimeMap, FeedBackConvertDTO feedBackConvertDTO,
                                      BigDecimal quantity, MesFeedbackDTO mesFeedbackDTO) {
        for (Date date : eventRange) {
            if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                break;
            }
            OperationVO parentOperationVO = parentOperationTimeMap.get(date);
            String parentId = parentOperationVO.getId();
            String plannedResourceId = parentOperationVO.getPlannedResourceId();
            if (!feedBackConvertDTO.getResourceMap().containsKey(plannedResourceId)) {
                continue;
            }
            String standardResourceCode = feedBackConvertDTO.getResourceMap().get(plannedResourceId).getStandardResourceCode();
            List<OperationVO> childOperations = feedBackConvertDTO.getChildOperationMap().get(parentId);
            childOperations.sort(Comparator.comparing(OperationVO::getStartTime));
            if (childOperations.size() > 1) {
                // 烘弯炉
                if (feedBackConvertDTO.getHwLimitResultList().contains(standardResourceCode)) {
                    BigDecimal size = new BigDecimal(childOperations.size());
                    // 整除，减少循环次数
                    BigDecimal quotient = quantity.divide(size, 0, RoundingMode.DOWN);
                    // 取余
                    BigDecimal remainder = quantity.remainder(size);
                    //均分
                    for (int i = 0; i < childOperations.size(); i++) {
                        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                        OperationVO operationVO = childOperations.get(i);
                        BigDecimal num;
                        // 尾差加到第一道工序
                        if (i == 0) {
                            num = quotient.add(remainder);
                        } else {
                            num = quotient;
                        }
                        if (num.compareTo(operationVO.getQuantity()) > 0) {
                            num = operationVO.getQuantity();
                        }
                        BigDecimal finishedQty = calculateFinishedQty(num, operationVO, quantity, feedBackConvertDTO, mesFeedbackDTO);
                        // 扣减分配完工数量
                        quantity = BigDecimalUtils.subtract(quantity, finishedQty);
                    }
                } else {
                    for (OperationVO operationVO : childOperations) {
                        if (quantity.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                        BigDecimal num = operationVO.getQuantity();
                        BigDecimal finishedQty = calculateFinishedQty(num, operationVO, quantity, feedBackConvertDTO, mesFeedbackDTO);
                        // 扣减分配完工数量
                        quantity = BigDecimalUtils.subtract(quantity, finishedQty);
                    }
                }
            } else {
                OperationVO operationVO = childOperations.get(0);
                BigDecimal num = operationVO.getQuantity();
                BigDecimal finishedQty = calculateFinishedQty(num, operationVO, quantity, feedBackConvertDTO, mesFeedbackDTO);
                // 扣减分配完工数量
                quantity = BigDecimalUtils.subtract(quantity, finishedQty);
            }
        }
    }


    private BigDecimal deductionFeedBack(BigDecimal num, OperationVO operationVO, FeedBackConvertDTO feedBackConvertDTO,
                                         MesFeedbackDTO mesFeedbackDTO, BigDecimal quantity, Boolean whetherHw) {
        String id = operationVO.getId();
        FeedbackProductionDTO feedbackProductionPO = feedBackConvertDTO.getExitsFeedbackProductionPO();
        BigDecimal reportingQuantity = feedbackProductionPO.getReportingQuantity();
        int compare = num.compareTo(reportingQuantity);
        if (compare < 0 && whetherHw) {
            // 烘弯炉扣减报工逻辑，不能扣完，依次均分扣减
            reportingQuantity = num;
        }
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        BigDecimal calculateQty = calculateQty(reportingQuantity, quantity, isCompleted);
        reportingQuantity = feedbackProductionPO.getReportingQuantity().subtract(calculateQty);
        feedbackProductionPO.setReportingQuantity(reportingQuantity);
        feedbackProductionPO.setRemark(mesFeedbackDTO.getId());
        feedbackProductionPO.setCountingUnitId(DateUtils.dateToString(mesFeedbackDTO.getEventTime(), DateUtils.COMMON_DATE_STR1));
        if (feedBackConvertDTO.getFeedBackType().equals(FeedBackConvertDTO.FeedbackTypeEnum.TEMP.getCode())) {
            boolean b = reportingQuantity.compareTo(BigDecimal.ZERO) <= 0;
            if (b) {
                // 如果是未入库的反馈数据，并且报工数量=0，则取消生产反馈
                feedBackConvertDTO.getFeedbackProductionInsertValueMap().remove(id);
                feedBackConvertDTO.getFeedbackProductionPOMap().remove(id);
            } else {
                feedbackProductionPO.setReportingStatus(reportingQuantity.compareTo(operationVO.getQuantity()) >= 0 ? ReportingStatusEnum.FINISHED.getCode() :
                        ReportingStatusEnum.STARTED.getCode());
                feedBackConvertDTO.getFeedbackProductionInsertValueMap().put(id, feedbackProductionPO);
                feedBackConvertDTO.getFeedbackProductionPOMap().put(id, feedbackProductionPO);
            }
        }
        if (feedBackConvertDTO.getFeedBackType().equals(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode())) {
            // FINISHED状态不做状态得更新
            if (!feedbackProductionPO.getReportingStatus().equals(ReportingStatusEnum.FINISHED.getCode())) {
                feedbackProductionPO.setReportingStatus(reportingQuantity.compareTo(operationVO.getQuantity()) >= 0 ? ReportingStatusEnum.FINISHED.getCode() :
                        ReportingStatusEnum.STARTED.getCode());
            }
            // 已存在的报工，只扣减报工数量做更新
            feedBackConvertDTO.getUpdateFeedbackProductionMap().put(id, feedbackProductionPO);
            feedBackConvertDTO.getFeedbackProductionPOMap().put(id, feedbackProductionPO);
        }
        return calculateQty;
    }

    private BigDecimal calculateQty(BigDecimal num, BigDecimal quantity, AtomicBoolean isCompleted) {
        int compareResult = quantity.compareTo(num);
        if (compareResult > 0) {
            // 报工数量大于工序所需数量
            isCompleted.set(true);
            return num;
        } else if (compareResult == 0) {
            // 报工数量等于工序所需数量
            isCompleted.set(true);
            return quantity;
        } else {
            // 报工数量小于工序所需数量
            return quantity;
        }
    }

    private void buildFeedBackList(FeedBackConvertDTO feedBackConvertDTO, OperationVO operationVO,
                                   BigDecimal finishQuantity, boolean whetherFinished,
                                   MesFeedbackDTO mesFeedbackDTO) {
        String operationId = operationVO.getId();
        BigDecimal planQuantity = operationVO.getQuantity();
        // 判断是否存在报工反馈数据
        String feedBackType = feedBackConvertDTO.getFeedBackType();
        FeedbackProductionDTO feedbackProductionOld = feedBackConvertDTO.getExitsFeedbackProductionPO();
        // 生产反馈
        FeedbackProductionDTO po = new FeedbackProductionDTO();
        po.setWorkOrderId(operationVO.getOrderId());
        po.setOperationId(operationId);
        po.setRoutingStepId(operationVO.getRoutingStepId());
        po.setPhysicalResourceId(operationVO.getPlannedResourceId());
        po.setReportingType(ReportingTypeEnum.SON.getCode());
        po.setTaskType(TaskTypeEnum.PRODUCTION.getCode());
        po.setReportingStatus(finishQuantity.compareTo(operationVO.getQuantity()) >= 0 ? ReportingStatusEnum.FINISHED.getCode() :
                ReportingStatusEnum.STARTED.getCode());
        po.setReportingQuantity(finishQuantity);
        po.setReportingTime(mesFeedbackDTO.getEventTime());
        po.setStartTime(operationVO.getStartTime());
        po.setEndTime(operationVO.getEndTime());
        po.setCountingUnitId(DateUtils.dateToString(mesFeedbackDTO.getEventTime(), DateUtils.COMMON_DATE_STR1));
        po.setReportingScrap(BigDecimal.ZERO);
        po.setRemark(mesFeedbackDTO.getId());
        po.setWhetherFeedBack(YesOrNoEnum.NO.getCode());
        // 已存在得生产反馈数据做更新
        if (feedBackType.equals(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode())) {
            po.setId(feedbackProductionOld.getId());
            feedBackConvertDTO.getUpdateFeedbackProductionMap().put(operationId, po);
        }
        // 未被覆盖完整started得报工数据，后续其它event事件报工为已完工
        if (feedBackType.equals(FeedBackConvertDTO.FeedbackTypeEnum.TEMP.getCode())) {
            feedBackConvertDTO.getFeedbackProductionInsertValueMap().put(operationId, po);
        }
        // 新增生产反馈
        if (feedBackType.equals(FeedBackConvertDTO.FeedbackTypeEnum.NEW.getCode())) {
            po.setId(UUIDUtil.getUUID());
            feedBackConvertDTO.getFeedbackProductionInsertValueMap().put(operationId, po);
        }
        feedBackConvertDTO.getFeedbackProductionPOMap().put(operationId, po);
    }

    private boolean whetherReduceAmount(String operationId, FeedBackConvertDTO feedBackConvertDTO) {
        feedBackConvertDTO.setExitsFeedbackProductionPO(null);
        Map<String, FeedbackProductionDTO> feedbackProductionPOMap = feedBackConvertDTO.getFeedbackProductionPOMap();
        Map<String, FeedbackProductionDTO> exitsFeedbackProductionPOMap = feedBackConvertDTO.getExitsFeedbackProductionPOMap();
        if (feedbackProductionPOMap.containsKey(operationId)) {
            FeedbackProductionDTO reportingFeedbackDTO = feedbackProductionPOMap.get(operationId);
            feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.TEMP.getCode());
            feedBackConvertDTO.setExitsFeedbackProductionPO(reportingFeedbackDTO);
            // 如果临时记录中也记录了已存在未完全报工得数据
            if (exitsFeedbackProductionPOMap.containsKey(operationId)) {
                feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode());
            }
            return true;
        }
        if (exitsFeedbackProductionPOMap.containsKey(operationId)) {
            FeedbackProductionDTO reportingFeedbackDTO = exitsFeedbackProductionPOMap.get(operationId);
            feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode());
            feedBackConvertDTO.setExitsFeedbackProductionPO(reportingFeedbackDTO);
            return true;
        }
        return false;
    }

    private boolean whetherFeedBack(String operationId, FeedBackConvertDTO feedBackConvertDTO) {
        feedBackConvertDTO.setExitsFeedbackProductionPO(null);
        Map<String, FeedbackProductionDTO> feedbackProductionPOMap = feedBackConvertDTO.getFeedbackProductionPOMap();
        Map<String, FeedbackProductionDTO> exitsFeedbackProductionPOMap = feedBackConvertDTO.getExitsFeedbackProductionPOMap();
        // 如果当前工序已被其它事件覆盖，则不处理该工序
        if (feedbackProductionPOMap.containsKey(operationId)) {
            FeedbackProductionDTO reportingFeedbackDTO = feedbackProductionPOMap.get(operationId);
            String reportingStatus = reportingFeedbackDTO.getReportingStatus();
            feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.TEMP.getCode());
            feedBackConvertDTO.setExitsFeedbackProductionPO(reportingFeedbackDTO);
            if (ReportingStatusEnum.FINISHED.getCode().equals(reportingStatus)) {
                return true;
            }
            // 如果临时记录中也记录了已存在未完全报工得数据
            if (exitsFeedbackProductionPOMap.containsKey(operationId)) {
                feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode());
            }
            return false;
        }
        // 如果已存在报工数据为已完工，则不处理该工序
        if (exitsFeedbackProductionPOMap.containsKey(operationId)) {
            FeedbackProductionDTO reportingFeedbackDTO = exitsFeedbackProductionPOMap.get(operationId);
            String reportingStatus = reportingFeedbackDTO.getReportingStatus();
            feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.EXITS.getCode());
            feedBackConvertDTO.setExitsFeedbackProductionPO(reportingFeedbackDTO);
            return ReportingStatusEnum.FINISHED.getCode().equals(reportingStatus);
        }
        feedBackConvertDTO.setFeedBackType(FeedBackConvertDTO.FeedbackTypeEnum.NEW.getCode());
        return false;
    }


    private BigDecimal calculateFinishedQty(BigDecimal num, OperationVO operationVO, BigDecimal quantity, FeedBackConvertDTO feedBackConvertDTO, MesFeedbackDTO mesFeedbackDTO) {
        boolean whetherFeedBack = whetherFeedBack(operationVO.getId(), feedBackConvertDTO);
        if (whetherFeedBack) {
            return BigDecimal.ZERO;
        }
        if (!feedBackConvertDTO.getFeedBackType().equals(FeedBackConvertDTO.FeedbackTypeEnum.NEW.getCode())) {
            // 之前的生产反馈未被分配完整的情况
            BigDecimal reportingQuantity = feedBackConvertDTO.getExitsFeedbackProductionPO().getReportingQuantity();
            // 剩余未报工数量 = 计划量 - 已报工数量
            num = BigDecimalUtils.subtract(operationVO.getQuantity(), reportingQuantity);
            if (num.compareTo(BigDecimal.ZERO) <= 0) {
                num = operationVO.getQuantity();
            }
        }
        // 是否完工标志
        AtomicBoolean isCompleted = new AtomicBoolean(false);
        // 计算完工数量
        BigDecimal finishQuantity = calculateQty(num, quantity, isCompleted);
        BigDecimal originalFinishQuantity = finishQuantity;
        if (!feedBackConvertDTO.getFeedBackType().equals(FeedBackConvertDTO.FeedbackTypeEnum.NEW.getCode())) {
            // 最新完工数量 = 已报工数量 + 最新报工数量
            BigDecimal reportingQuantity = feedBackConvertDTO.getExitsFeedbackProductionPO().getReportingQuantity();
            finishQuantity = BigDecimalUtils.add(finishQuantity, reportingQuantity);
        }
        buildFeedBackList(feedBackConvertDTO, operationVO, finishQuantity, isCompleted.get(), mesFeedbackDTO);
        return originalFinishQuantity;
    }

    private Map<Date, OperationVO> getParentOperationTimeMap(List<WorkOrderVO> workOrderVOS, Integer operationNum, FeedBackConvertDTO feedBackConvertDTO) {
        Map<Date, OperationVO> parentOperationTimeMap = new HashMap<>();
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            String id = workOrderVO.getId();
            String key = StrUtil.join(StringConstants.SPLIT_STR_1, id, operationNum);
            if (!feedBackConvertDTO.getOperationMap().containsKey(key)) {
                continue;
            }
            // 全工序报工，根据eventTime匹配结束时间
            List<OperationVO> operationSchedules = feedBackConvertDTO.getOperationMap().get(key);
            OperationVO parentOperation = operationSchedules.stream().filter(p -> StrUtil.isEmpty(p.getParentId())).findFirst().get();
            Date endTime = parentOperation.getEndTime();
            parentOperationTimeMap.put(endTime, parentOperation);
        }
        return parentOperationTimeMap;
    }

}
