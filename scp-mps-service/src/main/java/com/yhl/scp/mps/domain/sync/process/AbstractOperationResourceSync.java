package com.yhl.scp.mps.domain.sync.process;

import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.mds.extension.resource.domain.entity.PhysicalResourceDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepDO;
import com.yhl.scp.mds.extension.routing.domain.entity.RoutingStepResourceDO;
import com.yhl.scp.mds.product.vo.ProductCandidateResourceTimeVO;
import com.yhl.scp.mps.domain.sync.model.SyncContext;
import com.yhl.scp.sds.basic.enums.ActionEnum;
import com.yhl.scp.sds.basic.order.enums.OrderTypeEnum;
import com.yhl.scp.sds.extension.order.domain.entity.OperationDO;
import com.yhl.scp.sds.extension.order.domain.entity.OperationResourceDO;
import com.yhl.scp.sds.extension.order.domain.entity.WorkOrderDO;
import com.yhl.scp.sds.extension.order.dto.OperationResourceDTO;
import com.yhl.scp.sds.order.convertor.OperationResourceConvertor;
import com.yhl.scp.sds.order.service.OperationResourceService;
import org.apache.commons.collections4.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>AbstractOperationResourceSync</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-11 16:53:52
 */
public abstract class AbstractOperationResourceSync extends AbstractOperationSync {



    @Resource
    private OperationResourceService operationResourceService;


    @Override
    protected void syncOperationResource(SyncContext syncContext) {
        //初始化同步工序候选资源参数
        Map<ActionEnum, List<OperationResourceDTO>> operationResourceMap = createOperationResource(syncContext);
        //处理工序候选资源(进行数据库操作)
        dealOperationResources(operationResourceMap);
    }

    private Map<ActionEnum, List<OperationResourceDTO>> createOperationResource(SyncContext syncContext) {
        Map<ActionEnum, List<OperationResourceDTO>> resultMap = new HashMap<>();
        List<OperationResourceDTO> insertOperationResources = new ArrayList<>();
        List<OperationResourceDTO> updateOperationResources = new ArrayList<>();
        List<OperationResourceDTO> deleteOperationResources = new ArrayList<>();

        List<OperationDO> operations = syncContext.getOperationDOS();
        Map<String, List<OperationDO>> collect = operations.stream().collect(Collectors.groupingBy(OperationDO::getOrderId));
        List<WorkOrderDO> needElectRoutingWorkOrderList = syncContext.getSyncWorkOrderList();
        Map<String, WorkOrderDO> workOrderDTOMapOfId = needElectRoutingWorkOrderList.stream().collect(Collectors.toMap(WorkOrderDO::getId, Function.identity()));
        List<ProductCandidateResourceTimeVO> productCandidateResourceTimeVOS = syncContext.getProductCandidateResourceTimeVOS();
        Map<String, ProductCandidateResourceTimeVO> candidateResourceTimeMap = productCandidateResourceTimeVOS.stream()
                .collect(Collectors.toMap(ProductCandidateResourceTimeVO::getCandidateResourceId, Function.identity()));

        String currentDate = DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH);
        for (Map.Entry<String, List<OperationDO>> entry : collect.entrySet()) {
            //获取工艺路径
            WorkOrderDO workOrderDO = workOrderDTOMapOfId.get(entry.getKey());
            RoutingDO routingDO = workOrderDO.getRoutingDO();
            if (null == routingDO) {
                continue;
            }
            //获取路径步骤
            List<RoutingStepDO> routingStepDOList = routingDO.getRoutingStepDOList();
            Map<String, RoutingStepDO> routingStepDOMapOfId = routingStepDOList.stream().collect(Collectors.toMap(RoutingStepDO::getId, Function.identity()));
            for (OperationDO operationDO : entry.getValue()) {
                //获取路径步骤
                RoutingStepDO routingStepDO = routingStepDOMapOfId.get(operationDO.getRoutingStepId());
                Integer routingStepSequenceNr = routingStepDO.getSequenceNo();
                //获取路径步骤候选资源
                List<RoutingStepResourceDO> routingStepResourceDOList = routingStepDO.getRoutingStepResourceDOList();
                //可能出现路径步骤没有候选资源的情况
                if (CollectionUtils.isEmpty(routingStepResourceDOList)) {
                    continue;
                }

                //获取工序已同步候选资源
                List<OperationResourceDO> operationResourcesOfCurrenOperation = operationDO.getOperationResourceDOList();
                if (CollectionUtils.isEmpty(operationResourcesOfCurrenOperation)) {
                    //如果当前工序没有已同步的候选资源，则添加新增的工序候选资源
                    for (int j = 0; j < routingStepResourceDOList.size(); j++) {
                        //判断标准资源是否为*
                        RoutingStepResourceDO routingStepResourceDO = routingStepResourceDOList.get(j);
                        ProductCandidateResourceTimeVO candidateResourceTimeVO = candidateResourceTimeMap.get(routingStepResourceDO.getId());
                        List<PhysicalResourceDO> physicalResourceDOS = routingStepResourceDO.getPhysicalResourceDOS();
                        for (PhysicalResourceDO physicalResourceDO : physicalResourceDOS) {
                            if (YesOrNoEnum.NO.getCode().equals(physicalResourceDO.getEnabled())) {
                                continue;
                            }
                            OperationResourceDTO operationResourceDTO = createOrUpdateOperationResourceDTO(operationDO, null, routingStepResourceDO, physicalResourceDO, candidateResourceTimeVO, syncContext);
                            insertOperationResources.add(operationResourceDTO);
                        }
                    }
                } else {
                    //已开始，已完工不允许删除计划资源
                    boolean flag = keepStatus.contains(operationDO.getPlanStatus());
                    //如果 当前工序有已同步的候选资源，则判断是否需要进行增删改
                    List<String> newKeys = new ArrayList<>();
                    //比较
                    for (int j = 0; j < routingStepResourceDOList.size(); j++) {
                        RoutingStepResourceDO routingStepResourceDO = routingStepResourceDOList.get(j);
                        ProductCandidateResourceTimeVO candidateResourceTimeVO = candidateResourceTimeMap.get(routingStepResourceDO.getId());
                        List<PhysicalResourceDO> physicalResourceDOS = routingStepResourceDO.getPhysicalResourceDOS();
                        for (PhysicalResourceDO physicalResourceDO : physicalResourceDOS) {
                            List<OperationResourceDO> existResources = operationResourcesOfCurrenOperation.stream().filter(k -> k.getPhysicalResourceId().equals(physicalResourceDO.getId()) && Objects.equals(k.getRoutingStepSequenceNo(), routingStepSequenceNr)).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(existResources)) {
                                if (YesOrNoEnum.NO.getCode().equals(physicalResourceDO.getEnabled())) {
                                    continue;
                                }
                                //对候选新添加的资源进行新增
                                OperationResourceDTO operationResourceDTO = createOrUpdateOperationResourceDTO(operationDO, null, routingStepResourceDO, physicalResourceDO, candidateResourceTimeVO, syncContext);
                                insertOperationResources.add(operationResourceDTO);
                                newKeys.add(operationResourceDTO.getPhysicalResourceId());
                            } else {

                                //对候选资源进行修改
                                OperationResourceDO operationResource = existResources.get(0);
                                if (YesOrNoEnum.NO.getCode().equals(physicalResourceDO.getEnabled())) {
                                    deleteOperationResources.add(OperationResourceConvertor.INSTANCE.do2Dto(operationResource));
                                }
                                operationResource.setOperationId(operationDO.getId());
                                operationResource.setOrderId(operationDO.getOrderId());
                                operationResource.setRoutingStepId(operationDO.getRoutingStepId());
                                newKeys.add(operationResource.getPhysicalResourceId());
                                OperationResourceDTO operationResourceDTO = createOrUpdateOperationResourceDTO(operationDO, operationResource, routingStepResourceDO, physicalResourceDO, candidateResourceTimeVO, syncContext);
                                updateOperationResources.add(operationResourceDTO);
                                List<OperationResourceDO> collectionList = existResources.stream().filter(it -> !it.getId().equals(operationResource.getId())).collect(Collectors.toList());
                                deleteOperationResources.addAll(OperationResourceConvertor.INSTANCE.do2Dtos(collectionList));
                            }
                        }
                    }
                    //删除之前同步多出来的资源
                    List<OperationResourceDO> removeOperationResources = operationResourcesOfCurrenOperation.stream()
                            .filter(k -> !newKeys.contains(k.getPhysicalResourceId())).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(removeOperationResources) && !flag) {
                        List<OperationResourceDTO> operationResourceDTOS = OperationResourceConvertor.INSTANCE.do2Dtos(removeOperationResources);
                        deleteOperationResources.addAll(operationResourceDTOS);
                    }
                }
            }
        }
        resultMap.put(ActionEnum.INSERT, insertOperationResources);
        resultMap.put(ActionEnum.UPDATE, updateOperationResources);
        resultMap.put(ActionEnum.DELETE, deleteOperationResources);
        return resultMap;
    }

    private OperationResourceDTO createOrUpdateOperationResourceDTO(OperationDO operationDO,
                                                                    OperationResourceDO operationResource,
                                                                    RoutingStepResourceDO routingStepResourceDO,
                                                                    PhysicalResourceDO physicalResourceDO,
                                                                    ProductCandidateResourceTimeVO candidateResourceTimeVO,
                                                                    SyncContext syncContext
    ) {
        OperationResourceDTO operationResourceDTO = new OperationResourceDTO();
        if (null == operationResource) {
            //新增
            operationResourceDTO.setOrderId(operationDO.getOrderId());
            operationResourceDTO.setOperationId(operationDO.getId());
            operationResourceDTO.setRoutingStepId(operationDO.getRoutingStepId());
            operationResourceDTO.setRoutingStepSequenceNo(operationDO.getRoutingStepSequenceNo());
            operationResourceDTO.setPlannedResourceId(physicalResourceDO.getStandardResourceId());
        } else {
            //修改
            operationResourceDTO = OperationResourceConvertor.INSTANCE.do2Dto(operationResource);
        }
        BigDecimal maxLotSize = null;
        if (null != physicalResourceDO.getMaxLotSize()) {
            maxLotSize = BigDecimal.valueOf(physicalResourceDO.getMaxLotSize());
        }
        operationResourceDTO.setMaxLotSize(routingStepResourceDO.getMaxLotSize() == null ? maxLotSize : routingStepResourceDO.getMaxLotSize());
        if (routingStepResourceDO.getSetupDuration() == null) {
            operationResourceDTO.setSetupDuration(physicalResourceDO.getSetupDuration());
        } else {
            operationResourceDTO.setSetupDuration(Integer.parseInt(routingStepResourceDO.getSetupDuration()));
        }
        if (routingStepResourceDO.getCleanupDuration() == null) {
            operationResourceDTO.setCleanupDuration(physicalResourceDO.getCleanupDuration());
        } else {
            operationResourceDTO.setCleanupDuration(Integer.parseInt(routingStepResourceDO.getCleanupDuration()));
        }
        operationResourceDTO.setPhysicalResourceId(physicalResourceDO.getId());
        operationResourceDTO.setOrderType(OrderTypeEnum.WORK_ORDER.getCode());
        operationResourceDTO.setPriority(routingStepResourceDO.getPriority());
        operationResourceDTO.setFixedWorkHours(routingStepResourceDO.getFixedWorkHours());
        operationResourceDTO.setUnitsPerHour(routingStepResourceDO.getUnitsPerHour());
        operationResourceDTO.setAltToolCode(routingStepResourceDO.getAltToolCode());
        operationResourceDTO.setCleanupUnitBatchSize(routingStepResourceDO.getCleanupUnitBatchSize());
        operationResourceDTO.setMatchCode(routingStepResourceDO.getMatchCode());
        operationResourceDTO.setMaxProductionSuspendDuration(routingStepResourceDO.getMaxProductionSuspendDuration());
        operationResourceDTO.setMaxSetupSuspendDuration(routingStepResourceDO.getMaxSetupSuspendDuration());
        operationResourceDTO.setSetupUnitBatchSize(routingStepResourceDO.getSetupUnitBatchSize());
        operationResourceDTO.setProductionUnitBatchSize(routingStepResourceDO.getProductionUnitBatchSize());
        operationResourceDTO.setMaxCleanupSuspendDuration(routingStepResourceDO.getMaxCleanupSuspendDuration());
        operationResourceDTO.setProductionLine(physicalResourceDO.getProductionLine());
        operationResourceDTO.setStrictProductionLineConstraints(physicalResourceDO.getStrictProductionLineConstraints());
        operationResourceDTO.setUnitProductionTime(routingStepResourceDO.getUnitProductionTime());
        //根据产能平衡计算后的优先级取值
        if (null != candidateResourceTimeVO) {
            operationResourceDTO.setPriority(candidateResourceTimeVO.getPriority());
        }
        //候选资源里有烘弯限制资源
        if (syncContext.getMjLimitResourceList().contains(physicalResourceDO.getStandardResourceId())) {
            String key = String.join("-", operationDO.getStockPointId(), operationDO.getProductId(), operationDO.getRoutingStepSequenceNo().toString());
            if (syncContext.getMoldQuantityLimitMap().containsKey(key)) {
                operationResourceDTO.setMoldQuantityLimit(syncContext.getMoldQuantityLimitMap().get(key));
            }
        }

        return operationResourceDTO;
    }


    private void dealOperationResources(Map<ActionEnum, List<OperationResourceDTO>> operationResourceMap) {
        //新增
        List<OperationResourceDTO> insertOperationResources = operationResourceMap.get(ActionEnum.INSERT);
        if (CollectionUtils.isNotEmpty(insertOperationResources)) {
            operationResourceService.doCreateBatch(insertOperationResources);
        }
        //修改
        List<OperationResourceDTO> updateOperationResources = operationResourceMap.get(ActionEnum.UPDATE);
        if (CollectionUtils.isNotEmpty(updateOperationResources)) {
            operationResourceService.doUpdateBatch(updateOperationResources);
        }
        //删除
        List<OperationResourceDTO> deleteOperationResources = operationResourceMap.get(ActionEnum.DELETE);
        if (CollectionUtils.isNotEmpty(deleteOperationResources)) {
            List<String> operationResourceIds = deleteOperationResources.stream().map(OperationResourceDTO::getId).collect(Collectors.toList());
            operationResourceService.doDelete(operationResourceIds);
        }
    }
}
