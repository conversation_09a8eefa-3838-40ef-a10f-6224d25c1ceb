package com.yhl.scp.mps.schedule.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.StopWatch;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.annotation.FieldInterpretation;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.*;
import com.yhl.platform.component.gantt.Process;
import com.yhl.platform.component.gantt.*;
import com.yhl.platform.pivot.result.Field;
import com.yhl.scp.ams.basic.enums.TimelineDimensionEnum;

import com.yhl.scp.ams.extension.schedule.domain.entity.SchedulePeriod;
import com.yhl.scp.ams.extension.schedule.domain.entity.SubBlock;
import com.yhl.scp.mps.schedule.service.OperationPlanGanttService;
import com.yhl.scp.mds.basic.enums.ObjectTypeEnum;
import com.yhl.scp.mds.basic.resource.enums.ResourceCategoryEnum;
import com.yhl.scp.mds.extension.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.mds.extension.calendar.vo.ShiftVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mps.schedule.vo.GanttLineVO;
import com.yhl.scp.mps.schedule.vo.GanttVO;
import com.yhl.scp.sds.extension.feign.dto.ScheduleParamDTO;
import com.yhl.scp.sds.extension.order.vo.OperationResourceVO;
import com.yhl.scp.sds.extension.order.vo.OperationSubTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationTaskVO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.pegging.vo.DemandVO;
import com.yhl.scp.sds.extension.pegging.vo.SupplyVO;
import com.yhl.scp.sds.feign.SdsFeign;
import com.yhl.scp.sds.feign.SdsScheduleFeign;
import com.yhl.scp.sds.order.service.OperationResourceService;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.OperationSubTaskService;
import com.yhl.scp.sds.order.service.OperationTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>OperationPlanGanttServiceImpl</code>
 * <p>
 * 工序计划甘特应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-05-13 15:25:04
 */
@Slf4j
@Service
public class OperationPlanGanttServiceImpl implements OperationPlanGanttService {
    @Resource
    private SdsFeign sdsFeign;
    @Resource
    private SdsScheduleFeign sdsScheduleFeign;
    @Resource
    private MdsFeign mdsFeign;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private GanttCategoryStrategy ganttCategoryStrategy;
    @Resource
    private OperationService operationService;
    @Resource
    private OperationResourceService operationResourceService;
    @Resource
    private OperationTaskService operationTaskService;
    @Resource
    private OperationSubTaskService operationSubTaskService;
    /**
     * 甘特可分组字段
     */
    protected static final Map<String, ObjectTypeEnum> GANTT_GROUP_MAP = new HashMap<String, ObjectTypeEnum>() {
        private static final long serialVersionUID = 2010069706955999809L;

        {
            put("physicalResourceCode", ObjectTypeEnum.PHYSICAL_RESOURCE);
        }
    };

    @Override
    public List<LabelValue> operationResources(String operationIds) {
        List<LabelValue> labelValueList = new ArrayList<>();
        Map<String, Object> map = new HashMap<>(2);
        try {
            if (StringUtils.isEmpty(operationIds)) {
                List<OperationResourceVO> resourceList = operationResourceService.selectByParams(map);
                labelValueList = LabelValue.convertList2LabelValue(resourceList, "plannedResourceId", "standardResourceCode");

            } else {
                String[] split = operationIds.split(",");
                for (int i = 0; i < split.length; i++) {
                    String operationId = split[i];
                    map.put("operationId", operationId);
                    List<OperationResourceVO> operationResourceVOS = operationResourceService.selectByParams(map);
                    if (i == 0) {
                        labelValueList.addAll(LabelValue.convertList2LabelValue(operationResourceVOS, "plannedResourceId", "standardResourceCode"));
                    } else {
                        labelValueList.retainAll(LabelValue.convertList2LabelValue(operationResourceVOS, "plannedResourceId", "standardResourceCode"));
                    }
                }
            }
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
        Set<LabelValue> set = new HashSet<>(labelValueList);
        List<LabelValue> uniqueList = new ArrayList<>(set);
        return uniqueList;
    }


    @Override
    public PageInfo<Category> getResourceCategories(String property, String parentProcessKey, boolean hasTask, Pagination pagination,
                                                    String sortParam, String queryCriteriaParam) {
        String objectType = GANTT_GROUP_MAP.get(property).getCode();
        if (hasTask) {
            List<String> assignedPhysicalResourceIds = selectAssignedPhysicalResourceIds();
            queryCriteriaParam = StringUtils.isEmpty(queryCriteriaParam) ? "" : queryCriteriaParam;
            StringBuilder stringBuilder = new StringBuilder();
            for (String assignedPhysicalResourceId : assignedPhysicalResourceIds) {
                stringBuilder.append("'" + assignedPhysicalResourceId + "',");
            }
            queryCriteriaParam = queryCriteriaParam + " and id in (" + stringBuilder.substring(0, stringBuilder.length() - 1) + ")";
        }
        queryCriteriaParam = queryCriteriaParam == null ? "resource_category = 'MAIN' and infinite_capacity='NO'" : queryCriteriaParam + " " +
                "and resource_category = 'MAIN' and infinite_capacity='NO'";
        // 获取分页的总条数
        PageInfo<Category> categories = ganttCategoryStrategy.getCategoriesPage(objectType, property, parentProcessKey, pagination, sortParam, queryCriteriaParam);
        return categories;
    }

    @Override
    public List<Field> getGroupFields() {
        List<Field> fields = new ArrayList<>();
        try {
            Class clazz = Class.forName(ObjectTypeEnum.PHYSICAL_RESOURCE.getMappingValue());
            for (Map.Entry<String, ObjectTypeEnum> entry : GANTT_GROUP_MAP.entrySet()) {
                java.lang.reflect.Field classField = clazz.getSuperclass().getDeclaredField(entry.getKey());
                classField.setAccessible(true);
                FieldInterpretation fieldInterpretation = classField.getAnnotation(FieldInterpretation.class);
                if (null == fieldInterpretation) {
                    continue;
                }
                Field field = new Field();
                field.setObjectType(entry.getValue().getCode());
                field.setLabel(fieldInterpretation.value());
                field.setProp(classField.getName());
                field.setDataType(classField.getType().toString());
                fields.add(field);
            }
        } catch (ClassNotFoundException | NoSuchFieldException e) {
            throw new BusinessException("获取分组字段失败", e);
        }
        return fields;
    }

    @Override
    public Gantt getGanttResourceInfo(List<String> resourceIds) {
        SchedulePeriod schedulePeriod = this.getSchedulePeriod();
        List<ResourceCalendarVO> resourceCalendars = mdsFeign.getResourceCalendarsByResourceIds(resourceIds);
        resourceCalendars = resourceCalendars.stream().filter(k -> "NORMAL".equals(k.getCalendarType()) || "OVERTIME".equals(k.getCalendarType())).collect(Collectors.toList());
        // 根据resourceId分组
        Map<String, List<ResourceCalendarVO>> resourceCalendarMap = resourceCalendars.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getPhysicalResourceId));
        //将Map的key从资源id改为资源code
        List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.getPhysicalResourcesByResourceIds(resourceIds);
        Map<String, PhysicalResourceVO> physicalResourceIdMap = physicalResourceVOS.stream().collect(Collectors.toMap(k -> k.getId(), v -> v));
        Map<String, List<ResourceCalendarVO>> resourceCalendarCodeMap = new HashMap<>();
        resourceCalendarMap.forEach((k, v) -> {
            PhysicalResourceVO physicalResourceVO = physicalResourceIdMap.get(k);
            String physicalResourceCode = physicalResourceVO.getPhysicalResourceCode();
            resourceCalendarCodeMap.put(physicalResourceCode, v);
        });

        Gantt gantt = new Gantt();
        gantt.setProcesses(getResourceProcesses(resourceCalendarCodeMap, schedulePeriod));
        return gantt;
    }


    @Override
    public Gantt getGanttOperationInfo(List<String> properties, Pagination pagination, String sortParam, String queryCriteriaParam) {
        StopWatch stopWatch = new StopWatch();
        Gantt gantt = new Gantt();
        List<Process> processes = new ArrayList<>();
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        stopWatch.start();
        queryCriteriaParam = queryCriteriaParam == null ? "" : queryCriteriaParam;
        queryCriteriaParam = queryCriteriaParam + "and start_time between '" + DateUtils.dateToString(planningHorizon.getHistoryRetrospectStartTime(), DateUtils.COMMON_DATE_STR1) +
                "' and '" + DateUtils.dateToString(planningHorizon.getPlanEndTime(), DateUtils.COMMON_DATE_STR1) + "'";
        List<OperationTaskVO> tasks = operationTaskService.selectByPage(pagination, sortParam, queryCriteriaParam);
        stopWatch.stop();
        log.info("getGantt 1, time is " + stopWatch.getLastTaskTimeMillis() / 1000 + "s");
        List<String> taskIds = tasks.stream().map(OperationTaskVO::getId).collect(Collectors.toList());
        List<OperationSubTaskVO> operationSubTasks = operationSubTaskService.selectByParams(ImmutableMap.of("taskIds", taskIds));
        Map<String, List<OperationSubTaskVO>> subTaskMap = operationSubTasks.stream().collect(Collectors.groupingBy(OperationSubTaskVO::getTaskId));
        Map<String, List<OperationTaskVO>> processMap = tasks.stream().collect(Collectors.groupingBy(item -> getProcessKey(item, properties)));
        // 查询工序
        List<String> operationIdList = tasks.stream().map(OperationTaskVO::getOperationId).distinct().collect(Collectors.toList());
        List<OperationVO> operations = operationService.getVOListByIds(operationIdList);
        // 获取父工序ID
        List<String> parentOperationIdList = operations.stream().map(OperationVO::getParentId).distinct().collect(Collectors.toList());
        // 查询父工序
        List<OperationVO> parentOperations = operationService.getVOListByIds(parentOperationIdList);
        Map<String, OperationVO> operationMapOfId = operations.stream().collect(Collectors.toMap(OperationVO::getId, Function.identity()));
        Map<String, OperationVO> parentOperationMapOfId = parentOperations.stream().collect(Collectors.toMap(OperationVO::getId, Function.identity()));

        for (Map.Entry<String, List<OperationTaskVO>> entry : processMap.entrySet()) {
            Process process = new Process();
            process.setKey(entry.getKey());
            List<OperationTaskVO> tks = entry.getValue();
            List<Block> blocks = new ArrayList<>();
            //多重能力资源分组
            Map<Integer, List<OperationTaskVO>> multipleResourceGroupMap = getMultipleResourceGroupMap(tks);
            for (OperationTaskVO task : tks) {
                Block block = new Block();
                block.setKey(task.getOperationId());
                block.setStartTime(task.getStartTime());
                block.setEndTime(task.getEndTime());
                Map<String, Object> customMap = task.getCustomMap();
                if (customMap == null) {
                    customMap = new HashMap<>();
                }
                List<SubBlock> subBlocks = new ArrayList<>();
                List<OperationSubTaskVO> subTaskVOList = subTaskMap.get(task.getId());
                String physicalResourceId = null;
                if (CollUtil.isNotEmpty(subTaskVOList)) {
                    OperationSubTaskVO operationSubTaskVO = subTaskVOList.get(0);
                    physicalResourceId = operationSubTaskVO.getPhysicalResourceId();
                    for (OperationSubTaskVO subTask : subTaskVOList) {
                        SubBlock subBlock = new SubBlock();
                        subBlock.setStartTime(subTask.getStartTime());
                        subBlock.setEndTime(subTask.getEndTime());
                        subBlock.setTaskType(subTask.getTaskType());
                        subBlocks.add(subBlock);
                    }
                }
                customMap.put("subBlocks", subBlocks);
                customMap.put("physicalResourceId", physicalResourceId);
                Map<String, Object> propertiesMap = new HashMap<>(2);
                List<OperationTaskVO> multipleTasks = multipleResourceGroupMap.get(task.getMultipleGroupId());
                propertiesMap.put("multipleCount", multipleTasks.size());
                propertiesMap.put("multipleSort", task.getMultipleNum());
                // 拖拽可排时间提醒
                OperationVO operation = operationMapOfId.get(task.getOperationId());
                propertiesMap.put("earliestStartTime", operation.getEarliestStartTime());
                if (StringUtils.isNotEmpty(operation.getParentId())) {
                    propertiesMap.put("earliestEndTime", parentOperationMapOfId.get(operation.getParentId()).getCalcLatestEndTime());
                } else {
                    propertiesMap.put("earliestEndTime", operation.getCalcLatestEndTime());
                }
                customMap.put("properties", propertiesMap);
                customMap.put("planStatus", operation.getPlanStatus());
                customMap.put("frozen", operation.getFrozen());
                block.setCustomMap(customMap);
                blocks.add(block);
            }
            process.setBlocks(blocks);
            processes.add(process);
        }
        gantt.setProcesses(processes);
        return gantt;
    }

    private String getProcessKey(Object object, List<String> properties) {
        String processKey = "";
        for (String property : properties) {
            if (StringUtils.isEmpty(processKey)) {
                processKey = String.valueOf(ReflectUtils.getFieldValueByName(property, object));
            } else {
                processKey += "-" + ReflectUtils.getFieldValueByName(property, object);
            }

        }
        return processKey;
    }

    public Map<Integer, List<OperationTaskVO>> getMultipleResourceGroupMap(List<OperationTaskVO> operationTasks) {
        //按照开始时间排序
        CollectionUtils.sort(operationTasks, "startTime,endTime");
        Map<Integer, List<OperationTaskVO>> map = new HashMap<>(16);
        Integer groupId = 1;
        List<OperationTaskVO> groupList = new ArrayList<>();
        //所有工序任务给个默认值,后面比较重叠
        for (OperationTaskVO operationTask : operationTasks) {
            operationTask.setMultipleNum(0);
        }
        for (int i = 0; i < operationTasks.size(); i++) {
            OperationTaskVO task = operationTasks.get(i);
            //用于前端计算block高度(连续重叠个数)
            task.setMultipleNum(groupList.size());
            groupList.add(task);
            map.put(groupId, groupList);
            task.setMultipleGroupId(groupId);

            if (i < operationTasks.size() - 1) {
                OperationTaskVO nextTask = operationTasks.get(i + 1);
                if (!isContain(task.getStartTime(), task.getEndTime(), nextTask.getStartTime(), nextTask.getEndTime())) {
                    //跟下一block没有交集
                    //初始化list
                    groupList = new ArrayList<>();
                    //下一个分组id
                    groupId++;
                }
            }
            //减少甘特的高度
            if (i > 0) {
                OperationTaskVO operationTask = operationTasks.get(i);
                //与前一个工序不重叠
                List<OperationTaskVO> preOperationTasks = map.get(operationTask.getMultipleGroupId());
                //获取工序任务的multipleNum
                Integer multipleNum = getMultipleNum(operationTasks, task);
                //为空则每个multipleNum都有重复,multipleNum需要+1
                if (null != multipleNum) {
                    task.setMultipleNum(multipleNum);
                } else {
                    Optional<OperationTaskVO> maxMultipleNumOperationTask = operationTasks.stream().max(Comparator.comparing(OperationTaskVO::getMultipleNum));
                    task.setMultipleNum(maxMultipleNumOperationTask.get().getMultipleNum() + 1);
                }
                task.setMultipleGroupId(++groupId);
                map.put(task.getMultipleGroupId(), preOperationTasks);
            }
        }
        Optional<OperationTaskVO> maxMultipleNumOperationTask = operationTasks.stream().max(Comparator.comparing(OperationTaskVO::getMultipleNum));
        Integer multipleNum = maxMultipleNumOperationTask.get().getMultipleNum();
        List<OperationTaskVO> operationTasks1 = new ArrayList<>();
        if (multipleNum > 0) {
            for (int i = 0; i <= multipleNum; i++) {
                operationTasks1.add(operationTasks.get(i));
            }
        }
        for (Map.Entry<Integer, List<OperationTaskVO>> entry : map.entrySet()) {
            map.put(entry.getKey(), operationTasks1);
        }
        return map;
    }

    private Integer getMultipleNum(List<OperationTaskVO> operationTasks, OperationTaskVO task) {
        List<Integer> matchMultipleList = new ArrayList<>();
        Integer returnMultipleNum = null;
        //根据MultipleNum分组
        Map<Integer, List<OperationTaskVO>> collect = operationTasks.stream().collect(Collectors.groupingBy(OperationTaskVO::getMultipleNum));
        //优先放在连续任务上面
        for (Map.Entry<Integer, List<OperationTaskVO>> entry : collect.entrySet()) {
            //默认为不重叠
            boolean repeatFlag = false;
            Integer multipleNum = entry.getKey();
            List<OperationTaskVO> operationTaskList = entry.getValue();
            //判断该工序是否在同一个高度上是否有重叠的
            for (OperationTaskVO operationTask : operationTaskList) {
                if (task != operationTask) {
                    if (isContain(operationTask.getStartTime(), operationTask.getEndTime(), task.getStartTime(), task.getEndTime())) {
                        //重叠
                        repeatFlag = true;
                        break;
                    }
                }
            }
            if (!repeatFlag) {
                //没有重叠
                matchMultipleList.add(multipleNum);
            }
        }
        //选择高度行,尽量匹配连续的高度行
        Map<Date, Integer> multipleNumMap = new HashMap<>(6);
        if (CollectionUtils.isNotEmpty(matchMultipleList)) {
            for (Integer multipleNum : matchMultipleList) {
                List<OperationTaskVO> operationTaskList = collect.get(multipleNum);
                operationTaskList = operationTaskList.stream().sorted(Comparator.comparing(OperationTaskVO::getStartTime)).collect(Collectors.toList());
                Map<Date, OperationTaskVO> operationTaskMap = operationTaskList.stream().collect(Collectors.toMap(OperationTaskVO::getEndTime, Function.identity(), (k1, k2) -> k2));
                Date nearestValue = findNearestValue(new ArrayList<>(operationTaskMap.keySet()), task.getStartTime());
                multipleNumMap.put(nearestValue, multipleNum);
            }
            Date nearestValue = findNearestValue(new ArrayList<>(multipleNumMap.keySet()), task.getStartTime());
            returnMultipleNum = multipleNumMap.get(nearestValue);
        }
        return returnMultipleNum;
    }

    /**
     * 找到最近值
     *
     * @param list       列表
     * @param targetDate 输入
     * @return {@link String}
     */
    public static Date findNearestValue(List<Date> list, Date targetDate) {

        try {
            long targetLong = targetDate.getTime();
            Date nearestValue = list.get(0);
            long diff = Math.abs(list.get(0).getTime() - targetLong);
            for (int i = 1; i < list.size(); i++) {
                long newDiff = Math.abs(list.get(i).getTime() - targetLong);
                if (newDiff < diff) {
                    nearestValue = list.get(i);
                    diff = newDiff;
                }
            }
            return nearestValue;
        } catch (Exception e) {
            return list.get(0);
        }
    }


    private boolean isContain(Date startDate1, Date endDate1, Date startDate2, Date endDate2) {
        if (startDate2.compareTo(startDate1) > 0 && startDate2.compareTo(endDate1) < 0) {
            return true;
        }
        if (endDate2.compareTo(endDate1) < 0 && endDate2.compareTo(startDate1) > 0) {
            return true;
        }
        if (startDate2.compareTo(startDate1) < 0 && endDate2.compareTo(endDate1) > 0) {
            return true;
        }
        return false;
    }


    private SchedulePeriod getSchedulePeriod() {
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        if (planningHorizon == null) {
            throw new BusinessException("请设置计划排程期间");
        }
        SchedulePeriod schedulePeriod = new SchedulePeriod();
        schedulePeriod.setPlanStartDate(planningHorizon.getPlanStartTime());
        schedulePeriod.setBaseDate(planningHorizon.getHistoryRetrospectStartTime());
        schedulePeriod.setEndDate(planningHorizon.getPlanEndTime());
        schedulePeriod.setLockEndDate(planningHorizon.getPlanLockEndTime());
        return schedulePeriod;
    }

    private List<Process> getResourceProcesses(Map<String, List<ResourceCalendarVO>> resourceCalendarMap, SchedulePeriod schedulePeriod) {
        List<Process> processes = new ArrayList<>();
        for (Map.Entry<String, List<ResourceCalendarVO>> entry : resourceCalendarMap.entrySet()) {
            Process process = new Process();
            process.setKey(entry.getKey());
            process.setBlocks(getResourceBlocks(entry.getValue(), schedulePeriod));
            processes.add(process);
        }
        return processes;
    }

    private List<Block> getResourceBlocks(List<ResourceCalendarVO> calendars, SchedulePeriod schedulePeriod) {
        calendars =  calendars.stream().filter(k->! k.getEndTime().before(schedulePeriod.getBaseDate()))
                .sorted(Comparator.comparing(ResourceCalendarVO::getStartTime)).collect(Collectors.toList());
        List<Block> blocks = new ArrayList<>();
        for (int i = 0; i < calendars.size(); i++) {
            ResourceCalendarVO calendar = calendars.get(i);
            if (i == 0) {
                if(!calendar.getStartTime().before(schedulePeriod.getBaseDate())){
                    Block block = new Block();
                    block.setStartTime(schedulePeriod.getBaseDate());
                    block.setEndTime(calendar.getStartTime());
                    blocks.add(block);
                }

            } else {
                ResourceCalendarVO preCalendar = calendars.get(i - 1);
                Block block = new Block();
                block.setStartTime(preCalendar.getEndTime());
                block.setEndTime(calendar.getStartTime());
                blocks.add(block);
            }

            if (i == (calendars.size() - 1)) {
                Block block = new Block();
                block.setStartTime(calendar.getEndTime());
                block.setEndTime(schedulePeriod.getEndDate());
                blocks.add(block);
            }
        }
        //剔除开始结束时间一致的
        blocks.removeIf(block -> block.getStartTime().equals(block.getEndTime()));
        //剔除时间在计划期间范围外的,考虑周期结束时间截止的
        blocks = blocks.stream().map(k -> {
            if (k.getEndTime().after(schedulePeriod.getEndDate())) {
                k.setEndTime(schedulePeriod.getEndDate());
                return k;
            }
            return k;
        }).collect(Collectors.toList());
        return blocks;
    }

    /**
     * 查询计划周期内已排任务的物理资源id
     * @return
     */
    private List<String> selectAssignedPhysicalResourceIds(){
        PlanningHorizonVO planningHorizon = mdsFeign.getPlanningHorizon();
        return operationTaskService.selectAssignedPhysicalResourceIds(planningHorizon.getHistoryRetrospectStartTime()
                , planningHorizon.getPlanEndTime());
    }
}
