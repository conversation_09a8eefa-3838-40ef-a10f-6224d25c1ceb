package com.yhl.scp.mps.adjustmantSuggestion.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.adjustmantSuggestion.dto.SdsAdjustmentSuggestionDTO;
import com.yhl.scp.mps.adjustmantSuggestion.service.SdsAdjustmentSuggestionService;
import com.yhl.scp.mps.adjustmantSuggestion.vo.SdsAdjustmentSuggestionVO;
import com.yhl.scp.mps.aop.PermissionMethodPrevention;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>SdsAdjustmentSuggestionController</code>
 * <p>
 * 主计划调整建议控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-01-15 14:53:51
 */
@Slf4j
@Api(tags = "主计划调整建议控制器")
@RestController
@RequestMapping("sdsAdjustmentSuggestion")
public class SdsAdjustmentSuggestionController extends BaseController {

    @Resource
    private SdsAdjustmentSuggestionService sdsAdjustmentSuggestionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<SdsAdjustmentSuggestionVO>> page() {
        List<SdsAdjustmentSuggestionVO> sdsAdjustmentSuggestionList = sdsAdjustmentSuggestionService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<SdsAdjustmentSuggestionVO> pageInfo = new PageInfo<>(sdsAdjustmentSuggestionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "延期调整建议查看")
    @GetMapping(value = "delayInfoList")
    @SuppressWarnings("unchecked")
    public BaseResponse<List<SdsAdjustmentSuggestionVO>> delayInfoList() {
        List<SdsAdjustmentSuggestionVO> sdsAdjustmentSuggestionList = sdsAdjustmentSuggestionService.delayInfoList();
        return BaseResponse.success(BaseResponse.OP_SUCCESS, sdsAdjustmentSuggestionList);
    }

    @ApiOperation(value = "延期检查")
    @PostMapping(value = "delayCheck")
    public BaseResponse<String> delayCheck(@RequestParam(value = "operationId", required = false) String operationId) {
        return sdsAdjustmentSuggestionService.delayCheck(operationId);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody SdsAdjustmentSuggestionDTO sdsAdjustmentSuggestionDTO) {
        return sdsAdjustmentSuggestionService.doCreate(sdsAdjustmentSuggestionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody SdsAdjustmentSuggestionDTO sdsAdjustmentSuggestionDTO) {
        return sdsAdjustmentSuggestionService.doUpdate(sdsAdjustmentSuggestionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        sdsAdjustmentSuggestionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<SdsAdjustmentSuggestionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, sdsAdjustmentSuggestionService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "一键调整")
    @PostMapping(value = "adjustment")
    @PermissionMethodPrevention
    public BaseResponse<Void> adjustment(@RequestBody List<String> ids) {
        sdsAdjustmentSuggestionService.doAdjustment(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
