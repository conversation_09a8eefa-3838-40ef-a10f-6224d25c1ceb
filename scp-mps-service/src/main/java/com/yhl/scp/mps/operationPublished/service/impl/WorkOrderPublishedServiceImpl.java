package com.yhl.scp.mps.operationPublished.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.operationPublished.convertor.WorkOrderPublishedConvertor;
import com.yhl.scp.mps.operationPublished.domain.entity.WorkOrderPublishedDO;
import com.yhl.scp.mps.operationPublished.domain.service.WorkOrderPublishedDomainService;
import com.yhl.scp.mps.operationPublished.dto.WorkOrderPublishedDTO;
import com.yhl.scp.mps.operationPublished.infrastructure.dao.WorkOrderPublishedDao;
import com.yhl.scp.mps.operationPublished.infrastructure.po.WorkOrderPublishedPO;
import com.yhl.scp.mps.operationPublished.service.WorkOrderPublishedService;
import com.yhl.scp.mps.operationPublished.vo.CopyPublishedStatusVO;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>WorkOrderPublishedServiceImpl</code>
 * <p>
 * 制造订单发布信息表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-20 09:29:29
 */
@Slf4j
@Service
public class WorkOrderPublishedServiceImpl extends AbstractService implements WorkOrderPublishedService {

    @Resource
    private WorkOrderPublishedDao workOrderPublishedDao;

    @Resource
    private WorkOrderPublishedDomainService workOrderPublishedDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    public BaseResponse<Void> doCreate(WorkOrderPublishedDTO workOrderPublishedDTO) {
        // 0.数据转换
        WorkOrderPublishedDO workOrderPublishedDO = WorkOrderPublishedConvertor.INSTANCE.dto2Do(workOrderPublishedDTO);
        WorkOrderPublishedPO workOrderPublishedPO = WorkOrderPublishedConvertor.INSTANCE.dto2Po(workOrderPublishedDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        workOrderPublishedDomainService.validation(workOrderPublishedDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(workOrderPublishedPO);
        workOrderPublishedDao.insertWithPrimaryKey(workOrderPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(WorkOrderPublishedDTO workOrderPublishedDTO) {
        // 0.数据转换
        WorkOrderPublishedDO workOrderPublishedDO = WorkOrderPublishedConvertor.INSTANCE.dto2Do(workOrderPublishedDTO);
        WorkOrderPublishedPO workOrderPublishedPO = WorkOrderPublishedConvertor.INSTANCE.dto2Po(workOrderPublishedDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        workOrderPublishedDomainService.validation(workOrderPublishedDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(workOrderPublishedPO);
        workOrderPublishedDao.update(workOrderPublishedPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<WorkOrderPublishedDTO> list) {
        List<WorkOrderPublishedPO> newList = WorkOrderPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        workOrderPublishedDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<WorkOrderPublishedDTO> list) {
        List<WorkOrderPublishedPO> newList = WorkOrderPublishedConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        workOrderPublishedDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return workOrderPublishedDao.deleteBatch(idList);
        }
        return workOrderPublishedDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public WorkOrderPublishedVO selectByPrimaryKey(String id) {
        WorkOrderPublishedPO po = workOrderPublishedDao.selectByPrimaryKey(id);
        return WorkOrderPublishedConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_ord_work_order_published")
    public List<WorkOrderPublishedVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_ord_work_order_published")
    public List<WorkOrderPublishedVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<WorkOrderPublishedVO> dataList = workOrderPublishedDao.selectByCondition(sortParam, queryCriteriaParam);
        WorkOrderPublishedServiceImpl target = springBeanUtils.getBean(WorkOrderPublishedServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<WorkOrderPublishedVO> selectByParams(Map<String, Object> params) {
        List<WorkOrderPublishedPO> list = workOrderPublishedDao.selectByParams(params);
        return WorkOrderPublishedConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<WorkOrderPublishedVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.WORK_ORDER_PUBLISHED.getCode();
    }

    @Override
    public List<WorkOrderPublishedVO> invocation(List<WorkOrderPublishedVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

	@Override
	public Boolean disposeSnapshotData(String oldPublishedLogId, String bindPublishedLogId,
			String publishedTime, Boolean copyFlag) {
		try {
			if(StringUtils.isNotEmpty(oldPublishedLogId)) {
				workOrderPublishedDao.deleteByPublishedLogId(oldPublishedLogId);
			}
			if(copyFlag) {
				int count = workOrderPublishedDao.selectAllCount();
				if(count > 0) {
					workOrderPublishedDao.doSnapshotData(bindPublishedLogId, publishedTime);
				}
			}
		} catch (Exception e) {
			log.error("主生产计划快照制作订单数据表失败:" + e);
			return false;
		}
		return true;
	}

    @Override
    public List<WorkOrderPublishedVO> selectVOByParams(Map<String, Object> params) {
        return workOrderPublishedDao.selectVOByParams(params);
    }

    private final List<String> orderType = Arrays.asList("SZ", "XSB", "SYTS");

	@Override
	public CopyPublishedStatusVO doSnapshotDataForLineGroup(List<String> productIds,
			List<String> publishedLogIds, Map<String, String> productLineGroupMap) {
		//先删除资源组下的数据，然后查询数据，并插入，返回结果
		workOrderPublishedDao.deleteByPublishedLogIds(publishedLogIds);
		//查询数据
		List<WorkOrderPublishedPO> workOrders = new ArrayList<>();
		Lists.partition(productIds, 1000).forEach(item -> {
			List<WorkOrderPublishedPO> subList = workOrderPublishedDao.selectUnFinishedOrder(item);
			if(CollectionUtils.isNotEmpty(subList)) {
				workOrders.addAll(subList);
			}
		});
		if(CollectionUtils.isEmpty(workOrders)) {
			return new CopyPublishedStatusVO(true, new HashMap<>());
		}
        List<WorkOrderPublishedPO> collect = workOrders.stream().filter(t -> orderType.contains(t.getOrderType())
                        && StringUtils.isEmpty(t.getTestOrderNumber())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            List<String> workOrderNoList = collect.stream().map(WorkOrderPublishedPO::getOrderNo).collect(Collectors.toList());
            throw new BusinessException("工单号:" + StringUtils.join(workOrderNoList, ",") + "未填写试制单号，无法下发");
        }
        Date date = new Date();
        String userId = SystemHolder.getUserId();
        String newDayStr = DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR4);
		Map<String, String> orderIdLineGroupMap = new HashMap<>();
        workOrders.forEach( item -> {
			item.setNewId(item.getId() + "-" + newDayStr);
			item.setPublishedLogId(productLineGroupMap.get(item.getProductId()));
            item.setCreator(userId);
            item.setCreateTime(date);
            item.setModifier(userId);
            item.setModifyTime(date);
			orderIdLineGroupMap.put(item.getId(), item.getPublishedLogId());
		});
		//插入数据
		Lists.partition(workOrders, 1000).forEach(item -> {
			workOrderPublishedDao.insertBatchWithPrimaryKey(item);
		});
		return new CopyPublishedStatusVO(true, orderIdLineGroupMap);
	}

}
