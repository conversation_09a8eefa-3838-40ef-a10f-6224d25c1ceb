package com.yhl.scp.mps.domain.dispatch.process;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.das.core.InputBase;
import com.yhl.scp.ips.algorithm.dto.AlgorithmStepLogDTO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.domain.dispatch.IMpsSchedule;
import com.yhl.scp.mps.domain.dispatch.model.context.MpsAnalysisContext;
import com.yhl.scp.mps.domain.dispatch.support.BaseScheduleSupport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
public abstract class AbstractSchedule extends BaseScheduleSupport implements IMpsSchedule, IAmsSchedule {

    /**
     * 主计划算法调用
     *
     * @param algorithmLog 算法日志
     */
    @Override
    public void doMpsSchedule(AlgorithmLog algorithmLog) {
        List<AlgorithmStepLogDTO> algorithmStepLogDTOList = new ArrayList<>();
        try {
            // 1.初始化数据
            InputBase inputBase = super.initProcess(algorithmLog, ModuleCodeEnum.MPS.getCode());
            // 2.组装输入数据（异步线程+多场景切换）
            getInputData(algorithmLog, inputBase, algorithmStepLogDTOList);
            // 3.RPC-das服务器调用
            rpcDasServer(algorithmLog, inputBase, algorithmStepLogDTOList);
            // 4.标记任务状态
            algorithmLog.setStatus(AlgorithmLogStatusEnum.RUNNING.getCode());
            //通过算法日志id创建主生产计划版本
            masterPlanVersionService.doCreateByAlgorithmLogId(algorithmLog.getId());
        } catch (Exception e) {
            log.error("MPS主计划排产算法调用失败：", e);
            algorithmLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            algorithmLog.setFailMsg("MPS算法调用失败：" + e.getMessage());
        } finally {
            // 更新ips算法日志调用状态
            ipsFeign.updateAlgorithmLog(algorithmLog);
            ipsFeign.batchSaveStepLog(algorithmStepLogDTOList);
            log.info("MPS算法日志更新完成");
        }
    }


    /**
     * 日计划算法调用
     *
     * @param masterAlgorithmLog 主计划运行算法日志
     * @param mpsAnalysisContext context
     */
    @Override
    @Transactional
    public void doAmsSchedule(AlgorithmLog masterAlgorithmLog, MpsAnalysisContext mpsAnalysisContext) {
        // 创建算法日志
        AlgorithmLog amsAlgoLog = createAmsAlgoLog(masterAlgorithmLog);
        try {
            // 1.AMS调用+解析结果
            analysisAmsResult(amsAlgoLog, mpsAnalysisContext);
            // 2.自动排产后失效掉全部的变更通知
            deliveryPlanPublishedCompareDoEnable(mpsAnalysisContext);
        } catch (Exception e) {
            log.error("AMS算法运行失败：", e);
            amsAlgoLog.setStatus(AlgorithmLogStatusEnum.FAIL.getCode());
            amsAlgoLog.setEndTime(new Date());
            amsAlgoLog.setFailMsg(e.getMessage());
            throw new RuntimeException("AMS算法运行失败：" + e.getMessage());
        } finally {
            amsAlgoLog.setEndTime(new Date());
            // 更新ips算法日志调用状态
            CompletableFuture.runAsync(() -> ipsFeign.updateAlgorithmLog(amsAlgoLog));
            log.info("AMS算法日志更新完成");
        }
    }

    protected abstract void analysisAmsResult(AlgorithmLog amsAlgoLog, MpsAnalysisContext mpsAnalysisContext);

    protected abstract AlgorithmLog createAmsAlgoLog(AlgorithmLog masterAlgorithmLog);

    protected abstract BaseResponse rpcDasServer(AlgorithmLog algorithmLog, InputBase inputBase, List<AlgorithmStepLogDTO> algorithmStepLogDTOList);

    /**
     * 自动排产后失效掉全部的变更通知
     */
    protected abstract void deliveryPlanPublishedCompareDoEnable(MpsAnalysisContext analysisContext);

}
