package com.yhl.scp.mps.plan.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.newProduct.vo.NewProductTrialSubmissionDetailVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.extension.resource.vo.StandardResourceVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.adjust.dao.AdjustDao;
import com.yhl.scp.mps.domain.dispatch.IAmsSchedule;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.operationPublished.service.WorkOrderPublishedService;
import com.yhl.scp.mps.operationPublished.vo.WorkOrderPublishedVO;
import com.yhl.scp.mps.plan.convertor.MasterPlanVersionConvertor;
import com.yhl.scp.mps.plan.domain.entity.MasterPlanVersionDO;
import com.yhl.scp.mps.plan.domain.service.MasterPlanVersionDomainService;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationDTO;
import com.yhl.scp.mps.plan.dto.MasterPlanVersionDTO;
import com.yhl.scp.mps.plan.dto.MasterPlanWorkOrderBodyDTO;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanExtDao;
import com.yhl.scp.mps.plan.infrastructure.dao.MasterPlanVersionDao;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.infrastructure.po.MasterPlanVersionPO;
import com.yhl.scp.mps.plan.service.MasterPlanIssuedDataService;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.service.MasterPlanVersionService;
import com.yhl.scp.mps.plan.vo.MasterPlanVersionVO;
import com.yhl.scp.mps.published.service.MasterPlanPublishedService;
import com.yhl.scp.sds.basic.order.vo.OperationBasicVO;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.vo.OperationVO;
import com.yhl.scp.sds.extension.order.vo.WorkOrderVO;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertor;
import com.yhl.scp.sds.order.convertor.WorkOrderConvertorImpl;
import com.yhl.scp.sds.order.service.WorkOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>MasterPlanVersionServiceImpl</code>
 * <p>
 * 主计划发布版本表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 11:50:45
 */
@Slf4j
@Service
public class MasterPlanVersionServiceImpl extends AbstractService implements MasterPlanVersionService {

    /**
     * 版本号初始编码
     */
    public static final String MASTER_PLAN_INIT_VERSION_CODE = "0001";

    /**
     * 版本号初始前缀
     */
    public static final String MASTER_PLAN_INIT_VERSION_CODE_PRE = "PDI";

    @Resource
    private MasterPlanVersionDao masterPlanVersionDao;

    @Resource
    private MasterPlanVersionDomainService masterPlanVersionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DfpFeign dfpFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;


    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private WorkOrderService workOrderService;

    @Resource
    private MasterPlanRelationService masterPlanRelationService;
    @Resource
    private WorkOrderPublishedService workOrderPublishedService;
    @Resource
    private MasterPlanPublishedService masterPlanPublishedService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private AdjustDao adjustDao;
    @Resource
    private MasterPlanExtDao operationExtDao;


    public static final String BR_TAG = "<br/>";

    public static final String WORK_ORDER_NO_MSG = "制造订单号：";

    @Override
    public BaseResponse<Void> doCreate(MasterPlanVersionDTO masterPlanVersionDTO) {
        // 0.数据转换
        MasterPlanVersionDO masterPlanVersionDO = MasterPlanVersionConvertor.INSTANCE.dto2Do(masterPlanVersionDTO);
        MasterPlanVersionPO masterPlanVersionPO = MasterPlanVersionConvertor.INSTANCE.dto2Po(masterPlanVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        masterPlanVersionDomainService.validation(masterPlanVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(masterPlanVersionPO);
        masterPlanVersionDao.insertWithPrimaryKey(masterPlanVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(MasterPlanVersionDTO masterPlanVersionDTO) {
        // 0.数据转换
        MasterPlanVersionDO masterPlanVersionDO = MasterPlanVersionConvertor.INSTANCE.dto2Do(masterPlanVersionDTO);
        MasterPlanVersionPO masterPlanVersionPO = MasterPlanVersionConvertor.INSTANCE.dto2Po(masterPlanVersionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        masterPlanVersionDomainService.validation(masterPlanVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(masterPlanVersionPO);
        masterPlanVersionDao.update(masterPlanVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<MasterPlanVersionDTO> list) {
        List<MasterPlanVersionPO> newList = MasterPlanVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        masterPlanVersionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<MasterPlanVersionDTO> list) {
        List<MasterPlanVersionPO> newList = MasterPlanVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        masterPlanVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return masterPlanVersionDao.deleteBatch(idList);
        }
        return masterPlanVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public MasterPlanVersionVO selectByPrimaryKey(String id) {
        MasterPlanVersionPO po = masterPlanVersionDao.selectByPrimaryKey(id);
        return MasterPlanVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_sds_master_plan_version")
    public List<MasterPlanVersionVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_sds_master_plan_version")
    public List<MasterPlanVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<MasterPlanVersionVO> dataList = masterPlanVersionDao.selectByCondition(sortParam, queryCriteriaParam);
        MasterPlanVersionServiceImpl target = springBeanUtils.getBean(MasterPlanVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<MasterPlanVersionVO> selectByParams(Map<String, Object> params) {
        List<MasterPlanVersionPO> list = masterPlanVersionDao.selectByParams(params);
        return MasterPlanVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<MasterPlanVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersion() {
        return MasterPlanVersionConvertor.INSTANCE.po2Vo(masterPlanVersionDao.selectNewestMasterPlanVersion());
    }

    @Override
    public MasterPlanVersionVO getLatestPublishedMpsVersionPublished() {
        return MasterPlanVersionConvertor.INSTANCE.po2Vo(masterPlanVersionDao.selectNewestMasterPlanVersionPublished());
    }

    @Override
    public List<StandardResourceVO> getStandardCodes(List<MasterPlanWorkOrderBodyDTO> workOrderList) {
        List<String> collect = workOrderList.stream().map(MasterPlanWorkOrderBodyDTO::getResourceCode).collect(Collectors.toList());
        return newMdsFeign.selectStandardByPhysicalCodes(SystemHolder.getScenario(),collect);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.MASTER_PLAN_VERSION.getCode();
    }

    @Override
    public List<MasterPlanVersionVO> invocation(List<MasterPlanVersionVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public void doCreateByAlgorithmLogId(String algorithmLogId) {
        //获取最新的发货计划版本id,code
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        DeliveryPlanVersionVO newestDpvVO = dfpFeign.selectNewestDeliveryPlanVersion(defaultScenario.getData());
        if (newestDpvVO == null) {
            throw new BusinessException("主生产计划计划下发失败,未获取到发货计划版本数据信息!");
        }
        MasterPlanVersionPO addVO = new MasterPlanVersionPO();
        //获取当天最大的版本号
        String prefix = MASTER_PLAN_INIT_VERSION_CODE_PRE + DateUtils.dateToString(new Date(), "yyyyMMdd");
        String maxVersionCode = masterPlanVersionDao.selectCurrentDayMaxVersion(prefix);
        if (StringUtils.isEmpty(maxVersionCode)) {
            addVO.setMasterPlanVersionCode(prefix + MASTER_PLAN_INIT_VERSION_CODE);
        } else {
            addVO.setMasterPlanVersionCode(prefix + getNewVersionCode(maxVersionCode));
        }
        addVO.setMasterPlanVersionCode(prefix);
        addVO.setIssuesPlanStatus(PublishStatusEnum.UNPUBLISH.getCode());
        addVO.setDeliveryPlanVersionId(newestDpvVO.getId());
        addVO.setDeliveryPlanVersionCode(newestDpvVO.getVersionCode());
        addVO.setAlgorithmLogId(algorithmLogId);
        BasePOUtils.insertFiller(addVO);
        masterPlanVersionDao.insertWithPrimaryKey(addVO);
    }

    /**
     * 最新版本号加一
     *
     * @param targetVersionCode 目标版本号
     * @return java.lang.String
     */
    private String getNewVersionCode(String targetVersionCode) {
        String newVersionCode;
        int number = 1;
        if (StringUtils.isNotEmpty(targetVersionCode)) {
            if (targetVersionCode.length() < 4) {
                throw new IllegalArgumentException("The version code must be at least 4 characters long.");
            }
            String lastFive = targetVersionCode.substring(targetVersionCode.length() - 4);
            number = Integer.parseInt(lastFive);
            // 数字加1
            number++;
        }
        // 格式化为五位数，不足五位补零
        newVersionCode = String.format("%04d", number);
        return newVersionCode;
    }

    @Resource
    private IAmsSchedule amsSchedule;

    @Override
    public BaseResponse<Void> publish(List<MasterPlanWorkOrderBodyDTO> workOrderList) {
        if(CollectionUtils.isEmpty(workOrderList)){
            return BaseResponse.error("请勾选需要下发的制造订单");
        }
//        String scenario = SystemHolder.getScenario();
/*        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        Date planStartTime = planningHorizon.getPlanStartTime();
        Date planLockEndTime = planningHorizon.getPlanLockEndTime();
        boolean overFlag = workOrderList.stream().anyMatch(x -> {
            Date date = DateUtils.stringToDate(x.getPlanStartDate(), DateUtils.COMMON_DATE_STR5);
            return date.before(planStartTime) || date.after(planLockEndTime);
        });
        if (overFlag) {
            return BaseResponse.error("只能发布锁定期内工单");
        }*/
        log.info("计划调整开始");
        List<String> ids = StreamUtils.columnToList(workOrderList, MasterPlanWorkOrderBodyDTO::getOperationId);
        List<String> allWorkOrder = operationExtDao.selectDeleteWorkOrder(ids);
        amsSchedule.doAdjustPlan(allWorkOrder);
        log.info("计划调整结束");
        List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("ids", allWorkOrder));
        for (WorkOrderVO workOrderVO : workOrderVOS) {
            workOrderVO.setPlanStatus(PlanStatusEnum.HAS_ISSUED.getCode());
            workOrderVO.setFulfillmentInfo(PlanStatusEnum.HAS_ISSUED.getCode());
            // 下发后得制造订单设置固定
            workOrderVO.setFixed(YesOrNoEnum.YES.getCode());
        }
        List<WorkOrderDTO> workOrderDTOS = WorkOrderConvertor.INSTANCE.vo2Dtos(workOrderVOS);
        workOrderService.doUpdateBatch(workOrderDTOS);

//        log.info("计划下发制造订单数量：{}", workOrderList.size());
//        List<String> publishOrderIds = workOrderList.stream().map(MasterPlanWorkOrderBodyDTO::getWorkOrderId).distinct().collect(Collectors.toList());
        //维护主计划版本下的数据存储
//        masterPlanIssuedDataService.txNewCreateForMasterPlan(SystemHolder.getUserId(), SystemHolder.getUserName());
        // 计划调整


        //获取最新的发布计划数据
//        MasterPlanVersionPO po = masterPlanVersionDao.selectNewestMasterPlanVersion();
//        po.setIssuesPlanTime(new Date());
//        po.setIssuesPlanStatus(PublishStatusEnum.PUBLISHED.getCode());
//        BasePOUtils.updateFiller(po);
//        masterPlanVersionDao.updateSelective(po);
//        Set<String> validOrderTypes = new HashSet<>(Arrays.asList("SZ", "XSB", "SYTS", "SY", "MF"));
//
//        List<WorkOrderPublishedVO> rootWorkList;
//        if (CollectionUtils.isNotEmpty(workOrderList)) {
//            List<WorkOrderVO> workOrderVOS = workOrderService.selectByParams(ImmutableMap.of("ids", publishOrderIds));
//            // 半品订单
//            List<String> rootIds = workOrderVOS.stream().filter(t -> Objects.isNull(t.getTopOrderId())
//                    && Objects.isNull(t.getParentId())).map(WorkOrderVO::getId).collect(Collectors.toList());
//            // 成品订单
//            List<String> topIds = workOrderVOS.stream().filter(t -> Objects.nonNull(t.getTopOrderId())
//                    && Objects.nonNull(t.getParentId())).map(WorkOrderVO::getTopOrderId).distinct().collect(Collectors.toList());
//            List<String> rootOrderIds = Lists.newArrayList();
//            rootOrderIds.addAll(rootIds);
//            rootOrderIds.addAll(topIds);
//            if (CollectionUtils.isEmpty(rootOrderIds)) {
//                return BaseResponse.error("制造订单找不到顶层工单");
//            }
//            Map<String, Object> workParams = MapUtil.newHashMap();
//            workParams.put("parentIdIsNull", "parentIdIsNull");
//            workParams.put("planNoIsNull", "planNoIsNull");
//            workParams.put("ids", rootOrderIds);
//            rootWorkList = workOrderPublishedService.selectVOByParams(workParams);
//        } else {
//            Map<String, Object> workParams = MapUtil.newHashMap();
//            workParams.put("parentIdIsNull", "parentIdIsNull");
//            workParams.put("planNoIsNull", "planNoIsNull");
//            //        workParams.put("creator", SystemHolder.getUserId());
//            rootWorkList = workOrderPublishedService.selectVOByParams(workParams);
//        }
//
//        if (CollectionUtils.isEmpty(rootWorkList)) {
//            return BaseResponse.error("没有可以下发的制造工单");
//        }
//
//        // TODO 主生产计划计划下发逻辑
//        Map<String, Object> stockParams = MapUtil.<String, Object>builder()
//                .put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
//                .put("stockPointType", StockPointTypeEnum.BC.getCode())
//                .put("enabled", YesOrNoEnum.YES.getCode()).build();
//        List<NewStockPointVO> stockPointVOS = mdsFeign.selectStockPointByParams(scenario, stockParams);
//        if (CollectionUtils.isEmpty(stockPointVOS)) {
//            return BaseResponse.error("库存点信息为空");
//        }
//        NewStockPointVO saleOrganizeVO = stockPointVOS.get(0);
//        List<String> workIds = rootWorkList.stream().map(WorkOrderPublishedVO::getId).distinct().collect(Collectors.toList());
//        List<WorkOrderVO> workOrders = workOrderService.selectByParams(ImmutableMap.of("ids", workIds));
//        Map<String, WorkOrderVO> workOrderVOMap = workOrders.stream().collect(Collectors
//                .toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
//        List<String> productCodes = rootWorkList.stream().map(WorkOrderPublishedVO::getProductCode).distinct().collect(Collectors.toList());
//        Map<String, String> productOemMap = Collections.emptyMap();
//        Map<String, List<DeliveryPlanVO2>> productDeliveryListMap  = Collections.emptyMap();
//        Map<String, NewProductTrialSubmissionDetailVO> trialMap = Collections.emptyMap();
//        Map<String, OemVO> trialOemMap = Collections.emptyMap();
//        Map<String, OemVO> oemMap = Collections.emptyMap();
//        boolean trialFlag = rootWorkList.stream().map(WorkOrderPublishedVO::getOrderType).distinct().
//                allMatch(orderTypeCode -> validOrderTypes.contains(orderTypeCode));
//        if(trialFlag){
//            List<NewProductTrialSubmissionDetailVO> trialSubmissionDetailVOS = dfpFeign.
//                    selectNewProductTrialSubmissionDetailByParams(scenario, ImmutableMap.of("productCodes", productCodes));
//            List<String> customerCodes = trialSubmissionDetailVOS.stream().map(NewProductTrialSubmissionDetailVO::getCustomerCode)
//                    .distinct().collect(Collectors.toList());
//            if (CollectionUtils.isEmpty(customerCodes)) {
//                return BaseResponse.error("新品试制表中找不到客户信息");
//            }
//            List<OemVO> oemVOS = dfpFeign.selectOemVOByParams(scenario, ImmutableMap.of("erpCustomerCodes", customerCodes,"enabled","YES"));
//            if (CollectionUtils.isEmpty(oemVOS)) {
//                return BaseResponse.error("主机厂信息表中找不到客户信息");
//            }
//            trialOemMap = oemVOS.stream().collect(Collectors.toMap(OemVO::getErpCustomerCode, Function.identity(), (v1, v2) -> v1));
//            trialMap = CollectionUtils.isEmpty(trialSubmissionDetailVOS)?MapUtil.newHashMap():
//                    trialSubmissionDetailVOS.stream().collect(Collectors.toMap(NewProductTrialSubmissionDetailVO::getProductCode,
//                            Function.identity(), (v1, v2) -> v1));
//        }else{
//            List<DeliveryPlanVO2> deliveryPlanVO2List = dfpFeign.selectDeliveryPlanPublishedByParams(scenario, ImmutableMap.of("productCodes", productCodes));
//            if (CollectionUtils.isEmpty(deliveryPlanVO2List)) {
//                return BaseResponse.error("发货计划信息为空");
//            }
//            productOemMap = deliveryPlanVO2List.stream().collect(
//                    Collectors.toMap(
//                            DeliveryPlanVO2::getProductCode,
//                            DeliveryPlanVO2::getOemCode,
//                            (v1, v2) -> v1
//                    )
//            );
//            productDeliveryListMap = deliveryPlanVO2List.stream().collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));
//            List<String> oemCodes = deliveryPlanVO2List.stream().map(DeliveryPlanVO2::getOemCode).distinct().collect(Collectors.toList());
//            List<OemVO> oemVOS = dfpFeign.selectOemVOByParams(scenario, ImmutableMap.of("oemCodes", oemCodes));
//            if (CollectionUtils.isEmpty(oemVOS)) {
//                return BaseResponse.error("主机厂信息为空");
//            }
//            oemMap = oemVOS.stream().collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (v1, v2) -> v1));
//        }
//
//        int successCount = 0;
//        int failCount = 0;
//        StringBuilder totalErrMsg = new StringBuilder();
//        PlanningHorizonVO planningHorizonVO = mdsFeign.selectPlanningHorizon(null);
//
//        if (CollectionUtils.isEmpty(workOrderList) && Objects.isNull(planningHorizonVO)) {
//            log.error("计划锁定期为空，无法下发");
//            return BaseResponse.error("计划锁定期为空，无法下发");
//        }
//        List<OperationVO> operationVOList = adjustDao.selectKeyOperationByWorkOrderIds(workIds);
//        List<String> resourceIds = operationVOList.stream().map(OperationBasicVO::getPlannedResourceId).filter(StringUtils::isNotEmpty).distinct()
//                .collect(Collectors.toList());
//        List<PhysicalResourceVO> physicalResourceVOS = mdsFeign.selectByPhysicalIds(scenario, resourceIds);
//        Map<String, Date> lockEndTimeMap = getLockEndTimeMap(rootWorkList, operationVOList, physicalResourceVOS, planningHorizonVO);
//        List<MasterPlanRelationDTO> insertMasterPlanRelationDTOS = Lists.newArrayList();
//        List<WorkOrderDTO> updateWorkOrderDtoList = Lists.newArrayList();
//        Map<String, OperationVO> operationVOMap = operationVOList.stream()
//                .collect(Collectors.toMap(OperationBasicVO::getOrderId, Function.identity()));
//        for (WorkOrderPublishedVO workOrderPublishedVO : rootWorkList) {
//            String orderNo = workOrderPublishedVO.getOrderNo();
//            Date lockEndTime = lockEndTimeMap.get(workOrderPublishedVO.getId());
//            OperationVO operationVO = operationVOMap.get(workOrderPublishedVO.getId());
//            if (operationVO.getStartTime().after(lockEndTime)
//                        || operationVO.getStartTime().before(planningHorizonVO.getPlanStartTime())) {
//                    failCount++;
//                    totalErrMsg.append("制造工单号：").append(orderNo).append("计划开始时间不在计划锁定期内");
//                    totalErrMsg.append(BR_TAG);
//                    log.error("制造工单号：{}，计划结束时间不在计划锁定期", orderNo);
//                    continue;
//            }
//            Map<String, Object> createHeadMap = new HashMap<>();
//            Map<String, Object> createPayLoad = MapUtil.newHashMap();
//            List<Map<String, Object>> lineList = new ArrayList<>();
//            List<Map<String, Object>> createParamList = Lists.newArrayList();
//
//            String productCode = workOrderPublishedVO.getProductCode();
//            String inventoryItemId = workOrderPublishedVO.getInventoryItemId();
//            String orderTypeCode = workOrderPublishedVO.getOrderType();
//            String testOrderNumber = workOrderPublishedVO.getTestOrderNumber();
//            Date startTimeDate = DateUtils.formatDate(workOrderPublishedVO.getStartTime(), "yyyy-MM-dd");
//            Date currentDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
//            if (startTimeDate.before(currentDate)) {
//                startTimeDate = currentDate;
//            }
//            if (validOrderTypes.contains(orderTypeCode)) {
//                if(trialMap.containsKey(productCode)){
//                    NewProductTrialSubmissionDetailVO newProductTrialSubmissionDetailVO = trialMap.get(productCode);
//                    OemVO oemVO = trialOemMap.get(newProductTrialSubmissionDetailVO.getCustomerCode());
//                    createHeadMap.put("custNumber", newProductTrialSubmissionDetailVO.getCustomerCode());
//                    createHeadMap.put("customerId", oemVO.getErpCustomerId());
//                    createHeadMap.put("siteUseId", oemVO.getErpShipToSiteUseId());
//                    createHeadMap.put("siteNumber", oemVO.getErpSiteCode());
//                    createHeadMap.put("orderCode", orderTypeCode);
//                }else {
//                    failCount++;
//                    log.error("制造订单号：{}新品试制信息为空", orderNo);
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("新品试制信息为空");
//                    totalErrMsg.append(BR_TAG);
//                    continue;
//                }
//                createHeadMap.put("orderCode", orderTypeCode);
//
//            }else{
//                if (!productOemMap.containsKey(productCode)) {
//                    failCount++;
//                    log.error("发货记录表找不到该物料信息:{}", productCode);
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("发货记录表找不到该物料信息");
//                    totalErrMsg.append(BR_TAG);
//                    continue;
//                }
//                String oemCode = productOemMap.get(productCode);
//                if (!oemMap.containsKey(oemCode)) {
//                    failCount++;
//                    log.error("主机厂信息表找不到该主机厂信息:{}为空", oemCode);
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("主机厂信息表找不到该主机厂信息");
//                    totalErrMsg.append(BR_TAG);
//                    continue;
//                }
//                OemVO oemVO = oemMap.get(oemCode);
//                if (Objects.isNull(oemVO.getErpShipToSiteUseId())) {
//                    failCount++;
//                    log.error("制造订单号：{}，{}主机厂地点信息为空", orderNo, oemVO.getOemCode());
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("主机厂地点信息为空");
//                    totalErrMsg.append(BR_TAG);
//                    continue;
//                }
//                if (Objects.isNull(oemVO.getErpCustomerAddressId())) {
//                    failCount++;
//                    log.error("制造订单号：{}，{}客户地址信息为空", orderNo, oemVO.getOemCode());
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("客户地址信息为空");
//                    totalErrMsg.append(BR_TAG);
//                    continue;
//                }
//                List<DeliveryPlanVO2> deliveryPlanVO2List = productDeliveryListMap.get(productCode);
//                Date finalStartTimeDate = startTimeDate;
//                List<DeliveryPlanVO2> filterDeliveryList = deliveryPlanVO2List.stream().filter(t -> !t.getDemandTime()
//                        .before(finalStartTimeDate)).collect(Collectors.toList());
//
//                if (CollectionUtils.isEmpty(filterDeliveryList)) {
//                    createHeadMap.put("orderCode", "LC");
//                } else {
//                    boolean hasOutputDemand = false;
//                    boolean hasProjectDemand = false;
//
//                    for (DeliveryPlanVO2 plan : filterDeliveryList) {
//                        if (ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(plan.getDemandCategory())) {
//                            hasOutputDemand = true;
//                        } else if (ProductionDemandTypeEnum.PROJECT_DEMAND.getCode().equals(plan.getDemandCategory())) {
//                            hasProjectDemand = true;
//                        }
//                    }
//
//                    if (hasOutputDemand && hasProjectDemand) {
//                        createHeadMap.put("orderCode", "LC");
//                    } else if (hasOutputDemand) {
//                        createHeadMap.put("orderCode", "LC");
//                    } else if (hasProjectDemand) {
//                        createHeadMap.put("orderCode", "SP");
//                    } else {
//                        createHeadMap.put("orderCode", "LC");
//                    }
//                }
//
//                createHeadMap.put("custNumber", oemVO.getErpCustomerCode());
//                createHeadMap.put("customerId", oemVO.getErpCustomerId());
//                createHeadMap.put("siteUseId", oemVO.getErpShipToSiteUseId());
//                createHeadMap.put("siteNumber", oemVO.getErpSiteCode());
//            }
//
//
//
//
//            createHeadMap.put("reqSource", "BPIM");
//            createHeadMap.put("reqOrgId", saleOrganizeVO.getOrganizeId());
//            createHeadMap.put("reqInvOrgId", saleOrganizeVO.getOrganizeId());
//            createHeadMap.put("orgName", saleOrganizeVO.getStockPointName());
//            createHeadMap.put("effectiveFlag", "Y");
//
//            createHeadMap.put("overTolerance", 0.00001);
//            createHeadMap.put("underTolerance", 0.00001);
//            createHeadMap.put("deductFlag", "Y");
//
//            if (StringUtils.isNotEmpty(testOrderNumber)) {
//                createHeadMap.put("testOrderNumber", testOrderNumber);
//            }
//            createHeadMap.put("changeFlag", "N");
//            createHeadMap.put("attribute1", SystemHolder.getUserName());
//            createPayLoad.put("workOrderNumber", orderNo);
//            log.info("创建头层参数：工单号{}", orderNo);
//            Map<String, Object> lineMap = new HashMap<>();
//            lineMap.put("lineNum", 1);//计划单行号
//            lineMap.put("status", "15");//计划单行状态
//            lineMap.put("inventoryItemId", inventoryItemId);//ERP物料ID
//            lineMap.put("itemNumber", productCode);//物料编码
//            lineMap.put("orderQuantity", "0");//订单数量
//            lineMap.put("holdInvQuantity", "0");//保留数量
//            lineMap.put("reqQuantity", workOrderPublishedVO.getQuantity());//请求数量
//            lineMap.put("deliveryQuantity", "0");//发运数量
//            lineMap.put("defaultReqDate", DateUtils.dateToString(workOrderPublishedVO.getStartTime()));//请求日期
//            lineMap.put("actualReqDate", DateUtils.dateToString(startTimeDate));//计划日期
//            lineMap.put("effectiveFlag", "Y");//有效标志
//            lineMap.put("productDate", DateUtils.dateToString(workOrderPublishedVO.getStartTime()));//生产日期
//            lineMap.put("productQuantity", workOrderPublishedVO.getQuantity());//生产数量
//            lineMap.put("underTolerance", 0.00001);//计划单行允差上限%
//            lineMap.put("overTolerance", 0.00001);//计划单行允差下限%
//            lineMap.put("mixFlag", "N");//混箱标识
//            lineList.add(lineMap);
//            createHeadMap.put("lineList", lineList);
//            createParamList.add(createHeadMap);
//            createPayLoad.put("payLoad", createParamList);
//            BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.ERP.getCode(),
//                    ApiCategoryEnum.PLAN_ORDER_CREATE.getCode(), createPayLoad);
//            if (Boolean.TRUE.equals(baseResponse.getSuccess())) {
//                ErpResponse erpResponse = JSON.parseObject(baseResponse.getData(), ErpResponse.class);
//                if (Boolean.TRUE.equals(erpResponse.getSuccess())) {
//                    successCount++;
//                    List<ErpPlanOrderCreate> erpPlanOrderCreates = JSON.parseArray(String.valueOf(erpResponse.getData()), ErpPlanOrderCreate.class);
//                    log.info("计划单创建成功，返回数据:{}", erpResponse.getData());
//
//                    MasterPlanRelationDTO masterPlanRelationDTO = new MasterPlanRelationDTO();
//                    masterPlanRelationDTO.setOrderNo(orderNo);
//                    masterPlanRelationDTO.setPlanNo(erpPlanOrderCreates.get(0).getReqNumber());
//                    masterPlanRelationDTO.setLineNo(String.valueOf(erpPlanOrderCreates.get(0).getLineList().get(0).getLineNum()));
//                    masterPlanRelationDTO.setPlanStatus(erpPlanOrderCreates.get(0).getLineList().get(0).getStatus());
//                    masterPlanRelationDTO.setOrgId(erpPlanOrderCreates.get(0).getReqOrgId());
//                    masterPlanRelationDTO.setProductCode(workOrderPublishedVO.getProductCode());
//                    masterPlanRelationDTO.setStockPointCode(workOrderPublishedVO.getStockPointCode());
//                    insertMasterPlanRelationDTOS.add(masterPlanRelationDTO);
//                    WorkOrderVO workOrderVO = workOrderVOMap.get(workOrderPublishedVO.getId());
//                    WorkOrderDTO workOrderDTO  = WorkOrderConvertorImpl.INSTANCE.vo2Dto(workOrderVO);
//                    workOrderDTO.setPlanStatus(PlanStatusEnum.HAS_ISSUED.getCode());
//                    updateWorkOrderDtoList.add(workOrderDTO);
//                } else {
//                    failCount++;
//                    totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("计划单创建接口返回失败，错误信息:").append(erpResponse.getMessage());
//                    totalErrMsg.append(BR_TAG);
//                    log.error("计划单创建接口失败,制造单号:{}报错，{}", orderNo, erpResponse.getMessage());
//                }
//            } else {
//                failCount++;
//                totalErrMsg.append(WORK_ORDER_NO_MSG).append(orderNo).append("调用ERP接口失败");
//                totalErrMsg.append(BR_TAG);
//            }
//        }
//
//        if (org.apache.commons.lang3.StringUtils.isBlank(totalErrMsg)) {
//            masterPlanPublishedService.doDeleteAll();
//            this.calculateData();
//        }
//        if (CollectionUtils.isNotEmpty(insertMasterPlanRelationDTOS)) {
//            masterPlanRelationService.doCreateBatch(insertMasterPlanRelationDTOS);
//            log.info("计划单关联表新增数量：{}", insertMasterPlanRelationDTOS.size());
//        }
//        if (CollectionUtils.isNotEmpty(updateWorkOrderDtoList)) {
//            workOrderService.doUpdateBatch(updateWorkOrderDtoList);
//            log.info("制造单表更新数量：{}", updateWorkOrderDtoList.size());
//        }

//        CompletableFuture.runAsync(() -> {
//            try {
//                mrpFeign.noGlassRecalculateMrp(scenario);
//            } catch (Exception e) {
//                log.error("MRP材料推移失败", e);
//            }
//        });

//        CompletableFuture.runAsync(() -> {
//            try {
//                mrpFeign.glassRecalculateMrp(scenario, GlassRefreshMrpEnum.DEMAND_UPDATE.getCode());
//            } catch (Exception e) {
//                log.error("原片材料推移失败", e);
//            }
//        });

//        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
//            @Override
//            public void afterCommit() {
//                //需要提交之后执行的代码
//                log.info("MRP材料推移");
//            }
//        });

//        if (org.apache.commons.lang3.StringUtils.isBlank(totalErrMsg.toString())) {
//            return BaseResponse.success(String.format("计划发布成功，共处理%d条工单", successCount));
//        } else {
//            if(successCount==0){
//                return BaseResponse.error(String.format("计划发布失败：共处理%d条工单，成功%d条，失败%d条。失败明细：%s",
//                        (successCount + failCount), successCount, failCount, totalErrMsg));
//            }
//            return BaseResponse.error(String.format("计划发布部分成功：共处理%d条工单，成功%d条，失败%d条。失败明细：%s",
//                    (successCount + failCount), successCount, failCount, totalErrMsg));
//        }
        return BaseResponse.success();
    }

    public void calculateData() {
//        List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOS = new ArrayList<>();
//        List<MasterPlanPublishedVO> masterPlanPublishedVOS = new ArrayList<>();
//        masterPlanService.masterPlanWorkOrder(new Pagination(), new MasterPlanReq(), Boolean.TRUE, masterPlanWorkOrderBodyVOS);
//        for (MasterPlanWorkOrderBodyVO masterPlanWorkOrderBodyVO : masterPlanWorkOrderBodyVOS) {
//            List<Map<String, Object>> dynamicData = masterPlanWorkOrderBodyVO.getDynamicData();
//            List<String> header = masterPlanWorkOrderBodyVO.getHeader();
//            String dynamicDataJsonStr = JSONUtil.toJsonStr(dynamicData);
//            String headerJsonStr = JSONUtil.toJsonStr(header);
//            MasterPlanPublishedVO masterPlanPublishedVO = new MasterPlanPublishedVO();
//            BeanUtils.copyProperties(masterPlanWorkOrderBodyVO, masterPlanPublishedVO);
//            masterPlanPublishedVO.setDynamicData(dynamicDataJsonStr);
//            masterPlanPublishedVO.setHeader(headerJsonStr);
//            masterPlanPublishedVOS.add(masterPlanPublishedVO);
//        }
//        List<MasterPlanPublishedDTO> masterPlanPublishedDTOS = new ArrayList<>();
//        for (MasterPlanPublishedVO vo : masterPlanPublishedVOS) {
//            MasterPlanPublishedDTO dto = new MasterPlanPublishedDTO();
//            BeanUtils.copyProperties(vo, dto);
//            masterPlanPublishedDTOS.add(dto);
//        }
//        if (CollectionUtils.isNotEmpty(masterPlanPublishedDTOS)){
//            masterPlanPublishedService.doCreateBatch(masterPlanPublishedDTOS);
//        }
    }

    public Map<String, Date> getLockEndTimeMap(List<WorkOrderPublishedVO> rootWorkList,
                                               List<OperationVO> operationVOList,
                                               List<PhysicalResourceVO> physicalResourceVOS,
                                               PlanningHorizonVO planningHorizonVO) {
        Map<String, Date> lockEndTimeMap = new HashMap<>();
        Map<String, PhysicalResourceVO> resourceVOMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(BaseVO::getId, Function.identity()));
        Map<String, OperationVO> operationVOMap = operationVOList.stream()
                .collect(Collectors.toMap(OperationBasicVO::getOrderId, Function.identity()));
        Date planStartTime = planningHorizonVO.getPlanStartTime();
        Date planLockEndTime = planningHorizonVO.getPlanLockEndTime();
        for (WorkOrderPublishedVO workOrderPublishedVO : rootWorkList) {
            String id = workOrderPublishedVO.getId();
            if (operationVOMap.containsKey(id)
                    && StringUtils.isNotEmpty(operationVOMap.get(id).getPlannedResourceId())) {
                PhysicalResourceVO physicalResourceVO = resourceVOMap.get(operationVOMap.get(id).getPlannedResourceId());
                if (physicalResourceVO != null && physicalResourceVO.getNoBufferActionDuration() != null) {
                    Integer noBufferActionDuration = physicalResourceVO.getNoBufferActionDuration();
                    Date moveDay = DateUtils.moveDay(planStartTime, noBufferActionDuration);
                    lockEndTimeMap.put(id, moveDay);
                    continue;
                }
            }
            lockEndTimeMap.put(id, planLockEndTime);
        }

        return lockEndTimeMap;
    }


}
