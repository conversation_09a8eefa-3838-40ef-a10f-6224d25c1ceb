<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.operationPublished.infrastructure.dao.OperationPublishedDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
        <!--@Table sds_ord_operation_published-->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="plan_unit_id" jdbcType="VARCHAR" property="planUnitId"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
        <result column="routing_step_sequence_no" jdbcType="INTEGER" property="routingStepSequenceNo"/>
        <result column="pre_routing_step_sequence_no" jdbcType="VARCHAR" property="preRoutingStepSequenceNo"/>
        <result column="next_routing_step_sequence_no" jdbcType="VARCHAR" property="nextRoutingStepSequenceNo"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="planned_resource_id" jdbcType="VARCHAR" property="plannedResourceId"/>
        <result column="frozen" jdbcType="VARCHAR" property="frozen"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="kit_status" jdbcType="VARCHAR" property="kitStatus"/>
        <result column="processing_time" jdbcType="INTEGER" property="processingTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="earliest_start_time" jdbcType="TIMESTAMP" property="earliestStartTime"/>
        <result column="latest_end_time" jdbcType="TIMESTAMP" property="latestEndTime"/>
        <result column="calc_earliest_start_time" jdbcType="TIMESTAMP" property="calcEarliestStartTime"/>
        <result column="calc_latest_end_time" jdbcType="TIMESTAMP" property="calcLatestEndTime"/>
        <result column="connection_task" jdbcType="VARCHAR" property="connectionTask"/>
        <result column="connection_type" jdbcType="VARCHAR" property="connectionType"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="partition_type" jdbcType="VARCHAR" property="partitionType"/>
        <result column="resource_fixed" jdbcType="VARCHAR" property="resourceFixed"/>
        <result column="time_fixed" jdbcType="VARCHAR" property="timeFixed"/>
        <result column="lock_status" jdbcType="VARCHAR" property="lockStatus"/>
        <result column="delay_reason" jdbcType="VARCHAR" property="delayReason"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="operation_index" jdbcType="INTEGER" property="operationIndex"/>
        <result column="unscheduled_reason" jdbcType="VARCHAR" property="unscheduledReason"/>
        <result column="last_insertion" jdbcType="VARCHAR" property="lastInsertion"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
     	<result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.vo.OperationPublishedVO">
        <!-- TODO -->
        <id column="new_id" jdbcType="VARCHAR" property="newId"/>
        <result column="operation_code" jdbcType="VARCHAR" property="operationCode"/>
        <result column="order_id" jdbcType="VARCHAR" property="orderId"/>
        <result column="order_no" jdbcType="VARCHAR" property="orderNo"/>
        <result column="order_type_code" jdbcType="VARCHAR" property="orderTypeCode"/>
        <result column="due_date" jdbcType="VARCHAR" property="dueDate"/>
        <result column="plan_unit_id" jdbcType="VARCHAR" property="planUnitId"/>
        <result column="routing_step_id" jdbcType="VARCHAR" property="routingStepId"/>
        <result column="routing_step_sequence_no" jdbcType="INTEGER" property="routingStepSequenceNo"/>
        <result column="pre_routing_step_sequence_no" jdbcType="VARCHAR" property="preRoutingStepSequenceNo"/>
        <result column="next_routing_step_sequence_no" jdbcType="VARCHAR" property="nextRoutingStepSequenceNo"/>
        <result column="product_stock_point_id" jdbcType="VARCHAR" property="productStockPointId"/>
        <result column="product_id" jdbcType="VARCHAR" property="productId"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="inventory_item_id" jdbcType="VARCHAR" property="inventoryItemId"/>
        <result column="stock_point_id" jdbcType="VARCHAR" property="stockPointId"/>
        <result column="quantity" jdbcType="VARCHAR" property="quantity"/>
        <result column="planned_resource_id" jdbcType="VARCHAR" property="plannedResourceId"/>
        <result column="frozen" jdbcType="VARCHAR" property="frozen"/>
        <result column="plan_status" jdbcType="VARCHAR" property="planStatus"/>
        <result column="order_type" jdbcType="VARCHAR" property="orderType"/>
        <result column="kit_status" jdbcType="VARCHAR" property="kitStatus"/>
        <result column="processing_time" jdbcType="INTEGER" property="processingTime"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="earliest_start_time" jdbcType="TIMESTAMP" property="earliestStartTime"/>
        <result column="latest_end_time" jdbcType="TIMESTAMP" property="latestEndTime"/>
        <result column="calc_earliest_start_time" jdbcType="TIMESTAMP" property="calcEarliestStartTime"/>
        <result column="calc_latest_end_time" jdbcType="TIMESTAMP" property="calcLatestEndTime"/>
        <result column="connection_task" jdbcType="VARCHAR" property="connectionTask"/>
        <result column="connection_type" jdbcType="VARCHAR" property="connectionType"/>
        <result column="max_connection_duration" jdbcType="INTEGER" property="maxConnectionDuration"/>
        <result column="min_connection_duration" jdbcType="INTEGER" property="minConnectionDuration"/>
        <result column="partition_type" jdbcType="VARCHAR" property="partitionType"/>
        <result column="resource_fixed" jdbcType="VARCHAR" property="resourceFixed"/>
        <result column="time_fixed" jdbcType="VARCHAR" property="timeFixed"/>
        <result column="lock_status" jdbcType="VARCHAR" property="lockStatus"/>
        <result column="delay_reason" jdbcType="VARCHAR" property="delayReason"/>
        <result column="parent_id" jdbcType="VARCHAR" property="parentId"/>
        <result column="operation_index" jdbcType="INTEGER" property="operationIndex"/>
        <result column="unscheduled_reason" jdbcType="VARCHAR" property="unscheduledReason"/>
        <result column="last_insertion" jdbcType="VARCHAR" property="lastInsertion"/>
        <result column="standard_step_id" jdbcType="VARCHAR" property="standardStepId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="standard_step_type" jdbcType="VARCHAR" property="standardStepType"/>
        <result column="published_log_id" jdbcType="VARCHAR" property="publishedLogId"/>
        <result column="id" jdbcType="VARCHAR" property="id"/>
    </resultMap>
    
    <resultMap id="POResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
    </resultMap>
    
    <sql id="Base_Column_List">
        new_id,operation_code,order_id,plan_unit_id,routing_step_id,routing_step_sequence_no,pre_routing_step_sequence_no,next_routing_step_sequence_no,product_stock_point_id,product_id,stock_point_id,quantity,planned_resource_id,frozen,plan_status,order_type,kit_status,processing_time,start_time,end_time,earliest_start_time,latest_end_time,calc_earliest_start_time,calc_latest_end_time,connection_task,connection_type,max_connection_duration,min_connection_duration,partition_type,resource_fixed,time_fixed,lock_status,delay_reason,parent_id,operation_index,unscheduled_reason,last_insertion,standard_step_id,remark,enabled,creator,create_time,modifier,modify_time,id,published_log_id
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,order_no,order_type_code,product_Code,due_date,inventory_item_id,standard_step_type
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.newId != null and params.newId != ''">
                and new_id = #{params.newId,jdbcType=VARCHAR}
            </if>
            <if test="params.operationCode != null and params.operationCode != ''">
                and operation_code = #{params.operationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.orderId != null and params.orderId != ''">
                and order_id = #{params.orderId,jdbcType=VARCHAR}
            </if>
            <if test="params.orderIdList != null and params.orderIdList.size() > 0">
                and order_id in
                <foreach collection="params.orderIdList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.planUnitId != null and params.planUnitId != ''">
                and plan_unit_id = #{params.planUnitId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepId != null and params.routingStepId != ''">
                and routing_step_id = #{params.routingStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.routingStepSequenceNo != null">
                and routing_step_sequence_no = #{params.routingStepSequenceNo,jdbcType=INTEGER}
            </if>
            <if test="params.preRoutingStepSequenceNo != null and params.preRoutingStepSequenceNo != ''">
                and pre_routing_step_sequence_no = #{params.preRoutingStepSequenceNo,jdbcType=VARCHAR}
            </if>
            <if test="params.nextRoutingStepSequenceNo != null and params.nextRoutingStepSequenceNo != ''">
                and next_routing_step_sequence_no = #{params.nextRoutingStepSequenceNo,jdbcType=VARCHAR}
            </if>
            <if test="params.productStockPointId != null and params.productStockPointId != ''">
                and product_stock_point_id = #{params.productStockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.productId != null and params.productId != ''">
                and product_id = #{params.productId,jdbcType=VARCHAR}
            </if>
            <if test="params.stockPointId != null and params.stockPointId != ''">
                and stock_point_id = #{params.stockPointId,jdbcType=VARCHAR}
            </if>
            <if test="params.quantity != null">
                and quantity = #{params.quantity,jdbcType=VARCHAR}
            </if>
            <if test="params.plannedResourceId != null and params.plannedResourceId != ''">
                and planned_resource_id = #{params.plannedResourceId,jdbcType=VARCHAR}
            </if>
            <if test="params.frozen != null and params.frozen != ''">
                and frozen = #{params.frozen,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatus != null and params.planStatus != ''">
                and plan_status = #{params.planStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.planStatusList != null and params.planStatusList.size() > 0">
                and plan_status in
                <foreach collection="params.planStatusList" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.orderType != null and params.orderType != ''">
                and order_type = #{params.orderType,jdbcType=VARCHAR}
            </if>
            <if test="params.kitStatus != null and params.kitStatus != ''">
                and kit_status = #{params.kitStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.processingTime != null">
                and processing_time = #{params.processingTime,jdbcType=INTEGER}
            </if>
            <if test="params.startTimeNotNull != null and params.startTimeNotNull != ''">
                and start_time is not null
            </if>
            <if test="params.endTime != null">
                and end_time = #{params.endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.earliestStartTime != null">
                and earliest_start_time = #{params.earliestStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.latestEndTime != null">
                and latest_end_time = #{params.latestEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.calcEarliestStartTime != null">
                and calc_earliest_start_time = #{params.calcEarliestStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.calcLatestEndTime != null">
                and calc_latest_end_time = #{params.calcLatestEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.connectionTask != null and params.connectionTask != ''">
                and connection_task = #{params.connectionTask,jdbcType=VARCHAR}
            </if>
            <if test="params.connectionType != null and params.connectionType != ''">
                and connection_type = #{params.connectionType,jdbcType=VARCHAR}
            </if>
            <if test="params.maxConnectionDuration != null">
                and max_connection_duration = #{params.maxConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.minConnectionDuration != null">
                and min_connection_duration = #{params.minConnectionDuration,jdbcType=INTEGER}
            </if>
            <if test="params.partitionType != null and params.partitionType != ''">
                and partition_type = #{params.partitionType,jdbcType=VARCHAR}
            </if>
            <if test="params.resourceFixed != null and params.resourceFixed != ''">
                and resource_fixed = #{params.resourceFixed,jdbcType=VARCHAR}
            </if>
            <if test="params.timeFixed != null and params.timeFixed != ''">
                and time_fixed = #{params.timeFixed,jdbcType=VARCHAR}
            </if>
            <if test="params.lockStatus != null and params.lockStatus != ''">
                and lock_status = #{params.lockStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.delayReason != null and params.delayReason != ''">
                and delay_reason = #{params.delayReason,jdbcType=VARCHAR}
            </if>
            <if test="params.parentId != null and params.parentId != ''">
                and parent_id = #{params.parentId,jdbcType=VARCHAR}
            </if>
            <if test="params.parentIdNotNull != null and params.parentIdNotNull != ''">
                and parent_id is not null
            </if>
            <if test="params.operationIndex != null">
                and operation_index = #{params.operationIndex,jdbcType=INTEGER}
            </if>
            <if test="params.unscheduledReason != null and params.unscheduledReason != ''">
                and unscheduled_reason = #{params.unscheduledReason,jdbcType=VARCHAR}
            </if>
            <if test="params.lastInsertion != null and params.lastInsertion != ''">
                and last_insertion = #{params.lastInsertion,jdbcType=VARCHAR}
            </if>
            <if test="params.standardStepId != null and params.standardStepId != ''">
                and standard_step_id = #{params.standardStepId,jdbcType=VARCHAR}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.standardStepType != null and params.standardStepType != ''">
                and standard_step_type = #{params.standardStepType,jdbcType=VARCHAR}
            </if>
            <if test="params.publishedLogId != null and params.publishedLogId != ''">
                and published_log_id = #{params.publishedLogId,jdbcType=VARCHAR}
            </if>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_published
        where new_id = #{newId,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_published
        where new_id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from sds_ord_operation_published
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from sds_ord_operation_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_sds_ord_operation_published
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into sds_ord_operation_published(
        new_id,
        operation_code,
        order_id,
        plan_unit_id,
        routing_step_id,
        routing_step_sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        quantity,
        planned_resource_id,
        frozen,
        plan_status,
        order_type,
        kit_status,
        processing_time,
        start_time,
        end_time,
        earliest_start_time,
        latest_end_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        partition_type,
        resource_fixed,
        time_fixed,
        lock_status,
        delay_reason,
        parent_id,
        operation_index,
        unscheduled_reason,
        last_insertion,
        standard_step_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{orderId,jdbcType=VARCHAR},
        #{planUnitId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{routingStepSequenceNo,jdbcType=INTEGER},
        #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{plannedResourceId,jdbcType=VARCHAR},
        #{frozen,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{kitStatus,jdbcType=VARCHAR},
        #{processingTime,jdbcType=INTEGER},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{earliestStartTime,jdbcType=TIMESTAMP},
        #{latestEndTime,jdbcType=TIMESTAMP},
        #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{calcLatestEndTime,jdbcType=TIMESTAMP},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{partitionType,jdbcType=VARCHAR},
        #{resourceFixed,jdbcType=VARCHAR},
        #{timeFixed,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{delayReason,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{operationIndex,jdbcType=INTEGER},
        #{unscheduledReason,jdbcType=VARCHAR},
        #{lastInsertion,jdbcType=VARCHAR},
        #{standardStepId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
        insert into sds_ord_operation_published(
        new_id,
        operation_code,
        order_id,
        plan_unit_id,
        routing_step_id,
        routing_step_sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        quantity,
        planned_resource_id,
        frozen,
        plan_status,
        order_type,
        kit_status,
        processing_time,
        start_time,
        end_time,
        earliest_start_time,
        latest_end_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        partition_type,
        resource_fixed,
        time_fixed,
        lock_status,
        delay_reason,
        parent_id,
        operation_index,
        unscheduled_reason,
        last_insertion,
        standard_step_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values (
        #{newId,jdbcType=VARCHAR},
        #{operationCode,jdbcType=VARCHAR},
        #{orderId,jdbcType=VARCHAR},
        #{planUnitId,jdbcType=VARCHAR},
        #{routingStepId,jdbcType=VARCHAR},
        #{routingStepSequenceNo,jdbcType=INTEGER},
        #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{productStockPointId,jdbcType=VARCHAR},
        #{productId,jdbcType=VARCHAR},
        #{stockPointId,jdbcType=VARCHAR},
        #{quantity,jdbcType=VARCHAR},
        #{plannedResourceId,jdbcType=VARCHAR},
        #{frozen,jdbcType=VARCHAR},
        #{planStatus,jdbcType=VARCHAR},
        #{orderType,jdbcType=VARCHAR},
        #{kitStatus,jdbcType=VARCHAR},
        #{processingTime,jdbcType=INTEGER},
        #{startTime,jdbcType=TIMESTAMP},
        #{endTime,jdbcType=TIMESTAMP},
        #{earliestStartTime,jdbcType=TIMESTAMP},
        #{latestEndTime,jdbcType=TIMESTAMP},
        #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{calcLatestEndTime,jdbcType=TIMESTAMP},
        #{connectionTask,jdbcType=VARCHAR},
        #{connectionType,jdbcType=VARCHAR},
        #{maxConnectionDuration,jdbcType=INTEGER},
        #{minConnectionDuration,jdbcType=INTEGER},
        #{partitionType,jdbcType=VARCHAR},
        #{resourceFixed,jdbcType=VARCHAR},
        #{timeFixed,jdbcType=VARCHAR},
        #{lockStatus,jdbcType=VARCHAR},
        #{delayReason,jdbcType=VARCHAR},
        #{parentId,jdbcType=VARCHAR},
        #{operationIndex,jdbcType=INTEGER},
        #{unscheduledReason,jdbcType=VARCHAR},
        #{lastInsertion,jdbcType=VARCHAR},
        #{standardStepId,jdbcType=VARCHAR},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
     	#{id,jdbcType=VARCHAR},
        #{publishedLogId,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into sds_ord_operation_published(
        new_id,
        operation_code,
        order_id,
        plan_unit_id,
        routing_step_id,
        routing_step_sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        quantity,
        planned_resource_id,
        frozen,
        plan_status,
        order_type,
        kit_status,
        processing_time,
        start_time,
        end_time,
        earliest_start_time,
        latest_end_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        partition_type,
        resource_fixed,
        time_fixed,
        lock_status,
        delay_reason,
        parent_id,
        operation_index,
        unscheduled_reason,
        last_insertion,
        standard_step_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.orderId,jdbcType=VARCHAR},
        #{entity.planUnitId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.routingStepSequenceNo,jdbcType=INTEGER},
        #{entity.preRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{entity.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.quantity,jdbcType=VARCHAR},
        #{entity.plannedResourceId,jdbcType=VARCHAR},
        #{entity.frozen,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.kitStatus,jdbcType=VARCHAR},
        #{entity.processingTime,jdbcType=INTEGER},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.earliestStartTime,jdbcType=TIMESTAMP},
        #{entity.latestEndTime,jdbcType=TIMESTAMP},
        #{entity.calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcLatestEndTime,jdbcType=TIMESTAMP},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.partitionType,jdbcType=VARCHAR},
        #{entity.resourceFixed,jdbcType=VARCHAR},
        #{entity.timeFixed,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.delayReason,jdbcType=VARCHAR},
        #{entity.parentId,jdbcType=VARCHAR},
        #{entity.operationIndex,jdbcType=INTEGER},
        #{entity.unscheduledReason,jdbcType=VARCHAR},
        #{entity.lastInsertion,jdbcType=VARCHAR},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into sds_ord_operation_published(
        new_id,
        operation_code,
        order_id,
        plan_unit_id,
        routing_step_id,
        routing_step_sequence_no,
        pre_routing_step_sequence_no,
        next_routing_step_sequence_no,
        product_stock_point_id,
        product_id,
        stock_point_id,
        quantity,
        planned_resource_id,
        frozen,
        plan_status,
        order_type,
        kit_status,
        processing_time,
        start_time,
        end_time,
        earliest_start_time,
        latest_end_time,
        calc_earliest_start_time,
        calc_latest_end_time,
        connection_task,
        connection_type,
        max_connection_duration,
        min_connection_duration,
        partition_type,
        resource_fixed,
        time_fixed,
        lock_status,
        delay_reason,
        parent_id,
        operation_index,
        unscheduled_reason,
        last_insertion,
        standard_step_id,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        id,
        published_log_id)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.newId,jdbcType=VARCHAR},
        #{entity.operationCode,jdbcType=VARCHAR},
        #{entity.orderId,jdbcType=VARCHAR},
        #{entity.planUnitId,jdbcType=VARCHAR},
        #{entity.routingStepId,jdbcType=VARCHAR},
        #{entity.routingStepSequenceNo,jdbcType=INTEGER},
        #{entity.preRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{entity.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        #{entity.productStockPointId,jdbcType=VARCHAR},
        #{entity.productId,jdbcType=VARCHAR},
        #{entity.stockPointId,jdbcType=VARCHAR},
        #{entity.quantity,jdbcType=VARCHAR},
        #{entity.plannedResourceId,jdbcType=VARCHAR},
        #{entity.frozen,jdbcType=VARCHAR},
        #{entity.planStatus,jdbcType=VARCHAR},
        #{entity.orderType,jdbcType=VARCHAR},
        #{entity.kitStatus,jdbcType=VARCHAR},
        #{entity.processingTime,jdbcType=INTEGER},
        #{entity.startTime,jdbcType=TIMESTAMP},
        #{entity.endTime,jdbcType=TIMESTAMP},
        #{entity.earliestStartTime,jdbcType=TIMESTAMP},
        #{entity.latestEndTime,jdbcType=TIMESTAMP},
        #{entity.calcEarliestStartTime,jdbcType=TIMESTAMP},
        #{entity.calcLatestEndTime,jdbcType=TIMESTAMP},
        #{entity.connectionTask,jdbcType=VARCHAR},
        #{entity.connectionType,jdbcType=VARCHAR},
        #{entity.maxConnectionDuration,jdbcType=INTEGER},
        #{entity.minConnectionDuration,jdbcType=INTEGER},
        #{entity.partitionType,jdbcType=VARCHAR},
        #{entity.resourceFixed,jdbcType=VARCHAR},
        #{entity.timeFixed,jdbcType=VARCHAR},
        #{entity.lockStatus,jdbcType=VARCHAR},
        #{entity.delayReason,jdbcType=VARCHAR},
        #{entity.parentId,jdbcType=VARCHAR},
        #{entity.operationIndex,jdbcType=INTEGER},
        #{entity.unscheduledReason,jdbcType=VARCHAR},
        #{entity.lastInsertion,jdbcType=VARCHAR},
        #{entity.standardStepId,jdbcType=VARCHAR},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.id,jdbcType=VARCHAR},
        #{entity.publishedLogId,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
        update sds_ord_operation_published set
        operation_code = #{operationCode,jdbcType=VARCHAR},
        order_id = #{orderId,jdbcType=VARCHAR},
        plan_unit_id = #{planUnitId,jdbcType=VARCHAR},
        routing_step_id = #{routingStepId,jdbcType=VARCHAR},
        routing_step_sequence_no = #{routingStepSequenceNo,jdbcType=INTEGER},
        pre_routing_step_sequence_no = #{preRoutingStepSequenceNo,jdbcType=VARCHAR},
        next_routing_step_sequence_no = #{nextRoutingStepSequenceNo,jdbcType=VARCHAR},
        product_stock_point_id = #{productStockPointId,jdbcType=VARCHAR},
        product_id = #{productId,jdbcType=VARCHAR},
        stock_point_id = #{stockPointId,jdbcType=VARCHAR},
        quantity = #{quantity,jdbcType=VARCHAR},
        planned_resource_id = #{plannedResourceId,jdbcType=VARCHAR},
        frozen = #{frozen,jdbcType=VARCHAR},
        plan_status = #{planStatus,jdbcType=VARCHAR},
        order_type = #{orderType,jdbcType=VARCHAR},
        kit_status = #{kitStatus,jdbcType=VARCHAR},
        processing_time = #{processingTime,jdbcType=INTEGER},
        start_time = #{startTime,jdbcType=TIMESTAMP},
        end_time = #{endTime,jdbcType=TIMESTAMP},
        earliest_start_time = #{earliestStartTime,jdbcType=TIMESTAMP},
        latest_end_time = #{latestEndTime,jdbcType=TIMESTAMP},
        calc_earliest_start_time = #{calcEarliestStartTime,jdbcType=TIMESTAMP},
        calc_latest_end_time = #{calcLatestEndTime,jdbcType=TIMESTAMP},
        connection_task = #{connectionTask,jdbcType=VARCHAR},
        connection_type = #{connectionType,jdbcType=VARCHAR},
        max_connection_duration = #{maxConnectionDuration,jdbcType=INTEGER},
        min_connection_duration = #{minConnectionDuration,jdbcType=INTEGER},
        partition_type = #{partitionType,jdbcType=VARCHAR},
        resource_fixed = #{resourceFixed,jdbcType=VARCHAR},
        time_fixed = #{timeFixed,jdbcType=VARCHAR},
        lock_status = #{lockStatus,jdbcType=VARCHAR},
        delay_reason = #{delayReason,jdbcType=VARCHAR},
        parent_id = #{parentId,jdbcType=VARCHAR},
        operation_index = #{operationIndex,jdbcType=INTEGER},
        unscheduled_reason = #{unscheduledReason,jdbcType=VARCHAR},
        last_insertion = #{lastInsertion,jdbcType=VARCHAR},
        standard_step_id = #{standardStepId,jdbcType=VARCHAR},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        id = #{id,jdbcType=VARCHAR},
        published_log_id = #{publishedLogId,jdbcType=VARCHAR}
        where new_id = #{newId,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.operationPublished.infrastructure.po.OperationPublishedPO">
        update sds_ord_operation_published
        <set>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.orderId != null and item.orderId != ''">
                order_id = #{item.orderId,jdbcType=VARCHAR},
            </if>
            <if test="item.planUnitId != null and item.planUnitId != ''">
                plan_unit_id = #{item.planUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepSequenceNo != null">
                routing_step_sequence_no = #{item.routingStepSequenceNo,jdbcType=INTEGER},
            </if>
            <if test="item.preRoutingStepSequenceNo != null and item.preRoutingStepSequenceNo != ''">
                pre_routing_step_sequence_no = #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.nextRoutingStepSequenceNo != null and item.nextRoutingStepSequenceNo != ''">
                next_routing_step_sequence_no = #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedResourceId != null and item.plannedResourceId != ''">
                planned_resource_id = #{item.plannedResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.frozen != null and item.frozen != ''">
                frozen = #{item.frozen,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
                order_type = #{item.orderType,jdbcType=VARCHAR},
            </if>
            <if test="item.kitStatus != null and item.kitStatus != ''">
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.processingTime != null">
                processing_time = #{item.processingTime,jdbcType=INTEGER},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.earliestStartTime != null">
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.latestEndTime != null">
                latest_end_time = #{item.latestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcEarliestStartTime != null">
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcLatestEndTime != null">
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.partitionType != null and item.partitionType != ''">
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceFixed != null and item.resourceFixed != ''">
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.timeFixed != null and item.timeFixed != ''">
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.delayReason != null and item.delayReason != ''">
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationIndex != null">
                operation_index = #{item.operationIndex,jdbcType=INTEGER},
            </if>
            <if test="item.unscheduledReason != null and item.unscheduledReason != ''">
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastInsertion != null and item.lastInsertion != ''">
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update sds_ord_operation_published
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="operation_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_unit_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.planUnitId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.routingStepSequenceNo,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="pre_routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="next_routing_step_sequence_no = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productStockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.productId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="stock_point_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.stockPointId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.quantity,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="planned_resource_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.plannedResourceId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="frozen = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.frozen,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="plan_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.planStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="order_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.orderType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="kit_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.kitStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="processing_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.processingTime,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.startTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.endTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.earliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="latest_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.latestEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_earliest_start_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.calcEarliestStartTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="calc_latest_end_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.calcLatestEndTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="connection_task = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.connectionTask,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="connection_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.connectionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="max_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.maxConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="min_connection_duration = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.minConnectionDuration,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="partition_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.partitionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="resource_fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.resourceFixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="time_fixed = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.timeFixed,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="lock_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lockStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="delay_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.delayReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="parent_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.parentId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="operation_index = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.operationIndex,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="unscheduled_reason = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.unscheduledReason,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="last_insertion = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.lastInsertion,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_step_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.standardStepId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.id,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="published_log_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when new_id = #{item.newId,jdbcType=VARCHAR} then #{item.publishedLogId,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where new_id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update sds_ord_operation_published
        <set>
            <if test="item.operationCode != null and item.operationCode != ''">
                operation_code = #{item.operationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.orderId != null and item.orderId != ''">
                order_id = #{item.orderId,jdbcType=VARCHAR},
            </if>
            <if test="item.planUnitId != null and item.planUnitId != ''">
                plan_unit_id = #{item.planUnitId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepId != null and item.routingStepId != ''">
                routing_step_id = #{item.routingStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.routingStepSequenceNo != null">
                routing_step_sequence_no = #{item.routingStepSequenceNo,jdbcType=INTEGER},
            </if>
            <if test="item.preRoutingStepSequenceNo != null and item.preRoutingStepSequenceNo != ''">
                pre_routing_step_sequence_no = #{item.preRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.nextRoutingStepSequenceNo != null and item.nextRoutingStepSequenceNo != ''">
                next_routing_step_sequence_no = #{item.nextRoutingStepSequenceNo,jdbcType=VARCHAR},
            </if>
            <if test="item.productStockPointId != null and item.productStockPointId != ''">
                product_stock_point_id = #{item.productStockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.productId != null and item.productId != ''">
                product_id = #{item.productId,jdbcType=VARCHAR},
            </if>
            <if test="item.stockPointId != null and item.stockPointId != ''">
                stock_point_id = #{item.stockPointId,jdbcType=VARCHAR},
            </if>
            <if test="item.quantity != null">
                quantity = #{item.quantity,jdbcType=VARCHAR},
            </if>
            <if test="item.plannedResourceId != null and item.plannedResourceId != ''">
                planned_resource_id = #{item.plannedResourceId,jdbcType=VARCHAR},
            </if>
            <if test="item.frozen != null and item.frozen != ''">
                frozen = #{item.frozen,jdbcType=VARCHAR},
            </if>
            <if test="item.planStatus != null and item.planStatus != ''">
                plan_status = #{item.planStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.orderType != null and item.orderType != ''">
                order_type = #{item.orderType,jdbcType=VARCHAR},
            </if>
            <if test="item.kitStatus != null and item.kitStatus != ''">
                kit_status = #{item.kitStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.processingTime != null">
                processing_time = #{item.processingTime,jdbcType=INTEGER},
            </if>
            <if test="item.startTime != null">
                start_time = #{item.startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endTime != null">
                end_time = #{item.endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.earliestStartTime != null">
                earliest_start_time = #{item.earliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.latestEndTime != null">
                latest_end_time = #{item.latestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcEarliestStartTime != null">
                calc_earliest_start_time = #{item.calcEarliestStartTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.calcLatestEndTime != null">
                calc_latest_end_time = #{item.calcLatestEndTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.connectionTask != null and item.connectionTask != ''">
                connection_task = #{item.connectionTask,jdbcType=VARCHAR},
            </if>
            <if test="item.connectionType != null and item.connectionType != ''">
                connection_type = #{item.connectionType,jdbcType=VARCHAR},
            </if>
            <if test="item.maxConnectionDuration != null">
                max_connection_duration = #{item.maxConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.minConnectionDuration != null">
                min_connection_duration = #{item.minConnectionDuration,jdbcType=INTEGER},
            </if>
            <if test="item.partitionType != null and item.partitionType != ''">
                partition_type = #{item.partitionType,jdbcType=VARCHAR},
            </if>
            <if test="item.resourceFixed != null and item.resourceFixed != ''">
                resource_fixed = #{item.resourceFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.timeFixed != null and item.timeFixed != ''">
                time_fixed = #{item.timeFixed,jdbcType=VARCHAR},
            </if>
            <if test="item.lockStatus != null and item.lockStatus != ''">
                lock_status = #{item.lockStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.delayReason != null and item.delayReason != ''">
                delay_reason = #{item.delayReason,jdbcType=VARCHAR},
            </if>
            <if test="item.parentId != null and item.parentId != ''">
                parent_id = #{item.parentId,jdbcType=VARCHAR},
            </if>
            <if test="item.operationIndex != null">
                operation_index = #{item.operationIndex,jdbcType=INTEGER},
            </if>
            <if test="item.unscheduledReason != null and item.unscheduledReason != ''">
                unscheduled_reason = #{item.unscheduledReason,jdbcType=VARCHAR},
            </if>
            <if test="item.lastInsertion != null and item.lastInsertion != ''">
                last_insertion = #{item.lastInsertion,jdbcType=VARCHAR},
            </if>
            <if test="item.standardStepId != null and item.standardStepId != ''">
                standard_step_id = #{item.standardStepId,jdbcType=VARCHAR},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.id != null and item.id != ''">
                id = #{item.id,jdbcType=VARCHAR},
            </if>
            <if test="item.publishedLogId != null and item.publishedLogId != ''">
                published_log_id = #{item.publishedLogId,jdbcType=VARCHAR},
            </if>
        </set>
        where new_id = #{item.newId,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from sds_ord_operation_published where new_id = #{newId,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from sds_ord_operation_published where new_id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="truncateTable">
        TRUNCATE TABLE sds_ord_operation_published
    </update>

    <insert id="doSnapshotData">
	 INSERT INTO sds_ord_operation_published (
			new_id,
			id,
			operation_code,
			order_id,
			plan_unit_id,
			routing_step_id,
			routing_step_sequence_no,
			pre_routing_step_sequence_no,
			next_routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			quantity,
			planned_resource_id,
			frozen,
			plan_status,
			order_type,
			kit_status,
			processing_time,
			start_time,
			end_time,
			earliest_start_time,
			latest_end_time,
			calc_earliest_start_time,
			calc_latest_end_time,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			partition_type,
			resource_fixed,
			time_fixed,
			lock_status,
			delay_reason,
			parent_id,
			operation_index,
			unscheduled_reason,
			last_insertion,
			standard_step_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			published_log_id
		)
		SELECT
			CONCAT(id, ${publishedTime}) as new_id,
			id as id,
			operation_code,
			order_id,
			plan_unit_id,
			routing_step_id,
			routing_step_sequence_no,
			pre_routing_step_sequence_no,
			next_routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			quantity,
			planned_resource_id,
			frozen,
			plan_status,
			order_type,
			kit_status,
			processing_time,
			start_time,
			end_time,
			earliest_start_time,
			latest_end_time,
			calc_earliest_start_time,
			calc_latest_end_time,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			partition_type,
			resource_fixed,
			time_fixed,
			lock_status,
			delay_reason,
			parent_id,
			operation_index,
			unscheduled_reason,
			last_insertion,
			standard_step_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time,
			${publishedLogId} AS published_log_id
		FROM
			sds_ord_operation
	</insert>

	<delete id="deleteByPublishedLogId" parameterType="java.lang.String">
        delete from
        	sds_ord_operation_published
        where
        	published_log_id = #{publishedLogId,jdbcType=VARCHAR}
    </delete>

    <select id="selectAllCount" resultType="java.lang.Integer">
        select
        	count(*)
        from sds_ord_operation
    </select>
    
    <select id="selectOperationByOrderIds" resultMap="POResultMap">
        SELECT
			id as id,
			operation_code,
			order_id,
			plan_unit_id,
			routing_step_id,
			routing_step_sequence_no,
			pre_routing_step_sequence_no,
			next_routing_step_sequence_no,
			product_stock_point_id,
			product_id,
			stock_point_id,
			quantity,
			planned_resource_id,
			frozen,
			plan_status,
			order_type,
			kit_status,
			processing_time,
			start_time,
			end_time,
			earliest_start_time,
			latest_end_time,
			calc_earliest_start_time,
			calc_latest_end_time,
			connection_task,
			connection_type,
			max_connection_duration,
			min_connection_duration,
			partition_type,
			resource_fixed,
			time_fixed,
			lock_status,
			delay_reason,
			parent_id,
			operation_index,
			unscheduled_reason,
			last_insertion,
			standard_step_id,
			remark,
			enabled,
			creator,
			create_time,
			modifier,
			modify_time
		FROM
			sds_ord_operation
		where 
			order_id in	
		<foreach collection="orderIds" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    
    <delete id="deleteByPublishedLogIds">
        delete 
        from 
        	sds_ord_operation_published 
    	where 
    		published_log_id in 
    		<foreach collection="publishedLogIds" item="item" index="index" open="(" separator="," close=")">
            	#{item,jdbcType=VARCHAR}
        	</foreach>
    </delete>
</mapper>
