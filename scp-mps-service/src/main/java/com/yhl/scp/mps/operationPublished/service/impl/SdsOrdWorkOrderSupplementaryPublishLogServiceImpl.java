package com.yhl.scp.mps.operationPublished.service.impl;

import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.PlatformUser;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.UserTypeEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPlanOrderCreate;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.basic.rule.enums.RuleEncodingsEnum;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.rule.vo.RuleEncodingsVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.rule.util.RuleEncodingsUtils;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.enums.ProductAdvanceBatchRuleTypeEnum;
import com.yhl.scp.mps.operationPublished.convertor.SdsOrdWorkOrderSupplementaryPublishLogConvertor;
import com.yhl.scp.mps.operationPublished.domain.entity.SdsOrdWorkOrderSupplementaryPublishLogDO;
import com.yhl.scp.mps.operationPublished.domain.service.SdsOrdWorkOrderSupplementaryPublishLogDomainService;
import com.yhl.scp.mps.operationPublished.dto.SdsOrdWorkOrderSupplementaryPublishLogDTO;
import com.yhl.scp.mps.operationPublished.infrastructure.dao.SdsOrdWorkOrderSupplementaryPublishLogDao;
import com.yhl.scp.mps.operationPublished.infrastructure.po.SdsOrdWorkOrderSupplementaryPublishLogPO;
import com.yhl.scp.mps.operationPublished.service.SdsOrdWorkOrderSupplementaryPublishLogService;
import com.yhl.scp.mps.operationPublished.vo.SdsOrdWorkOrderSupplementaryPublishLogVO;
import com.yhl.scp.mps.plan.dto.MasterPlanRelationDTO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.service.MasterPlanRelationService;
import com.yhl.scp.mps.plan.vo.MasterPlanRelationVO;
import com.yhl.scp.mps.plan.vo.MasterPlanWorkOrderBodyVO;
import com.yhl.scp.mps.product.service.ProductAdvanceBatchRuleService;
import com.yhl.scp.mps.product.vo.ProductAdvanceBatchRuleVO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>SdsOrdWorkOrderSupplementaryPublishLogServiceImpl</code>
 * <p>
 * 应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-25 09:17:50
 */
@Slf4j
@Service
public class SdsOrdWorkOrderSupplementaryPublishLogServiceImpl extends AbstractService implements SdsOrdWorkOrderSupplementaryPublishLogService {

    @Resource
    private SdsOrdWorkOrderSupplementaryPublishLogDao sdsOrdWorkOrderSupplementaryPublishLogDao;

    @Resource
    private SdsOrdWorkOrderSupplementaryPublishLogDomainService sdsOrdWorkOrderSupplementaryPublishLogDomainService;
    @Resource
    private MasterPlanRelationService masterPlanRelationService;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private IpsFeign ipsFeign;
    @Resource
    private ProductAdvanceBatchRuleService productAdvanceBatchRuleService;
    public static final String FIELD_DEMAND_TIME = "demandTime";
    public static final String FIELD_DEMAND_QTY = "demandQuantity";

    public static final String PARAM_PRODUCT_CODES = "productCodes";
    public static final String FORMING_PROCESS = "FORMING_PROCESS";
    public static final String PARAM_STOCK_POINT_TYPE = "stockPointType";

    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";
    public static final String SUB_INVENTORY_CPSJ = "CPSJ";

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SdsOrdWorkOrderSupplementaryPublishLogDTO sdsOrdWorkOrderSupplementaryPublishLogDTO) {
        // 0.数据转换
        SdsOrdWorkOrderSupplementaryPublishLogPO sdsOrdWorkOrderSupplementaryPublishLogPO = SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.dto2Po(sdsOrdWorkOrderSupplementaryPublishLogDTO);
        // 1.数据校验
        // 2.数据持久化
        PlatformUser user = SystemHolder.getUser();
        BaseResponse<String> scenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MDS.getCode(),
                TenantCodeEnum.FYQB.getCode());
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointByParams(scenario.getData(),
                ImmutableMap.of("id", sdsOrdWorkOrderSupplementaryPublishLogDTO.getProductId()));
        if(CollectionUtils.isEmpty(newProductStockPointVOS)){
            return BaseResponse.error("新增失败，找不到物料");
        }
        NewProductStockPointVO newProductStockPointVO = newProductStockPointVOS.get(0);
        String orderPlanner = newProductStockPointVO.getOrderPlanner();
        if(!Objects.equals(user.getUserType(), UserTypeEnum.TENANT_ADMIN.getCode())){
            if(!Objects.equals(orderPlanner, user.getId())){
                throw new BusinessException("无该产品权限");
            }
        }

        BasePOUtils.insertFiller(sdsOrdWorkOrderSupplementaryPublishLogPO);
        List<RuleEncodingsVO> prsRulRuleEncodingsVOS = mdsFeign.getRuleEncoding();
        Map<String, RuleEncodingsVO> ruleEncodingsMap = prsRulRuleEncodingsVOS.stream()
                .collect(Collectors.toMap(RuleEncodingsVO::getRuleName, v -> v));
        mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
        RuleEncodingsVO ruleEncodings = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
        String serialNumber = RuleEncodingsUtils.getSerialNumber(ruleEncodings);
        String orderNo = RuleEncodingsUtils.getCode(ruleEncodings, null, serialNumber);
        String newOrderNo=orderNo.replaceAll("M","B");
        sdsOrdWorkOrderSupplementaryPublishLogPO.setOrderType("LC");
        sdsOrdWorkOrderSupplementaryPublishLogPO.setOrderNo(newOrderNo);
        sdsOrdWorkOrderSupplementaryPublishLogDao.insert(sdsOrdWorkOrderSupplementaryPublishLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SdsOrdWorkOrderSupplementaryPublishLogDTO sdsOrdWorkOrderSupplementaryPublishLogDTO) {
        // 0.数据转换
        SdsOrdWorkOrderSupplementaryPublishLogDO sdsOrdWorkOrderSupplementaryPublishLogDO = SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.dto2Do(sdsOrdWorkOrderSupplementaryPublishLogDTO);
        SdsOrdWorkOrderSupplementaryPublishLogPO sdsOrdWorkOrderSupplementaryPublishLogPO = SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.dto2Po(sdsOrdWorkOrderSupplementaryPublishLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        sdsOrdWorkOrderSupplementaryPublishLogDomainService.validation(sdsOrdWorkOrderSupplementaryPublishLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(sdsOrdWorkOrderSupplementaryPublishLogPO);
        sdsOrdWorkOrderSupplementaryPublishLogDao.update(sdsOrdWorkOrderSupplementaryPublishLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SdsOrdWorkOrderSupplementaryPublishLogDTO> list) {
        List<SdsOrdWorkOrderSupplementaryPublishLogPO> newList = SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        sdsOrdWorkOrderSupplementaryPublishLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SdsOrdWorkOrderSupplementaryPublishLogDTO> list) {
        List<SdsOrdWorkOrderSupplementaryPublishLogPO> newList = SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        sdsOrdWorkOrderSupplementaryPublishLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return sdsOrdWorkOrderSupplementaryPublishLogDao.deleteBatch(idList);
        }
        return sdsOrdWorkOrderSupplementaryPublishLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SdsOrdWorkOrderSupplementaryPublishLogVO selectByPrimaryKey(String id) {
        SdsOrdWorkOrderSupplementaryPublishLogPO po = sdsOrdWorkOrderSupplementaryPublishLogDao.selectByPrimaryKey(id);
        return SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "SDS_ORD_WORK_ORDER_SUPPLEMENTARY_PUBLISH_LOG")
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "SDS_ORD_WORK_ORDER_SUPPLEMENTARY_PUBLISH_LOG")
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SdsOrdWorkOrderSupplementaryPublishLogVO> dataList = sdsOrdWorkOrderSupplementaryPublishLogDao.selectByCondition(sortParam, queryCriteriaParam);
        SdsOrdWorkOrderSupplementaryPublishLogServiceImpl target = SpringBeanUtils.getBean(SdsOrdWorkOrderSupplementaryPublishLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectByParams(Map<String, Object> params) {
        List<SdsOrdWorkOrderSupplementaryPublishLogPO> list = sdsOrdWorkOrderSupplementaryPublishLogDao.selectByParams(params);
        return SdsOrdWorkOrderSupplementaryPublishLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> selectVOByParams(Map<String, Object> params) {
        return sdsOrdWorkOrderSupplementaryPublishLogDao.selectVOByParams(params);
    }

    @Override
    public BaseResponse<Void> publish(List<String> ids) {
        if(CollectionUtils.isNotEmpty(ids)){
            HashMap<String, Object> queryOrderMap = MapUtil.newHashMap();
            queryOrderMap.put("ids",ids);
            queryOrderMap.put("planNoIsNull","planNoIsNull");
            List<SdsOrdWorkOrderSupplementaryPublishLogVO> unPublishWorkOrderList = this.selectVOByParams(queryOrderMap);
            if(CollectionUtils.isEmpty(unPublishWorkOrderList)){
                return BaseResponse.error("找不到可下发制造工单");
            }

            Map stockParams = MapUtil.builder()
                    .put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode())
                    .put("stockPointType", StockPointTypeEnum.BC.getCode())
                    .put("enabled", YesOrNoEnum.YES.getCode()).build();
            String scenario = SystemHolder.getScenario();
            List<NewStockPointVO> stockPointVOS = mdsFeign.selectStockPointByParams(scenario, stockParams);
            if(CollectionUtils.isEmpty(stockPointVOS)){
                return BaseResponse.error("库存点信息为空");
            }
            NewStockPointVO saleOrganizeVO = stockPointVOS.get(0);

            List<String> productCodes = unPublishWorkOrderList.stream().map(SdsOrdWorkOrderSupplementaryPublishLogVO::
                    getProductCode).distinct().collect(Collectors.toList());
            List<String> productTypes = unPublishWorkOrderList.stream().map(SdsOrdWorkOrderSupplementaryPublishLogVO::
                    getProductType).distinct().collect(Collectors.toList());
            List<String> riskLevels = unPublishWorkOrderList.stream().map(SdsOrdWorkOrderSupplementaryPublishLogVO::
                    getRiskLevel).distinct().collect(Collectors.toList());
            List<DeliveryPlanVO2> deliveryPlanVO2List = dfpFeign.selectDeliveryPlanPublishedByParams(scenario, ImmutableMap.of("productCodes", productCodes));
            if(CollectionUtils.isEmpty(deliveryPlanVO2List)){
                return BaseResponse.error("发货计划信息为空");
            }
            Map<String, List<DeliveryPlanVO2>> productDeliveryPlanMap = deliveryPlanVO2List.stream()
                    .collect(Collectors.groupingBy(DeliveryPlanVO2::getProductCode));

            Map<String, String> productOemMap = deliveryPlanVO2List.stream().collect(Collectors.
                    toMap(DeliveryPlanVO2::getProductCode, DeliveryPlanVO2::getOemCode, (v1, v2) -> v1));
            List<String> oemCodes = deliveryPlanVO2List.stream().map(DeliveryPlanVO2::getOemCode).distinct().collect(Collectors.toList());
            List<OemVO> oemVOS = dfpFeign.selectOemVOByParams(scenario, ImmutableMap.of("oemCodes", oemCodes));
            if(CollectionUtils.isEmpty(oemVOS)){
                return BaseResponse.error("主机厂信息为空");
            }
            Map<String, OemVO> oemMap = oemVOS.stream().collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (v1, v2) -> v1));

            List<ProductAdvanceBatchRuleVO> advanceRuleByProductCodes = productAdvanceBatchRuleService
                    .selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.productCode.getCode(),"productCodes", productCodes));
            Map<String, BigDecimal> advanceRuleByProductCodesMap=CollectionUtils.isEmpty(advanceRuleByProductCodes)?
                    new HashMap<>():
                    advanceRuleByProductCodes.stream().collect(Collectors.
                            toMap(ProductAdvanceBatchRuleVO::getProductCode,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));

            List<ProductAdvanceBatchRuleVO> advanceRuleByProductTypes = productAdvanceBatchRuleService
                    .selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.productType.getCode(),"productTypes", productTypes));
            Map<String, BigDecimal> advanceRuleByProductTypesMap=CollectionUtils.isEmpty(advanceRuleByProductTypes)?
                    new HashMap<>():
                    advanceRuleByProductTypes.stream().collect(Collectors.
                            toMap(ProductAdvanceBatchRuleVO::getProductType,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));

            List<ProductAdvanceBatchRuleVO> advanceRuleByRiskLevels = productAdvanceBatchRuleService.selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.riskLevel.getCode(),"riskLevels", riskLevels));
            Map<String, BigDecimal> advanceRuleByRiskLevelsMap=CollectionUtils.isEmpty(advanceRuleByRiskLevels)?
                    new HashMap<>():
                    advanceRuleByRiskLevels.stream().collect(Collectors.
                            toMap(ProductAdvanceBatchRuleVO::getRiskLevel,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));


            List<MasterPlanRelationVO> masterPlanRelationVOS = masterPlanRelationService.selectByParams(ImmutableMap.of("productCodes", productCodes,"planStatusNotIn", ImmutableList.of("50","60")));
            Map<String, List<MasterPlanRelationVO>> masterPlanMap = CollectionUtils.isEmpty(masterPlanRelationVOS) ?
                    new HashMap<>() :
                    masterPlanRelationVOS.stream().collect(Collectors.groupingBy(MasterPlanRelationVO::getProductCode));

            List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                            .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                    .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getOrganizeType())
                            && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                    .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

            List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario,
                    productCodes, StockPointTypeEnum.BC.getCode());
            BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
            String subInventory = scenarioBusinessRange.getData().getRangeData();
            if(org.apache.commons.lang3.StringUtils.isBlank(subInventory)){
                log.error("未找到对应的成品子库存信息！");
                throw new BusinessException("未找到对应的成品子库存信息！");
            }
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap = inventoryBatchDetails
                    .stream().filter(t -> org.apache.commons.lang3.StringUtils.isEmpty(t.getOperationCode())
                            && saleOrganizations.contains(t.getStockPointCode())
                            && subInventory.equals(t.getSubinventory()))
                    .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
            int successCount = 0;
            int failCount = 0;
            StringBuilder totalErrMsg = new StringBuilder();
            List<MasterPlanRelationDTO> insertMasterPlanRelationDTOS = Lists.newArrayList();
            for (SdsOrdWorkOrderSupplementaryPublishLogVO workOrderPublishedVO : unPublishWorkOrderList) {
                BigDecimal maximumNumberDay=BigDecimal.ZERO;
                BigDecimal planQuantity=BigDecimal.ZERO;
                BigDecimal inventoryQuantity =BigDecimal.ZERO;
                String productType=workOrderPublishedVO.getProductType();
                String riskLevel=workOrderPublishedVO.getRiskLevel();
                String orderNo = workOrderPublishedVO.getOrderNo();
                String productCode = workOrderPublishedVO.getProductCode();
                String inventoryItemId = workOrderPublishedVO.getInventoryItemId();
                String orderTypeCode = workOrderPublishedVO.getOrderType();
                String testOrderNumber = workOrderPublishedVO.getTestOrderNumber();
                Date startTimeDate = DateUtils.formatDate(workOrderPublishedVO.getOrderTime(), "yyyy-MM-dd");
                Date currentDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
                Date planDate = DateUtils.moveDay(startTimeDate, 30);

                if(!productOemMap.containsKey(productCode)){
                    failCount++;
                    log.error("发货记录表找不到该物料信息:{}",productCode);
                    totalErrMsg.append("制造订单号："+orderNo+"发货记录表找不到该物料信息");
                    totalErrMsg.append("<br/>");
                    continue;
                }
                String oemCode = productOemMap.get(productCode);
                if(!oemMap.containsKey(oemCode)){
                    failCount++;
                    log.error("主机厂信息表找不到该主机厂信息:{}为空",oemCode);
                    totalErrMsg.append("制造订单号："+orderNo+"主机厂信息表找不到该主机厂信息");
                    totalErrMsg.append("<br/>");
                    continue;
                }
                OemVO oemVO = oemMap.get(oemCode);
                if(Objects.isNull(oemVO.getErpShipToSiteUseId())){
                    failCount++;
                    log.error("制造订单号：{}，{}主机厂地点信息为空",orderNo,oemVO.getOemCode());
                    totalErrMsg.append("制造订单号："+orderNo+"主机厂地点信息为空");
                    totalErrMsg.append("<br/>");
                    continue;
                }
                if(Objects.isNull(oemVO.getErpCustomerAddressId())){
                    failCount++;
                    log.error("制造订单号：{}，{}客户地址信息为空",orderNo,oemVO.getOemCode());
                    totalErrMsg.append("制造订单号："+orderNo+"客户地址信息为空");
                    totalErrMsg.append("<br/>");
                    continue;
                }

                if(advanceRuleByProductCodesMap.containsKey(productCode)){
                    maximumNumberDay = advanceRuleByProductCodesMap.get(productCode);
                }else if(advanceRuleByProductTypesMap.containsKey(productType)){
                    maximumNumberDay=advanceRuleByProductTypesMap.get(productType);
                } else if (advanceRuleByRiskLevelsMap.containsKey(riskLevel)) {
                    maximumNumberDay=advanceRuleByRiskLevelsMap.get(riskLevel);
                }
                Date lastDeliveryDay = DateUtils.moveDay(currentDate, maximumNumberDay.intValue());

                List<DeliveryPlanVO2> lastDelivertPlanList = productDeliveryPlanMap.get(productCode);
                int deliveryQuantity = lastDelivertPlanList.stream()
                        .filter(t -> !DateUtils.formatDate(t.getDemandTime(), "yyyy-MM-dd").before(currentDate)
                                && !DateUtils.formatDate(t.getDemandTime(), "yyyy-MM-dd").after(lastDeliveryDay))
                        .map(DeliveryPlanVO2::getDemandQuantity)
                        .filter(Objects::nonNull)
                        .mapToInt(Integer::intValue)
                        .sum();
                if(masterPlanMap.containsKey(productCode)){
                    List<MasterPlanRelationVO> masterPlanRelationVOList = masterPlanMap.get(productCode);
                    planQuantity = masterPlanRelationVOList.stream().map(t -> {
                                BigDecimal orderQty = Optional.ofNullable(t.getOrderQuantity()).orElse(BigDecimal.ZERO);
                                BigDecimal deliveryQty = Optional.ofNullable(t.getDeliveryQuantity()).orElse(BigDecimal.ZERO);
                                return orderQty.subtract(deliveryQty);
                            }).reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                if(inventoryBatchDetailMap.containsKey(productCode)){
                    List<InventoryBatchDetailVO> inventoryBatchDetailList= inventoryBatchDetailMap.get(productCode);
                    inventoryQuantity = inventoryBatchDetailList.stream().map(t -> Optional.ofNullable(t.getCurrentQuantity())
                                    .map(BigDecimal::new)
                                    .orElse(BigDecimal.ZERO))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                }
                int totalQuantity = deliveryQuantity-planQuantity.intValue()-inventoryQuantity.intValue();
                if(workOrderPublishedVO.getQuantity().intValue()>totalQuantity){
                    failCount++;
                    log.error("制造订单号：{}，超过最大下发数量{}",orderNo,totalQuantity);
                    totalErrMsg.append("制造订单号："+orderNo+"，超过最大下发数量:"+totalQuantity);
                    totalErrMsg.append("<br/>");
                    continue;
                }


                Map<String, Object> createPayLoad = MapUtil.newHashMap();
                List<Map<String, Object>> lineList = new ArrayList<>();
                List<Map<String, Object>> createParamList= Lists.newArrayList();

                Map<String, Object> createHeadMap = new HashMap<>();
                createHeadMap.put("reqSource", "BPIM");
                createHeadMap.put("reqOrgId", saleOrganizeVO.getOrganizeId());
                createHeadMap.put("reqInvOrgId", saleOrganizeVO.getOrganizeId());
                createHeadMap.put("orgName", saleOrganizeVO.getStockPointName());
                createHeadMap.put("effectiveFlag", "Y");
                createHeadMap.put("customerId", oemVO.getErpCustomerId());
                createHeadMap.put("custNumber", oemVO.getErpCustomerCode());
                createHeadMap.put("siteUseId", oemVO.getErpShipToSiteUseId());
                createHeadMap.put("siteNumber", oemVO.getErpSiteCode());
                createHeadMap.put("overTolerance", 0.00001);
                createHeadMap.put("underTolerance", 0.00001);
                createHeadMap.put("deductFlag", "Y");
                createHeadMap.put("orderCode", orderTypeCode);
                if(StringUtils.isNotEmpty(testOrderNumber)){
                    createHeadMap.put("testOrderNumber", testOrderNumber);
                }
                createHeadMap.put("changeFlag", "N");
                createHeadMap.put("attribute1", SystemHolder.getUserName());
                createPayLoad.put("workOrderNumber", orderNo);
                log.info("创建头层参数：工单号{}",orderNo);
                Map<String, Object> lineMap = new HashMap<>();
                lineMap.put("lineNum", 1);//计划单行号

                lineMap.put("status", "15");//计划单行状态

                lineMap.put("inventoryItemId", inventoryItemId);//ERP物料ID
                lineMap.put("itemNumber", productCode);//物料编码
                lineMap.put("orderQuantity", "0");//订单数量
                lineMap.put("holdInvQuantity", "0");//保留数量
                lineMap.put("reqQuantity", workOrderPublishedVO.getQuantity());//请求数量
                lineMap.put("deliveryQuantity", "0");//发运数量
                lineMap.put("defaultReqDate", DateUtils.dateToString(planDate));//请求日期
                lineMap.put("actualReqDate",DateUtils.dateToString(planDate));//计划日期
                lineMap.put("effectiveFlag", "Y");//有效标志
                lineMap.put("productDate", DateUtils.dateToString(planDate));//生产日期
                lineMap.put("productQuantity", workOrderPublishedVO.getQuantity());//生产数量
                lineMap.put("underTolerance", 0.00001);//计划单行允差上限%
                lineMap.put("overTolerance", 0.00001);//计划单行允差下限%
                lineMap.put("mixFlag", "N");//混箱标识
                lineList.add(lineMap);
                createHeadMap.put("lineList", lineList);
                createParamList.add(createHeadMap);
                createPayLoad.put("payLoad",createParamList);
                BaseResponse<String> baseResponse = newDcpFeign.callExternalApi(SystemHolder.getTenantCode(), ApiSourceEnum.ERP.getCode(),
                        ApiCategoryEnum.PLAN_ORDER_CREATE.getCode(), createPayLoad);
                if (baseResponse.getSuccess()) {
                    ErpResponse erpResponse = JSONObject.parseObject(baseResponse.getData(), ErpResponse.class);
                    if(erpResponse.getSuccess()){
                        successCount++;
                        List<ErpPlanOrderCreate> erpPlanOrderCreates = JSONObject.parseArray(String.valueOf(erpResponse.getData()), ErpPlanOrderCreate.class);
                        log.info("计划单创建成功，返回数据:{}",erpResponse.getData());

                        MasterPlanRelationDTO masterPlanRelationDTO = new MasterPlanRelationDTO();
                        masterPlanRelationDTO.setOrderNo(orderNo);
                        masterPlanRelationDTO.setPlanNo(erpPlanOrderCreates.get(0).getReqNumber());
                        masterPlanRelationDTO.setLineNo(String.valueOf(erpPlanOrderCreates.get(0).getLineList().get(0).getLineNum()));
                        masterPlanRelationDTO.setPlanStatus(erpPlanOrderCreates.get(0).getLineList().get(0).getStatus());
                        masterPlanRelationDTO.setOrgId(erpPlanOrderCreates.get(0).getReqOrgId());
                        masterPlanRelationDTO.setProductCode(workOrderPublishedVO.getProductCode());
                        masterPlanRelationDTO.setStockPointCode(workOrderPublishedVO.getStockPointCode());
                        masterPlanRelationDTO.setDeliveryQuantity(BigDecimal.ZERO);
                        masterPlanRelationDTO.setOrderQuantity(workOrderPublishedVO.getQuantity());
                        insertMasterPlanRelationDTOS.add(masterPlanRelationDTO);
                    }else{
                        failCount++;
                        totalErrMsg.append("制造订单号："+orderNo+"计划单创建接口返回失败，错误信息:"+erpResponse.getMessage());
                        totalErrMsg.append("<br/>");
                        log.error("计划单创建接口失败,制造单号:{}报错，{}", orderNo,erpResponse.getMessage());
                    }
                } else {
                    failCount++;
                    totalErrMsg.append("制造订单号："+orderNo+"调用ERP接口失败");
                    totalErrMsg.append("<br/>");
                }
            }
            if(CollectionUtils.isNotEmpty(insertMasterPlanRelationDTOS)){
                masterPlanRelationService.doCreateBatch(insertMasterPlanRelationDTOS);
                log.info("计划单关联表新增数量：{}",insertMasterPlanRelationDTOS.size());
            }
            if (org.apache.commons.lang3.StringUtils.isBlank(totalErrMsg.toString())) {
                return BaseResponse.success(String.format("计划发布成功，共处理%d条工单", successCount));
            } else {
                if(successCount==0){
                    return BaseResponse.error(String.format("计划发布失败：共处理%d条工单，成功%d条，失败%d条。失败明细：<br/>%s",
                            (successCount + failCount), successCount, failCount, totalErrMsg));
                }
                return BaseResponse.error(String.format("计划发布部分成功：共处理%d条工单，成功%d条，失败%d条。失败明细：<br/>%s",
                        (successCount + failCount), successCount, failCount, totalErrMsg));
            }
        }else{
            return BaseResponse.error("无勾选任何数据，无法下发");
        }
    }

    @Override
    public BaseResponse<Void> generate() {
        List<CollectionValueVO> planOrderDates = ipsFeign.getByCollectionCode("PLAN_ORDER_DATE");
        if (CollectionUtils.isEmpty(planOrderDates)) {
            log.warn("PLAN_ORDER_DATE字典信息为空");
            return BaseResponse.error("PLAN_ORDER_DATE字典信息为空");
        }
        String orderDateConfig=planOrderDates.stream().map(CollectionValueVO::getCollectionValue)
                .filter(org.apache.commons.lang3.StringUtils::isNotBlank).findFirst().orElse(null);

        if (org.apache.commons.lang3.StringUtils.isBlank(orderDateConfig)) {
            log.warn("PLAN_ORDER_DATE字典值为空");
            return BaseResponse.error("PLAN_ORDER_DATE字典值为空");
        }
        String userId = SystemHolder.getUserId();
        String scenario = SystemHolder.getScenario();

        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                        .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saleOrganizations)){
            return BaseResponse.error("销售组织信息为空");
        }
        String saleOrganization= saleOrganizations.get(0);
        List<NewProductStockPointVO> newProductStockPointVOS = mdsFeign.selectProductStockPointVOByParams(scenario,
                ImmutableMap.of("orderPlanner",userId,"stockPointCode",saleOrganization));

        if(CollectionUtils.isEmpty(newProductStockPointVOS)){
            return BaseResponse.error("找不到该账号权限下的物料");
        }
        List<String> productCodes = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        List<String> productTypes = newProductStockPointVOS.stream().map(NewProductStockPointVO::
                getProductType).distinct().collect(Collectors.toList());
        List<String> riskLevels = newProductStockPointVOS.stream().map(NewProductStockPointVO::
                getRiskLevel).distinct().collect(Collectors.toList());
        List<ProductAdvanceBatchRuleVO> advanceRuleByProductCodes = productAdvanceBatchRuleService
                .selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.productCode.getCode(),"productCodes", productCodes));
        Map<String, BigDecimal> advanceRuleByProductCodesMap=CollectionUtils.isEmpty(advanceRuleByProductCodes)?
                new HashMap<>():
                advanceRuleByProductCodes.stream().collect(Collectors.
                        toMap(ProductAdvanceBatchRuleVO::getProductCode,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));

        List<ProductAdvanceBatchRuleVO> advanceRuleByProductTypes = productAdvanceBatchRuleService
                .selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.productType.getCode(),"productTypes", productTypes));
        Map<String, BigDecimal> advanceRuleByProductTypesMap=CollectionUtils.isEmpty(advanceRuleByProductTypes)?
                new HashMap<>():
                advanceRuleByProductTypes.stream().collect(Collectors.
                        toMap(ProductAdvanceBatchRuleVO::getProductType,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));

        List<ProductAdvanceBatchRuleVO> advanceRuleByRiskLevels = productAdvanceBatchRuleService.selectByParams(ImmutableMap.of("ruleType", ProductAdvanceBatchRuleTypeEnum.riskLevel.getCode(),"riskLevels", riskLevels));
        Map<String, BigDecimal> advanceRuleByRiskLevelsMap=CollectionUtils.isEmpty(advanceRuleByRiskLevels)?
                new HashMap<>():
                advanceRuleByRiskLevels.stream().collect(Collectors.
                        toMap(ProductAdvanceBatchRuleVO::getRiskLevel,ProductAdvanceBatchRuleVO::getMaximumNumberDays,(v1, v2) -> v1));

        HashMap<String, Object> queryOrderMap = MapUtil.newHashMap();
        queryOrderMap.put("productCodes",productCodes);
        queryOrderMap.put("planNoIsNull","planNoIsNull");
        List<SdsOrdWorkOrderSupplementaryPublishLogVO> unPublishWorkOrderList = this.selectVOByParams(queryOrderMap);
        if(CollectionUtils.isNotEmpty(unPublishWorkOrderList)){
            List<String> ids = unPublishWorkOrderList.stream().map(SdsOrdWorkOrderSupplementaryPublishLogVO::getId).collect(Collectors.toList());
            this.doDelete(ids);
            log.info("删除用户id：{}权限下的物料的未下发的计划单",userId);
        }
        DeliveryPlanVersionVO deliveryPlanVersionVO = dfpFeign.selectLatestDeliveryPlanVersion(scenario);
        if(Objects.isNull(deliveryPlanVersionVO)){
            return BaseResponse.error("发货计划信息为空");
        }
        List<DeliveryPlanPublishedVO> deliveryPlanVOList = dfpFeign.selectPublishDeliveryPlanByParams(ImmutableMap.of("productCodes", productCodes,"deliveryVersionId",deliveryPlanVersionVO.getId()));
        if(CollectionUtils.isEmpty(deliveryPlanVOList)){
            return BaseResponse.error("发货计划信息为空");
        }
        Map<String, List<DeliveryPlanPublishedVO>> productDeliveryPlanMap = deliveryPlanVOList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));

        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario,
                productCodes, StockPointTypeEnum.BC.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        String subInventory = scenarioBusinessRange.getData().getRangeData();
        if(org.apache.commons.lang3.StringUtils.isBlank(subInventory)){
            log.error("未找到对应的成品子库存信息！");
            throw new BusinessException("未找到对应的成品子库存信息！");
        }
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap = inventoryBatchDetails
                .stream().filter(t -> org.apache.commons.lang3.StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
        List<MasterPlanRelationVO> masterPlanRelationVOS = masterPlanRelationService.selectByParams(
                ImmutableMap.of("productCodes", productCodes,"planStatusNotIn", ImmutableList.of("50","60")));
        Map<String, List<MasterPlanRelationVO>> masterPlanMap = CollectionUtils.isEmpty(masterPlanRelationVOS) ?
                new HashMap<>() :
                masterPlanRelationVOS.stream().collect(Collectors.groupingBy(MasterPlanRelationVO::getProductCode));
        List<SdsOrdWorkOrderSupplementaryPublishLogDTO> insertSupplementaryPublishLogDTOSList = new ArrayList<>();
        for (NewProductStockPointVO productStockPointVO : newProductStockPointVOS) {
            BigDecimal maximumNumberDay=BigDecimal.ZERO;
            BigDecimal planQuantity=BigDecimal.ZERO;
            BigDecimal inventoryQuantity =BigDecimal.ZERO;
            String productType=productStockPointVO.getProductType();
            String productId=productStockPointVO.getId();
            String riskLevel=productStockPointVO.getRiskLevel();
            String productCode = productStockPointVO.getProductCode();
            Date currentDate = DateUtils.formatDate(new Date(), "yyyy-MM-dd");
            if(advanceRuleByProductCodesMap.containsKey(productCode)){
                maximumNumberDay = advanceRuleByProductCodesMap.get(productCode);
            }else if(advanceRuleByProductTypesMap.containsKey(productType)){
                maximumNumberDay=advanceRuleByProductTypesMap.get(productType);
            } else if (advanceRuleByRiskLevelsMap.containsKey(riskLevel)) {
                maximumNumberDay=advanceRuleByRiskLevelsMap.get(riskLevel);
            }
            Date lastDeliveryDay = DateUtils.moveDay(currentDate, maximumNumberDay.intValue());
            Date lastDeliveryDayByConfig = DateUtils.moveDay(currentDate, Integer.parseInt(orderDateConfig));
            if(!productDeliveryPlanMap.containsKey(productCode)){
                log.info("没有找到物料编码:{}对应的发货计划，跳过",productCode);
                continue;
            }
            List<DeliveryPlanPublishedVO> lastDeliveryPlanList = productDeliveryPlanMap.get(productCode);

            int deliveryQuantity = lastDeliveryPlanList.stream()
                    .filter(t -> !DateUtils.formatDate(t.getDemandTime(),"yyyy-MM-dd").before(currentDate) && !DateUtils.formatDate(t.getDemandTime(),"yyyy-MM-dd").after(lastDeliveryDay))
                    .map(DeliveryPlanPublishedVO::getDemandQuantity)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::intValue)
                    .sum();
            int deliveryQuantityByConfig=lastDeliveryPlanList.stream()
                    .filter(t -> !t.getDemandTime().before(currentDate) && !t.getDemandTime().after(lastDeliveryDayByConfig))
                    .map(DeliveryPlanPublishedVO::getDemandQuantity)
                    .filter(Objects::nonNull)
                    .mapToInt(Integer::intValue)
                    .sum();
            if(masterPlanMap.containsKey(productCode)){
                List<MasterPlanRelationVO> masterPlanRelationVOList = masterPlanMap.get(productCode);
                planQuantity = masterPlanRelationVOList.stream().map(t -> {
                    BigDecimal orderQty = Optional.ofNullable(t.getOrderQuantity()).orElse(BigDecimal.ZERO);
                    BigDecimal deliveryQty = Optional.ofNullable(t.getDeliveryQuantity()).orElse(BigDecimal.ZERO);
                    return orderQty.subtract(deliveryQty);
                }).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            if(inventoryBatchDetailMap.containsKey(productCode)){
                List<InventoryBatchDetailVO> inventoryBatchDetailList= inventoryBatchDetailMap.get(productCode);
                inventoryQuantity = inventoryBatchDetailList.stream().map(t -> Optional.ofNullable(t.getCurrentQuantity())
                                .map(BigDecimal::new)
                                .orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            if(planQuantity.compareTo(BigDecimal.ZERO)<0){
                planQuantity=  BigDecimal.ZERO;
            }
            int totalQuantity = deliveryQuantity-planQuantity.intValue()-inventoryQuantity.intValue();
            log.info("物料编码：{},发货需求数量:{},最大提前期需求数量:{},未进仓数量：{}，库存数量:{},计划单配置天数最大下发数量{} 小于等于0，跳过",
                    productCode,deliveryQuantity,deliveryQuantityByConfig,planQuantity,inventoryQuantity,totalQuantity);
            if(totalQuantity<=0){
                log.info("物料编码：{},最大下发数量{} 小于等于0，跳过",productCode,totalQuantity);
                continue;
            }
            int totalQuantityByConfig = deliveryQuantityByConfig-planQuantity.intValue()-inventoryQuantity.intValue();
            if(totalQuantityByConfig<=0){
                log.info("物料编码：{},计划单配置天数最大下发数量{} 小于等于0，跳过",productCode,totalQuantity);
                continue;
            }
            List<RuleEncodingsVO> prsRulRuleEncodingsVOS = mdsFeign.getRuleEncoding();
            Map<String, RuleEncodingsVO> ruleEncodingsMap = prsRulRuleEncodingsVOS.stream()
                    .collect(Collectors.toMap(RuleEncodingsVO::getRuleName, v -> v));
            mdsFeign.selectiveUpdateRuleEncodings(ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc()));
            RuleEncodingsVO ruleEncodings = ruleEncodingsMap.get(RuleEncodingsEnum.WORK_ORDER_NO.getDesc());
            String serialNumber = RuleEncodingsUtils.getSerialNumber(ruleEncodings);
            String orderNo = RuleEncodingsUtils.getCode(ruleEncodings, null, serialNumber);
            String newOrderNo=orderNo.replaceAll("M","B");
            SdsOrdWorkOrderSupplementaryPublishLogDTO sdsOrdWorkOrderSupplementaryPublishLogDTO = new SdsOrdWorkOrderSupplementaryPublishLogDTO();
            sdsOrdWorkOrderSupplementaryPublishLogDTO.setProductId(productId);
            sdsOrdWorkOrderSupplementaryPublishLogDTO.setOrderType("LC");
            sdsOrdWorkOrderSupplementaryPublishLogDTO.setOrderNo(newOrderNo);
            sdsOrdWorkOrderSupplementaryPublishLogDTO.setQuantity(new BigDecimal(totalQuantity));
            sdsOrdWorkOrderSupplementaryPublishLogDTO.setOrderTime(new Date());
            log.info("物料编码{},工单号：{},数量{}进入计划单创建列表",productCode,newOrderNo,totalQuantity);
            insertSupplementaryPublishLogDTOSList.add(sdsOrdWorkOrderSupplementaryPublishLogDTO);
        }
        if(CollectionUtils.isNotEmpty(insertSupplementaryPublishLogDTOSList)){
            this.doCreateBatch(insertSupplementaryPublishLogDTOSList);
            log.info("批量创建计划单数量：{}",insertSupplementaryPublishLogDTOSList.size());
        }
        return BaseResponse.success(String.format("一键生成成功，共生成%d条计划单", insertSupplementaryPublishLogDTOSList.size()));

    }

    @Override
    public PageInfo masterPlanWorkOrder(Pagination pagination, MasterPlanReq masterPlanReq,boolean status,
                                        List<MasterPlanWorkOrderBodyVO> masterPlanWorkOrderBodyVOS,List<SdsOrdWorkOrderSupplementaryPublishLogVO> sdsOrdWorkOrderSupplementaryPublishLogVOS) {
        String scenario = SystemHolder.getScenario();
        if(CollectionUtils.isEmpty(sdsOrdWorkOrderSupplementaryPublishLogVOS)){
            return new PageInfo<>();
        }
        List<String> productCodes = sdsOrdWorkOrderSupplementaryPublishLogVOS.stream().map(SdsOrdWorkOrderSupplementaryPublishLogVO::getProductCode).distinct().collect(Collectors.toList());

        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveMonth(planStartTime,1);
        List<Date> intervalDates =
                DateUtils.getIntervalDates(planStartTime, planEndTime);
        Map<String, List<DeliveryPlanVO2>> deliveryPlanMap = dfpFeign
                .selectDeliveryPlanPublishedByParams(
                        scenario,
                        ImmutableMap.of(
                                "productCodes", productCodes,
                                "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
                                "endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
                        )
                )
                .stream()
                .collect(Collectors.groupingBy(
                        DeliveryPlanVO2::getProductCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                plans -> plans.stream()
                                        .collect(Collectors.groupingBy(
                                                DeliveryPlanVO2::getDemandTime,
                                                TreeMap::new,
                                                Collectors.collectingAndThen(
                                                        Collectors.toList(),
                                                        sameTimePlans -> {
                                                            DeliveryPlanVO2 merged = new DeliveryPlanVO2();
                                                            DeliveryPlanVO2 first = sameTimePlans.get(0);
                                                            merged.setProductCode(first.getProductCode());
                                                            merged.setDemandTime(first.getDemandTime());
                                                            // 累加 demandQuantity
                                                            int totalQuantity = sameTimePlans.stream()
                                                                    .mapToInt(DeliveryPlanVO2::getDemandQuantity)
                                                                    .sum();
                                                            merged.setDemandQuantity(totalQuantity);
                                                            return merged;
                                                        }
                                                )
                                        ))
                                        .values()
                                        .stream()
                                        .collect(Collectors.toList())
                        )
                ));



        List<InventoryBatchDetailVO> inventoryBatchDetails = dfpFeign.selectInventoryDataByProductCodes(scenario,
                productCodes, StockPointTypeEnum.BC.getCode());
        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                        .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(scenario, "SUB_INVENTORY", "INTERNAL", null);
        String subInventory = scenarioBusinessRange.getData().getRangeData();
        if(org.apache.commons.lang3.StringUtils.isBlank(subInventory)){
            log.error("未找到对应的成品子库存信息！");
            throw new BusinessException("未找到对应的成品子库存信息！");
        }
        Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap = inventoryBatchDetails
                .stream().filter(t -> org.apache.commons.lang3.StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));

        List<String> spaceList = inventoryBatchDetails.stream().map(InventoryBatchDetailVO::getFreightSpace)
                .distinct().collect(Collectors.toList());
        List<RoutingVO> routingVOS = newMdsFeign.selectRoutingByParams(scenario, ImmutableMap.of("routingCodes", productCodes));
        Map<String, String> productStockMap =CollectionUtils.isEmpty(routingVOS)? new HashMap<>():
                routingVOS.stream().collect(Collectors.toMap(RoutingBasicVO::getRoutingCode, RoutingBasicVO::getStockPointId, (v1, v2) -> v1));
        List<SubInventoryCargoLocationVO> subInventoryCargoLocations=new ArrayList<>();

        if(CollectionUtils.isNotEmpty(spaceList)){
            subInventoryCargoLocations =
                    subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, StockPointTypeEnum.BC.getCode());
        }

        Map<String,SubInventoryCargoLocationVO> cargoLocationMap=CollectionUtils.isEmpty(subInventoryCargoLocations)
                ? new HashMap<>() : subInventoryCargoLocations
                .stream().collect(Collectors.toMap(SubInventoryCargoLocationVO::getFreightSpaceCode,
                        Function.identity(), (v1, v2) -> v1));
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(scenario);
        Map<String, String> standardStepMap = standardStepVOS.stream().collect(Collectors
                .toMap(p -> p.getStockPointCode() + p.getStandardStepName(),
                        StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));
        //获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream()
                .filter(e -> org.apache.commons.lang3.StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());
        // 工序在制量
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = inventoryBatchDetails.stream()
                .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(),
                        p.getProductCode(), p.getOperationCode())));


        sdsOrdWorkOrderSupplementaryPublishLogVOS.forEach(t->{
            String productCode = t.getProductCode();
            List<Map<String, Object> > dynamicData = new ArrayList<>();
            if (deliveryPlanMap.containsKey(productCode)) {
                List<DeliveryPlanVO2> deliveryPlanVO2List = deliveryPlanMap.get(productCode);
                Map<String, Integer> deliveryMap = deliveryPlanVO2List.stream().collect(Collectors.toMap(o -> DateUtils.dateToString(o.getDemandTime(), DateUtils.COMMON_DATE_STR3),o  -> o.getDemandQuantity(), (v1, v2) -> v1));
                for (Date intervalDate : intervalDates) {
                    Map<String, Object> dataMap = new HashMap<>();
                    String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                    if (!deliveryMap.containsKey(deliveryDate)) {
                        dataMap.put(FIELD_DEMAND_TIME, deliveryDate);
                        dataMap.put(FIELD_DEMAND_QTY, 0);
                        dynamicData.add(dataMap);
                    }else{
                        dataMap.put(FIELD_DEMAND_TIME, deliveryDate);
                        dataMap.put(FIELD_DEMAND_QTY, deliveryMap.get(deliveryDate));
                        dynamicData.add(dataMap);
                    }
                }
            }else{
                for (Date intervalDate : intervalDates) {
                    Map<String, Object> dataMap = new HashMap<>();
                    String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                    dataMap.put(FIELD_DEMAND_TIME, deliveryDate);
                    dataMap.put(FIELD_DEMAND_QTY, 0);
                    dynamicData.add(dataMap);
                }
            }
            t.setDynamicData(dynamicData);
            if(productStockMap.containsKey(productCode)){
                String stockPointCode = productStockMap.get(t.getProductCode());
                String cx = getInventory(FORMING_OPERATION, stockPointCode,productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
                String hp = getInventory(MERGING_OPERATION, stockPointCode,productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
                String bz = getInventory(PACKAGING_OPERATION, stockPointCode,productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
                t.setSuppress(cx);
                t.setPostLamination(hp);
                t.setPostPackaging(bz);
            }else{
                t.setSuppress("0");
                t.setPostLamination("0");
                t.setPostPackaging("0");
            }
            if(inventoryBatchDetailMap.containsKey(productCode)){
                List<InventoryBatchDetailVO> inventoryBatchDetailList= inventoryBatchDetailMap.get(productCode);
                BigDecimal inventoryQuantity = inventoryBatchDetailList.stream().map(o -> Optional.ofNullable(o.getCurrentQuantity())
                                .map(BigDecimal::new)
                                .orElse(BigDecimal.ZERO))
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                t.setFinishInventoryQuantity(inventoryQuantity);
            }else{
                t.setFinishInventoryQuantity(BigDecimal.ZERO);

            }
        });
        PageInfo<SdsOrdWorkOrderSupplementaryPublishLogVO> pageInfo = new PageInfo<>(sdsOrdWorkOrderSupplementaryPublishLogVOS);
        return pageInfo;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SDS_ORD_WORK_ORDER_SUPPLEMENTARY_PUBLISH_LOG.getCode();
    }

    @Override
    public List<SdsOrdWorkOrderSupplementaryPublishLogVO> invocation(List<SdsOrdWorkOrderSupplementaryPublishLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    private String getInventory(String op, String stockPointCode, String productCode,Map<String, String> stepMap,
                                Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (Objects.isNull(productCode)) {
            return "0";
        }

        String stockPoint = "S1";
        String stockPoint2 = "S2";
        if (stockPoint.equals(stockPointCode) && op.equals(PAINTING_OPERATION)) {
            op = "镀膜";
        }
        if (stockPoint2.equals(stockPointCode) && op.equals(FORMING_OPERATION)) {
            op = "钢化";
        }
        // 工序代码
        String op1 = stepMap.get(stockPointCode + op);
        String opMainKey = String.join("-", stockPointCode, productCode, op1);
        String opMainKey1 = String.join("-", stockPointCode, productCode, "");
        String finalOp = op;
        List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(opMainKey)
                        || (PACKAGING_OPERATION.equals(finalOp) && entry.getKey().equals(opMainKey1)))
                .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                    if (null == subInventoryCargoLocationVO) {
                        return false;
                    }
                    return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return "0";
        }
        OptionalDouble sum = inventoryBatchDetails.stream()
                .mapToDouble(t -> Double.parseDouble(t.getCurrentQuantity())).reduce(Double::sum);
        double totalQuantity = sum.orElse(0);
        return String.valueOf(totalQuantity);
    }
}
