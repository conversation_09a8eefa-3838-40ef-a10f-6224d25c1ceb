package com.yhl.scp.mps.model.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.mps.model.dto.MoldChangeTimeDTO;
import com.yhl.scp.mps.model.service.MoldChangeTimeService;
import com.yhl.scp.mps.model.vo.MoldChangeTimeVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>MoldChangeTimeController</code>
 * <p>
 * 换模换型时间控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-13 15:20:39
 */
@Slf4j
@Api(tags = "换模换型时间控制器")
@RestController
@RequestMapping("moldChangeTime")
public class MoldChangeTimeController extends BaseController {

    @Resource
    private MoldChangeTimeService moldChangeTimeService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<MoldChangeTimeVO>> page() {
        List<MoldChangeTimeVO> moldChangeTimeList = moldChangeTimeService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MoldChangeTimeVO> pageInfo = new PageInfo<>(moldChangeTimeList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MoldChangeTimeDTO moldChangeTimeDTO) {
        return moldChangeTimeService.doCreate(moldChangeTimeDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MoldChangeTimeDTO moldChangeTimeDTO) {
        return moldChangeTimeService.doUpdate(moldChangeTimeDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        moldChangeTimeService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<MoldChangeTimeVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, moldChangeTimeService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "导出")
    @GetMapping(value = "export")
    public void export(HttpServletResponse response) {
        moldChangeTimeService.export(response);
    }

    @ApiOperation(value = "批量失效")
    @PostMapping(value = "loseEffectivenessBatch")
    public BaseResponse<Void> loseEffectivenessBatch(@RequestBody List<String> ids) {
        moldChangeTimeService.loseEffectivenessBatch(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "换模换型时间同步")
    @PostMapping(value = "sync")
    public BaseResponse<MoldChangeTimeVO> syncData() {
        return moldChangeTimeService.syncData(null, null);
    }

}
