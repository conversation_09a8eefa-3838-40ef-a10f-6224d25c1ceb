package com.yhl.scp.mps.plan.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.EnumUtils;
import com.yhl.platform.common.utils.SpringContextUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.dfp.delivery.dto.DeliveryPlanPublishedCompareDTO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mps.adjust.enums.AdjustPlanType;
import com.yhl.scp.mps.adjust.po.PublishQtyCheckResultPO;
import com.yhl.scp.mps.adjust.support.AdjustPlanSupport;
import com.yhl.scp.mps.algorithm.dto.RzzAdjustmentParam;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.domain.adjust.context.AdjustPlanStrategyService;
import com.yhl.scp.mps.enums.PlanStatusEnum;
import com.yhl.scp.mps.feedback.req.FeedBackReq;
import com.yhl.scp.mps.plan.dto.MasterPlanInsertDTO;
import com.yhl.scp.mps.plan.dto.MasterPlanWorkOrderBodyDTO;
import com.yhl.scp.mps.plan.dto.UpdateDueDateDTO;
import com.yhl.scp.mps.plan.dto.UpdateRemarkDTO;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.req.MasterQuantityReq;
import com.yhl.scp.mps.plan.service.MasterPlanChangeSyncService;
import com.yhl.scp.mps.plan.service.MasterPlanInsertOrEditService;
import com.yhl.scp.mps.plan.service.MasterPlanService;
import com.yhl.scp.mps.plan.vo.*;
import com.yhl.scp.mps.schedule.HandworkUnScheduleDTO;
import com.yhl.scp.mps.schedule.service.HandworkScheduleService;
import com.yhl.scp.mps.util.LabelValueThree;
import com.yhl.scp.sds.basic.enums.KitStatusEnum;
import com.yhl.scp.sds.extension.order.dto.WorkOrderDTO;
import com.yhl.scp.sds.extension.order.infrastructure.po.WorkOrderPO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <code>MasterPlanController</code>
 * <p>
 * 主生产计划表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 13:52:50
 */
@Slf4j
@Api(tags = "A主生产计划表控制器")
@RestController
@RequestMapping("masterPlan")
public class MasterPlanController extends BaseController {

    public static final String SCHEDULING_MSG = "自动排产正在执行中，请等待结束后再操作";
    @Resource
    private MasterPlanService masterPlanService;
    @Resource
    private MasterPlanInsertOrEditService masterPlanInsertOrEditService;
    @Resource
    private HandworkScheduleService handworkScheduleService;
    @Resource
    private AdjustPlanStrategyService adjustPlanStrategyService;
    @Resource
    private MasterPlanChangeSyncService masterPlanChangeSyncService;
    @Resource
    private AdjustPlanSupport adjustPlanSupport;

    @ApiOperation(value = "主生产计划-工单")
    @PostMapping(value = "masterPlanWorkOrder")
    public BaseResponse<PageInfo<MasterPlanWorkOrderBodyVO>> masterPlanWorkOrder(@RequestBody MasterPlanReq masterPlanReq) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.masterPlanWorkOrder(getPagination(),
                masterPlanReq));
    }

    @ApiOperation(value = "主生产计划-工单2")
    @PostMapping(value = "masterPlanWorkOrder2")
    public BaseResponse<PageInfo<MasterPlanWorkOrderVO>> masterPlanWorkOrder2(@RequestBody MasterPlanReq masterPlanReq) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.masterPlanWorkOrder2(getPagination(),
                masterPlanReq));
    }

    @ApiOperation(value = "主计划批量关闭")
    @PostMapping(value = "batchClose")
    public BaseResponse<Void> batchClose(@RequestBody List<String> ids) {
        adjustPlanStrategyService.checkRunTaskByOperationIds(ids);
        masterPlanService.doBatchClose(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主计划批量取消计划")
    @PostMapping(value = "batchCancelPlan")
    public BaseResponse<Void> batchCancelPlan(@RequestBody List<String> ids) {
        adjustPlanStrategyService.checkRunTaskByOperationIds(ids);
        masterPlanService.doBatchCancelPlan(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "产线组下拉")
    @GetMapping(value = "standardResourceDropdown")
    public BaseResponse<List<LabelValue<String>>> standardResourceDropdown(
            @RequestParam(value = "organizationCode", required = false) String organizationCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanService.selectStandardResourceDropdown(organizationCode));
    }

    @ApiOperation(value = "产线组下拉2（name和code和id）")
    @GetMapping(value = "standardResourceDropdown2")
    public BaseResponse<List<LabelValueThree<String>>> standardResourceDropdown2(
            @RequestParam(value = "organizationCode", required = false) String organizationCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanService.selectStandardResourceDropdown2(organizationCode));
    }

    @ApiOperation(value = "排产资源下拉")
    @GetMapping(value = "physicalResourceDropdown")
    public BaseResponse<List<LabelValue<String>>> physicalResourceDropdown(
            @RequestParam(value = "organizationCode", required = false) String organizationCode,
            @RequestParam(value = "standardResourceId", required = false) String standardResourceId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanService.selectPhysicalResourceDropdown(organizationCode, standardResourceId));
    }

    @ApiOperation(value = "排产资源下拉2（name和code和id）")
    @GetMapping(value = "physicalResourceDropdown2")
    public BaseResponse<List<LabelValueThree<String>>> physicalResourceDropdown2(
            @RequestParam(value = "organizationCode", required = false) String organizationCode,
            @RequestParam(value = "standardResourceId", required = false) String standardResourceId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanService.selectPhysicalResourceDropdown2(organizationCode, standardResourceId));
    }

    @ApiOperation(value = "排产资源下拉（value值code）")
    @GetMapping(value = "physicalResource02")
    public BaseResponse<List<LabelValue<String>>> physicalResource02() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.listResourceDropDown02());
    }

    @ApiOperation(value = "排产工序下拉")
    @GetMapping(value = "operationStep")
    public BaseResponse<List<LabelValue<String>>> operationStep() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.operationStep());
    }

    @ApiOperation(value = "排产工序下拉2")
    @GetMapping(value = "operationStepTwo")
    public BaseResponse<List<LabelValue<String>>> operationStepTwo() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.operationStepTwo());
    }

    @ApiOperation(value = "齐套状态下拉")
    @GetMapping(value = "kilStatus")
    public BaseResponse<List<LabelValue<String>>> kilStatus() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, EnumUtils.getEnumValueList(KitStatusEnum.class));
    }

    @ApiOperation(value = "计划状态下拉")
    @GetMapping(value = "planStatus")
    public BaseResponse<List<LabelValue<String>>> planStatus() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, EnumUtils.getEnumValueList(PlanStatusEnum.class));
    }

    @ApiOperation(value = "组织下拉")
    @GetMapping(value = "org")
    public BaseResponse<List<LabelValue<String>>> org() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.org());
    }

    @ApiOperation(value = "产线组下拉（自动排产指定产线使用）")
    @GetMapping(value = "standardResource")
    public BaseResponse<List<LabelValue<String>>> standardResource() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.standardResource());
    }

    @ApiOperation(value = "产线下拉（自动排产指定产线使用）")
    @PostMapping(value = "physicalResource")
    public BaseResponse<List<LabelValue<String>>> physicalResource(@RequestBody List<String> standardResourceIds) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.physicalResource(standardResourceIds));
    }

    @ApiOperation(value = "组织下拉（只有S1和S2两个组织）")
    @GetMapping(value = "ruleOrg")
    public BaseResponse<List<LabelValue<String>>> ruleOrg() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.ruleOrg());
    }

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MasterPlanVO>> page() {
        List<MasterPlanVO> masterPlanList = masterPlanService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MasterPlanVO> pageInfo = new PageInfo<>(masterPlanList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "主计划新增检查")
    @PostMapping(value = "insertOperationCheck")
    public BaseResponse<Void> insertOperationCheck(@RequestBody MasterPlanInsertDTO dto) {
        adjustPlanStrategyService.checkStandardPermission(null, dto.getPhysicalResourceId());
        masterPlanInsertOrEditService.insertOperationCheck(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主计划新增")
    @PostMapping(value = "insertData")
    public BaseResponse<WorkOrderPO> insertData(@RequestBody MasterPlanInsertDTO dto) {
        adjustPlanStrategyService.checkStandardPermission(null, dto.getPhysicalResourceId());
        HandworkUnScheduleDTO data = masterPlanInsertOrEditService.insertData(dto);
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() ->
                // 刷新workOrder缓存
                SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario)
        );
        return BaseResponse.success(BaseResponse.OP_SUCCESS, data.getWorkOrderPO());
    }

    @ApiOperation(value = "新增排产工序下拉")
    @GetMapping(value = "insertOperationDropDown")
    public BaseResponse<List<LabelValue<String>>> insertOperationDropDown(
            @RequestParam(value = "productCode", required = false) String productCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanInsertOrEditService.operationDropDown(productCode));
    }

    @ApiOperation(value = "新增排产资源下拉")
    @GetMapping(value = "insertResourceDropDown")
    public BaseResponse<List<LabelValue<String>>> insertResourceDropDown(
            @RequestParam(value = "productCode", required = false) String productCode,
            @RequestParam(value = "routingStepId", required = false) String routingStepId) {
        List<LabelValue<String>> labelValues = masterPlanInsertOrEditService.resourceDropDown(productCode,
                routingStepId);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, labelValues);
    }

    @ApiOperation(value = "主计划齐套拆分")
    @PostMapping(value = "workOrderSplit")
    public BaseResponse<Void> doCompleteSetOfWorkOrderSplit(@RequestBody List<String> subOperationIds) {
        adjustPlanStrategyService.checkStandardPermission(null, null);
        masterPlanInsertOrEditService.doCompleteSetOfWorkOrderSplit(subOperationIds);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MasterPlanVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "主生产计划-发货计划总览")
    @PostMapping(value = "getDeliveryPlanGeneralView")
    public BaseResponse<List<DeliveryPlanGeneralViewVO>> getDeliveryPlanGeneralView(@RequestBody MasterPlanReq masterPlanReq) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                masterPlanService.getDeliveryPlanGeneralView(masterPlanReq));
    }

    @ApiOperation(value = "主生产计划-发货计划总览（分页））")
    @PostMapping(value = "deliveryPlanOverviewPage")
    public BaseResponse<PageInfo<DeliveryPlanOverviewVO>> deliveryPlanOverviewPage(@RequestBody MasterPlanReq masterPlanReq) {
        List<DeliveryPlanOverviewVO> overviewList =
                masterPlanService.selectDeliveryPlanOverviewByPage(getPagination(), getSortParam(),
                        getQueryCriteriaParam(), masterPlanReq);
        PageInfo<DeliveryPlanOverviewVO> pageInfo = new PageInfo<>(overviewList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "资源下拉-根据工序和物品")
    @GetMapping(value = "resourceDropDown")
    public BaseResponse<List<LabelValue<String>>> resourceDropDown(
            @RequestParam(value = "productId") String productId,
            @RequestParam(value = "standardStepId") String standardStepId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.resourceDropDown(productId,
                standardStepId));
    }

    @ApiOperation(value = "主计划编辑-批次调整")
    @PostMapping(value = "adjustQuantity")
    public BaseResponse<OperationAdjustResultVO> adjustQuantity(@RequestBody RzzAdjustmentParam dto) {
        dto.setAdjustQuantityFlag(true);
        dto.setAdjustMoldQuantityFlag(false);
        dto.setAdjustPlanType(AdjustPlanType.QUANTITY.getCode());
        OperationAdjustResultVO operationAdjustResultVO = adjustPlanStrategyService.execute(dto);
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() ->
                // 刷新workOrder缓存
                SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario)
        );
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationAdjustResultVO);
    }

    @ApiOperation(value = "主计划编辑-计划调整")
    @PostMapping(value = "adjustExcludeQuantity")
    public BaseResponse<OperationAdjustResultVO> adjustExcludeQuantity(@RequestBody RzzAdjustmentParam dto) {
        dto.setAdjustQuantityFlag(false);
        dto.setAdjustMoldQuantityFlag(false);
        dto.setAdjustPlanType(AdjustPlanType.RESOURCE.getCode());
        OperationAdjustResultVO operationAdjustResultVO = adjustPlanStrategyService.execute(dto);
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() ->
                // 刷新workOrder缓存
                SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario)
        );
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationAdjustResultVO);
    }

    @ApiOperation(value = "主计划编辑-模具数量调整")
    @PostMapping(value = "adjustMoldQuantity")
    public BaseResponse<OperationAdjustResultVO> adjustMoldQuantity(@RequestBody RzzAdjustmentParam dto) {
        dto.setAdjustQuantityFlag(false);
        dto.setAdjustMoldQuantityFlag(true);
        dto.setAdjustPlanType(AdjustPlanType.MOLD_QUANTITY.getCode());
        OperationAdjustResultVO operationAdjustResultVO = adjustPlanStrategyService.execute(dto);
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() ->
                // 刷新workOrder缓存
                SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario)
        );
        return BaseResponse.success(BaseResponse.OP_SUCCESS, operationAdjustResultVO);
    }

    @ApiOperation(value = "主计划编辑-批量调整")
    @PostMapping(value = "adjustBatch")
    //    @PermissionMethodPrevention
    public BaseResponse<OperationAdjustResultVO> adjustBatch(@RequestBody RzzAdjustmentParam adjustmentParam) {
        adjustPlanStrategyService.checkStandardPermission(adjustmentParam.getTargetResourceId(), adjustmentParam.getSourceResourceId());
        handworkScheduleService.doHandworkScheduleBatch(adjustmentParam);
        OperationAdjustResultVO resultVO = new OperationAdjustResultVO();
        resultVO.setSuccess(true);
        String scenario = SystemHolder.getScenario();
        CompletableFuture.runAsync(() ->
                // 刷新workOrder缓存
                SpringContextUtils.getBean(CacheSetService.class).refreshWorkOrderCache(scenario)
        );
        return BaseResponse.success(BaseResponse.OP_SUCCESS, resultVO);
    }

    @ApiOperation(value = "变更同步-执行")
    @PostMapping(value = "changeSyncExecute")
    public BaseResponse<Void> changeSyncExecute(@RequestBody RzzAdjustmentParam dto) {
        adjustPlanStrategyService.checkStandardPermission(dto.getSourceResourceId(), dto.getTargetResourceId());
        masterPlanChangeSyncService.doChangeSync(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主计划编辑-校验")
    @PostMapping(value = "updateDataCheck")
    public BaseResponse<Void> updateDataCheck(@RequestBody RzzAdjustmentParam dto) {
        handworkScheduleService.doHandworkScheduleCheck(dto);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "主计划-变更同步")
    @PostMapping(value = "changeSync")
    public BaseResponse<RzzAdjustmentParam> changeSync(@RequestBody DeliveryPlanPublishedCompareDTO dto) {
        adjustPlanStrategyService.checkStandardPermission(null, null);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanChangeSyncService.changeSyncCheck(dto));
    }

    @ApiOperation(value = "编辑资源下拉-根据工序")
    @GetMapping(value = "editResourceDropDown")
    public BaseResponse<List<LabelValue<String>>> editResourceDropDown(
            @RequestParam(value = "operationId") String operationId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, handworkScheduleService.resourceDropDown(operationId));
    }

    @ApiOperation(value = "主计划编辑(报工异常反馈)")
    @PostMapping(value = "doHandworkScheduleForFeedBack")
    public BaseResponse<Void> doHandworkScheduleForFeedBack(@RequestBody List<String> ids) {
        handworkScheduleService.doHandworkScheduleForFeedBack(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "排产资源下拉(资源编码)")
    @GetMapping(value = "physicalResourceDown")
    public BaseResponse<List<LabelValue<String>>> physicalResourceDown() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanService.queryListResourceDropDown());
    }

    @ApiOperation(value = "工序下拉")
    @PostMapping(value = "operationDropdown")
    public BaseResponse<List<LabelValue<String>>> operationDropdown(
            @RequestParam(value = "physicalResourceCode", required = false) String physicalResourceCode) {
        Map<String, Object> params = new HashMap<>();
        params.put("physicalResourceCode", physicalResourceCode);
        List<LabelValue<String>> dataList = masterPlanService.selectOperationDropdown(params);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, dataList);
    }

    @ApiOperation(value = "保存试制单号")
    @PostMapping(value = "doSaveTestOrderNum")
    public BaseResponse<Void> doSaveTestOrderNum(@RequestBody WorkOrderDTO workOrderDTO) {
        masterPlanInsertOrEditService.doSaveTestOrderNum(workOrderDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "根据成型工序数量-计算成品订单数量")
    @PostMapping(value = "calculateWorkOrderQty")
    public BaseResponse<BigDecimal> calculateWorkOrderQty(@RequestBody MasterQuantityReq masterQuantityReq) {
        BigDecimal workOrderQty = masterPlanInsertOrEditService.calculateWorkOrderQty(masterQuantityReq);
        return BaseResponse.success(workOrderQty);
    }

    @ApiOperation(value = "数量调整校验")
    @GetMapping(value = "adjustQtyCheck")
    public BaseResponse<String> adjustQtyCheck(
            @RequestParam(value = "productCode", required = false) String productCode,
            @RequestParam(value = "startTime", required = false) String startTime,
            @RequestParam(value = "quantity", required = false) BigDecimal quantity,
            @RequestParam(value = "oldQuantity", required = false) BigDecimal oldQuantity
    ) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, adjustPlanSupport.adjustQtyCheck(productCode, startTime, quantity, oldQuantity));
    }

    @ApiOperation(value = "下发校验")
    @PostMapping(value = "publishCheck")
    public BaseResponse<List<PublishQtyCheckResultPO>> publishCheck(@RequestBody List<MasterPlanWorkOrderBodyDTO> workOrderList) {
//        return BaseResponse.success(BaseResponse.OP_SUCCESS, adjustPlanSupport.publishQtyCheck(workOrderList));
        return BaseResponse.success(BaseResponse.OP_SUCCESS, Lists.newArrayList());
    }

    @ApiOperation(value = "更新完工数量检查")
    @PostMapping(value = "updateFeedbackCheck")
    public BaseResponse<Void> updateFeedbackCheck(@RequestBody FeedBackReq feedBackReq
    ) {
        masterPlanInsertOrEditService.updateFeedbackCheck(feedBackReq);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "更新完工数量")
    @PostMapping(value = "updateFeedback")
    @BusinessMonitorLog(businessCode = "生产交付&异常跟踪", moduleCode = "MPS", businessFrequency = "DAY")
    public BaseResponse<Void> updateFeedback(@RequestBody FeedBackReq feedBackReq
    ) {
        masterPlanInsertOrEditService.doUpdateFeedback(feedBackReq);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "更新备注")
    @PostMapping(value = "updateRemark")
    public BaseResponse<Void> updateRemark(@RequestBody UpdateRemarkDTO updateRemarkDTO) {
        masterPlanService.doUpdateRemark(updateRemarkDTO);
        return BaseResponse.success();
    }

    @ApiOperation(value = "更新交期")
    @PostMapping(value = "updateDueDate")
    public BaseResponse<Void> updateDueDate(@RequestBody UpdateDueDateDTO updateDueDateDTO) {
        masterPlanService.updateDueDate(updateDueDateDTO);
        return BaseResponse.success();
    }

}