package com.yhl.scp.mps.plan.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.biz.common.enums.ModuleCodeEnum;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.mps.cache.service.CacheSetService;
import com.yhl.scp.mps.plan.dto.MasterPlanVersionDTO;
import com.yhl.scp.mps.plan.dto.MasterPlanWorkOrderBodyDTO;
import com.yhl.scp.mps.plan.service.MasterPlanVersionService;
import com.yhl.scp.mps.plan.vo.MasterPlanDoPublishVO;
import com.yhl.scp.mps.plan.vo.MasterPlanVersionVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * <code>MasterPlanVersionController</code>
 * <p>
 * 主计划发布版本表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 11:50:45
 */
@Slf4j
@Api(tags = "主计划发布版本表控制器")
@RestController
@RequestMapping("masterPlanVersion")
public class MasterPlanVersionController extends BaseController {

    @Resource
    private MasterPlanVersionService masterPlanVersionService;

    @Resource
    protected RedisUtil redisUtil;

    @Resource
    private CacheSetService cacheSetService;

    @Resource
    private RedisTemplate<String, String> redisTemplate;
    @Resource
    private IpsFeign ipsFeign;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<MasterPlanVersionVO>> page() {
        List<MasterPlanVersionVO> masterPlanVersionList = masterPlanVersionService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<MasterPlanVersionVO> pageInfo = new PageInfo<>(masterPlanVersionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody MasterPlanVersionDTO masterPlanVersionDTO) {
        return masterPlanVersionService.doCreate(masterPlanVersionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody MasterPlanVersionDTO masterPlanVersionDTO) {
        return masterPlanVersionService.doUpdate(masterPlanVersionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        masterPlanVersionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<MasterPlanVersionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, masterPlanVersionService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "计划发布")
    @PostMapping(value = "doPublish")
    @BusinessMonitorLog(businessCode = "生产计划发布", moduleCode = "MPS", businessFrequency = "DAY")
    public BaseResponse<Void> doPublish(@RequestBody List<MasterPlanWorkOrderBodyDTO> workOrderList) {
        String scenario = SystemHolder.getScenario();
        taskCheck(workOrderList);
        String publishKey = "MATER_PLAN_DOPUBLISH_" + SystemHolder.getUserId();
        String publishStatusKey = "MATER_PLAN_DOPUBLISH_STATUS_" + SystemHolder.getUserId();
        Boolean success = redisTemplate.opsForValue().setIfAbsent(publishKey, SystemHolder.getUserId(), 60, TimeUnit.MINUTES);

        // 校验锁是否被占用
        if (!Boolean.TRUE.equals(success)) {
            return BaseResponse.error("计划正在发布!");
        }
//        redisUtil.set(publishKey, SystemHolder.getUserId(), 3600);
        BaseResponse<Void> returnResponse;
        try {
            redisUtil.set(publishStatusKey, JSON.toJSONString(new MasterPlanDoPublishVO("WAIT", null)), 3600);
            returnResponse = masterPlanVersionService.publish(workOrderList);
            if (Boolean.FALSE.equals(returnResponse.getSuccess())) {
                // 发布失败
                String msg = returnResponse.getMsg();
                redisUtil.set(publishStatusKey, JSON.toJSONString(new MasterPlanDoPublishVO("FAIL", msg)), 3600);
            }
        } catch (Exception e) {
            StackTraceElement stackTraceElement = e.getStackTrace()[0];
            String errorMsg = e + " at " + stackTraceElement.getClassName() + "."
                    + stackTraceElement.getMethodName() + ":" + stackTraceElement.getLineNumber();
            log.error("主生产计划发布失败：{}", errorMsg);
            redisUtil.set(publishStatusKey, JSON.toJSONString(new MasterPlanDoPublishVO("FAIL", "主生产计划发布失败!")), 3600);
            return BaseResponse.error("主生产计划发布失败:" + e);
        } finally {
            redisUtil.delete(publishKey);
        }
        redisUtil.set(publishStatusKey, JSON.toJSONString(new MasterPlanDoPublishVO("SUCCESS", null)), 3600);
        CompletableFuture.runAsync(() -> {
            // 刷新workOrder缓存
            cacheSetService.refreshWorkOrderCache(scenario);
        });
        return returnResponse;
    }

    private void taskCheck(List<MasterPlanWorkOrderBodyDTO> workOrderList) {
        if(CollectionUtils.isEmpty(workOrderList)){
            return;
        }
        List<AlgorithmLog> algorithmLogs = ipsFeign.selectTaskIsNotFail(Collections.singletonList(ModuleCodeEnum.MPS.getCode()));
        if(CollectionUtils.isEmpty(algorithmLogs)){
            return;
        }
        List<String> productLines = StreamUtils.columnToList(workOrderList, MasterPlanWorkOrderBodyDTO::getResourceCode);
        List<String> runningProductLine = new ArrayList<>();
        for (AlgorithmLog algorithmLog : algorithmLogs) {
            String lineGroup = algorithmLog.getProductLine();
            if (StrUtil.isNotEmpty(lineGroup)) {
                runningProductLine.addAll(Arrays.asList(lineGroup.split(",")));
            }
        }
        List<String> intersection = CollectionUtils.getInterSection(
                productLines, runningProductLine);
        if (CollectionUtils.isNotEmpty(intersection)) {
            throw new BusinessException("产线正在运行排产任务，请稍后再试：" + intersection);
        }
    }

    @ApiOperation(value = "计划发布")
    @GetMapping(value = "getPublishStatus")
    public BaseResponse<String> getPublishStatus() {
        String publishStatusKey = "MATER_PLAN_DOPUBLISH_STATUS_" + SystemHolder.getUserId();
        if (Boolean.TRUE.equals(redisUtil.hasKey(publishStatusKey))) {
            String syncStatusMsg = redisUtil.get(publishStatusKey).toString();
            MasterPlanDoPublishVO doPublishStatus = JSON.parseObject(syncStatusMsg, MasterPlanDoPublishVO.class);
            if ("FAIL".equals(doPublishStatus.getStatus())) {
                redisUtil.delete(publishStatusKey);
                return BaseResponse.error(doPublishStatus.getMsg());
            } else if ("WAIT".equals(doPublishStatus.getStatus())) {
                return BaseResponse.success(BaseResponse.OP_SUCCESS, "WAIT");
            }
            redisUtil.delete(publishStatusKey);
        }
        // 校验锁是否被占用
        return BaseResponse.success(BaseResponse.OP_SUCCESS, "SUCCESS");
    }

}
