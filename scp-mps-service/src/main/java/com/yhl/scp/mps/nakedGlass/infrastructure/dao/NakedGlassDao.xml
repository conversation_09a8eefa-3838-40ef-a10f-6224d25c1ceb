<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.mps.nakedGlass.infrastructure.dao.NakedGlassDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO">
        <!--@Table mps_naked_glass-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="raw_item_id" jdbcType="VARCHAR" property="rawItemId"/>
        <result column="raw_material" jdbcType="VARCHAR" property="rawMaterial"/>
        <result column="supplier_ou" jdbcType="VARCHAR" property="supplierOu"/>
        <result column="supplier_code" jdbcType="VARCHAR" property="supplierCode"/>
        <result column="transit_warehouse" jdbcType="VARCHAR" property="transitWarehouse"/>
        <result column="semi_finished_product" jdbcType="VARCHAR" property="semiFinishedProduct"/>
        <result column="finished_product" jdbcType="VARCHAR" property="finishedProduct"/>
        <result column="work_type" jdbcType="VARCHAR" property="workType"/>
        <result column="sal_id" jdbcType="VARCHAR" property="salId"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.mps.nakedGlass.vo.NakedGlassVO">
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
        id,raw_item_id,raw_material,supplier_ou,supplier_code,transit_warehouse,semi_finished_product,finished_product,work_type,sal_id,creator,create_time,modifier,modify_time,enabled,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.rawItemId != null and params.rawItemId != ''">
                and raw_item_id = #{params.rawItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.rawMaterial != null and params.rawMaterial != ''">
                and raw_material = #{params.rawMaterial,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierOu != null and params.supplierOu != ''">
                and supplier_ou = #{params.supplierOu,jdbcType=VARCHAR}
            </if>
            <if test="params.supplierCode != null and params.supplierCode != ''">
                and supplier_code = #{params.supplierCode,jdbcType=VARCHAR}
            </if>
            <if test="params.transitWarehouse != null and params.transitWarehouse != ''">
                and transit_warehouse = #{params.transitWarehouse,jdbcType=VARCHAR}
            </if>
            <if test="params.semiFinishedProduct != null and params.semiFinishedProduct != ''">
                and semi_finished_product = #{params.semiFinishedProduct,jdbcType=VARCHAR}
            </if>
            <if test="params.finishedProduct != null and params.finishedProduct != ''">
                and finished_product = #{params.finishedProduct,jdbcType=VARCHAR}
            </if>
            <if test="params.workType != null and params.workType != ''">
                and work_type = #{params.workType,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.salId != null and params.salId != ''">
                and sal_id = #{params.salId,jdbcType=VARCHAR}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_naked_glass
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_naked_glass
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from mps_naked_glass
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from mps_naked_glass
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectNakedGlassByFinishedProducts" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from mps_naked_glass
        where finished_product in
        <foreach collection="productCodes" item="productCode" index="index" open="(" separator="," close=")">
            #{productCode,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into mps_naked_glass(
        id,
        raw_item_id,
        raw_material,
        supplier_ou,
        supplier_code,
        transit_warehouse,
        semi_finished_product,
        finished_product,
        work_type,
        sal_id,
        creator,
        create_time,
        modifier,
        modify_time,
        enabled,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{rawItemId,jdbcType=VARCHAR},
        #{rawMaterial,jdbcType=VARCHAR},
        #{supplierOu,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{transitWarehouse,jdbcType=VARCHAR},
        #{semiFinishedProduct,jdbcType=VARCHAR},
        #{finishedProduct,jdbcType=VARCHAR},
        #{workType,jdbcType=VARCHAR},
        #{salId,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO">
        insert into mps_naked_glass(
        id,
        raw_item_id,
        raw_material,
        supplier_ou,
        supplier_code,
        transit_warehouse,
        semi_finished_product,
        finished_product,
        work_type,
        sal_id,
        creator,
        create_time,
        modifier,
        modify_time,
        enabled,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{rawItemId,jdbcType=VARCHAR},
        #{rawMaterial,jdbcType=VARCHAR},
        #{supplierOu,jdbcType=VARCHAR},
        #{supplierCode,jdbcType=VARCHAR},
        #{transitWarehouse,jdbcType=VARCHAR},
        #{semiFinishedProduct,jdbcType=VARCHAR},
        #{finishedProduct,jdbcType=VARCHAR},
        #{workType,jdbcType=VARCHAR},
        #{salId,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into mps_naked_glass(
        id,
        raw_item_id,
        raw_material,
        supplier_ou,
        supplier_code,
        transit_warehouse,
        semi_finished_product,
        finished_product,
        work_type,
        sal_id,
        creator,
        create_time,
        modifier,
        modify_time,
        enabled,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.rawItemId,jdbcType=VARCHAR},
        #{entity.rawMaterial,jdbcType=VARCHAR},
        #{entity.supplierOu,jdbcType=VARCHAR},
        #{entity.supplierCode,jdbcType=VARCHAR},
        #{entity.transitWarehouse,jdbcType=VARCHAR},
        #{entity.semiFinishedProduct,jdbcType=VARCHAR},
        #{entity.finishedProduct,jdbcType=VARCHAR},
        #{entity.workType,jdbcType=VARCHAR},
        #{entity.salId,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into mps_naked_glass(
        id,
        raw_item_id,
        raw_material,
        supplier_ou,
        supplier_code,
        transit_warehouse,
        semi_finished_product,
        finished_product,
        work_type,
        sal_id,
        creator,
        create_time,
        modifier,
        modify_time,
        enabled,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.rawItemId,jdbcType=VARCHAR},
        #{entity.rawMaterial,jdbcType=VARCHAR},
        #{entity.supplierOu,jdbcType=VARCHAR},
        #{entity.supplierCode,jdbcType=VARCHAR},
        #{entity.transitWarehouse,jdbcType=VARCHAR},
        #{entity.semiFinishedProduct,jdbcType=VARCHAR},
        #{entity.finishedProduct,jdbcType=VARCHAR},
        #{entity.workType,jdbcType=VARCHAR},
        #{entity.salId,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO">
        update mps_naked_glass set
        raw_item_id = #{rawItemId,jdbcType=VARCHAR},
        raw_material = #{rawMaterial,jdbcType=VARCHAR},
        supplier_ou = #{supplierOu,jdbcType=VARCHAR},
        supplier_code = #{supplierCode,jdbcType=VARCHAR},
        transit_warehouse = #{transitWarehouse,jdbcType=VARCHAR},
        semi_finished_product = #{semiFinishedProduct,jdbcType=VARCHAR},
        finished_product = #{finishedProduct,jdbcType=VARCHAR},
        work_type = #{workType,jdbcType=VARCHAR},
        sal_id = #{salId,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        enabled = #{enabled,jdbcType=VARCHAR},
        version_value = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.mps.nakedGlass.infrastructure.po.NakedGlassPO">
        update mps_naked_glass
        <set>
            <if test="item.rawItemId != null and item.rawItemId != ''">
                raw_item_id = #{item.rawItemId,jdbcType=VARCHAR},
            </if>
            <if test="item.rawMaterial != null and item.rawMaterial != ''">
                raw_material = #{item.rawMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierOu != null and item.supplierOu != ''">
                supplier_ou = #{item.supplierOu,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transitWarehouse != null and item.transitWarehouse != ''">
                transit_warehouse = #{item.transitWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="item.semiFinishedProduct != null and item.semiFinishedProduct != ''">
                semi_finished_product = #{item.semiFinishedProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedProduct != null and item.finishedProduct != ''">
                finished_product = #{item.finishedProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.workType != null and item.workType != ''">
                work_type = #{item.workType,jdbcType=VARCHAR},
            </if>
            <if test="item.salId != null and item.salId != ''">
                sal_id = #{item.salId,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update mps_naked_glass
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="raw_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawItemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="raw_material = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.rawMaterial,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_ou = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierOu,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supplier_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplierCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transit_warehouse = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transitWarehouse,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="semi_finished_product = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.semiFinishedProduct,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="finished_product = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.finishedProduct,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.workType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="sal_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.salId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update mps_naked_glass 
        <set>
            <if test="item.rawItemId != null and item.rawItemId != ''">
                raw_item_id = #{item.rawItemId,jdbcType=VARCHAR},
            </if>
            <if test="item.rawMaterial != null and item.rawMaterial != ''">
                raw_material = #{item.rawMaterial,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierOu != null and item.supplierOu != ''">
                supplier_ou = #{item.supplierOu,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null and item.supplierCode != ''">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transitWarehouse != null and item.transitWarehouse != ''">
                transit_warehouse = #{item.transitWarehouse,jdbcType=VARCHAR},
            </if>
            <if test="item.semiFinishedProduct != null and item.semiFinishedProduct != ''">
                semi_finished_product = #{item.semiFinishedProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.finishedProduct != null and item.finishedProduct != ''">
                finished_product = #{item.finishedProduct,jdbcType=VARCHAR},
            </if>
            <if test="item.workType != null and item.workType != ''">
                work_type = #{item.workType,jdbcType=VARCHAR},
            </if>
            <if test="item.salId != null and item.salId != ''">
                sal_id = #{item.salId,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from mps_naked_glass where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from mps_naked_glass where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
