package com.yhl.scp.mps.excel.service.impl;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.common.excel.DataImportInfo;
import com.yhl.scp.common.excel.ImportDataTypeEnum;
import com.yhl.scp.common.excel.model.ImportAnalysisResultHolder;
import com.yhl.scp.common.excel.model.ImportContext;
import com.yhl.scp.common.excel.model.ImportRelatedDataHolder;
import com.yhl.scp.common.excel.service.AbstractExcelService;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mds.basic.resource.vo.PhysicalResourceBasicVO;
import com.yhl.scp.mds.extension.resource.vo.PhysicalResourceVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.dynamicDeliveryTracking.convertor.DynamicDeliveryTrackingConvertor;
import com.yhl.scp.mps.dynamicDeliveryTracking.dto.DynamicDeliveryTrackingJCDTO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.dao.DynamicDeliveryTrackingDao;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingSubTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.infrastructure.po.DynamicDeliveryTrackingTaskPO;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.DynamicDeliveryTrackingService;
import com.yhl.scp.mps.dynamicDeliveryTracking.service.impl.DynamicDeliveryTrackingServiceImpl;
import com.yhl.scp.mps.dynamicDeliveryTracking.support.DynamicDeliveryTrackingExcelSupport;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingTaskVO;
import com.yhl.scp.mps.dynamicDeliveryTracking.vo.DynamicDeliveryTrackingVO;
import com.yhl.scp.mps.enums.TrackingStatusEnum;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DynamicDeliveryTrackingJCExcelService</code>
 * <p>
 * 动态追踪-夹层导入
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-03 10:36:51
 */
@Service
public class DynamicDeliveryTrackingJCExcelService extends AbstractExcelService<DynamicDeliveryTrackingJCDTO, DynamicDeliveryTrackingPO, DynamicDeliveryTrackingVO> {

    @Resource
    private DynamicDeliveryTrackingDao dynamicDeliveryTrackingDao;
    @Resource
    private DynamicDeliveryTrackingService dynamicDeliveryTrackingService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DynamicDeliveryTrackingExcelSupport dynamicDeliveryTrackingExcelSupport;

    @Override
    public BaseDao<DynamicDeliveryTrackingPO, DynamicDeliveryTrackingVO> getBaseDao() {
        return dynamicDeliveryTrackingDao;
    }

    @Override
    public Function<DynamicDeliveryTrackingJCDTO, DynamicDeliveryTrackingPO> getDTO2POConvertor() {
        return DynamicDeliveryTrackingConvertor.INSTANCE::jcDto2Po;
    }

    @Override
    public Class<DynamicDeliveryTrackingJCDTO> getDTOClass() {
        return DynamicDeliveryTrackingJCDTO.class;
    }

    @Override
    public BaseService<DynamicDeliveryTrackingJCDTO, DynamicDeliveryTrackingVO> getBaseService() {
        return null;
    }

    @Override
    protected void fillIdForUpdateData(List<DynamicDeliveryTrackingJCDTO> updateList, Map<String, DynamicDeliveryTrackingPO> existingDataMap) {
        for (DynamicDeliveryTrackingJCDTO trackingJCDTO : updateList) {
            DynamicDeliveryTrackingPO trackingPO = existingDataMap.get(trackingJCDTO.getProductCode() + "&" +
                    DateUtils.dateToString(trackingJCDTO.getDeliveryTime(), DateUtils.COMMON_DATE_STR4));
            if (trackingPO != null) {
                trackingJCDTO.setId(trackingPO.getId());
            }
        }
    }

    @Override
    protected ImportRelatedDataHolder<DynamicDeliveryTrackingPO> prepareData(List<DynamicDeliveryTrackingJCDTO> dynamicDeliveryTrackingJCDTOS) {
        return dynamicDeliveryTrackingExcelSupport.getRelatedDataHolder();
    }

    @Override
    protected void doInsert(ImportAnalysisResultHolder<DynamicDeliveryTrackingJCDTO, DynamicDeliveryTrackingPO> resultHolder, ImportContext importContext) {
        List<DynamicDeliveryTrackingJCDTO> jcInsertList = resultHolder.getInsertList();
        List<DynamicDeliveryTrackingJCDTO> jcUpdateList = resultHolder.getUpdateList();
        List<DynamicDeliveryTrackingPO> deteleList = resultHolder.getDeleteList();
        List<DataImportInfo> importLogList = resultHolder.getImportLogList();
        // 额外逻辑，重新生产工序任务和工序任务明细
        List<String> productCodeList = jcInsertList.stream().map(DynamicDeliveryTrackingJCDTO::getProductCode).distinct().collect(Collectors.toList());
        productCodeList.addAll(jcUpdateList.stream().map(DynamicDeliveryTrackingJCDTO::getProductCode).distinct().collect(Collectors.toList()));
        productCodeList = productCodeList.stream().distinct().collect(Collectors.toList());
        List<DynamicDeliveryTrackingVO> trackingVOList = dynamicDeliveryTrackingService.buildTrackingByProductCodeList(productCodeList);
        Map<String, List<DynamicDeliveryTrackingVO>> map = trackingVOList.stream()
                .collect(Collectors.groupingBy(DynamicDeliveryTrackingVO::getProductCode));
        // 进仓工序
        String warehousedOperation = dynamicDeliveryTrackingExcelSupport.getWarehousedOperation();
        // 进仓工序资源
        String warehousedOperationResource = dynamicDeliveryTrackingExcelSupport.getWarehousedOperationResource();
        // 发货工序
        String deliveryOperation = dynamicDeliveryTrackingExcelSupport.getDeliveryOperation();
        // 发货产线
        String deliveryResource = dynamicDeliveryTrackingExcelSupport.getDeliveryResource();
        // 无论新增还是更新，它原来下面挂的task和subTask都要删除，重新生成
        List<DynamicDeliveryTrackingJCDTO> dataList = new ArrayList<>();
        dataList.addAll(jcInsertList);
        dataList.addAll(jcUpdateList);
        Map<String, List<DynamicDeliveryTrackingTaskVO>> taskMap = new HashMap<>();
        List<DynamicDeliveryTrackingTaskPO> taskInsertList = new ArrayList<>();
        List<DynamicDeliveryTrackingSubTaskPO> subTaskInsertList = new ArrayList<>();
        List<String> removeList = new ArrayList<>();
        for (DynamicDeliveryTrackingJCDTO trackingJCDTO : dataList) {
            if (StringUtils.isEmpty(trackingJCDTO.getId())) {
                trackingJCDTO.setId(UUIDUtil.getUUID());
            }
            String productCode = trackingJCDTO.getProductCode();
            List<DynamicDeliveryTrackingVO> trackingVOS = map.get(productCode);
            if (CollectionUtils.isNotEmpty(trackingVOS)) {
                DynamicDeliveryTrackingVO trackingVO = trackingVOS.get(0);
                trackingJCDTO.setProductId(trackingVO.getProductId());
                trackingJCDTO.setTrackingStatus(TrackingStatusEnum.PUBLISHED.getCode());
                trackingVO.setId(trackingJCDTO.getId());
                trackingVO.setInWarehousedTime(trackingJCDTO.getInWarehousedTime());
                trackingVO.setInWarehousedQuantity(trackingJCDTO.getInWarehousedQuantity());
                if (CollectionUtils.isNotEmpty(trackingVO.getTaskVOS())) {
                    Map<String, DynamicDeliveryTrackingTaskVO> taskVOMap = trackingVO.getTaskVOS().stream()
                            .collect(Collectors.toMap(DynamicDeliveryTrackingTaskVO::getStandardStepName, Function.identity()));

                    dynamicDeliveryTrackingExcelSupport.processStep(taskVOMap, "预处理", trackingJCDTO.getYclResourceId(), trackingJCDTO.getYclQty(),
                            trackingJCDTO.getYclStartTime(), importLogList, taskInsertList, taskMap, subTaskInsertList,
                            trackingJCDTO.getId(), trackingJCDTO.getRowIndex());
                    dynamicDeliveryTrackingExcelSupport.processStep(taskVOMap, "镀膜", trackingJCDTO.getDmResourceId(), trackingJCDTO.getDmQty(),
                            trackingJCDTO.getDmStartTime(), importLogList, taskInsertList, taskMap, subTaskInsertList,
                            trackingJCDTO.getId(), trackingJCDTO.getRowIndex());
                    dynamicDeliveryTrackingExcelSupport.processStep(taskVOMap, "压制", trackingJCDTO.getCxResourceId(), trackingJCDTO.getCxQty(),
                            trackingJCDTO.getCxStartTime(), importLogList, taskInsertList, taskMap, subTaskInsertList,
                            trackingJCDTO.getId(), trackingJCDTO.getRowIndex());
                    dynamicDeliveryTrackingExcelSupport.processStep(taskVOMap, "合片", trackingJCDTO.getHpResourceId(), trackingJCDTO.getHpQty(),
                            trackingJCDTO.getHpStartTime(), importLogList, taskInsertList, taskMap, subTaskInsertList,
                            trackingJCDTO.getId(), trackingJCDTO.getRowIndex());
                    dynamicDeliveryTrackingExcelSupport.processStep(taskVOMap, "包装", trackingJCDTO.getBzResourceId(), trackingJCDTO.getBzQty(),
                            trackingJCDTO.getBzStartTime(), importLogList, taskInsertList, taskMap, subTaskInsertList,
                            trackingJCDTO.getId(), trackingJCDTO.getRowIndex());

                    // 后处理进仓工序
                    DynamicDeliveryTrackingServiceImpl.afterHandleWarehouseOperation(taskMap.get(trackingJCDTO.getId()), warehousedOperation, warehousedOperationResource, taskInsertList,
                            new ArrayList<>(), subTaskInsertList, new ArrayList<>(), trackingVO, deliveryOperation, deliveryResource);
                }else {
                    removeList.add(trackingJCDTO.getId());
                }
            }
        }

        for (Map.Entry<String, List<DynamicDeliveryTrackingTaskVO>> entry : taskMap.entrySet()) {
            if (CollectionUtils.isEmpty(entry.getValue())) {
                removeList.add(entry.getKey());
            }
        }
        jcInsertList.removeIf(t -> removeList.contains(t.getId()));
        jcUpdateList.removeIf(t -> removeList.contains(t.getId()));
        List<DynamicDeliveryTrackingPO> insertList = new ArrayList<>();
        List<DynamicDeliveryTrackingPO> updateList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(jcInsertList)) {
            insertList = jcInsertList.stream().map(DynamicDeliveryTrackingConvertor.INSTANCE::jcDto2Po).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(jcUpdateList)) {
            updateList= jcUpdateList.stream().map(DynamicDeliveryTrackingConvertor.INSTANCE::jcDto2Po).collect(Collectors.toList());
        }
        List<String> trackingIds = dataList.stream().map(DynamicDeliveryTrackingJCDTO::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(insertList) || CollectionUtils.isNotEmpty(updateList)) {
            // 数据处理
            dynamicDeliveryTrackingExcelSupport.doDataProcess(insertList, updateList, subTaskInsertList, trackingIds, deteleList, taskInsertList);
        }
    }


    @Override
    protected void specialVerification(ImportAnalysisResultHolder<DynamicDeliveryTrackingJCDTO, DynamicDeliveryTrackingPO> resultHolder, ImportContext importContext) {
        List<DynamicDeliveryTrackingJCDTO> insertList = resultHolder.getInsertList();
        List<DynamicDeliveryTrackingJCDTO> updateList = resultHolder.getUpdateList();
        List<DataImportInfo> importLogList = resultHolder.getImportLogList();
        List<String> ids = resultHolder.getUpdateList().stream().map(DynamicDeliveryTrackingJCDTO::getId).collect(Collectors.toList());
        List<DynamicDeliveryTrackingPO> dynamicDeliveryTrackingList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(ids)) {
            dynamicDeliveryTrackingList = dynamicDeliveryTrackingDao.selectByPrimaryKeys(ids);
        }

        Map<String, DynamicDeliveryTrackingPO> trackingMap = dynamicDeliveryTrackingList.stream()
                .collect(Collectors.toMap(BasePO::getId, Function.identity()));

        // 校验选择产线是否存在
        List<PhysicalResourceVO> physicalResourceVOS = newMdsFeign.selectAllPhysicalResource(SystemHolder.getScenario());
        Map<String, String> resourceMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(PhysicalResourceBasicVO::getPhysicalResourceCode, BaseVO::getId));
        verifyPaternity(insertList, resourceMap, trackingMap, importLogList);
        verifyPaternity(updateList, resourceMap, trackingMap, importLogList);
    }

    private void verifyPaternity(List<DynamicDeliveryTrackingJCDTO> checkList,
                                 Map<String, String> resourceMap,
                                 Map<String, DynamicDeliveryTrackingPO> trackingMap,
                                 List<DataImportInfo> importLogList) {
        Iterator<DynamicDeliveryTrackingJCDTO> iterator = checkList.iterator();
        while (iterator.hasNext()) {
            DynamicDeliveryTrackingJCDTO trackingJCDTO = iterator.next();
            String remark = null;
            boolean error = false;
            if (StringUtils.isNotEmpty(trackingJCDTO.getYclResourceId()) && !resourceMap.containsKey(trackingJCDTO.getYclResourceId())) {
                remark = "行数：" + trackingJCDTO.getRowIndex() + ";产线编码" + trackingJCDTO.getYclResourceId() + "不存在";
                error = true;
            } else {
                trackingJCDTO.setYclResourceId(resourceMap.get(trackingJCDTO.getYclResourceId()));
            }
            if (StringUtils.isNotEmpty(trackingJCDTO.getDmResourceId()) && !resourceMap.containsKey(trackingJCDTO.getDmResourceId())) {
                remark = "行数：" + trackingJCDTO.getRowIndex() + ";产线编码" + trackingJCDTO.getDmResourceId() + "不存在";
                error = true;
            } else {
                trackingJCDTO.setDmResourceId(resourceMap.get(trackingJCDTO.getDmResourceId()));
            }
            if (StringUtils.isNotEmpty(trackingJCDTO.getCxResourceId()) && !resourceMap.containsKey(trackingJCDTO.getCxResourceId())) {
                remark = "行数：" + trackingJCDTO.getRowIndex() + ";产线编码" + trackingJCDTO.getCxResourceId() + "不存在";
                error = true;
            } else {
                trackingJCDTO.setCxResourceId(resourceMap.get(trackingJCDTO.getCxResourceId()));
            }
            if (StringUtils.isNotEmpty(trackingJCDTO.getHpResourceId()) && !resourceMap.containsKey(trackingJCDTO.getHpResourceId())) {
                remark = "行数：" + trackingJCDTO.getRowIndex() + ";产线编码" + trackingJCDTO.getHpResourceId() + "不存在";
                error = true;
            } else {
                trackingJCDTO.setHpResourceId(resourceMap.get(trackingJCDTO.getHpResourceId()));
            }
            if (StringUtils.isNotEmpty(trackingJCDTO.getBzResourceId()) && !resourceMap.containsKey(trackingJCDTO.getBzResourceId())) {
                remark = "行数：" + trackingJCDTO.getRowIndex() + ";产线编码" + trackingJCDTO.getBzResourceId() + "不存在";
                error = true;
            } else {
                trackingJCDTO.setBzResourceId(resourceMap.get(trackingJCDTO.getBzResourceId()));
            }
            if (StringUtils.isNotEmpty(trackingJCDTO.getId()) && trackingMap.containsKey(trackingJCDTO.getId())) {
                // 已开始的不允许修改
                DynamicDeliveryTrackingPO trackingPO = trackingMap.get(trackingJCDTO.getId());
                if (TrackingStatusEnum.IN_EXECUTION.getCode().equals(trackingPO.getTrackingStatus())) {
                    remark = "行数：" + trackingJCDTO.getRowIndex() + ";已有报工数据，不允许修改";
                    error = true;
                }
            }
            if (error) {
                DataImportInfo dataImportInfo = new DataImportInfo();
                dataImportInfo.setInfoType(ImportDataTypeEnum.ERROR.getCode());
                dataImportInfo.setRemark(remark);
                dataImportInfo.setDisplayIndex(trackingJCDTO.getRowIndex());
                importLogList.add(dataImportInfo);
                iterator.remove();
            }
        }
    }


    @Override
    protected List<DynamicDeliveryTrackingJCDTO> getCustomizedExampleData() {
        DynamicDeliveryTrackingJCDTO jcDto = new DynamicDeliveryTrackingJCDTO();
        jcDto.setProductCode("例：xxx");
        jcDto.setDeliveryTime(new Date());
        jcDto.setInWarehousedQuantity(BigDecimal.ONE);
        jcDto.setInWarehousedTime(new Date());
        jcDto.setYclQty(BigDecimal.ONE);
        jcDto.setYclResourceId("S2XL01");
        jcDto.setYclStartTime(new Date());
        return Collections.singletonList(jcDto);
    }
}
