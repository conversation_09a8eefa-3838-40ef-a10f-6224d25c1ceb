package com.yhl.scp.mps.manualAdjust.support;

import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ams.extension.schedule.dto.AdjustmentParam;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.MdsFeign;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mps.manualAdjust.dao.ManualAdjustHandleDao;
import com.yhl.scp.mps.manualAdjust.dto.ManualAdjustParam;
import com.yhl.scp.mps.manualAdjust.service.impl.ManualAdjustAlgorithmService;
import com.yhl.scp.mps.plan.infrastructure.dao.OperationTaskExtDao;
import com.yhl.scp.mps.plan.support.MasterPlanSplitSupport;
import com.yhl.scp.sds.order.infrastructure.dao.*;
import com.yhl.scp.sds.order.service.OperationService;
import com.yhl.scp.sds.order.service.WorkOrderService;
import com.yhl.scp.sds.pegging.infrastructure.dao.DemandDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.FulfillmentDao;
import com.yhl.scp.sds.pegging.infrastructure.dao.SupplyDao;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * 手工调整抽象类
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class ManualAdjustSupport {

  /** 限制资源使用人 */
  public static final String RESOURCE_LOCK_KEY_PREFIX = "adjust_resource_operate_user_id:%s";

  /** 运行算法锁 */
  public static final String ALGORITHM_RUNNING_LOCK_KEY_PREFIX =
      "adjust_algorithm_running_lock_key:%s";

  /** 算法参数锁 */
  public static final String ALGORITHM_RUNNING_PARAM_KEY_PREFIX =
      "adjust_algorithm_running_param_key:%s";

  /** 延迟请求缓存 */
  public static final String REQUEST_DEFERRED_MAP_KEY = "adjust_request_deferred_key:%s";

  /** 请求次数 */
  public static final String REQUEST_QUANTITY_LOCK_KEY_PREFIX =
      "adjust_request_quantity_lock_key_prefix:%s";

  @Resource public ManualAdjustHandleDao manualAdjustHandleDao;

  @Resource public MdsFeign mdsFeign;

  @Resource public OperationDao operationDao;

  @Resource public OperationTaskDao operationTaskDao;

  @Resource public OperationSubTaskDao operationSubTaskDao;

  @Resource public WorkOrderDao workOrderDao;

  @Resource public NewMdsFeign newMdsFeign;

  @Resource public DemandDao demandDao;

  @Resource public SupplyDao supplyDao;

  @Resource public FulfillmentDao fulfillmentDao;

  @Resource public OperationExtendDao operationExtendDao;

  @Resource public OperationInputDao operationInputDao;

  @Resource public OperationOutputDao operationOutputDao;

  @Resource public MasterPlanSplitSupport masterPlanSplitSupport;

  @Resource public OperationTaskExtDao operationTaskExtDao;

  @Resource public OperationService operationService;

  @Resource public WorkOrderService workOrderService;

  @Resource public ManualAdjustAlgorithmService manualAdjustAlgorithmService;

  protected abstract String getCommand();

  protected abstract BaseResponse<Void> verify(ManualAdjustParam param);

  protected abstract BaseResponse<Void> executeBackEnd(ManualAdjustParam param);

  protected abstract List<AdjustmentParam> executeAlgorithm(
      PlanningHorizonVO planningHorizon, ManualAdjustParam param);
}
