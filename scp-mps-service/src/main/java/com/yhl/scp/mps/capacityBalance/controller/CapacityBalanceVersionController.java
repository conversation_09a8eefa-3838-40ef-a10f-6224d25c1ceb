package com.yhl.scp.mps.capacityBalance.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import com.yhl.scp.mps.capacityBalance.dto.CapacityBalanceVersionDTO;
import com.yhl.scp.mps.capacityBalance.service.CapacityBalanceVersionService;
import com.yhl.scp.mps.capacityBalance.vo.CapacityBalanceVersionVO;
import com.yhl.scp.mps.capacityBalance.vo.CapacityLoadVO4;
import com.yhl.scp.mps.capacityBalance.vo.CapacitySupplyRelationshipVO2;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CapacityBalanceVersionController</code>
 * <p>
 * 产能平衡版本控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-16 14:16:50
 */
@Slf4j
@Api(tags = "产能平衡版本控制器")
@RestController
@RequestMapping("capacityBalanceVersion")
public class CapacityBalanceVersionController extends BaseController {

    @Resource
    private CapacityBalanceVersionService capacityBalanceVersionService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<CapacityBalanceVersionVO>> page() {
        List<CapacityBalanceVersionVO> capacityBalanceVersionList = capacityBalanceVersionService
                .selectByPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<CapacityBalanceVersionVO> pageInfo = new PageInfo<>(capacityBalanceVersionList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        return capacityBalanceVersionService.doCreate(capacityBalanceVersionDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody CapacityBalanceVersionDTO capacityBalanceVersionDTO) {
        return capacityBalanceVersionService.doUpdate(capacityBalanceVersionDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        capacityBalanceVersionService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<CapacityBalanceVersionVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "publishVersion")
    @BusinessMonitorLog(businessCode = "产能平衡发布", moduleCode = "MPS", businessFrequency = "MONTH")
    public BaseResponse<String> publishVersion() {
        return capacityBalanceVersionService.publishVersionLock();
    }

    @ApiOperation(value = "根据预测月份获取最新版本号")
    @GetMapping(value = "getLatestVersionCode")
    public BaseResponse<CapacityBalanceVersionVO> selectLatestVersionCode() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService.selectLatestVersionCode());
    }

    @ApiOperation(value = "周产能平衡最新版本时间")
    @GetMapping(value = "weekMaxVersionTime")
    public BaseResponse<String> weekMaxVersionTime() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService.selectWeekMaxVersionTime());
    }

    @ApiOperation(value = "【产能平衡-负荷】 版本数据对比")
    @GetMapping(value = "contrastCapacityLoad")
    public BaseResponse<List<CapacityLoadVO4>> contrastCapacityLoad(
            @ApiParam(value = "版本ids") @RequestParam(value = "versionIds") List<String> versionIds,
            @ApiParam(value = "工厂编码") @RequestParam(value = "plantCode", required = false) String plantCode,
            @ApiParam(value = "工序编码") @RequestParam(value = "operationCode", required = false) String operationCode,
            @ApiParam(value = "工序名称") @RequestParam(value = "operationName", required = false) String operationName,
            @ApiParam(value = "设备组") @RequestParam(value = "resourceGroupCode", required = false) String resourceGroupCode,
            @ApiParam(value = "设备") @RequestParam(value = "resourceCode", required = false) String resourceCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService
                .contrastCapacityLoad(versionIds, plantCode, operationCode, operationName, resourceGroupCode,
                        resourceCode));
    }

    @ApiOperation(value = "【产能平衡-产能明细】版本数据对比")
    @GetMapping(value = "contrastCapacitySupplyRelationship")
    public BaseResponse<List<CapacitySupplyRelationshipVO2>> contrastCapacitySupplyRelationship(
            @ApiParam(value = "版本ids") @RequestParam(value = "versionIds") List<String> versionIds,
            @ApiParam(value = "工序编码") @RequestParam(value = "operationCode", required = false) String operationCode,
            @ApiParam(value = "工序名称") @RequestParam(value = "operationName", required = false) String operationName,
            @ApiParam(value = "需求时间-开始时间") @RequestParam(value = "supplyTimeStart", required = false) String supplyTimeStart,
            @ApiParam(value = "需求时间-结束时间") @RequestParam(value = "supplyTimeEnd", required = false) String supplyTimeEnd,
            @ApiParam(value = "本厂编码") @RequestParam(value = "productCode", required = false) String productCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService
                .contrastCapacitySupplyRelationship(versionIds, operationCode, operationName, supplyTimeStart,
                        supplyTimeEnd, productCode));
    }

    @ApiOperation(value = "版本号下拉")
    @GetMapping(value = "versionDropdown")
    public BaseResponse<List<LabelValue<String>>> versionDropdown() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService.versionDropdown());
    }

    @ApiOperation(value = "根据版本号获取预测信息")
    @GetMapping(value = "selectVersionInfoByVersionCode")
    public BaseResponse<CapacityBalanceVersionVO> selectVersionInfoByVersionCode(
            @ApiParam(value = "版本号") @RequestParam(value = "versionCode", required = false) String versionCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, capacityBalanceVersionService.selectVersionInfoByVersionCode(versionCode));
    }

}