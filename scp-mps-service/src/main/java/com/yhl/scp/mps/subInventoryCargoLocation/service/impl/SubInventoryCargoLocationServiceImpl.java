package com.yhl.scp.mps.subInventoryCargoLocation.service.impl;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesSubInventoryCargoLocation;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.enums.ObjectTypeEnum;
import com.yhl.scp.mps.subInventoryCargoLocation.convertor.SubInventoryCargoLocationConvertor;
import com.yhl.scp.mps.subInventoryCargoLocation.domain.entity.SubInventoryCargoLocationDO;
import com.yhl.scp.mps.subInventoryCargoLocation.domain.service.SubInventoryCargoLocationDomainService;
import com.yhl.scp.mps.subInventoryCargoLocation.dto.SubInventoryCargoLocationDTO;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.dao.SubInventoryCargoLocationDao;
import com.yhl.scp.mps.subInventoryCargoLocation.infrastructure.po.SubInventoryCargoLocationPO;
import com.yhl.scp.mps.subInventoryCargoLocation.service.SubInventoryCargoLocationService;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;

import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class SubInventoryCargoLocationServiceImpl extends AbstractService implements SubInventoryCargoLocationService {

    @Resource
    private SubInventoryCargoLocationDao subInventoryCargoLocationDao;

    @Resource
    private SubInventoryCargoLocationDomainService subInventoryCargoLocationDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private SubInventoryCargoLocationService subInventoryCargoLocationService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(SubInventoryCargoLocationDTO subInventoryCargoLocationDTO) {
        // 0.数据转换
        SubInventoryCargoLocationDO subInventoryCargoLocationDO = SubInventoryCargoLocationConvertor.INSTANCE.dto2Do(subInventoryCargoLocationDTO);
        SubInventoryCargoLocationPO subInventoryCargoLocationPO = SubInventoryCargoLocationConvertor.INSTANCE.dto2Po(subInventoryCargoLocationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        subInventoryCargoLocationDomainService.validation(subInventoryCargoLocationDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(subInventoryCargoLocationPO);
        subInventoryCargoLocationDao.insert(subInventoryCargoLocationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(SubInventoryCargoLocationDTO subInventoryCargoLocationDTO) {
        // 0.数据转换
        SubInventoryCargoLocationDO subInventoryCargoLocationDO = SubInventoryCargoLocationConvertor.INSTANCE.dto2Do(subInventoryCargoLocationDTO);
        SubInventoryCargoLocationPO subInventoryCargoLocationPO = SubInventoryCargoLocationConvertor.INSTANCE.dto2Po(subInventoryCargoLocationDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        subInventoryCargoLocationDomainService.validation(subInventoryCargoLocationDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(subInventoryCargoLocationPO);
        subInventoryCargoLocationDao.update(subInventoryCargoLocationPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<SubInventoryCargoLocationDTO> list) {
        List<SubInventoryCargoLocationPO> newList = SubInventoryCargoLocationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        subInventoryCargoLocationDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<SubInventoryCargoLocationDTO> list) {
        List<SubInventoryCargoLocationPO> newList = SubInventoryCargoLocationConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        subInventoryCargoLocationDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return subInventoryCargoLocationDao.deleteBatch(idList);
        }
        return subInventoryCargoLocationDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public SubInventoryCargoLocationVO selectByPrimaryKey(String id) {
        SubInventoryCargoLocationPO po = subInventoryCargoLocationDao.selectByPrimaryKey(id);
        return SubInventoryCargoLocationConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "SUB_CARGO_LOCATION_INFORMATION")
    public List<SubInventoryCargoLocationVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "SUB_CARGO_LOCATION_INFORMATION")
    public List<SubInventoryCargoLocationVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<SubInventoryCargoLocationVO> dataList = subInventoryCargoLocationDao.selectByCondition(sortParam, queryCriteriaParam);
        SubInventoryCargoLocationServiceImpl target = springBeanUtils.getBean(SubInventoryCargoLocationServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<SubInventoryCargoLocationVO> selectByParams(Map<String, Object> params) {
        List<SubInventoryCargoLocationPO> list = subInventoryCargoLocationDao.selectByParams(params);
        return SubInventoryCargoLocationConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<SubInventoryCargoLocationVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.SUB_CARGO_LOCATION_INFORMATION.getCode();
    }

    @Override
    public List<SubInventoryCargoLocationVO> invocation(List<SubInventoryCargoLocationVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<Void> syncSubInventoryCargoLocation(String scenario, List<MesSubInventoryCargoLocation> subInventoryCargoLocations) {
        // 如果列表为空，直接返回成功响应
        if (CollectionUtils.isEmpty(subInventoryCargoLocations)) {
            return BaseResponse.success();
        }

        // 设置数据源上下文
        DynamicDataSourceContextHolder.setDataSource(scenario);
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.MPS.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<MesSubInventoryCargoLocation> filteredList = subInventoryCargoLocations.stream()
                .filter(item ->rangeData.equals(item.getBusinessUnitt()) && "Y".equals(item.getEnableFlag()))
                .collect(Collectors.toList());

        // 提取并去重 corporationCodes, factoryCodes, stashCodes, freightSpaceCodes
        List<String> corporationCodes =
                filteredList.stream().map(MesSubInventoryCargoLocation::getPlantCode).distinct().collect(Collectors.toList());
        List<String> factoryCodes =
                filteredList.stream().map(MesSubInventoryCargoLocation::getBusinessUnitt).distinct().collect(Collectors.toList());
        List<String> stashCodes =
                filteredList.stream().map(MesSubInventoryCargoLocation::getWarehouseCode).distinct().collect(Collectors.toList());
        List<String> freightSpaceCodes =
                filteredList.stream().map(MesSubInventoryCargoLocation::getLocatorCode).distinct().collect(Collectors.toList());

        // 公司代码+工厂代码+仓库代码+货位代码作唯一性判断
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("corporationCodes", corporationCodes == null ? Collections.emptyList() : corporationCodes);
        params.put("factoryCodes", factoryCodes == null ? Collections.emptyList() : factoryCodes);
        params.put("stashCodes", stashCodes == null ? Collections.emptyList() : stashCodes);
        params.put("freightSpaceCodes", freightSpaceCodes == null ? Collections.emptyList() : freightSpaceCodes);

        List<SubInventoryCargoLocationVO> oldPOList =
                subInventoryCargoLocationService.selectByCorporationOrFactoryOrStashOrFreightSpace(params);
        Map<String, SubInventoryCargoLocationVO> oldPOMap = oldPOList.stream().collect(Collectors.toMap(
                t -> t.getCorporationCode() + "|" + t.getFactoryCode() + "|" + t.getStashCode() + "|" + t.getFreightSpaceCode(),
                Function.identity(), (v1, v2) -> v1));

        List<SubInventoryCargoLocationDTO> insertSubInventoryCargoLocationDTOS = org.apache.commons.compress.utils.Lists.newArrayList();
        List<SubInventoryCargoLocationDTO> updateSubInventoryCargoLocationDTOS = Lists.newArrayList();

        for (MesSubInventoryCargoLocation mesSubInventoryCargoLocation : filteredList) {
            SubInventoryCargoLocationDTO subInventoryCargoLocationDTO = new SubInventoryCargoLocationDTO();
            String dataKey = mesSubInventoryCargoLocation.getPlantCode() + "|" +
                    mesSubInventoryCargoLocation.getBusinessUnitt() + "|" +
                    mesSubInventoryCargoLocation.getWarehouseCode() + "|" +
                    mesSubInventoryCargoLocation.getLocatorCode();

            // 处理 enableFlag 转换
            String enabledValue = "Y".equals(mesSubInventoryCargoLocation.getEnableFlag()) ? "YES" : mesSubInventoryCargoLocation.getEnableFlag();

            if (oldPOMap.containsKey(dataKey)) {
                subInventoryCargoLocationDTO.setCorporationCode(mesSubInventoryCargoLocation.getPlantCode());
                subInventoryCargoLocationDTO.setFactoryCode(mesSubInventoryCargoLocation.getBusinessUnitt());
                subInventoryCargoLocationDTO.setStashCode(mesSubInventoryCargoLocation.getWarehouseCode());
                subInventoryCargoLocationDTO.setStashName(mesSubInventoryCargoLocation.getWareDesc());
                subInventoryCargoLocationDTO.setFreightSpaceCode(mesSubInventoryCargoLocation.getLocatorCode());
                subInventoryCargoLocationDTO.setFreightSpaceName(mesSubInventoryCargoLocation.getLocatorDesc());
                subInventoryCargoLocationDTO.setSource(mesSubInventoryCargoLocation.getSource());
                subInventoryCargoLocationDTO.setEnabled(enabledValue);
                subInventoryCargoLocationDTO.setValid(enabledValue);
                subInventoryCargoLocationDTO.setErpFreightSpaceCode(mesSubInventoryCargoLocation.getErpLocatorCode());
                subInventoryCargoLocationDTO.setUpdateTime(mesSubInventoryCargoLocation.getLastUpdateDate());

                updateSubInventoryCargoLocationDTOS.add(subInventoryCargoLocationDTO);
            } else {
                subInventoryCargoLocationDTO.setCorporationCode(mesSubInventoryCargoLocation.getPlantCode());
                subInventoryCargoLocationDTO.setFactoryCode(mesSubInventoryCargoLocation.getBusinessUnitt());
                subInventoryCargoLocationDTO.setStashCode(mesSubInventoryCargoLocation.getWarehouseCode());
                subInventoryCargoLocationDTO.setStashName(mesSubInventoryCargoLocation.getWareDesc());
                subInventoryCargoLocationDTO.setFreightSpaceCode(mesSubInventoryCargoLocation.getLocatorCode());
                subInventoryCargoLocationDTO.setFreightSpaceName(mesSubInventoryCargoLocation.getLocatorDesc());
                subInventoryCargoLocationDTO.setSource(mesSubInventoryCargoLocation.getSource());
                subInventoryCargoLocationDTO.setEnabled(enabledValue);
                subInventoryCargoLocationDTO.setValid(enabledValue);
                subInventoryCargoLocationDTO.setErpFreightSpaceCode(mesSubInventoryCargoLocation.getErpLocatorCode());
                subInventoryCargoLocationDTO.setUpdateTime(mesSubInventoryCargoLocation.getLastUpdateDate());

                insertSubInventoryCargoLocationDTOS.add(subInventoryCargoLocationDTO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertSubInventoryCargoLocationDTOS)) {
            List<List<SubInventoryCargoLocationDTO>> partition = com.google.common.collect.Lists.partition(insertSubInventoryCargoLocationDTOS, 3000);
            for (List<SubInventoryCargoLocationDTO> poDtos : partition) {
                subInventoryCargoLocationService.doCreateBatch(poDtos);
            }
        }

        if (CollectionUtils.isNotEmpty(updateSubInventoryCargoLocationDTOS)) {
            List<List<SubInventoryCargoLocationDTO>> partition = com.google.common.collect.Lists.partition(updateSubInventoryCargoLocationDTOS, 3000);
            for (List<SubInventoryCargoLocationDTO> poDtos : partition) {
                subInventoryCargoLocationService.doUpdateBatch(poDtos);
            }
        }

        DynamicDataSourceContextHolder.clearDataSource();

        return BaseResponse.success("同步成功");
    }

    @Override
    public List<SubInventoryCargoLocationVO> selectByCorporationOrFactoryOrStashOrFreightSpace(Map<String, Object> params) {

        // 从 Map 中提取参数并做 null 安全处理
        List<String> corporationCodes = (List<String>) params.getOrDefault("corporationCodes", Collections.emptyList());
        List<String> factoryCodes = (List<String>) params.getOrDefault("factoryCodes", Collections.emptyList());
        List<String> stashCodes = (List<String>) params.getOrDefault("stashCodes", Collections.emptyList());
        List<String> freightSpaceCodes = (List<String>) params.getOrDefault("freightSpaceCodes", Collections.emptyList());

        List<SubInventoryCargoLocationPO> poList = subInventoryCargoLocationDao.selectByCorporationOrFactoryOrStashOrFreightSpace(
                corporationCodes,
                factoryCodes,
                stashCodes,
                freightSpaceCodes
        );

        return SubInventoryCargoLocationConvertor.INSTANCE.po2Vos(poList);
    }

    @Override
    public BaseResponse<SubInventoryCargoLocationVO> syncData(String scenario, String tenantCode) {
        try {
            log.info("开始同步子库存货位信息接口");
            if (StringUtils.isEmpty(scenario)) {
                scenario = SystemHolder.getScenario();
                tenantCode = SystemHolder.getTenantId();
            }
            HashMap<String, Object> map = MapUtil.newHashMap();

            newDcpFeign.callExternalApi(tenantCode, ApiSourceEnum.MES.getCode(),
                    ApiCategoryEnum.LOCATOR_DESCRIPTION.getCode(), map);

        } catch (Exception e) {
            log.error("同步子库存货位信息报错,{}", e.getMessage());
            throw new BusinessException("同步子库存货位信息报错", e.getMessage());
        }
        return BaseResponse.success("同步操作完成");
    }

	@Override
	public List<LabelValue<String>> selectTargetStockLocation(String factoryCode) {
		List<String> freightSpaceCodes = subInventoryCargoLocationDao.selectOmeLocation(factoryCode);
		if(CollectionUtils.isEmpty(freightSpaceCodes)) {
			return Lists.newArrayList();
		}
		return freightSpaceCodes.stream().map(item -> new LabelValue<>(item, item)).collect(Collectors.toList());
	}

    @Override
    public List<SubInventoryCargoLocationVO> selectByBatchCodeAndStockType(List<String> spaceList, String stockPointType) {
        return subInventoryCargoLocationDao.selectByBatchCodeAndStockType(spaceList, stockPointType);
    }

}