package com.yhl.scp.mps.dynamicDeliveryTracking.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <code>DynamicDeliveryTrackingTaskDO</code>
 * <p>
 * 动态交付跟踪工序任务表DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-27 13:46:30
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DynamicDeliveryTrackingTaskDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -98346155170014993L;

    /**
     * 主键ID
     */
    private String id;
    /**
     * 动态交付跟踪ID
     */
    private String dynamicDeliveryTrackingId;
    /**
     * 标准工艺ID
     */
    private String standardStepId;
    /**
     * 物理资源ID
     */
    private String physicalResourceId;
    /**
     * 数量
     */
    private BigDecimal quantity;
    /**
     * 生产开始时间
     */
    private Date startTime;
    /**
     * 生产结束时间
     */
    private Date endTime;

    /**
     * 流水线时间
     */
    private BigDecimal pipelineTime;

}
