package com.yhl.scp.dfp.stock.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.stock.infrastructure.po.FdpInventoryBatchDetailLogPO;
import com.yhl.scp.dfp.stock.vo.FdpInventoryBatchDetailLogVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <code>FdpInventoryBatchDetailLogDao</code>
 * <p>
 * DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:07
 */
public interface FdpInventoryBatchDetailLogDao extends BaseDao<FdpInventoryBatchDetailLogPO, FdpInventoryBatchDetailLogVO> {
    int doDeleteAllByOrgIds(@Param("sourceType") String sourceType, @Param("orgIds") List<String> orgIds);

    int deleteAllByOrgId(@Param("sourceType") String sourceType,@Param("originalOrgId") String originalOrgId);
}
