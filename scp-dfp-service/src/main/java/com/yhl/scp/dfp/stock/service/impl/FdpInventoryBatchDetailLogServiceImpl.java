package com.yhl.scp.dfp.stock.service.impl;

import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogExcelDTO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.dfp.stock.convertor.FdpInventoryBatchDetailLogConvertor;
import com.yhl.scp.dfp.stock.domain.entity.FdpInventoryBatchDetailLogDO;
import com.yhl.scp.dfp.stock.domain.service.FdpInventoryBatchDetailLogDomainService;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.FdpInventoryBatchDetailLogDao;
import com.yhl.scp.dfp.stock.infrastructure.po.FdpInventoryBatchDetailLogPO;
import com.yhl.scp.dfp.stock.service.FdpInventoryBatchDetailLogService;
import com.yhl.scp.dfp.stock.vo.FdpInventoryBatchDetailLogVO;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mrp.inventory.dto.InventoryFloatGlassShippedDetailVailidateExcelDTO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailValidateVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <code>FdpInventoryBatchDetailLogServiceImpl</code>
 * <p>
 * 应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:08
 */
@Slf4j
@Service
public class FdpInventoryBatchDetailLogServiceImpl extends AbstractService implements FdpInventoryBatchDetailLogService {

    @Resource
    private FdpInventoryBatchDetailLogDao fdpInventoryBatchDetailLogDao;

    @Resource
    private FdpInventoryBatchDetailLogDomainService fdpInventoryBatchDetailLogDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(FdpInventoryBatchDetailLogDTO fdpInventoryBatchDetailLogDTO) {
        // 0.数据转换
        FdpInventoryBatchDetailLogDO fdpInventoryBatchDetailLogDO = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Do(fdpInventoryBatchDetailLogDTO);
        FdpInventoryBatchDetailLogPO fdpInventoryBatchDetailLogPO = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Po(fdpInventoryBatchDetailLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpInventoryBatchDetailLogDomainService.validation(fdpInventoryBatchDetailLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(fdpInventoryBatchDetailLogPO);
        fdpInventoryBatchDetailLogDao.insert(fdpInventoryBatchDetailLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(FdpInventoryBatchDetailLogDTO fdpInventoryBatchDetailLogDTO) {
        // 0.数据转换
        FdpInventoryBatchDetailLogDO fdpInventoryBatchDetailLogDO = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Do(fdpInventoryBatchDetailLogDTO);
        FdpInventoryBatchDetailLogPO fdpInventoryBatchDetailLogPO = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Po(fdpInventoryBatchDetailLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpInventoryBatchDetailLogDomainService.validation(fdpInventoryBatchDetailLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(fdpInventoryBatchDetailLogPO);
        fdpInventoryBatchDetailLogDao.update(fdpInventoryBatchDetailLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<FdpInventoryBatchDetailLogDTO> list) {
        List<FdpInventoryBatchDetailLogPO> newList = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        fdpInventoryBatchDetailLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<FdpInventoryBatchDetailLogDTO> list) {
        List<FdpInventoryBatchDetailLogPO> newList = FdpInventoryBatchDetailLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        fdpInventoryBatchDetailLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return fdpInventoryBatchDetailLogDao.deleteBatch(idList);
        }
        return fdpInventoryBatchDetailLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public FdpInventoryBatchDetailLogVO selectByPrimaryKey(String id) {
        FdpInventoryBatchDetailLogPO po = fdpInventoryBatchDetailLogDao.selectByPrimaryKey(id);
        return FdpInventoryBatchDetailLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "FDP_INVENTORY_BATCH_DETAIL_LOG")
    public List<FdpInventoryBatchDetailLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "FDP_INVENTORY_BATCH_DETAIL_LOG")
    public List<FdpInventoryBatchDetailLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<FdpInventoryBatchDetailLogVO> dataList = fdpInventoryBatchDetailLogDao.selectByCondition(sortParam, queryCriteriaParam);
        FdpInventoryBatchDetailLogServiceImpl target = SpringBeanUtils.getBean(FdpInventoryBatchDetailLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<FdpInventoryBatchDetailLogVO> selectByParams(Map<String, Object> params) {
        List<FdpInventoryBatchDetailLogPO> list = fdpInventoryBatchDetailLogDao.selectByParams(params);
        return FdpInventoryBatchDetailLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<FdpInventoryBatchDetailLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public int doDeleteAllByOrgIds(String sourceType, List<String> orgIds) {
        return fdpInventoryBatchDetailLogDao.doDeleteAllByOrgIds(sourceType,orgIds);
    }

    @Override
    public int doDeleteAllByOrgId(String sourceType, String originalOrgId) {
        return fdpInventoryBatchDetailLogDao.deleteAllByOrgId(sourceType,originalOrgId);
    }

    @Override
    public String getObjectType() {
        return null;
//        return ObjectTypeEnum.FDP_INVENTORY_BATCH_DETAIL_LOG.getCode();
    }

    @Override
    public List<FdpInventoryBatchDetailLogVO> invocation(List<FdpInventoryBatchDetailLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @SneakyThrows
    @Override
    public void downloadOriginalData(String stockPointCode, HttpServletResponse response) {
        List<FdpInventoryBatchDetailLogExcelDTO> excelList= Lists.newArrayList();
        String scenario = SystemHolder.getScenario();
        DynamicDataSourceContextHolder.setDataSource(scenario);
        List<FdpInventoryBatchDetailLogPO> inventoryList = fdpInventoryBatchDetailLogDao.selectByParams(ImmutableMap.of("stockPointCode", stockPointCode));
        DynamicDataSourceContextHolder.clearDataSource();

        for (FdpInventoryBatchDetailLogPO inventory : inventoryList) {
            FdpInventoryBatchDetailLogExcelDTO excelDTO = new FdpInventoryBatchDetailLogExcelDTO();
            BeanUtils.copyProperties(inventory, excelDTO);
            excelList.add(excelDTO);
        }

        String fileName = URLEncoder.encode(ObjectTypeEnum.INVENTORY_BATCH_DETAIL_LOG.getDesc()
                +"原始数据", "UTF-8");
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment;filename=" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), InventoryFloatGlassShippedDetailVailidateExcelDTO.class)
                .sheet(ObjectTypeEnum.INVENTORY_BATCH_DETAIL_LOG.getDesc()).doWrite(excelList);
    }

}
