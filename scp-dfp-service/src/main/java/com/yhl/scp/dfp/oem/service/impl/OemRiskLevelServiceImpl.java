package com.yhl.scp.dfp.oem.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.convertor.OemRiskLevelConvertor;
import com.yhl.scp.dfp.oem.domain.entity.OemRiskLevelDO;
import com.yhl.scp.dfp.oem.domain.service.OemRiskLevelDomainService;
import com.yhl.scp.dfp.oem.dto.OemRiskLevelDTO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemRiskLevelDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemRiskLevelPO;
import com.yhl.scp.dfp.oem.service.OemRiskLevelService;
import com.yhl.scp.dfp.oem.vo.OemRiskLevelVO;
import com.yhl.scp.dfp.risk.enums.RiskLevelEnum;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>OemRiskLevelServiceImpl</code>
 * <p>
 * 主机厂风险等级应用实现
 * </p>
 *
 * @version 1.0
 * @since 2024-07-28 21:45:11
 */
@Slf4j
@Service
public class OemRiskLevelServiceImpl extends AbstractService implements OemRiskLevelService {

    @Resource
    private OemRiskLevelDao oemRiskLevelDao;

    @Resource
    private OemRiskLevelDomainService oemRiskLevelDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private OemRiskLevelService oemRiskLevelService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(OemRiskLevelDTO oemRiskLevelDTO) {
        if (YesOrNoEnum.YES.getDesc().equals(oemRiskLevelDTO.getHighRiskJudge())) {
            oemRiskLevelDTO.setRiskLevel(RiskLevelEnum.HIGH.getCode());
        } else {
            if (YesOrNoEnum.YES.getDesc().equals(oemRiskLevelDTO.getMiddleRiskJudge())) {
                oemRiskLevelDTO.setRiskLevel(RiskLevelEnum.MIDDLE.getCode());
            } else {
                oemRiskLevelDTO.setRiskLevel(RiskLevelEnum.LOW.getCode());
            }
        }
        // 0.数据转换
        OemRiskLevelDO oemRiskLevelDO = OemRiskLevelConvertor.INSTANCE.dto2Do(oemRiskLevelDTO);
        OemRiskLevelPO oemRiskLevelPO = OemRiskLevelConvertor.INSTANCE.dto2Po(oemRiskLevelDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemRiskLevelDomainService.validation(oemRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(oemRiskLevelPO);
        oemRiskLevelDao.insert(oemRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(OemRiskLevelDTO oemRiskLevelDTO) {
        // 0.数据转换
        OemRiskLevelDO oemRiskLevelDO = OemRiskLevelConvertor.INSTANCE.dto2Do(oemRiskLevelDTO);
        OemRiskLevelPO oemRiskLevelPO = OemRiskLevelConvertor.INSTANCE.dto2Po(oemRiskLevelDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        oemRiskLevelDomainService.validation(oemRiskLevelDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(oemRiskLevelPO);
        oemRiskLevelDao.updateByOemCodeAndTime(oemRiskLevelPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<OemRiskLevelDTO> list) {
        List<OemRiskLevelPO> newList = OemRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        oemRiskLevelDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<OemRiskLevelDTO> list) {
        List<OemRiskLevelPO> newList = OemRiskLevelConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        oemRiskLevelDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return oemRiskLevelDao.deleteBatch(idList);
        }
        return oemRiskLevelDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public OemRiskLevelVO selectByPrimaryKey(String id) {
        OemRiskLevelPO po = oemRiskLevelDao.selectByPrimaryKey(id);
        return OemRiskLevelConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "OEM_RISK_LEVEL")
    public List<OemRiskLevelVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "OEM_RISK_LEVEL")
    public List<OemRiskLevelVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<OemRiskLevelVO> dataList = oemRiskLevelDao.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isNotEmpty(dataList)) {
            List<OemRiskLevelVO> oemRiskLevels = this.selectAll();
            Map<String, String> estimateTimeMap =
                    Optional.ofNullable(oemRiskLevels.stream().map(OemRiskLevelVO::getEstimateTime).distinct()
                            .collect(Collectors.toList())).orElse(new ArrayList<>()).stream()
                            .collect(TreeMap::new, (k, v) -> k.put(v, null), TreeMap::putAll);
            Map<String, Map<String, String>> oemRiskMap =
                    oemRiskLevels.stream()
                            .filter(x -> StringUtils.isNotBlank(x.getOemCode()))
                            .collect(Collectors.groupingBy(OemRiskLevelVO::getOemCode,
                                    Collectors.toMap(OemRiskLevelVO::getEstimateTime, OemRiskLevelVO::getRiskLevel,
                                            (v1, v2) -> v1)));
            for (OemRiskLevelVO oemRiskLevelVO : dataList) {
                String oemCode = oemRiskLevelVO.getOemCode();
                if (StringUtils.isBlank(oemCode)) {
                    continue;
                }
                oemRiskLevelVO.setRiskLevelMap(oemRiskMap.getOrDefault(oemCode, estimateTimeMap));
            }
        }
        OemRiskLevelServiceImpl target = springBeanUtils.getBean(OemRiskLevelServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<OemRiskLevelVO> selectByParams(Map<String, Object> params) {
        List<OemRiskLevelPO> list = oemRiskLevelDao.selectByParams(params);
        return OemRiskLevelConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<OemRiskLevelVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.OEM_RISK_LEVEL.getCode();
    }

    @Override
    public List<OemRiskLevelVO> invocation(List<OemRiskLevelVO> dataList, Map<String, Object> params,
                                           String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> getYearMonthLabelValues() {
        List<LabelValue<String>> result = Lists.newArrayList();
        Date currentDate = new Date();
        for (int i = 0; i <= 3; i++) {
            int month = i - 2;
            Date tmpDate = org.apache.commons.lang3.time.DateUtils.addMonths(currentDate, month);
            String yearMonth = DateUtils.dateToString(tmpDate, "yyyyMM");
            result.add(new LabelValue<>(yearMonth, yearMonth));
        }
        return BaseResponse.success(result);
    }

    //    @Resource
    //    private OemDao oemDao;

    @Override
    public BaseResponse<Map<String, Map<String, String>>> getDetailByOemCode(String oemCode) {
        Map<String, Map<String, String>> result = MapUtil.newHashMap();
        Map<String, Object> riskParam = MapUtil.newHashMap();
        //        OemPO oemPO = oemDao.selectByOemCode(oemCode);
        //        if (null == oemPO) {
        //            throw new BusinessException("主机厂信息为空：" + oemCode);
        //        }
        riskParam.put("enabled", YesOrNoEnum.YES.getCode());
        riskParam.put("oemCode", oemCode);
        List<OemRiskLevelVO> oemRiskLevelVOS = this.selectByParams(riskParam);
        if (CollectionUtils.isEmpty(oemRiskLevelVOS)) {
            return BaseResponse.success(result);
        }
        oemRiskLevelVOS.stream().forEach(x -> {
            String estimateTime = x.getEstimateTime();
            String negativeBusiness = x.getNegativeBusiness();
            Map<String, String> negativeMap = result.getOrDefault("出现经营方面负面消息", MapUtil.newHashMap());
            negativeMap.put(estimateTime, negativeBusiness);
            result.put("出现经营方面负面消息", negativeMap);
            String creditRestrict = x.getCreditRestrict();
            Map<String, String> creditRestrictMap = result.getOrDefault("信保公司限制/取消限制", MapUtil.newHashMap());
            creditRestrictMap.put(estimateTime, creditRestrict);
            result.put("信保公司限制/取消限制", creditRestrictMap);
            String twoMonthFullPayment = x.getTwoMonthFullPayment();
            Map<String, String> twoMonthFullPaymentMap = result.getOrDefault("持续2个月未能全额回款", MapUtil.newHashMap());
            twoMonthFullPaymentMap.put(estimateTime, twoMonthFullPayment);
            result.put("持续2个月未能全额回款", twoMonthFullPaymentMap);
            String twoMonthLoadingVolume = x.getTwoMonthLoadingVolume();
            Map<String, String> twoMonthLoadingVolumeMap = result.getOrDefault("持续2个月装车辆极小", MapUtil.newHashMap());
            twoMonthLoadingVolumeMap.put(estimateTime, twoMonthLoadingVolume);
            result.put("持续2个月装车辆极小", twoMonthLoadingVolumeMap);
            String remark = x.getRemark();
            Map<String, String> remarkMap = result.getOrDefault("备注", MapUtil.newHashMap());
            remarkMap.put(estimateTime, remark);
            result.put("备注", remarkMap);
        });
        return BaseResponse.success(result);
    }

    @Override
    public BaseResponse<Void> updateRiskLevelByYearMonth(String oemCode, String yearMonth, String riskLevel) {
        oemRiskLevelDao.updateRiskLevelByYearMonth(oemCode, yearMonth, riskLevel);
        return BaseResponse.success("修改成功");
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return oemRiskLevelDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<LabelValue<String>> getOemRiskLevel(String oemCode, String estimateTime) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("oemCode", oemCode);
//        params.put("estimateTime", estimateTime);
        List<OemRiskLevelVO> oemRiskLevelVOS = oemRiskLevelService.selectByParams(params);
        if (CollectionUtils.isNotEmpty(oemRiskLevelVOS)) {
        	oemRiskLevelVOS.sort(Comparator.comparing(OemRiskLevelVO::getEstimateTime).reversed());
        	oemRiskLevelVOS = Arrays.asList(oemRiskLevelVOS.get(0));
            return oemRiskLevelVOS.stream().map(x -> new LabelValue<>(x.getRiskLevel(), x.getRiskLevel())).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

	@Override
	public List<OemRiskLevelVO> selectByOemCodes(List<String> oemCodes) {
		return oemRiskLevelDao.selectByOemCodes(oemCodes);
	}

}
