package com.yhl.scp.dfp.switchrelation.domain.service;

import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.scp.dfp.common.enums.SwitchRelationModeEnum;
import com.yhl.scp.dfp.switchrelation.domain.entity.DfpSwitchRelationBetweenProductDO;
import com.yhl.scp.dfp.switchrelation.infrastructure.dao.DfpSwitchRelationBetweenProductDao;
import com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <code>DfpSwitchRelationBetweenProductDomainService</code>
 * <p>
 * 新旧产品工程变更关系领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-12-19 16:35:35
 */
@Service
public class DfpSwitchRelationBetweenProductDomainService {

    @Resource
    private DfpSwitchRelationBetweenProductDao dfpSwitchRelationBetweenProductDao;
    @Resource
    private NewMdsFeign newMdsFeign;

    /**
     * 数据校验
     *
     * @param dfpSwitchRelationBetweenProductDO 领域对象
     */
    public void validation(DfpSwitchRelationBetweenProductDO dfpSwitchRelationBetweenProductDO) {
        checkNotNull(dfpSwitchRelationBetweenProductDO);
        checkUniqueCode(dfpSwitchRelationBetweenProductDO);
        checkSwitchGroup(dfpSwitchRelationBetweenProductDO);
        // TODO 补充其他校验逻辑
    }

    public void updateQuantity(DfpSwitchRelationBetweenProductPO dfpSwitchRelationBetweenProductPO){
        if (!SwitchRelationModeEnum.FORCED_SWITCH_QUANTITY.getCode().equals(dfpSwitchRelationBetweenProductPO.getSwitchMode())){
            dfpSwitchRelationBetweenProductPO.setSwitchQuantity(null);
        }
    }

    /**
     * 非空检验
     *
     * @param dfpSwitchRelationBetweenProductDO 领域对象
     */
    private void checkNotNull(DfpSwitchRelationBetweenProductDO dfpSwitchRelationBetweenProductDO) {
        // TODO
    }

    private void checkSwitchGroup(DfpSwitchRelationBetweenProductDO dfpSwitchRelationBetweenProductDO){
        if (StringUtils.isEmpty(dfpSwitchRelationBetweenProductDO.getSwitchCodeGroup())){
            return;
        }
        List<DfpSwitchRelationBetweenProductPO> dfpSwitchRelationBetweenProductVOS =
                dfpSwitchRelationBetweenProductDao.selectByParams(ImmutableMap.of("switchCodeGroup",
                        dfpSwitchRelationBetweenProductDO.getSwitchCodeGroup()));

        //排除自己
        dfpSwitchRelationBetweenProductVOS = dfpSwitchRelationBetweenProductVOS.stream()
                        .filter(po -> !po.getId().equals(dfpSwitchRelationBetweenProductDO.getId()))
                        .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(dfpSwitchRelationBetweenProductVOS)){
            //齐套组切换模式和时间应该相同，只取一个做校验
            DfpSwitchRelationBetweenProductPO po = dfpSwitchRelationBetweenProductVOS.get(0);
            if (!po.getSwitchMode().equals(dfpSwitchRelationBetweenProductDO.getSwitchMode())){
                throw new BusinessException(String.format("切换模式和该齐套组切换模式不同，齐套组切换模式: %s, 旧产品编码: %s，请检查",
                        SwitchRelationModeEnum.getDescByCode(po.getSwitchMode()), po.getOldProductCode()));
            }
            if (!po.getSwitchTime().equals(dfpSwitchRelationBetweenProductDO.getSwitchTime())){
                throw new BusinessException(String.format("切换时间和该齐套组切换时间不同，齐套组切换时间: %s, 旧产品编码: %s，请检查",
                        DateUtils.dateToString(po.getSwitchTime()), po.getOldProductCode()));
            }
        }
        //权限限制先去掉
        // List<String> productCodes = new ArrayList<>();
        // productCodes.add(dfpSwitchRelationBetweenProductDO.getOldProductCode());
        // productCodes.add(dfpSwitchRelationBetweenProductDO.getNewProductCode());
        // List<NewProductStockPointVO> newProductStockPointVOS =
        //         newMdsFeign.selectProductStockPointVOByProductCodes(SystemHolder.getScenario(),productCodes);
        // String orderPlanner = null;
        // for (NewProductStockPointVO newProductStockPointVO : newProductStockPointVOS){
        //     if (StringUtils.isEmpty(orderPlanner)){
        //         orderPlanner = newProductStockPointVO.getOrderPlanner();
        //         continue;
        //     }
        //     if (!orderPlanner.equals(newProductStockPointVO.getOrderPlanner())){
        //         throw new BusinessException("新旧编码物料权限不一致，请检查");
        //     }
        // }
    }

    /**
     * 唯一性校验
     *
     * @param dfpSwitchRelationBetweenProductDO 领域对象
     */
    private void checkUniqueCode(DfpSwitchRelationBetweenProductDO dfpSwitchRelationBetweenProductDO) {
        if (dfpSwitchRelationBetweenProductDO.getOldProductCode().equals(dfpSwitchRelationBetweenProductDO.getNewProductCode())){
            throw new BusinessException("旧产品编码和新产品编码不能相同");
        }
        // TODO
        List<DfpSwitchRelationBetweenProductPO> oldList =
                dfpSwitchRelationBetweenProductDao.selectByParams(
                        ImmutableMap.of("oldProductCode",
                dfpSwitchRelationBetweenProductDO.getOldProductCode()));
        if (!oldList.isEmpty() && !oldList.get(0).getId().equals(dfpSwitchRelationBetweenProductDO.getId())){
            throw new BusinessException("已经存在该产品编码的变更规则");
        }
    }

}
