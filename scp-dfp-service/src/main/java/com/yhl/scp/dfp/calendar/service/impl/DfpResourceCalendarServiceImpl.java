package com.yhl.scp.dfp.calendar.service.impl;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BasePO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.calendar.convertor.CalendarRuleConvertor;
import com.yhl.scp.dfp.calendar.convertor.ResourceCalendarConvertor;
import com.yhl.scp.dfp.calendar.domain.entity.CalendarRuleDO;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceCalendarDO;
import com.yhl.scp.dfp.calendar.domain.entity.ResourceHourStatisticDO;
import com.yhl.scp.dfp.calendar.domain.entity.TimeRangeDO;
import com.yhl.scp.dfp.calendar.domain.service.DfpCalendarRuleDomainService;
import com.yhl.scp.dfp.calendar.domain.service.DfpResourceCalendarDomainService;
import com.yhl.scp.dfp.calendar.dto.CalendarRuleDTO;
import com.yhl.scp.dfp.calendar.dto.ResourceCalendarDTO;
import com.yhl.scp.dfp.calendar.infrastructure.dao.DfpResourceCalendarDao;
import com.yhl.scp.dfp.calendar.infrastructure.po.ResourceCalendarPO;
import com.yhl.scp.dfp.calendar.service.DfpCalendarRuleService;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.service.DfpShiftService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.calendar.vo.ResourceWorkHourStatisticsVO;
import com.yhl.scp.dfp.calendar.vo.WorkHourStatisticsVO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemProductLineMapDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapBasicPO;
import com.yhl.scp.dfp.oem.infrastructure.po.OemProductLineMapPO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.feign.IpsFeign;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.extension.feign.dto.ResourceCalendarParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>ResourceCalendarServiceImpl</code>
 * <p>
 * 资源日历应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-08-07 16:06:22
 */
@Slf4j
@Service
public class DfpResourceCalendarServiceImpl extends AbstractService implements DfpResourceCalendarService {

    @Resource
    private DfpResourceCalendarDao resourceCalendarDao;

    @Resource
    private DfpResourceCalendarDomainService resourceCalendarDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DfpCalendarRuleDomainService calendarRuleDomainService;

    @Resource
    private DfpCalendarRuleService calendarRuleService;

    @Resource
    private DfpShiftService shiftService;

    @Resource
    private OemDao oemDao;

    @Resource
    private OemProductLineDao oemProductLineDao;

    @Resource
    private OemProductLineMapDao oemProductLineMapDao;

//    @Resource
//    private PhysicalResourceService physicalResourceService;
//
//    @Resource
//    private StandardResourceService standardResourceService;

//    @Resource
//    private TimePeriodService timePeriodService;

//    @Resource
//    private HumanResourceGroupService humanResourceGroupService;
//
//
//    @Resource
//    private HumanResourceService humanResourceService;

    @Resource
    IpsFeign ipsFeign;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ResourceCalendarDTO resourceCalendarDTO) {
        // 设置日历开始时间和结束时间
        setStartAndEndTime(resourceCalendarDTO);
        // 0.数据转换
        ResourceCalendarDO resourceCalendarDO = ResourceCalendarConvertor.INSTANCE.dto2Do(resourceCalendarDTO);
        ResourceCalendarPO resourceCalendarPO = ResourceCalendarConvertor.INSTANCE.dto2Po(resourceCalendarDTO);
        // 1.数据校验
        resourceCalendarDomainService.validation(resourceCalendarDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(resourceCalendarPO);

        resourceCalendarDao.insert(resourceCalendarPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    private void setStartAndEndTime(ResourceCalendarDTO resourceCalendarDTO) {
        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, CollectionValueVO> collect = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, Function.identity()));
        String description = collect.get(resourceCalendarDTO.getCalendarType()).getDescription();


        String startTimeStr = DateUtils.dateToString(resourceCalendarDTO.getWorkDay(), DateUtils.COMMON_DATE_STR3)
                + " "
                + resourceCalendarDTO.getShiftPattern().split("-")[0];
        String endTimeStr = DateUtils.dateToString(resourceCalendarDTO.getWorkDay(), DateUtils.COMMON_DATE_STR3)
                + " "
                + resourceCalendarDTO.getShiftPattern().split("-")[1];
        resourceCalendarDTO.setStartTime(DateUtils.stringToDate(startTimeStr, DateUtils.COMMON_DATE_STR5));
        resourceCalendarDTO.setEndTime(DateUtils.stringToDate(endTimeStr, DateUtils.COMMON_DATE_STR5));
        Long minuteInterval = DateUtils.getMinuteInterval(resourceCalendarDTO.getStartTime(), resourceCalendarDTO.getEndTime());

        if ("异常".equals(description)) {
            resourceCalendarDTO.setWorkHours(BigDecimal.ZERO);
            resourceCalendarDTO.setOvertimeHours(BigDecimal.ZERO);
        } else {
            if (StringUtils.isEmpty(resourceCalendarDTO.getCalendarType()) || "OVERTIME".equals(resourceCalendarDTO.getCalendarType())) {
                resourceCalendarDTO.setOvertimeHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP));
                resourceCalendarDTO.setWorkHours(BigDecimal.ZERO);
            } else {
                resourceCalendarDTO.setWorkHours(BigDecimal.valueOf(minuteInterval).divide(BigDecimal.valueOf(60), 2, BigDecimal.ROUND_HALF_UP));
                resourceCalendarDTO.setOvertimeHours(BigDecimal.ZERO);
            }
        }
    }


    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ResourceCalendarDTO resourceCalendarDTO) {
        // 0.数据转换
        setStartAndEndTime(resourceCalendarDTO);
        ResourceCalendarDO resourceCalendarDO = ResourceCalendarConvertor.INSTANCE.dto2Do(resourceCalendarDTO);
        // 1.数据校验
        resourceCalendarDomainService.validation(resourceCalendarDO);


        // 查询时间重叠的日历
        List<ResourceCalendarPO> resourceCalendarPOS =
                resourceCalendarDao.selectOverlapByResourceIdsAndDate(resourceCalendarDTO.getPhysicalResourceId(),
                                resourceCalendarDTO.getStartTime(), resourceCalendarDTO.getEndTime()).stream()

                        .filter(t -> !t.getId().equals(resourceCalendarDTO.getId())).collect(Collectors.toList());

        // 如果没有与之时间重叠的日历，直接持久化
        if (CollectionUtils.isEmpty(resourceCalendarPOS)) {
            ResourceCalendarPO resourceCalendarPO = ResourceCalendarConvertor.INSTANCE.dto2Po(resourceCalendarDTO);
            BasePOUtils.updateFiller(resourceCalendarPO);
            resourceCalendarDao.updateSelective(resourceCalendarPO);
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        }
        // 如果有与之时间重叠，同样属于正常或者异常的日历存在，则提示修改失败
        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, String> shiftTypeMap = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getNotNullDescription));
        List<ResourceCalendarDO> normalResourceCalendarList = new ArrayList<>();
        List<ResourceCalendarDO> abnormalResourceCalendarList = new ArrayList<>();
        for (ResourceCalendarDO resourceCalendarDOTemp : ResourceCalendarConvertor.INSTANCE.pos2Dos(resourceCalendarPOS)) {
            if ("正常".equals(shiftTypeMap.get(resourceCalendarDOTemp.getCalendarType()))) {
                normalResourceCalendarList.add(resourceCalendarDOTemp);
            } else {
                abnormalResourceCalendarList.add(resourceCalendarDOTemp);
            }
        }
        if ("正常".equals(shiftTypeMap.get(resourceCalendarDTO.getCalendarType()))
                && CollectionUtils.isNotEmpty(normalResourceCalendarList)) {
            return BaseResponse.error("时间冲突，修改失败");
        }
        if ("异常".equals(shiftTypeMap.get(resourceCalendarDTO.getCalendarType()))
                && CollectionUtils.isNotEmpty(abnormalResourceCalendarList)) {
            return BaseResponse.error("时间冲突，修改失败");
        }
        if ("异常".equals(shiftTypeMap.get(resourceCalendarDTO.getCalendarType()))) {
            ResourceCalendarPO resourceCalendarPO = ResourceCalendarConvertor.INSTANCE.dto2Po(resourceCalendarDTO);
            BasePOUtils.updateFiller(resourceCalendarPO);
            resourceCalendarDao.updateSelective(resourceCalendarPO);
            if (CollectionUtils.isNotEmpty(normalResourceCalendarList)) {
                resourceCalendarDomainService.mergeAbnormalResourceClendar(normalResourceCalendarList, Lists.newArrayList(resourceCalendarDO));
            }
        }
        if ("正常".equals(shiftTypeMap.get(resourceCalendarDTO.getCalendarType()))) {
            if (CollectionUtils.isNotEmpty(abnormalResourceCalendarList)) {
                resourceCalendarDomainService.mergeAbnormalResourceClendar(Lists.newArrayList(resourceCalendarDO), abnormalResourceCalendarList);
            } else {
                ResourceCalendarPO resourceCalendarPO = ResourceCalendarConvertor.INSTANCE.dto2Po(resourceCalendarDTO);
                BasePOUtils.updateFiller(resourceCalendarPO);
                resourceCalendarDao.updateSelective(resourceCalendarPO);
            }
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ResourceCalendarDTO> list) {
        String physicalResourceId = list.get(0).getPhysicalResourceId();
        String standardResourceId = list.get(0).getStandardResourceId();
        String organizationId = list.get(0).getOrganizationId();

//        PhysicalResourceVO physicalResourceVO = physicalResourceService.selectByPrimaryKey(list.get(0).getPhysicalResourceId());
//        StandardResourceVO standardResourceVO = standardResourceService.selectByPrimaryKey(physicalResourceVO.getStandardResourceId());

        list.forEach(resourceCalendarDTO -> {
            resourceCalendarDTO.setStandardResourceId(standardResourceId);
            resourceCalendarDTO.setOrganizationId(organizationId);
            setStartAndEndTime(resourceCalendarDTO);
        });
        List<ResourceCalendarDO> resourceCalendarDOList = ResourceCalendarConvertor.INSTANCE.dto2Dos(list);
        Map<String, Object> params = new HashMap<>();
        params.put("physicalResourceId", resourceCalendarDOList.get(0).getPhysicalResourceId());
        List<Date> workDayList = new ArrayList<>();
        workDayList.add(resourceCalendarDOList.get(0).getWorkDay());
        workDayList.add(DateUtils.moveDay(resourceCalendarDOList.get(0).getWorkDay(), -1));
        params.put("workDayList", workDayList);
        List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByParams(params);


        List<ResourceCalendarDO> resourceCalendarDOSInDB = ResourceCalendarConvertor.INSTANCE.pos2Dos(resourceCalendarPOS);

        if (CollectionUtils.isNotEmpty(resourceCalendarDOSInDB)) {
            resourceCalendarDOList.addAll(resourceCalendarDOSInDB);
        }
        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, String> shiftTypeMap = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getNotNullDescription));
        List<ResourceCalendarDO> normalResourceCalendarList = new ArrayList<>();
        List<ResourceCalendarDO> abnormalResourceCalendarList = new ArrayList<>();
        for (ResourceCalendarDO resourceCalendarDO : resourceCalendarDOList) {
            if ("正常".equals(shiftTypeMap.get(resourceCalendarDO.getCalendarType()))) {
                normalResourceCalendarList.add(resourceCalendarDO);
            } else {
                abnormalResourceCalendarList.add(resourceCalendarDO);
            }
        }
        // 1.数据校验
        resourceCalendarDomainService.validation(normalResourceCalendarList);
        resourceCalendarDomainService.validation(abnormalResourceCalendarList);
        // 2.合并正常日历和异常日历
        resourceCalendarDomainService.mergeAbnormalResourceClendar(normalResourceCalendarList, abnormalResourceCalendarList);
        List<ResourceCalendarDO> collect = abnormalResourceCalendarList.stream().filter(t -> StringUtils.isEmpty(t.getId())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            List<ResourceCalendarPO> resourceCalendarPOS1 = ResourceCalendarConvertor.INSTANCE.dos2Pos(collect);
            BasePOUtils.insertBatchFiller(resourceCalendarPOS1, new Date());
            resourceCalendarDao.insertBatch(resourceCalendarPOS1);
        }
    }

    @Override
    public void doUpdateBatch(List<ResourceCalendarDTO> list) {
        List<ResourceCalendarPO> newList = ResourceCalendarConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        resourceCalendarDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        // 正常班次不能被删除
        List<ResourceCalendarPO> resourceCalendarPOList = resourceCalendarDao.selectByPrimaryKeys(idList);
        if(CollectionUtils.isEmpty(resourceCalendarPOList)){
            throw new BusinessException("无有效数据");
        }
        // 过滤掉正常班次
//        List<ResourceCalendarPO> notNormalResourceCalendarList = resourceCalendarPOList.stream().filter(item -> !StringUtils.equals(item.getCalendarType(), "NORMAL")).collect(Collectors.toList());
//        if (CollectionUtils.isEmpty(notNormalResourceCalendarList)) {
//            return 0;
//        }
        List<String> deleteIdList = resourceCalendarPOList.stream().map(ResourceCalendarPO::getId).collect(Collectors.toList());
        if (deleteIdList.size() > 1) {
            return resourceCalendarDao.deleteBatch(deleteIdList);
        }
        return resourceCalendarDao.deleteByPrimaryKey(deleteIdList.get(0));
    }

    @Override
    public ResourceCalendarVO selectByPrimaryKey(String id) {
        ResourceCalendarPO po = resourceCalendarDao.selectByPrimaryKey(id);
        return ResourceCalendarConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "mds_cal_resource_calendar")
    public List<ResourceCalendarVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "mds_cal_resource_calendar")
    public List<ResourceCalendarVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ResourceCalendarVO> dataList = resourceCalendarDao.selectByCondition(sortParam, queryCriteriaParam);
        DfpResourceCalendarServiceImpl target = springBeanUtils.getBean(DfpResourceCalendarServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ResourceCalendarVO> selectByParams(Map<String, Object> params) {
        List<ResourceCalendarPO> list = resourceCalendarDao.selectByParams(params);
        return ResourceCalendarConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ResourceCalendarVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }


    /*@Override
    public void refreshResourceCalendar() {

        Map<String, List<CalendarRuleDO>> calendarRuleGroup = calendarRuleDomainService.groupAndSortCelendarRule();

        List<OemProductLinePO> oemProductLinePOS = oemProductLineDao.selectAll();
        Map<String, String> physicalStandardMap = oemProductLinePOS.stream()
                .collect(Collectors.toMap(
                        OemProductLinePO::getLineCode,
                        OemProductLinePO::getOemCode,
                        (existing, replacement) -> existing // 当键冲突时保留第一个值
                ));

        // 1.生成资源正常日历
        List<CalendarRuleDO> normalCalendarRuleList = calendarRuleGroup.get("normal");
        List<ResourceCalendarDO> normalResourceCalendarDOList = resourceCalendarDomainService.generate(normalCalendarRuleList, physicalStandardMap, null, null, null);
        // 3. 正常日历和异常日历合并
        resourceCalendarDao.deleteAll();
        Date now = new Date();
        if (CollectionUtils.isNotEmpty(normalResourceCalendarDOList)) {
            List<ResourceCalendarPO> resourceCalendarPOS = ResourceCalendarConvertor.INSTANCE.dos2Pos(normalResourceCalendarDOList);
            BasePOUtils.insertBatchFiller(resourceCalendarPOS, now);
            resourceCalendarDao.insertBatch(resourceCalendarPOS);
            log.info("装车日历刷新数据条数：{}", resourceCalendarPOS.size());
        }
    }*/

    @Override
    public void refreshResourceCalendar() {

        Map<String, List<CalendarRuleDO>> calendarRuleGroup = calendarRuleDomainService.groupAndSortCelendarRule();

        List<OemProductLineMapPO> oemProductLinePOS = oemProductLineMapDao.selectAll();
        Map<String,String> physicalStandardMap = new HashMap<>();
        for (OemProductLineMapPO oemProductLineMapPO : oemProductLinePOS){
            physicalStandardMap.put(oemProductLineMapPO.getOemCode()+oemProductLineMapPO.getLineCode()+oemProductLineMapPO.getVehicleModelCode()
                    ,oemProductLineMapPO.getLineCode());
        }

        // 1.生成资源正常日历
        List<CalendarRuleDO> normalCalendarRuleList = calendarRuleGroup.get("normal");
        List<ResourceCalendarDO> normalResourceCalendarDOList = resourceCalendarDomainService.generate(normalCalendarRuleList, physicalStandardMap, null, null, null);
        // 3. 正常日历和异常日历合并
        resourceCalendarDao.deleteAll();
        Date now = new Date();
        if (CollectionUtils.isNotEmpty(normalResourceCalendarDOList)) {
            List<ResourceCalendarPO> resourceCalendarPOS = ResourceCalendarConvertor.INSTANCE.dos2Pos(normalResourceCalendarDOList);
            BasePOUtils.insertBatchFiller(resourceCalendarPOS, now);
            resourceCalendarDao.insertBatch(resourceCalendarPOS);
            log.info("装车日历刷新数据条数：{}", resourceCalendarPOS.size());
        }
    }


    @Override
    public void refreshResourceCalendar(
            CalendarRuleDTO calendarRuleDTO,
            String organizationId,
            List<String> standardResourceIds,
            List<String> physicalResourceIds,
            Date startDate,
            Date endDate) {

        Date truncatedStartDate = DateUtils.truncateTimeOfDate(startDate);
        Date truncatedEndDate = DateUtils.truncateTimeOfDate(endDate);

        List<OemProductLineMapPO> physicalResourceVOS;

        /**
         * 根据页面输入条件算出具体物理资源
         */
        if (CollectionUtils.isNotEmpty(physicalResourceIds) && !physicalResourceIds.contains("*")) {
            // 根据具体的资源
//            physicalResourceVOS = physicalResourceService.selectByIds(physicalResourceIds);
            //physicalResourceVOS = oemProductLineDao.selectByOemCodes(physicalResourceIds);
            physicalResourceVOS = oemProductLineMapDao.selectByLineCodes(physicalResourceIds);
        } else {
            if (CollectionUtils.isNotEmpty(standardResourceIds) && !standardResourceIds.contains("*")) {
                // 根据主机厂筛选资源
//                physicalResourceVOS = physicalResourceService.selectByStandResourceIds(standardResourceIds);
                //physicalResourceVOS = oemProductLineDao.selectByLineCodes(standardResourceIds);

                physicalResourceVOS = oemProductLineMapDao.selectByVehicleModelList(standardResourceIds);
            } else {
                physicalResourceVOS = oemProductLineMapDao.selectAll();
            }
        }

        physicalResourceIds = physicalResourceVOS.stream().map(OemProductLineMapPO::getVehicleModelCode).collect(Collectors.toList());
        Map<String, String> physicalIdToStandardIdMap =
                physicalResourceVOS.stream().collect(Collectors.toMap(OemProductLineMapPO::getVehicleModelCode,
                        OemProductLineMapPO::getLineCode));


        List<TimeRangeDO> timeRangeDOList = new ArrayList<>();
        timeRangeDOList.add(TimeRangeDO.builder().startDate(truncatedStartDate).endDate(truncatedEndDate).build());


        // 2、根据资源id，开始-结束时间，获取所有符合条件的日历规则
        List<CalendarRuleDO> calendarRules;
        if (null != calendarRuleDTO) {
            calendarRules = Lists.newArrayList(CalendarRuleConvertor.INSTANCE.dto2Do(calendarRuleDTO));
        } else {
            calendarRules = calendarRuleDomainService.getCalendarRuleDOs(
                    organizationId,
                    standardResourceIds,
                    physicalResourceIds,
                    truncatedStartDate, truncatedEndDate, null);
        }
        // 3、根据日历规则、班次、计划周期生成新的资源日历
        for (TimeRangeDO timeRangeDO : timeRangeDOList) {
            resourceCalendarDomainService.refreshCalendar(
                    organizationId,
                    standardResourceIds,
                    physicalResourceIds,
                    physicalIdToStandardIdMap,
                    timeRangeDO.getStartDate(),
                    timeRangeDO.getEndDate(), calendarRules);
        }

    }

    @Override
    public List<ResourceCalendarVO> selectResourceCalendarList(ResourceCalendarParamDTO paramDTO) {
        return resourceCalendarDao.selectVOByParamDTO(paramDTO);
    }

    @Override
    public List<ResourceCalendarVO> doSearch(String standardResourceId, String physicalResourceId, Date startDate, Date endDate) {
        List<ResourceCalendarDO> resourceCalendarDOS = resourceCalendarDomainService.getResourceCalendar(
                Collections.singletonList(standardResourceId),
                Collections.singletonList(physicalResourceId),
                startDate,
                endDate);
        List<ResourceCalendarVO> resourceCalendarVOS = ResourceCalendarConvertor.INSTANCE.dos2Vos(resourceCalendarDOS);
        List<CollectionValueVO> shiftType = ipsFeign.getByCollectionCode("SHIFT_TYPE");
        Map<String, String> shiftTypeMap = shiftType.stream().collect(Collectors.toMap(CollectionValueVO::getCollectionValue, CollectionValueVO::getNotNullDescription));
        resourceCalendarVOS.forEach(t -> t.setDescription(shiftTypeMap.get(t.getCalendarType())));
        return resourceCalendarVOS;
    }

    @Override
    public Map<String, List<ResourceCalendarVO>> selectByStandardResourceIds(List<String> standardResourceIds) {
        // 查询标准资源下所有物理资源
//        List<PhysicalResourceVO> physicalResourceVOS = physicalResourceService.selectByParams(ImmutableMap.of("standardResourceIds", standardResourceIds));
//        Map<String, List<PhysicalResourceVO>> standardResourceIdToPhysicalResourceMaps = physicalResourceVOS.stream().collect(Collectors.groupingBy(PhysicalResourceVO::getStandardResourceId));
//        // 查询物理资源日历
//        List<String> physicalResourceIds = physicalResourceVOS.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
//        List<ResourceCalendarVO> resourceCalendarVOS = this.selectByParams(ImmutableMap.of("resourceIds", physicalResourceIds));
//        Map<String, List<ResourceCalendarVO>> physicalResourceIdToCalendarMaps = resourceCalendarVOS.stream().collect(Collectors.groupingBy(ResourceCalendarVO::getPhysicalResourceId));
        Map<String, List<ResourceCalendarVO>> resultMap = new HashMap<>();
//        for (String standardResourceId : standardResourceIds) {
//            List<ResourceCalendarVO> resourceCalendarVOList = new ArrayList<>();
//            List<PhysicalResourceVO> physicalResourceVOSOfResource = standardResourceIdToPhysicalResourceMaps.get(standardResourceId);
//            if (CollectionUtils.isEmpty(physicalResourceVOSOfResource)) {
//                log.info("standardResourceId" + standardResourceId);
//                continue;
//            }
//            List<String> physicalResourceIdsOfResource = physicalResourceVOSOfResource.stream().map(PhysicalResourceVO::getId).collect(Collectors.toList());
//            for (String physicalResourceId : physicalResourceIdsOfResource) {
//                List<ResourceCalendarVO> resourceCalendarVOSOfPhysicalResource = physicalResourceIdToCalendarMaps.get(physicalResourceId);
//                if (!CollectionUtils.isEmpty(resourceCalendarVOSOfPhysicalResource)) {
//                    resourceCalendarVOList.addAll(resourceCalendarVOSOfPhysicalResource);
//                }
//            }
//            resultMap.put(standardResourceId, resourceCalendarVOList);
//        }
        return resultMap;
    }

    @Override
    public List<WorkHourStatisticsVO> calWorkHour(List<String> standardResourceIds,
                                                  List<String> physicalResourceIds,
                                                  String timePeriodGroupId,
                                                  Date startDate,
                                                  Date endDate,
                                                  List<String> timePeriodIds) {


        List<OemProductLineMapPO> physicalResourceVOS;
        if (CollectionUtils.isNotEmpty(standardResourceIds) && CollectionUtils.isEmpty(physicalResourceIds)) {
            //physicalResourceVOS = oemProductLineDao.selectByOemCodes(standardResourceIds);
            physicalResourceVOS = oemProductLineMapDao.selectByLineCodes(standardResourceIds);
        } else {
            physicalResourceVOS = oemProductLineMapDao.selectByVehicleModelList(physicalResourceIds);
        }
        Map<String, OemProductLineMapPO> physicalResourceMap = physicalResourceVOS.stream()
                .collect(Collectors.toMap(OemProductLineMapPO::getVehicleModelCode, it -> it));
        physicalResourceIds = new ArrayList<>(physicalResourceMap.keySet());

        // 2 处理时间段
        List<TimeRangeDO> timeRangeDOList = new ArrayList<>();
        Date truncatedStartDate = startDate == null ? null : DateUtils.truncateTimeOfDate(startDate);
        Date truncatedEndDate = endDate == null ? null : DateUtils.truncateTimeOfDate(endDate);
        if (CollectionUtils.isNotEmpty(timePeriodIds)) {
//            List<TimePeriodVO> byTimePeriodIds = timePeriodService.getByTimePeriodIds(timePeriodIds);
//            timeRangeDOList = byTimePeriodIds.stream().map(it -> TimeRangeDO.builder()
//                    .startDate(it.getPeriodStart()).endDate(it.getPeriodEnd()).build()).collect(Collectors.toList());
        } else if (StringUtils.isNotEmpty(timePeriodGroupId)) {
//            List<TimePeriodVO> timePeriods = timePeriodService.getByTimePeriodGroupIds(Collections.singletonList(timePeriodGroupId));
//            List<TimeRangeDO> rawTimeRanges = timePeriods.stream()
//                    .map(it -> TimeRangeDO.builder().startDate(it.getPeriodStart()).endDate(it.getPeriodEnd()).build()).collect(Collectors.toList());
//            timeRangeDOList.addAll(rawTimeRanges);
            // 通过startDate和endDate筛选
        } else {
            List<Date> intervalDates = DateUtils.getIntervalDates(truncatedStartDate, truncatedEndDate);
            timeRangeDOList.addAll(
                    intervalDates.stream()
                            .map(date -> TimeRangeDO.builder().startDate(date).endDate(date).build())
                            .collect(Collectors.toList()));
//            timeRangeDOList.add(TimeRangeDO.builder().startDate(truncatedStartDate).endDate(truncatedEndDate).build());
        }

        if (truncatedStartDate != null && truncatedEndDate != null) {
            timeRangeDOList = timeRangeDOList.stream().map(it -> it.getIntersectionTime(truncatedStartDate, truncatedEndDate)).filter(Objects::nonNull).collect(Collectors.toList());
        }
        if (timeRangeDOList.isEmpty()) {
            return new ArrayList<>();
        }
        timeRangeDOList.sort(Comparator.comparing(TimeRangeDO::getStartDate));

        // 3 查询资源日历
        List<ResourceHourStatisticDO> resourceHourStatisticDOS = resourceCalendarDomainService.calWorkHour(timeRangeDOList, standardResourceIds, physicalResourceIds);
        Map<String, List<ResourceHourStatisticDO>> resourceHourStatisticDOMap = resourceHourStatisticDOS.stream().collect(Collectors.groupingBy(ResourceHourStatisticDO::getStandardResourceId));

        // 4 组装结果
        List<WorkHourStatisticsVO> res = new ArrayList<>();
        for (Map.Entry<String, List<ResourceHourStatisticDO>> stringListEntry : resourceHourStatisticDOMap.entrySet()) {
            String standardResourceId = stringListEntry.getKey();
            List<ResourceHourStatisticDO> values = stringListEntry.getValue();
            List<ResourceWorkHourStatisticsVO> physicalResourceWorkHourStatisticsVOList = values.stream().map(it -> {
                        String physicalResourceId = it.getPhysicalResourceId();
                        OemProductLineMapPO physicalResourceVO = physicalResourceMap.get(physicalResourceId);
                        return ResourceWorkHourStatisticsVO.builder()
                                .resourceId(physicalResourceId)
                                .resourceCode(physicalResourceVO == null ? physicalResourceId :
                                        physicalResourceVO.getVehicleModelCode())
                                .resourceName(physicalResourceVO == null ? physicalResourceId : physicalResourceVO.getVehicleModelCode())
                                .workHour(it.getWorkHour())
                                .overtimeHour(it.getOvertimeHour())
                                .resourceNum(it.getResourceNum())
                                .totalWorkHour(it.getWorkHour().add(it.getOvertimeHour()))
                                .dataMap(it.getWorkHourByTimeRange())
                                .dataMapForOvertimeHour(it.getOvertimeHourByTimeRange())
                                .dataMapForTotalTimeHour(it.getTotalHourByTimeRange())
                                .build();
                    }
            ).collect(Collectors.toList());

            Map<Date, String> dateMap = timeRangeDOList.stream().collect(
                    Collectors.toMap(TimeRangeDO::getStartDate, TimeRangeDO::dateString, (a, b) -> a)
            );
            Map<Date, String> sortedDateMap = new LinkedHashMap<>();
            dateMap.entrySet()
                    .stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEachOrdered(e -> sortedDateMap.put(e.getKey(), e.getValue()));
            dateMap = sortedDateMap;

            Map<String, BigDecimal> totalDataHour = new HashMap<>();
            // 统计所有资源的总工时
            Map<String, BigDecimal> totalWorkHour = values.stream()
                    .map(ResourceHourStatisticDO::getTotalHourByTimeRange)
                    .reduce(totalDataHour, (stringBigDecimalMap, stringBigDecimalMap2) -> {
                        stringBigDecimalMap2.forEach((key, value) -> {
                            BigDecimal bigDecimal = stringBigDecimalMap2.getOrDefault(key, BigDecimal.ZERO);
                            totalDataHour.computeIfPresent(key, (key1, oldValue) -> oldValue.add(bigDecimal));
                            totalDataHour.computeIfAbsent(key, key1 -> bigDecimal);

                        });
                        return stringBigDecimalMap;
                    });
            WorkHourStatisticsVO build = WorkHourStatisticsVO.builder()
                    .headList(new ArrayList<>(dateMap.values()))
                    .standardResourceId(standardResourceId)
                    .dataList(physicalResourceWorkHourStatisticsVOList)
                    .totalDateMap(totalWorkHour).build();
            res.add(build);


        }
        return res;
    }

    @Override
    public List<ResourceWorkHourStatisticsVO> calWorkHourByStandardResourceIds(List<String> standardResourceIds, Date startDate, Date endDate, List<String> timePeriodIds) {
        List<ResourceWorkHourStatisticsVO> res = new ArrayList<>();
//        // 如果是*,则查出所有的标准资源
//        if (standardResourceIds.contains("*")) {
//            List<StandardResourceVO> standardResourceVOS = standardResourceService.selectAll();
//            standardResourceIds = standardResourceVOS.stream().map(StandardResourceVO::getId).collect(Collectors.toList());
//        }
//        List<StandardResourceVO> standardResourceVOS = standardResourceService.selectByParams(ImmutableMap.of("ids", standardResourceIds));
//        Map<String, String> standardResourceIdToCodeMaps = standardResourceVOS.stream().collect(Collectors.toMap(BaseVO::getId, StandardResourceVO::getStandardResourceCode));
//        List<WorkHourStatisticsVO> workHourStatisticsVOList = calWorkHour(standardResourceIds, null, null, startDate, endDate, timePeriodIds);
//        Map<String, WorkHourStatisticsVO> workHourStatisticsVOMap = workHourStatisticsVOList.stream().collect(Collectors.toMap(WorkHourStatisticsVO::getStandardResourceId, it -> it));
//        for (String standResourceId : standardResourceIds) {
//            WorkHourStatisticsVO workHourStatisticsVO = workHourStatisticsVOMap.get(standResourceId);
//            if (workHourStatisticsVO == null || workHourStatisticsVO.getDataList() == null) {
//                continue;
//            }
//            // 汇总所有物理资源的正班工时
//            BigDecimal workHours = workHourStatisticsVO.getDataList().stream()
//                    .map(ResourceWorkHourStatisticsVO::getWorkHour)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            // 汇总所有物理资源的加班工时
//            BigDecimal overtimeHours = workHourStatisticsVO.getDataList().stream()
//                    .map(ResourceWorkHourStatisticsVO::getOvertimeHour)
//                    .filter(Objects::nonNull)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
//
//            ResourceWorkHourStatisticsVO vo = ResourceWorkHourStatisticsVO.builder()
//                    .resourceId(standResourceId)
//                    .resourceCode(standardResourceIdToCodeMaps.get(standResourceId))
//                    .workHour(
//                            workHours
//                    ).overtimeHour(
//                            overtimeHours
//                    )
//                    .totalWorkHour(
//                            workHours.add(overtimeHours)
//                    ).resourceNum(
//                            workHourStatisticsVO.getDataList().stream()
//                                    .map(ResourceWorkHourStatisticsVO::getResourceNum)
//                                    .reduce(0, Integer::sum)
//                    )
//                    .dataMapForTotalTimeHour(workHourStatisticsVO.getTotalDateMap())
//                    .dataMap(workHourStatisticsVO.getTotalDateMap())
//                    .build();
//            res.add(vo);
//        }
        return res;
    }

    @Override
    public void deleteByDateByResourceId(List<String> standardResourceIds, List<String> physicalResourceIds, Date date) {
        Date truncatedStartDate = date == null ? null : DateUtils.truncateTimeOfDate(date);
        List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByResourceIdsAndDate(
                standardResourceIds, physicalResourceIds, truncatedStartDate, null);
        doDelete(resourceCalendarPOS.stream().map(BasePO::getId).collect(Collectors.toList()));
    }

    /**
     * 根据物理资源ids获取物理资源日历集合
     *
     * @param physicalResourceIds
     * @return
     */
    @Override
    public List<ResourceCalendarVO> selectByPhysicalResourceIds(List<String> physicalResourceIds) {
        return this.selectByParams(ImmutableMap.of("physicalResourceIds", physicalResourceIds));
    }

    @Override
    public List<ResourceCalendarVO> selectResourceByStandardResourceIds(List<String> standardResourceIds) {
        return this.selectByParams(ImmutableMap.of("standardResourceIds", standardResourceIds));
    }

    @Override
    public String getObjectType() {
        return null;
    }

    @Override
    public List<ResourceCalendarVO> invocation(List<ResourceCalendarVO> dataList, Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        String dataSource = DynamicDataSourceContextHolder.getDataSource();
        int degree = invocation.split("#").length;
        ThreadPoolExecutor THREAD_POOL_EXECUTOR = CustomThreadPoolFactory.getDegreeThreadPool(degree);
        // 异步任务集合
        List<CompletableFuture<Void>> completableFutures = new ArrayList<>();
//        completableFutures.add(CompletableFuture.runAsync(() -> {
//            DynamicDataSourceContextHolder.setDataSource(dataSource);
//            // 冗余资源字段
//            physicalResourceService.addPhysicalResourceColumn(dataList, invocation, "setResource", "ids", "resourceId", "id");
//        }, THREAD_POOL_EXECUTOR));

        completableFutures.add(CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            // 冗余日历规则字段
            calendarRuleService.addCalendarRuleColumn(dataList, invocation, "setCalendarRule", "ids", "ruleId", "id");
        }, THREAD_POOL_EXECUTOR));

        completableFutures.add(CompletableFuture.runAsync(() -> {
            DynamicDataSourceContextHolder.setDataSource(dataSource);
            // 冗余班组字段
            shiftService.addShiftColumn(dataList, invocation, "setShift", "ids", "shiftId", "id");
        }, THREAD_POOL_EXECUTOR));
        CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).join();
        return dataList;
    }

    @Override
    public List<ResourceCalendarVO> getLatestMonths(List<String> oemCodes, List<String> months) {
        return resourceCalendarDao.selectOemLatestMonths(oemCodes, months);
    }

    /*
     * 新增车型映射，查询参数改变，车型对应physicalResource
     * @return
     */
    @Override
    public List<ResourceCalendarVO> getLastestMonthsByVehicle(List<String> vehicles, List<String> months){
        List<ResourceCalendarVO> result = new ArrayList<>();
        for (String month : months){
            List<ResourceCalendarVO> resourceCalendarVOS = resourceCalendarDao.selectLatestMonthsByVehicle(vehicles, month);
            if (CollectionUtils.isEmpty(resourceCalendarVOS)){
                Date date = DateUtils.stringToDate(month,DateUtils.YEAR_MONTH);
                resourceCalendarVOS = getMonthFillResourceCalendarWithWeekend(date);
            }
            result.addAll(resourceCalendarVOS);
        }

        return result;
    }

    /**
     * vehicle对应physical，oem原本对应standardIds，目前要通过oem查lineCode映射standardResourceId
     * @param oemCodes
     * @param vehicles
     * @param months
     * @return
     */
    @Override
    public List<ResourceCalendarVO> getMonthsByOemByVehicle(List<String> oemCodes,List<String> vehicles,
                                                            List<String> months){
        List<ResourceCalendarVO> result = new ArrayList<>();

        List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByOemCodeList(oemCodes);
        List<String> lineCodes = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getLineCode).collect(Collectors.toList());
        for (String month : months){
            Date date = DateUtils.stringToDate(month,DateUtils.YEAR_MONTH);
            Date firstDay = DateUtils.getMonthFirstDay(date);
            Date lastDay = DateUtils.getMonthLastDay(date);
            List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByResourceIdsAndDate(lineCodes,
                    vehicles,firstDay,lastDay);
            if (CollectionUtils.isEmpty(resourceCalendarPOS)){
                result.addAll(getMonthFillResourceCalendarWithWeekend(date));
            }else {
                result.addAll(ResourceCalendarConvertor.INSTANCE.po2Vos(resourceCalendarPOS));
            }
        }

        return result;
    }

    /*
     * return <oemCode,<month,resourceCalendarVOList>>
     */
    @Override
    public Map<String,Map<String,List<Date>>> getMonthMapByOemByVehicle(List<String> oemCodes,
                                                                             List<String> vehicles,
                                                            List<String> months){
        Map<String,Map<String,List<Date>>> result = new HashMap<>();

        for (String oemCode : oemCodes){
            List<OemProductLineMapPO> oemProductLineMapPOS =
                    oemProductLineMapDao.selectByOemCodeList(new ArrayList<>(Collections.singleton(oemCode)));
            List<String> lineCodes = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getLineCode).collect(Collectors.toList());
            Map<String,List<Date>> map = result.computeIfAbsent(oemCode,
                    monthMap -> new HashMap<>());
            for (String month : months){
                List<Date> list = map.computeIfAbsent(month,key->new ArrayList<>());
                Date date = DateUtils.stringToDate(month,DateUtils.YEAR_MONTH);
                Date firstDay = DateUtils.getMonthFirstDay(date);
                Date lastDay = DateUtils.getMonthLastDay(date);
                List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByResourceIdsAndDate(lineCodes,
                        vehicles,firstDay,lastDay);
                if (CollectionUtils.isEmpty(resourceCalendarPOS)){
                    list.addAll(getMonthFillDate(date));
                }else {
                    resourceCalendarPOS.forEach(resourceCalendarPO -> {
                        if (!list.contains(resourceCalendarPO.getWorkDay())){
                            list.add(resourceCalendarPO.getWorkDay());
                        }
                    });

                }
            }
        }
        return result;
    }

    @Override
    public Map<String,List<ResourceCalendarVO>> getResourceByOem(List<String> oemCodes, List<Date> dateList,
    		Boolean holidayWeekendFlag){
        if (CollectionUtils.isEmpty(oemCodes)) {
            return new HashMap<>();
        }
        List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByOemCodeList(oemCodes);

        //查询数据
        List<String> lineCodes =
                oemProductLineMapPOS.stream().map(OemProductLineMapPO::getLineCode).distinct().collect(Collectors.toList());
        Map<String, Object> params0 = new HashMap<>();
        params0.put("standardResourceIds", lineCodes);
        params0.put("workDayList", dateList);
        List<ResourceCalendarPO> resourceCalendarPOS = resourceCalendarDao.selectByParams(params0);
        List<ResourceCalendarVO > resourceCalendarVOS = ResourceCalendarConvertor.INSTANCE.po2Vos(resourceCalendarPOS);

        //by oem分组
        //lineCode和vehicleModelCode确认唯一oem
        Map<String, String> oemMap = oemProductLineMapPOS.stream()
                .filter(item -> item.getLineCode() != null && item.getVehicleModelCode() != null && item.getOemCode() != null)
                .collect(Collectors.toMap(
                        item -> item.getLineCode() + "#" + item.getVehicleModelCode(),
                        OemProductLineMapBasicPO::getOemCode,
                        (existing, replacement) -> existing) // 处理键冲突，保留第一个出现的值
                );

        Map<String,List<ResourceCalendarVO>> result = new HashMap<>();
        resourceCalendarVOS.forEach(item->{
            String lineCodeAndVehicleModelCode = item.getStandardResourceId() + "#" + item.getPhysicalResourceId();

            if (oemMap.containsKey(lineCodeAndVehicleModelCode)){
                String oem = oemMap.get(lineCodeAndVehicleModelCode);
                List<ResourceCalendarVO> list = result.computeIfAbsent(oem,
                        key -> new ArrayList<>());
                list.add(item);
            }
        });

        List<String> monthList =
                dateList.stream().map(item->DateUtils.dateToString(item,DateUtils.YEAR_MONTH)).distinct().collect(Collectors.toList());
        result.forEach((key,value)->{
            //by月填充
            Map<String,List<ResourceCalendarVO>> resourceMap = value.stream()
                    .collect(Collectors.groupingBy(item->DateUtils.dateToString(item.getWorkDay(),DateUtils.YEAR_MONTH)));

            for (String month : monthList){
                if (CollectionUtils.isEmpty(resourceMap.get(month))){
                    value.addAll(getMonthFillResourceWithWeekend(month));
                }
            }

        });
        
        for (String oemCode : oemCodes){
            if (result.containsKey(oemCode)){
                continue;
            }
            List<ResourceCalendarVO> resourceCalendarVOList = new ArrayList<>();
            if(holidayWeekendFlag) {
            	//默认周末放假
            	for (Date d : dateList){
            		if(isWeekend(d)){
                         continue;
            		}
                    ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
                    resourceCalendarVO.setWorkDay(d);
                    resourceCalendarVOList.add(resourceCalendarVO);
                }
            }else {
            	//周末不放假
            	for (String month : monthList){
                    resourceCalendarVOList.addAll(getMonthFillResourceWithWeekend(month));
                }
            }
            result.put(oemCode,resourceCalendarVOList);
        }

        return result;
    }



    @Override
    public List<ResourceCalendarVO> getResourceByOem(String oemCode){

        List<OemProductLineMapPO> oemProductLineMapPOS = oemProductLineMapDao.selectByOemCodeList(new ArrayList<>(Collections.singleton(oemCode)));
        List<String> lineCodes = oemProductLineMapPOS.stream().map(OemProductLineMapPO::getLineCode).collect(Collectors.toList());
        Map<String, Object> params0 = new HashMap<>();
        params0.put("standardResourceIds", lineCodes);

        return ResourceCalendarConvertor.INSTANCE.po2Vos(resourceCalendarDao.selectByParams(params0));
    }

    /**
     * 如果日期当月无数据，则填充当月日历，周六、周日不填充
     * @param date
     * @return
     */
    public List<ResourceCalendarVO> getMonthFillResourceCalendarWithWeekend(Date date){
        Date firstDay = DateUtils.getMonthFirstDay(date);
        Date lastDay = DateUtils.getMonthLastDay(date);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);
        return getMonthFillResourceCalendarWithWeekend(dateList);
    }

    public List<Date> getMonthFillDate(Date date){
        Date firstDay = DateUtils.getMonthFirstDay(date);
        Date lastDay = DateUtils.getMonthLastDay(date);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);

        List<Date> list = new ArrayList<>();
        for (Date d : dateList){
            if (isWeekend(d)){
                continue;
            }
            list.add(d);
        }
        return list;
    }

    /**
     * 如果日期当月无数据，则填充当月日历，周六、周日不填充
     * 返回的是默认的虚拟数据
     * @param month
     * @return
     */
    @Override
    public List<ResourceCalendarVO> getMonthFillResourceCalendar(String month){
        Date date = DateUtils.stringToDate(month,DateUtils.YEAR_MONTH);
        Date firstDay = DateUtils.getMonthFirstDay(date);
        Date lastDay = DateUtils.getMonthLastDay(date);
        List<Date> dateList = DateUtils.getIntervalDates(firstDay,lastDay);
        List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
        for (Date d : dateList){
            if (isWeekend(d)){
                continue;
            }
            ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
            resourceCalendarVO.setWorkDay(d);
            resourceCalendarVOS.add(resourceCalendarVO);
        }
        return resourceCalendarVOS;
    }

    public List<ResourceCalendarVO> getMonthFillResourceWithWeekend(String month){
        Date date = DateUtils.stringToDate(month,DateUtils.YEAR_MONTH);
        return getMonthFillResourceCalendarWithWeekend(date);
    }

    public List<ResourceCalendarVO> getMonthFillResourceCalendarWithWeekend(List<Date> dateList){
        List<ResourceCalendarVO> resourceCalendarVOS = new ArrayList<>();
        for (Date d : dateList){
            // if (isWeekend(d)){
            //     continue;
            // }
            ResourceCalendarVO resourceCalendarVO = new ResourceCalendarVO();
            resourceCalendarVO.setWorkDay(d);
            resourceCalendarVOS.add(resourceCalendarVO);
        }
        return resourceCalendarVOS;
    }

    protected boolean isWeekend(Date date){
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }
}
