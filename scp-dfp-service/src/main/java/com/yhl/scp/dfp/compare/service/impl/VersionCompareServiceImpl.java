package com.yhl.scp.dfp.compare.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.scp.dfp.common.PageUtils;
import com.yhl.scp.dfp.compare.dto.VersionCompareDTO;
import com.yhl.scp.dfp.compare.service.IVersionCompare;
import com.yhl.scp.dfp.compare.service.VersionCompareService;
import com.yhl.scp.dfp.compare.service.factory.VersionCompareFactory;
import com.yhl.scp.dfp.compare.vo.VersionCompareVO;

/**
 * <code>VersionCompareServiceImpl</code>
 * <p>
 * 版本对比服务实现类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-29 11:25:36
 */
@Service
public class VersionCompareServiceImpl implements VersionCompareService {

    @Resource
    private VersionCompareFactory versionCompareFactory;

    @Override
    public List<LabelValue<String>> queryVersionByVersionType(String versionType) {
        IVersionCompare versionCompare = versionCompareFactory.getVersionCompare(versionType);
        return versionCompare.queryVersion();
    }

	@Override
    public PageInfo<VersionCompareVO> doVersionCompare(VersionCompareDTO versionCompareDTO) {
        IVersionCompare versionCompare = versionCompareFactory.getVersionCompare(versionCompareDTO.getVersionType());
        List<VersionCompareVO> versionCompareList = versionCompare.doVersionCompare(versionCompareDTO);
        Integer adjustNumber = versionCompareDTO.getAdjustNumber();
        BigDecimal adjustRatio = versionCompareDTO.getAdjustRatio();
        Boolean notSameFlag = versionCompareDTO.getNotSameFlag();
        if(adjustNumber != null) {
        	versionCompareList = versionCompareList.stream()
        			.filter( e -> e.getAdjustNumber() > adjustNumber).collect(Collectors.toList());
        }
        if(adjustRatio != null) {
        	versionCompareList = versionCompareList.stream()
        			.filter( e -> StringUtils.isNotEmpty(e.getAdjustRatio()) 
        					&& new BigDecimal(e.getAdjustRatio().replace("%", "")).abs().compareTo(adjustRatio) > 0)
        			.collect(Collectors.toList());
        }
        if(notSameFlag != null && notSameFlag) {
        	versionCompareList = versionCompareList.stream()
        			.filter( e -> StringUtils.isNotEmpty(e.getAdjustType()) 
        					&& !"一致".equals(e.getAdjustType()))
        			.collect(Collectors.toList());
        }
        //数据进行过滤（偏差阈值大于，调整比例）
        Integer pageNum = versionCompareDTO.getPageNum() == null ? 1 : versionCompareDTO.getPageNum();
        Integer pageSize = versionCompareDTO.getPageSize() == null ? 50 : versionCompareDTO.getPageSize();
        return PageUtils.getPageInfo(versionCompareList, pageNum, pageSize);
    }
}
