package com.yhl.scp.dfp.stock.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.QueryCriteriaParam;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.FormatConversionUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import com.yhl.scp.dfp.delivery.domain.service.DeliveryPlanDomainService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanService;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.service.impl.DeliveryPlanServiceImpl;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.stock.convertor.InventoryShiftConvertor;
import com.yhl.scp.dfp.stock.domain.entity.InventoryShiftDO;
import com.yhl.scp.dfp.stock.domain.service.InventoryShiftDomainService;
import com.yhl.scp.dfp.stock.dto.InventoryShiftDTO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.switchrelation.service.DfpSwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.DfpSwitchRelationBetweenProductVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <code>InventoryShiftServiceImpl</code>
 * <p>
 * 库存推移表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 10:26:59
 */
@Slf4j
@Service
public class InventoryShiftServiceImpl extends AbstractService implements InventoryShiftService {

	private final String QUERY_TYPE_OLD = "old";

	private final String QUERY_TYPE_NEW = "new";

    @Resource
    private InventoryShiftDao inventoryShiftDao;

    @Resource
    private InventoryShiftDomainService inventoryShiftDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private DfpSwitchRelationBetweenProductService DfpSwitchRelationBetweenProductService;

    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Resource
    private DeliveryPlanService deliveryPlanService;

    @Resource
    private DeliveryPlanDomainService deliveryPlanDomainService;

    @Resource
    private OemTransportTimeService oemTransportTimeService;

    @Resource
    private NewMdsFeign newMdsFeign;

    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @Resource
    private OemService oemService;

    @Resource
    private OemInventorySubmissionService oemInventorySubmissionService;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(InventoryShiftDTO inventoryShiftDTO) {
        // 0.数据转换
        InventoryShiftDO inventoryShiftDO = InventoryShiftConvertor.INSTANCE.dto2Do(inventoryShiftDTO);
        InventoryShiftPO inventoryShiftPO = InventoryShiftConvertor.INSTANCE.dto2Po(inventoryShiftDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryShiftDomainService.validation(inventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(inventoryShiftPO);
        inventoryShiftDao.insert(inventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(InventoryShiftDTO inventoryShiftDTO) {
        // 0.数据转换
        InventoryShiftDO inventoryShiftDO = InventoryShiftConvertor.INSTANCE.dto2Do(inventoryShiftDTO);
        InventoryShiftPO inventoryShiftPO = InventoryShiftConvertor.INSTANCE.dto2Po(inventoryShiftDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        inventoryShiftDomainService.validation(inventoryShiftDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(inventoryShiftPO);
        inventoryShiftDao.update(inventoryShiftPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<InventoryShiftDTO> list) {
        List<InventoryShiftPO> newList = InventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        inventoryShiftDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<InventoryShiftDTO> list) {
        List<InventoryShiftPO> newList = InventoryShiftConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        inventoryShiftDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return inventoryShiftDao.deleteBatch(idList);
        }
        return inventoryShiftDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public InventoryShiftVO selectByPrimaryKey(String id) {
        InventoryShiftPO po = inventoryShiftDao.selectByPrimaryKey(id);
        return InventoryShiftConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "INVENTORY_SHIFT")
    public List<InventoryShiftVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "INVENTORY_SHIFT")
    public List<InventoryShiftVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<InventoryShiftVO> dataList = inventoryShiftDao.selectByCondition(sortParam, queryCriteriaParam);
        InventoryShiftServiceImpl target = SpringBeanUtils.getBean(InventoryShiftServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<InventoryShiftVO> selectByParams(Map<String, Object> params) {
        List<InventoryShiftPO> list = inventoryShiftDao.selectByParams(params);
        return InventoryShiftConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<InventoryShiftVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<InventoryShiftVO> queryInventoryShiftData(InventoryShiftDTO inventoryShiftDTO) {
        List<InventoryShiftPO> inventoryShiftPOS = inventoryShiftDao.selectByParams(ImmutableMap.of("productCode", inventoryShiftDTO.getProductCode()));

        // List<InventoryShiftDetailVO> inventoryShiftDetailVOList = Lists.newArrayList();
        // for (String category : CategoryEnum.inventoryShiftCategorySet) {
        //     InventoryShiftDetailVO inventoryShiftDetailVO = new InventoryShiftDetailVO();
        //     inventoryShiftDetailVO.setCategory(category);
        //
        //     inventoryShiftDetailVO.setDetailList(getDynamicDataDetailVOList(category,inventoryShiftPOS));
        //     inventoryShiftDetailVOList.add(inventoryShiftDetailVO);
        // }

        return InventoryShiftConvertor.INSTANCE.po2Vos(inventoryShiftPOS);
    }

    @Override
    public List<InventoryShiftVO> selectPageByWeek(List<InventoryShiftVO> inventoryShiftVOList) {
        List<InventoryShiftVO> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(inventoryShiftVOList)){
            return result;
        }

        DeliveryPlanVersionVO deliveryPlanVersionVO =
                deliveryPlanVersionService.selectByPrimaryKey(inventoryShiftVOList.get(0).getVersionId());
        if (deliveryPlanVersionVO == null){
            return result;
        }
        List<String> productScope = new ArrayList<>();
        //如果是工程变更物料，则productCode是用&拼接新旧两个物料的，如果&判断不准确则从SwitchRelationBetweenProduct中查询
        if (StringUtils.isNotEmpty(inventoryShiftVOList.get(0).getProductCode()) && inventoryShiftVOList.get(0).getProductCode().contains("&")){
            productScope = Arrays.asList(inventoryShiftVOList.get(0).getProductCode().split("&"));
        }else if (StringUtils.isNotEmpty(inventoryShiftVOList.get(0).getProductCode())){
            productScope.add(inventoryShiftVOList.get(0).getProductCode());
        }
        List<DeliveryPlanVO> deliveryPlanVOList =deliveryPlanService.selectByParams(ImmutableMap.of(
                "versionId", inventoryShiftVOList.get(0).getVersionId(),
                "productCodes", productScope,
                "oemCode",inventoryShiftVOList.get(0).getOemCode()));
        if (CollectionUtils.isEmpty(deliveryPlanVOList)){
            return result;
        }
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS =
                oemInventorySubmissionService.selectByParams(ImmutableMap.of(
                        "oemCode", inventoryShiftVOList.get(0).getOemCode(),
                        "productCode", inventoryShiftVOList.get(0).getProductCode()));
        oemInventorySubmissionVOS = oemInventorySubmissionVOS.stream()
                .filter(t -> null != t.getSubmissionDate()).collect(Collectors.toList());
        oemInventorySubmissionVOS.sort(Comparator.comparing(OemInventorySubmissionVO::getSubmissionDate));
        List<OemVO> oemVOList = oemService.selectByParams(ImmutableMap.of("oemCode",
                inventoryShiftVOList.get(0).getOemCode()));
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordVOList = new ArrayList<>();
        //在途数据
        if (CollectionUtils.isNotEmpty(oemVOList) && StringUtils.isNotEmpty(oemVOList.get(0).getTargetStockLocation())){
            Map<String,String> shipmentLocatorCodesMap = new HashMap<>();
            oemVOList.forEach(t->{
                String[] targetStockLocations = t.getTargetStockLocation().split(",");
                List<String> shipmentLocatorCodes = Arrays.stream(targetStockLocations)
                        .filter(StringUtils::isNotEmpty)
                        .collect(Collectors.toList());
                if (com.yhl.platform.common.utils.CollectionUtils.isNotEmpty(shipmentLocatorCodes)){
                    shipmentLocatorCodes.forEach(shipmentLocatorCode->{
                        shipmentLocatorCodesMap.put(shipmentLocatorCode,t.getOemCode());
                    });
                }
            });
            warehouseReleaseRecordVOList =
                    warehouseReleaseToWarehouseService.getInRoad(Collections.singletonList(inventoryShiftVOList.get(0).getProductCode()),
                            new ArrayList<>(shipmentLocatorCodesMap.keySet()));
        }

        //运输时间
        int oemTransportSupplyDays = 0;
        if (StringUtils.isNotEmpty(deliveryPlanVOList.get(0).getTransportationRouteId())){
            oemTransportSupplyDays = getTransportationDays(deliveryPlanVOList.get(0).getTransportationRouteId());
        }

        //需求数量
        String versionCode = deliveryPlanVersionVO.getVersionCode();
        Date lastDay = DateUtils.stringToDate(versionCode.substring(versionCode.length() - 13,
                versionCode.length() - 5), "yyyyMMdd");
        Map<String,Integer> demandMap = getDemandMap(deliveryPlanVersionVO,inventoryShiftVOList.get(0).getOemCode(),
                deliveryPlanVOList.get(0).getProductCode(),DateUtils.getWeekFirstDay(lastDay));

        // 按周分组
        Map<Date, List<InventoryShiftVO>> groupedByWeek = inventoryShiftVOList.stream()
                .collect(Collectors.groupingBy(vo -> DateUtils.getWeekFirstDay(vo.getPlannedDate())));
        for (Map.Entry<Date, List<InventoryShiftVO>> entry : groupedByWeek.entrySet()) {
            InventoryShiftVO inventoryShiftVO = new InventoryShiftVO();
            List<InventoryShiftVO> weekList = entry.getValue();
            if (CollectionUtils.isEmpty(weekList)){
                continue;
            }
            //按计划时间排序
            weekList.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate));
            //初始化shift，从原本的里面复制属性
            initInventoryShiftVO(inventoryShiftVO,weekList.get(0),weekList.get(weekList.size()-1));

            Date startDate = entry.getValue().get(0).getPlannedDate();
            Date weekFirstDate = DateUtils.getWeekFirstDay(startDate);
            //计划时间取周一
            inventoryShiftVO.setDeliveryDate(weekList.get(0).getDeliveryDate());
            inventoryShiftVO.setPlannedDate(weekFirstDate);

            int customerDemand = weekList.stream().mapToInt(item ->
                    item.getCustomerDemand() != null ? item.getCustomerDemand() : 0).sum();
            // int arrivalPlan = weekList.stream().mapToInt(item ->
            //         item.getArrivalPlan() != null ? item.getArrivalPlan() : 0).sum();
            int deliveryPlan = weekList.stream().mapToInt(item ->
                    item.getDeliveryPlan() != null ? item.getDeliveryPlan() : 0).sum();
            int receiveQuantity = weekList.stream().mapToInt(item ->
                    item.getReceive() != null ? item.getReceive() : 0).sum();
            if (!weekFirstDate.equals(startDate)){
                List<Date> dateList = DateUtils.getIntervalDates(weekFirstDate,startDate);
                for (Date date : dateList){
                    if (date.equals(startDate)){
                        //当前开始时间不算，只算周一到开始时间之前
                        continue;
                    }
                    receiveQuantity += getReceiveQuantity(date,oemTransportSupplyDays,warehouseReleaseRecordVOList,oemInventorySubmissionVOS);
                    customerDemand += demandMap.getOrDefault(DateUtils.dateToString(date),0);
                }

                inventoryShiftVO.setDeliveryDate(DateUtils.moveDay(weekFirstDate, -oemTransportSupplyDays));
            }
            inventoryShiftVO.setArrivalPlan(deliveryPlan+receiveQuantity);
            inventoryShiftVO.setDeliveryPlan(deliveryPlan);
            inventoryShiftVO.setReceive(receiveQuantity);
            inventoryShiftVO.setCustomerDemand(customerDemand);

            result.add(inventoryShiftVO);
        }
        result.sort(Comparator.comparing(InventoryShiftVO::getPlannedDate));
        return result;
    }

    private Integer getReceiveQuantity(Date date,
                                       Integer supplyDays,
                                       List<WarehouseReleaseToWarehouseVO> warehouseReleaseRecordList,
                                       List<OemInventorySubmissionVO> receiveOemInventorySubmissionVOList) {
        int receiveQuantity = 0;
        if (CollectionUtils.isNotEmpty(warehouseReleaseRecordList)) {
            for (WarehouseReleaseToWarehouseVO warehouseReleaseRecordVO : warehouseReleaseRecordList) {
                if (warehouseReleaseRecordVO.getActualCompletionTime() != null) {
                    continue;
                }
                // 如果预计到达时间有值，实际到达时间没值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() != null
                        && DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getEstimatedCompletionTime()).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();
                }

                // 预计到达时间没有值
                if (warehouseReleaseRecordVO.getEstimatedCompletionTime() == null
                        && DateUtils.moveDay(DateUtils.getDayFirstTime(warehouseReleaseRecordVO.getCreationDate()),
                        supplyDays).compareTo(date) == 0) {
                    receiveQuantity += warehouseReleaseRecordVO.getSumQty().intValue();
                }

            }
        }

        if (CollectionUtils.isEmpty(receiveOemInventorySubmissionVOList)) {
            return receiveQuantity;
        }
        for (OemInventorySubmissionVO oemInventorySubmissionVO : receiveOemInventorySubmissionVOList){
            if (oemInventorySubmissionVO.getSubmissionDate() != null && oemInventorySubmissionVO.getTransitWaitQuantity() != null && DateUtils.getDayFirstTime(oemInventorySubmissionVO.getSubmissionDate()).compareTo(date) == 0){
                receiveQuantity += oemInventorySubmissionVO.getTransitWaitQuantity().intValue();
            }
        }
        return receiveQuantity;
    }

    private Map<String,Integer> getDemandMap(DeliveryPlanVersionVO deliveryPlanVersion,String oemCode,
                                             String productCode,Date weekFirstDate){
        //准备合并需求需要的参数
        List<String> productCodes = Collections.singletonList(productCode);
        List<NewProductStockPointVO> newProductStockPointVOS =
                newMdsFeign.selectProductStockPointVOByProductCodes(SystemHolder.getScenario(), productCodes);
        List<String> vehicleModelCodeList =
                newProductStockPointVOS.stream().map(NewProductStockPointVO::getVehicleModelCode).distinct().collect(Collectors.toList());
        List<String> months = new ArrayList<>();
        months.add(DateUtils.dateToString(weekFirstDate, DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(weekFirstDate, 1), DateUtils.YEAR_MONTH));

        Map<String,List<CleanDemandDataDetailVO>> cleanDemandDataDetailVOMap =
                deliveryPlanDomainService.mergeCleanDemandAndForecastDemandData(deliveryPlanVersion,
                        Collections.singletonList(oemCode),productCodes,vehicleModelCodeList,months,weekFirstDate,true);
        List<CleanDemandDataDetailVO> cleanDemandDataDetailVOList =
                cleanDemandDataDetailVOMap.get(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
        if (CollectionUtils.isEmpty(cleanDemandDataDetailVOList)){
            return new HashMap<>();
        }
        return cleanDemandDataDetailVOList.stream()
                .filter(t -> t.getDemandQuantity() != null)
                .collect(Collectors.groupingBy(t -> DateUtils.dateToString(t.getDemandTime()),
                        Collectors.summingInt(t -> t.getDemandQuantity().intValue())));
    }

    public int getTransportationDays(String transportId){
        OemTransportTimeVO oemTransportTimeVO =
                oemTransportTimeService.selectByPrimaryKey(transportId);
        if (null == oemTransportTimeVO){
            return 0;
        }
        return DeliveryPlanServiceImpl.calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
                0 : oemTransportTimeVO.getTransportationTime().doubleValue());
    }

    public void initInventoryShiftVO(InventoryShiftVO inventoryShiftVO, InventoryShiftVO firstShiftVO,
                                     InventoryShiftVO lastShiftVO){

        inventoryShiftVO.setProductCode(firstShiftVO.getProductCode());
        inventoryShiftVO.setOpeningInventory(firstShiftVO.getOpeningInventory());
        inventoryShiftVO.setOemOpeningInventory(firstShiftVO.getOemOpeningInventory());

        inventoryShiftVO.setEndingInventory(lastShiftVO.getEndingInventory());
        inventoryShiftVO.setOemEndingInventory(lastShiftVO.getOemEndingInventory());
        inventoryShiftVO.setAccumulatedInventoryGap(lastShiftVO.getAccumulatedInventoryGap());
        inventoryShiftVO.setEndingInventoryMinSafeDiff(lastShiftVO.getEndingInventoryMinSafeDiff());
        inventoryShiftVO.setEndingInventoryDays(lastShiftVO.getEndingInventoryDays());
        //安全库存配置
        inventoryShiftVO.setMinimumSafetyInventoryDays(lastShiftVO.getMinimumSafetyInventoryDays());
        inventoryShiftVO.setStandardSafetyInventoryDays(lastShiftVO.getStandardSafetyInventoryDays());
        inventoryShiftVO.setStandardSafetyInventoryLevel(lastShiftVO.getStandardSafetyInventoryLevel());
    }

    private List<DynamicDataDetailVO> getDynamicDataDetailVOList(String category, List<InventoryShiftPO> inventoryShiftPOS) {
        List<DynamicDataDetailVO> dynamicDataDetailVOList = Lists.newArrayList();
        switch (category) {
            case "期初库存":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "客户需求":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "到货计划":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "期末库存":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "期末库存天数":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "标准安全库存水位":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "标准安全库存天数":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "最小安全库存天数":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;
            case "累计库存缺口":
                for (InventoryShiftPO inventoryShiftPO : inventoryShiftPOS) {
                    DynamicDataDetailVO dynamicDataDetailVO = new DynamicDataDetailVO();
                    dynamicDataDetailVO.setSaleDate(DateUtils.dateToString(inventoryShiftPO.getPlannedDate(), "yyyyMMdd"));
                    dynamicDataDetailVO.setSaleQuantity(String.valueOf(inventoryShiftPO.getCustomerDemand()));
                    dynamicDataDetailVOList.add(dynamicDataDetailVO);
                }
                break;


        }

        return dynamicDataDetailVOList;
    }


    @Override
    public String getObjectType() {
        return ObjectTypeEnum.INVENTORY_SHIFT.getCode();
    }

    @Override
    public List<InventoryShiftVO> invocation(List<InventoryShiftVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

	@Override
	public List<String> getPageQueryCriteriaParam(String queryParam) {
        if (StringUtils.isEmpty(queryParam)){
            return new ArrayList<>();
        }
		String queryCriteriaParam = null;
		String queryType = QUERY_TYPE_OLD;
		List<QueryCriteriaParam> paramList = FormatConversionUtils.getQueryCriteriaParams(queryParam);
		//获取omeCode和productCode
		String oemCode = null;
		String productCode = null;
		for (QueryCriteriaParam criteriaParam : paramList) {
			if("oemCode".equals(criteriaParam.getProperty())) {
				oemCode = criteriaParam.getValue1();
    		}else if("productCode".equals(criteriaParam.getProperty())) {
    			productCode = criteriaParam.getValue1();
    		}
		}
		//通过omeCode和productCode获取新旧产品工程变更关系数据
		List<DfpSwitchRelationBetweenProductVO> betweenProductList = DfpSwitchRelationBetweenProductService
				.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oldOrNewProduct", productCode,
    			"oemCode" , oemCode));
		if(CollectionUtils.isNotEmpty(betweenProductList)) {
			DfpSwitchRelationBetweenProductVO betweenProductVO = betweenProductList.get(0);
			String combinationProductCode = betweenProductVO.getOldProductCode() + "&" + betweenProductVO.getNewProductCode();
        	for (QueryCriteriaParam criteriaParam : paramList) {
        		if("productCode".equals(criteriaParam.getProperty())) {
        			criteriaParam.setValue1(combinationProductCode);
        		}
        	}
        	queryParam = JSONObject.toJSONString(paramList);
        	queryType = QUERY_TYPE_NEW;
		}
    	queryCriteriaParam = FormatConversionUtils.getQueryCriteriaParam(queryParam);
		return Arrays.asList(queryCriteriaParam, queryType);
	}

	@Override
	public List<InventoryShiftVO> selectByNewPage(Pagination pagination, String sortParam, String queryParam,boolean weeklySummary) {
        //判断productCode是否存在工程变更的新旧物料，如果存在则改变productCode的查询方式，用oldProduct+newProduct组合
		List<String> queryCriteriaParamList = this.getPageQueryCriteriaParam(queryParam);
		PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<InventoryShiftVO> pageList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(queryCriteriaParamList)){
            pageList = this.selectByCondition(sortParam, queryCriteriaParamList.get(0)).stream().sorted(Comparator
                    .comparing(InventoryShiftVO::getPlannedDate)).collect(Collectors.toList());
        }else {
            pageList = this.selectByCondition(sortParam, null).stream().sorted(Comparator
                    .comparing(InventoryShiftVO::getPlannedDate)).collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(pageList)) {
            if (weeklySummary){//
                // by周汇总
                pageList = selectPageByWeek(pageList);
            }
            if (CollectionUtils.isNotEmpty(pageList)) {
                List<Date> dateList = pageList.stream().map(InventoryShiftVO::getPlannedDate).distinct().collect(Collectors.toList());
                pageList.get(0).setDateList(dateList);
                pageList.forEach(e -> {
                    if (e.getDeliveryPlan() != null){
                        e.setArrivalPlanDetail(Arrays.asList("发货: "+e.getDeliveryPlan(), "在途: "+e.getReceive()));
                    }else {
                        e.setArrivalPlanDetail(Collections.singletonList(String.valueOf(e.getArrivalPlan())));
                    }

                });
                if (CollectionUtils.isNotEmpty(queryCriteriaParamList) && QUERY_TYPE_NEW.equals(queryCriteriaParamList.get(1))) {
                    //处理展示的字段数据信息
                    pageList.forEach(e -> {
                        String[] splitProductCode = e.getProductCode().split("&");
                        e.setOpeningInventoryDesc(Arrays.asList(splitProductCode[0] + " " + e.getOldOpeningInventory(),
                                splitProductCode[1] + " " + e.getNewOpeningInventory()));
                        e.setArrivalPlanDesc(Arrays.asList(splitProductCode[0] + "发货 " + e.getOldDeliveryPlan(),
                                splitProductCode[1] + "发货 " + e.getNewDeliveryPlan(),
                                splitProductCode[0] + "在途 " + e.getOldReceive(),
                                splitProductCode[1]+ "在途 " + e.getNewReceive()));
                        e.setEndingInventoryDesc(Arrays.asList(splitProductCode[0] + " " + e.getOldEndingInventory(),
                                splitProductCode[1] + " " + e.getNewEndingInventory()));

                        //工程变更分两行展示
                        e.setOpeningInventoryList(Arrays.asList(getDesc(e.getOldOpeningInventory()),
                                getDesc(e.getNewOpeningInventory())));
                        e.setCustomerDemandList((Arrays.asList(getDesc(e.getOldDemand()),
                                getDesc(e.getNewDemand()))));
                        e.setArrivalPlanList(Arrays.asList("发货 "+getDesc(e.getOldDeliveryPlan())+" 在途"+getDesc(e.getOldReceive()),
                                "发货 "+getDesc(e.getNewDeliveryPlan()) +" 在途"+getDesc(e.getNewReceive())));
                        e.setEndingInventoryList(Arrays.asList(getDesc(e.getOldEndingInventory()),
                                getDesc(e.getNewEndingInventory())));

                        e.setOemOpeningInventoryList((Arrays.asList(getDesc(e.getOldOemOpeningInventory()),
                                getDesc(e.getNewOemOpeningInventory()))));
                        e.setOemEndingInventoryList((Arrays.asList(getDesc(e.getOldOemEndingInventory()),
                                getDesc(e.getNewOemEndingInventory()))));
                        e.setEndingInventoryDaysList((Arrays.asList(e.getOldEndingInventoryDays(),
                                e.getNewEndingInventoryDays())));
                    });
                }
            }
        }
        return pageList;
	}

    private String getDesc(Integer trans){
        if (trans == null){
            return "";
        }
        return String.valueOf(trans);
    }

    private String getDesc(BigDecimal trans){
        if (trans == null){
            return "";
        }
        return String.valueOf(trans.doubleValue());
    }

    public boolean isWeekend(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }

    @Override
    public List<InventoryShiftVO> selectVOByParams(Map<String, Object> params) {
        return inventoryShiftDao.selectVOByParams(params);
    }
}
