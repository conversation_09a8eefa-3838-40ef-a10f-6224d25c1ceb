package com.yhl.scp.dfp.clean.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.scp.dfp.clean.dto.CleanForecastDataDTO;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.constants.RedisConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.utils.BpimDateUtil;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.mrp.common.constants.MaterialRedisConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>CleanForecastDataController</code>
 * <p>
 * 滚动预测数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Api(tags = "滚动预测数据控制器")
@RestController
@RequestMapping("cleanForecastData")
public class CleanForecastDataController extends BaseController {

    @Resource
    private CleanForecastDataService cleanForecastDataService;

    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private RedisUtil redisUtil;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<CleanForecastDataVO>> page() {
        List<CleanForecastDataVO> cleanForecastDataList = cleanForecastDataService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<CleanForecastDataVO> pageInfo = new PageInfo<>(cleanForecastDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody CleanForecastDataDTO cleanForecastDataDTO) {
        return cleanForecastDataService.doCreate(cleanForecastDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody CleanForecastDataDTO cleanForecastDataDTO) {
        return cleanForecastDataService.doUpdate(cleanForecastDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        cleanForecastDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<CleanForecastDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, cleanForecastDataService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "重新计算")
    @GetMapping(value = "recalculate")
    public BaseResponse<String> recalculate(@RequestParam(name = "versionCode", required = false) String versionCode) {

        String redisKey = RedisConstants.CLEAN_FORECAST_DATA_RECALCULATE_KEY;
        try {
            if (redisUtil.hasKey(redisKey)){
                return BaseResponse.error("当前已有预测需求正在计算中，请等待计算完成");
            }
            redisUtil.set(redisKey, SystemHolder.getUserId(), 60 * 60);
            //获取最新版本
            DemandVersionVO demandVersionVO = demandVersionService.selectLastVersionByVersionTypeAndPlanPeriod(VersionTypeEnum.CLEAN_FORECAST.getCode(), null);
            if (null != demandVersionVO) {
                cleanForecastDataService.recalculate(demandVersionVO.getVersionCode());
            }
            return BaseResponse.success(BaseResponse.OP_SUCCESS);
        } catch (Exception e) {
            log.error("预测需求计算失败", e);
            throw new BusinessException("预测需求计算失败,{0}", e.getLocalizedMessage());
        }finally {
            redisUtil.delete(redisKey);
        }
    }

    @ApiOperation(value = "自动计算")
    @GetMapping(value = "autoCalculate")
    public BaseResponse<String> autoCalculate() {
        try {
            cleanForecastDataService.autoCalculate();
        } catch (Exception e) {
            return BaseResponse.error(BaseResponse.OP_FAILURE, e.getMessage());
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        cleanForecastDataService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
}
