package com.yhl.scp.dfp.originDemand.service.impl;

import cn.hutool.core.map.MapUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.originDemand.convertor.FdpOriginDemandForecastInterfaceLogConvertor;
import com.yhl.scp.dfp.originDemand.convertor.FdpOriginDemandInterfaceLogConvertor;
import com.yhl.scp.dfp.originDemand.domain.entity.FdpOriginDemandInterfaceLogDO;
import com.yhl.scp.dfp.originDemand.domain.service.FdpOriginDemandInterfaceLogDomainService;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandForecastInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.dto.FdpOriginDemandInterfaceLogDTO;
import com.yhl.scp.dfp.originDemand.infrastructure.dao.FdpOriginDemandInterfaceLogDao;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandForecastInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>FdpOriginDemandInterfaceLogServiceImpl</code>
 * <p>
 * Edi装车需求接口同步记录表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-04 18:25:09
 */
@Slf4j
@Service
public class FdpOriginDemandInterfaceLogServiceImpl extends AbstractService implements FdpOriginDemandInterfaceLogService {

    @Resource
    private FdpOriginDemandInterfaceLogDao fdpOriginDemandInterfaceLogDao;

    @Resource
    private FdpOriginDemandInterfaceLogDomainService fdpOriginDemandInterfaceLogDomainService;

    @Resource
    private OemService oemService;
    @Override
    public BaseResponse<Void> doCreate(FdpOriginDemandInterfaceLogDTO fdpOriginDemandInterfaceLogDTO) {
        // 0.数据转换
        FdpOriginDemandInterfaceLogDO fdpOriginDemandInterfaceLogDO = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Do(fdpOriginDemandInterfaceLogDTO);
        FdpOriginDemandInterfaceLogPO fdpOriginDemandInterfaceLogPO = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Po(fdpOriginDemandInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpOriginDemandInterfaceLogDomainService.validation(fdpOriginDemandInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(fdpOriginDemandInterfaceLogPO);
        fdpOriginDemandInterfaceLogDao.insert(fdpOriginDemandInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(FdpOriginDemandInterfaceLogDTO fdpOriginDemandInterfaceLogDTO) {
        // 0.数据转换
        FdpOriginDemandInterfaceLogDO fdpOriginDemandInterfaceLogDO = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Do(fdpOriginDemandInterfaceLogDTO);
        FdpOriginDemandInterfaceLogPO fdpOriginDemandInterfaceLogPO = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Po(fdpOriginDemandInterfaceLogDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        fdpOriginDemandInterfaceLogDomainService.validation(fdpOriginDemandInterfaceLogDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(fdpOriginDemandInterfaceLogPO);
        fdpOriginDemandInterfaceLogDao.update(fdpOriginDemandInterfaceLogPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<FdpOriginDemandInterfaceLogDTO> list) {
        List<FdpOriginDemandInterfaceLogPO> newList = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        fdpOriginDemandInterfaceLogDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<FdpOriginDemandInterfaceLogDTO> list) {
        List<FdpOriginDemandInterfaceLogPO> newList = FdpOriginDemandInterfaceLogConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        fdpOriginDemandInterfaceLogDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return fdpOriginDemandInterfaceLogDao.deleteBatch(idList);
        }
        return fdpOriginDemandInterfaceLogDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public FdpOriginDemandInterfaceLogVO selectByPrimaryKey(String id) {
        FdpOriginDemandInterfaceLogPO po = fdpOriginDemandInterfaceLogDao.selectByPrimaryKey(id);
        return FdpOriginDemandInterfaceLogConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "FDP_ORIGIN_DEMAND_INTERFACE_LOG")
    public List<FdpOriginDemandInterfaceLogVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "FDP_ORIGIN_DEMAND_INTERFACE_LOG")
    public List<FdpOriginDemandInterfaceLogVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<FdpOriginDemandInterfaceLogVO> dataList = fdpOriginDemandInterfaceLogDao.selectByCondition(sortParam, queryCriteriaParam);
        FdpOriginDemandInterfaceLogServiceImpl target = SpringBeanUtils.getBean(FdpOriginDemandInterfaceLogServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<FdpOriginDemandInterfaceLogVO> selectByParams(Map<String, Object> params) {
        List<FdpOriginDemandInterfaceLogPO> list = fdpOriginDemandInterfaceLogDao.selectByParams(params);
        return FdpOriginDemandInterfaceLogConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<FdpOriginDemandInterfaceLogVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<FdpOriginDemandInterfaceLogVO> selectByRelIds(List<String> relIds,String importType) {
        if (CollectionUtils.isNotEmpty(relIds)) {
            List<FdpOriginDemandInterfaceLogPO> list = fdpOriginDemandInterfaceLogDao.selectByRelIds(relIds,importType);
            return FdpOriginDemandInterfaceLogConvertor.INSTANCE.po2Vos(list);
        }
        return Collections.emptyList();
    }

    @Override
    public BaseResponse<Void> syncOriginDemandLog(List<FdpOriginDemandInterfaceLogDTO> list,String importType) {
        if (CollectionUtils.isEmpty(list)) {
            log.error("{}需求数据为空", importType);
            return BaseResponse.error("需求数据为空");
        }
        Map<String, Object> params = MapUtil.newHashMap();
        params.put("importType", importType);
        List<String> relIds = list.stream().map(FdpOriginDemandInterfaceLogDTO::getRelId).distinct().collect(Collectors.toList());
        List<FdpOriginDemandInterfaceLogVO> oldDemandList = this.selectByRelIds(relIds,importType);

        Map<String, FdpOriginDemandInterfaceLogVO> oldDemandMap = CollectionUtils.isEmpty(oldDemandList) ?
                MapUtil.newHashMap() :
                oldDemandList.stream().collect(Collectors.toMap(FdpOriginDemandInterfaceLogVO::getRelId,
                        Function.identity(), (v1, v2) -> v1));

        List<FdpOriginDemandInterfaceLogDTO> insertList = Lists.newArrayList();
        List<FdpOriginDemandInterfaceLogDTO> updateList = Lists.newArrayList();

        Map<String, Object> oemParams = MapUtil.newHashMap();
        oemParams.put("enable", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOS = oemService.selectVOByParams(oemParams);
        try {
            if("GRP".equals(importType)) {

                for (FdpOriginDemandInterfaceLogDTO dto : list) {
                    if (oldDemandMap.containsKey(dto.getRelId())) {
                        FdpOriginDemandInterfaceLogVO old = oldDemandMap.get(dto.getRelId());
                        dto.setId(old.getId());
                        oemVOS.stream().filter(t -> Objects.equals(t.getErpCustomerId(), dto.getPartyId()) && Objects
                                .equals(t.getErpSiteId(), dto.getSiteId())).findFirst().ifPresent(oemVO -> {
                            dto.setOemCode(oemVO.getOemCode());});
                        dto.setOrderedQty(Objects.isNull(dto.getShipQty()) ? BigDecimal.ZERO : dto.getShipQty().
                                subtract(Objects.isNull(dto.getAvailableQty()) ? BigDecimal.ZERO : dto.getAvailableQty()));
                        dto.setSubmissionType(GranularityEnum.DAY.getCode());//来自装车需求
                        updateList.add(dto);
                        log.info("找到relid：{}，更新数据,orderQty:{},oemCode:{}", dto.getRelId(), dto.getOrderedQty(), dto.getOemCode());
                    } else {
                        oemVOS.stream().filter(t -> Objects.equals(t.getErpCustomerId(), dto.getPartyId()) && Objects
                                .equals(t.getErpSiteId(), dto.getSiteId())).findFirst().ifPresent(oemVO -> {
                            dto.setOemCode(oemVO.getOemCode());});
                        dto.setSubmissionType(GranularityEnum.DAY.getCode());//来自装车需求
                        dto.setImportType(importType);
                        dto.setOrderedQty(Objects.isNull(dto.getShipQty()) ? BigDecimal.ZERO : dto.getShipQty().
                                subtract(Objects.isNull(dto.getAvailableQty()) ? BigDecimal.ZERO : dto.getAvailableQty()));
                        dto.setEnabled(YesOrNoEnum.YES.getCode());
                        insertList.add(dto);
                        log.info("未找到relid：{}，新增数据,orderQty:{},oemCode:{}", dto.getRelId(), dto.getOrderedQty(), dto.getOemCode());

                    }
                }
            }else{
                for (FdpOriginDemandInterfaceLogDTO dto : list) {
                    if (oldDemandMap.containsKey(dto.getRelId())) {
                        FdpOriginDemandInterfaceLogVO old = oldDemandMap.get(dto.getRelId());
                        dto.setId(old.getId());
                        dto.setOrderedQty(Objects.isNull(dto.getShipQty()) ? BigDecimal.ZERO : dto.getShipQty().
                                subtract(Objects.isNull(dto.getAvailableQty()) ? BigDecimal.ZERO : dto.getAvailableQty()));
                        dto.setImportType(importType);
                        updateList.add(dto);
                        log.info("找到relid：{}，更新数据,orderQty:{},oemCode:{}", dto.getRelId(), dto.getOrderedQty(), dto.getOemCode());
                    } else {
                        oemVOS.stream().filter(t -> Objects.equals(t.getErpCustomerId(), dto.getPartyId()) && Objects
                                .equals(t.getErpSiteId(), dto.getSiteId())).findFirst().ifPresent(oemVO -> {
                            dto.setOemCode(oemVO.getOemCode());});
                        dto.setSubmissionType(GranularityEnum.DAY.getCode());
                        dto.setImportType(importType);
                        dto.setOrderedQty(Objects.isNull(dto.getShipQty()) ? BigDecimal.ZERO : dto.getShipQty().
                                subtract(Objects.isNull(dto.getAvailableQty()) ? BigDecimal.ZERO : dto.getAvailableQty()));
                        dto.setEnabled(YesOrNoEnum.YES.getCode());
                        insertList.add(dto);
                        log.info("未找到relid：{}，新增数据,orderQty:{},oemCode:{}", dto.getRelId(), dto.getOrderedQty(), dto.getOemCode());
                    }
                }
            }

            if (CollectionUtils.isNotEmpty(insertList)) {
                doCreateBatch(insertList);
                log.info("批量创建原始需求接口日志完成,总计：{}", insertList.size());
            }
            if (CollectionUtils.isNotEmpty(updateList)) {
                doUpdateBatch(updateList);
                log.info("批量更新原始需求接口日志完成,总计：{}", updateList.size());
            }
        }catch (Exception e){
            log.error("同步原始需求接口日志异常",e);
            e.printStackTrace();
        }

        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public List<FdpOriginDemandInterfaceLogVO> selectVOByParams(Map<String, Object> params) {
        return fdpOriginDemandInterfaceLogDao.selectVOByParams(params);
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.FDP_ORIGIN_DEMAND_INTERFACE_LOG.getCode();
    }

    @Override
    public List<FdpOriginDemandInterfaceLogVO> invocation(List<FdpOriginDemandInterfaceLogVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
