<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.originDemand.infrastructure.dao.FdpOriginDemandInterfaceLogDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO">
        <!--@Table fdp_origin_demand_interface_log-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="bu_id" jdbcType="VARCHAR" property="buId"/>
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="account_number" jdbcType="VARCHAR" property="accountNumber"/>
        <result column="party_name" jdbcType="VARCHAR" property="partyName"/>
        <result column="party_id" jdbcType="VARCHAR" property="partyId"/>
        <result column="site_name" jdbcType="VARCHAR" property="siteName"/>
        <result column="item_num" jdbcType="VARCHAR" property="itemNum"/>
        <result column="cust_item_num" jdbcType="VARCHAR" property="custItemNum"/>
        <result column="ship_qty" jdbcType="VARCHAR" property="shipQty"/>
        <result column="ordered_qty" jdbcType="VARCHAR" property="orderedQty"/>
        <result column="available_qty" jdbcType="VARCHAR" property="availableQty"/>
        <result column="shipped_qty" jdbcType="VARCHAR" property="shippedQty"/>
        <result column="ship_time" jdbcType="TIMESTAMP" property="shipTime"/>
        <result column="delivery_time" jdbcType="TIMESTAMP" property="deliveryTime"/>
        <result column="last_update_date" jdbcType="TIMESTAMP" property="lastUpdateDate"/>
        <result column="site_id" jdbcType="VARCHAR" property="siteId"/>
        <result column="release_status" jdbcType="VARCHAR" property="releaseStatus"/>
        <result column="edi_location" jdbcType="VARCHAR" property="ediLocation"/>
        <result column="erp_party_site_id" jdbcType="VARCHAR" property="erpPartySiteId"/>
        <result column="business_type" jdbcType="VARCHAR" property="businessType"/>
        <result column="cust_item_revision" jdbcType="VARCHAR" property="custItemRevision"/>
        <result column="rel_id" jdbcType="VARCHAR" property="relId"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="price" jdbcType="VARCHAR" property="price"/>
        <result column="source" jdbcType="VARCHAR" property="source"/>
        <result column="ebs_item_id" jdbcType="VARCHAR" property="ebsItemId"/>
        <result column="ship_to_site_use_id" jdbcType="VARCHAR" property="shipToSiteUseId"/>
        <result column="back_order_qty" jdbcType="VARCHAR" property="backOrderQty"/>
        <result column="original_ship_time" jdbcType="TIMESTAMP" property="originalShipTime"/>
        <result column="original_delivery_time" jdbcType="TIMESTAMP" property="originalDeliveryTime"/>
        <result column="qty" jdbcType="VARCHAR" property="qty"/>
        <result column="ebs_customer_id" jdbcType="VARCHAR" property="ebsCustomerId"/>
        <result column="submission_type" jdbcType="VARCHAR" property="submissionType"/>
        <result column="import_type" jdbcType="VARCHAR" property="importType"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="serial_num" jdbcType="VARCHAR" property="serialNum"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap"
               type="com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO">
        <result column="product_edi_flag" jdbcType="VARCHAR" property="productEdiFlag"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,bu_id,organization_code,organization_name,account_number,party_name,party_id,site_name,item_num,cust_item_num,ship_qty,ordered_qty,available_qty,shipped_qty,ship_time,delivery_time,last_update_date,site_id,release_status,edi_location,erp_party_site_id,business_type,cust_item_revision,rel_id,org_id,price,source,ebs_item_id,ship_to_site_use_id,back_order_qty,original_ship_time,original_delivery_time,qty,ebs_customer_id,submission_type,import_type,enabled,creator,create_time,modifier,modify_time,version_value,oem_code,serial_num
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,product_edi_flag
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.buId != null and params.buId != ''">
                and bu_id = #{params.buId,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationCode != null and params.organizationCode != ''">
                and organization_code = #{params.organizationCode,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationName != null and params.organizationName != ''">
                and organization_name = #{params.organizationName,jdbcType=VARCHAR}
            </if>
            <if test="params.accountNumber != null and params.accountNumber != ''">
                and account_number = #{params.accountNumber,jdbcType=VARCHAR}
            </if>
            <if test="params.partyName != null and params.partyName != ''">
                and party_name = #{params.partyName,jdbcType=VARCHAR}
            </if>
            <if test="params.partyId != null and params.partyId != ''">
                and party_id = #{params.partyId,jdbcType=VARCHAR}
            </if>
            <if test="params.siteName != null and params.siteName != ''">
                and site_name = #{params.siteName,jdbcType=VARCHAR}
            </if>
            <if test="params.itemNum != null and params.itemNum != ''">
                and item_num = #{params.itemNum,jdbcType=VARCHAR}
            </if>
            <if test="params.custItemNum != null and params.custItemNum != ''">
                and cust_item_num = #{params.custItemNum,jdbcType=VARCHAR}
            </if>
            <if test="params.shipQty != null">
                and ship_qty = #{params.shipQty,jdbcType=VARCHAR}
            </if>
            <if test="params.orderedQty != null">
                and ordered_qty = #{params.orderedQty,jdbcType=VARCHAR}
            </if>
            <if test="params.availableQty != null">
                and available_qty = #{params.availableQty,jdbcType=VARCHAR}
            </if>
            <if test="params.shippedQty != null">
                and shipped_qty = #{params.shippedQty,jdbcType=VARCHAR}
            </if>
            <if test="params.shipTime != null">
                and ship_time = #{params.shipTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.deliveryTime != null">
                and delivery_time = #{params.deliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.lastUpdateDate != null">
                and last_update_date = #{params.lastUpdateDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.siteId != null and params.siteId != ''">
                and site_id = #{params.siteId,jdbcType=VARCHAR}
            </if>
            <if test="params.releaseStatus != null and params.releaseStatus != ''">
                and release_status = #{params.releaseStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.ediLocation != null and params.ediLocation != ''">
                and edi_location = #{params.ediLocation,jdbcType=VARCHAR}
            </if>
            <if test="params.erpPartySiteId != null and params.erpPartySiteId != ''">
                and erp_party_site_id = #{params.erpPartySiteId,jdbcType=VARCHAR}
            </if>
            <if test="params.businessType != null and params.businessType != ''">
                and business_type = #{params.businessType,jdbcType=VARCHAR}
            </if>
            <if test="params.custItemRevision != null and params.custItemRevision != ''">
                and cust_item_revision = #{params.custItemRevision,jdbcType=VARCHAR}
            </if>
            <if test="params.relId != null and params.relId != ''">
                and rel_id = #{params.relId,jdbcType=VARCHAR}
            </if>
            <if test="params.orgId != null and params.orgId != ''">
                and org_id = #{params.orgId,jdbcType=VARCHAR}
            </if>
            <if test="params.price != null and params.price != ''">
                and price = #{params.price,jdbcType=VARCHAR}
            </if>
            <if test="params.source != null and params.source != ''">
                and source = #{params.source,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsItemId != null and params.ebsItemId != ''">
                and ebs_item_id = #{params.ebsItemId,jdbcType=VARCHAR}
            </if>
            <if test="params.shipToSiteUseId != null and params.shipToSiteUseId != ''">
                and ship_to_site_use_id = #{params.shipToSiteUseId,jdbcType=VARCHAR}
            </if>
            <if test="params.backOrderQty != null and params.backOrderQty != ''">
                and back_order_qty = #{params.backOrderQty,jdbcType=VARCHAR}
            </if>
            <if test="params.originalShipTime != null">
                and original_ship_time = #{params.originalShipTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.originalDeliveryTime != null">
                and original_delivery_time = #{params.originalDeliveryTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.qty != null">
                and qty = #{params.qty,jdbcType=VARCHAR}
            </if>
            <if test="params.ebsCustomerId != null and params.ebsCustomerId != ''">
                and ebs_customer_id = #{params.ebsCustomerId,jdbcType=VARCHAR}
            </if>
            <if test="params.submissionType != null and params.submissionType != ''">
                and submission_type = #{params.submissionType,jdbcType=VARCHAR}
            </if>
            <if test="params.importType != null and params.importType != ''">
                and import_type = #{params.importType,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.serialNum != null and params.serialNum != ''">
                and serial_num = #{params.serialNum,jdbcType=VARCHAR}
            </if>
            <if test="params.productEdiFlag != null and params.productEdiFlag != ''">
                and product_edi_flag = #{params.product_edi_flag,jdbcType=VARCHAR}
            </if>
            <if test="params.relIds != null and params.relIds.size() > 0">
                and rel_id in
                <foreach collection="params.relIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_interface_log
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_interface_log
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from fdp_origin_demand_interface_log
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_interface_log
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_fdp_origin_demand_interface_log
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByRelIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_origin_demand_interface_log
        where import_type = #{importType,jdbcType=VARCHAR}
          and rel_id in
        <foreach collection="relIds" item="relId" index="index" open="(" separator="," close=")">
            #{relId,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_origin_demand_interface_log(
        id,
        bu_id,
        organization_code,
        organization_name,
        account_number,
        party_name,
        party_id,
        site_name,
        item_num,
        cust_item_num,
        ship_qty,
        ordered_qty,
        available_qty,
        shipped_qty,
        ship_time,
        delivery_time,
        last_update_date,
        site_id,
        release_status,
        edi_location,
        erp_party_site_id,
        business_type,
        cust_item_revision,
        rel_id,
        org_id,
        price,
        source,
        ebs_item_id,
        ship_to_site_use_id,
        back_order_qty,
        original_ship_time,
        original_delivery_time,
        qty,
        ebs_customer_id,
        submission_type,
        import_type,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        oem_code,
        serial_num)
        values (
        #{id,jdbcType=VARCHAR},
        #{buId,jdbcType=VARCHAR},
        #{organizationCode,jdbcType=VARCHAR},
        #{organizationName,jdbcType=VARCHAR},
        #{accountNumber,jdbcType=VARCHAR},
        #{partyName,jdbcType=VARCHAR},
        #{partyId,jdbcType=VARCHAR},
        #{siteName,jdbcType=VARCHAR},
        #{itemNum,jdbcType=VARCHAR},
        #{custItemNum,jdbcType=VARCHAR},
        #{shipQty,jdbcType=VARCHAR},
        #{orderedQty,jdbcType=VARCHAR},
        #{availableQty,jdbcType=VARCHAR},
        #{shippedQty,jdbcType=VARCHAR},
        #{shipTime,jdbcType=TIMESTAMP},
        #{deliveryTime,jdbcType=TIMESTAMP},
        #{lastUpdateDate,jdbcType=TIMESTAMP},
        #{siteId,jdbcType=VARCHAR},
        #{releaseStatus,jdbcType=VARCHAR},
        #{ediLocation,jdbcType=VARCHAR},
        #{erpPartySiteId,jdbcType=VARCHAR},
        #{businessType,jdbcType=VARCHAR},
        #{custItemRevision,jdbcType=VARCHAR},
        #{relId,jdbcType=VARCHAR},
        #{orgId,jdbcType=VARCHAR},
        #{price,jdbcType=VARCHAR},
        #{source,jdbcType=VARCHAR},
        #{ebsItemId,jdbcType=VARCHAR},
        #{shipToSiteUseId,jdbcType=VARCHAR},
        #{backOrderQty,jdbcType=VARCHAR},
        #{originalShipTime,jdbcType=TIMESTAMP},
        #{originalDeliveryTime,jdbcType=TIMESTAMP},
        #{qty,jdbcType=VARCHAR},
        #{ebsCustomerId,jdbcType=VARCHAR},
        #{submissionType,jdbcType=VARCHAR},
        #{importType,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER},
        #{oemCode,jdbcType=VARCHAR},
        #{serialNum,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO">
        insert into fdp_origin_demand_interface_log(id,
                                                    bu_id,
                                                    organization_code,
                                                    organization_name,
                                                    account_number,
                                                    party_name,
                                                    party_id,
                                                    site_name,
                                                    item_num,
                                                    cust_item_num,
                                                    ship_qty,
                                                    ordered_qty,
                                                    available_qty,
                                                    shipped_qty,
                                                    ship_time,
                                                    delivery_time,
                                                    last_update_date,
                                                    site_id,
                                                    release_status,
                                                    edi_location,
                                                    erp_party_site_id,
                                                    business_type,
                                                    cust_item_revision,
                                                    rel_id,
                                                    org_id,
                                                    price,
                                                    source,
                                                    ebs_item_id,
                                                    ship_to_site_use_id,
                                                    back_order_qty,
                                                    original_ship_time,
                                                    original_delivery_time,
                                                    qty,
                                                    ebs_customer_id,
                                                    submission_type,
                                                    import_type,
                                                    enabled,
                                                    creator,
                                                    create_time,
                                                    modifier,
                                                    modify_time,
                                                    version_value,
                                                    oem_code,
                                                    serial_num)
        values (#{id,jdbcType=VARCHAR},
                #{buId,jdbcType=VARCHAR},
                #{organizationCode,jdbcType=VARCHAR},
                #{organizationName,jdbcType=VARCHAR},
                #{accountNumber,jdbcType=VARCHAR},
                #{partyName,jdbcType=VARCHAR},
                #{partyId,jdbcType=VARCHAR},
                #{siteName,jdbcType=VARCHAR},
                #{itemNum,jdbcType=VARCHAR},
                #{custItemNum,jdbcType=VARCHAR},
                #{shipQty,jdbcType=VARCHAR},
                #{orderedQty,jdbcType=VARCHAR},
                #{availableQty,jdbcType=VARCHAR},
                #{shippedQty,jdbcType=VARCHAR},
                #{shipTime,jdbcType=TIMESTAMP},
                #{deliveryTime,jdbcType=TIMESTAMP},
                #{lastUpdateDate,jdbcType=TIMESTAMP},
                #{siteId,jdbcType=VARCHAR},
                #{releaseStatus,jdbcType=VARCHAR},
                #{ediLocation,jdbcType=VARCHAR},
                #{erpPartySiteId,jdbcType=VARCHAR},
                #{businessType,jdbcType=VARCHAR},
                #{custItemRevision,jdbcType=VARCHAR},
                #{relId,jdbcType=VARCHAR},
                #{orgId,jdbcType=VARCHAR},
                #{price,jdbcType=VARCHAR},
                #{source,jdbcType=VARCHAR},
                #{ebsItemId,jdbcType=VARCHAR},
                #{shipToSiteUseId,jdbcType=VARCHAR},
                #{backOrderQty,jdbcType=VARCHAR},
                #{originalShipTime,jdbcType=TIMESTAMP},
                #{originalDeliveryTime,jdbcType=TIMESTAMP},
                #{qty,jdbcType=VARCHAR},
                #{ebsCustomerId,jdbcType=VARCHAR},
                #{submissionType,jdbcType=VARCHAR},
                #{importType,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER},
                #{oemCode,jdbcType=VARCHAR},
                #{serialNum,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_origin_demand_interface_log(
        id,
        bu_id,
        organization_code,
        organization_name,
        account_number,
        party_name,
        party_id,
        site_name,
        item_num,
        cust_item_num,
        ship_qty,
        ordered_qty,
        available_qty,
        shipped_qty,
        ship_time,
        delivery_time,
        last_update_date,
        site_id,
        release_status,
        edi_location,
        erp_party_site_id,
        business_type,
        cust_item_revision,
        rel_id,
        org_id,
        price,
        source,
        ebs_item_id,
        ship_to_site_use_id,
        back_order_qty,
        original_ship_time,
        original_delivery_time,
        qty,
        ebs_customer_id,
        submission_type,
        import_type,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        oem_code,
        serial_num)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.buId,jdbcType=VARCHAR},
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.accountNumber,jdbcType=VARCHAR},
            #{entity.partyName,jdbcType=VARCHAR},
            #{entity.partyId,jdbcType=VARCHAR},
            #{entity.siteName,jdbcType=VARCHAR},
            #{entity.itemNum,jdbcType=VARCHAR},
            #{entity.custItemNum,jdbcType=VARCHAR},
            #{entity.shipQty,jdbcType=VARCHAR},
            #{entity.orderedQty,jdbcType=VARCHAR},
            #{entity.availableQty,jdbcType=VARCHAR},
            #{entity.shippedQty,jdbcType=VARCHAR},
            #{entity.shipTime,jdbcType=TIMESTAMP},
            #{entity.deliveryTime,jdbcType=TIMESTAMP},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.siteId,jdbcType=VARCHAR},
            #{entity.releaseStatus,jdbcType=VARCHAR},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.erpPartySiteId,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.custItemRevision,jdbcType=VARCHAR},
            #{entity.relId,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.price,jdbcType=VARCHAR},
            #{entity.source,jdbcType=VARCHAR},
            #{entity.ebsItemId,jdbcType=VARCHAR},
            #{entity.shipToSiteUseId,jdbcType=VARCHAR},
            #{entity.backOrderQty,jdbcType=VARCHAR},
            #{entity.originalShipTime,jdbcType=TIMESTAMP},
            #{entity.originalDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.importType,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.serialNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_origin_demand_interface_log(
        id,
        bu_id,
        organization_code,
        organization_name,
        account_number,
        party_name,
        party_id,
        site_name,
        item_num,
        cust_item_num,
        ship_qty,
        ordered_qty,
        available_qty,
        shipped_qty,
        ship_time,
        delivery_time,
        last_update_date,
        site_id,
        release_status,
        edi_location,
        erp_party_site_id,
        business_type,
        cust_item_revision,
        rel_id,
        org_id,
        price,
        source,
        ebs_item_id,
        ship_to_site_use_id,
        back_order_qty,
        original_ship_time,
        original_delivery_time,
        qty,
        ebs_customer_id,
        submission_type,
        import_type,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value,
        oem_code,
        serial_num)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.buId,jdbcType=VARCHAR},
            #{entity.organizationCode,jdbcType=VARCHAR},
            #{entity.organizationName,jdbcType=VARCHAR},
            #{entity.accountNumber,jdbcType=VARCHAR},
            #{entity.partyName,jdbcType=VARCHAR},
            #{entity.partyId,jdbcType=VARCHAR},
            #{entity.siteName,jdbcType=VARCHAR},
            #{entity.itemNum,jdbcType=VARCHAR},
            #{entity.custItemNum,jdbcType=VARCHAR},
            #{entity.shipQty,jdbcType=VARCHAR},
            #{entity.orderedQty,jdbcType=VARCHAR},
            #{entity.availableQty,jdbcType=VARCHAR},
            #{entity.shippedQty,jdbcType=VARCHAR},
            #{entity.shipTime,jdbcType=TIMESTAMP},
            #{entity.deliveryTime,jdbcType=TIMESTAMP},
            #{entity.lastUpdateDate,jdbcType=TIMESTAMP},
            #{entity.siteId,jdbcType=VARCHAR},
            #{entity.releaseStatus,jdbcType=VARCHAR},
            #{entity.ediLocation,jdbcType=VARCHAR},
            #{entity.erpPartySiteId,jdbcType=VARCHAR},
            #{entity.businessType,jdbcType=VARCHAR},
            #{entity.custItemRevision,jdbcType=VARCHAR},
            #{entity.relId,jdbcType=VARCHAR},
            #{entity.orgId,jdbcType=VARCHAR},
            #{entity.price,jdbcType=VARCHAR},
            #{entity.source,jdbcType=VARCHAR},
            #{entity.ebsItemId,jdbcType=VARCHAR},
            #{entity.shipToSiteUseId,jdbcType=VARCHAR},
            #{entity.backOrderQty,jdbcType=VARCHAR},
            #{entity.originalShipTime,jdbcType=TIMESTAMP},
            #{entity.originalDeliveryTime,jdbcType=TIMESTAMP},
            #{entity.qty,jdbcType=VARCHAR},
            #{entity.ebsCustomerId,jdbcType=VARCHAR},
            #{entity.submissionType,jdbcType=VARCHAR},
            #{entity.importType,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.serialNum,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO">
        update fdp_origin_demand_interface_log
        set bu_id                  = #{buId,jdbcType=VARCHAR},
            organization_code      = #{organizationCode,jdbcType=VARCHAR},
            organization_name      = #{organizationName,jdbcType=VARCHAR},
            account_number         = #{accountNumber,jdbcType=VARCHAR},
            party_name             = #{partyName,jdbcType=VARCHAR},
            party_id               = #{partyId,jdbcType=VARCHAR},
            site_name              = #{siteName,jdbcType=VARCHAR},
            item_num               = #{itemNum,jdbcType=VARCHAR},
            cust_item_num          = #{custItemNum,jdbcType=VARCHAR},
            ship_qty               = #{shipQty,jdbcType=VARCHAR},
            ordered_qty            = #{orderedQty,jdbcType=VARCHAR},
            available_qty          = #{availableQty,jdbcType=VARCHAR},
            shipped_qty            = #{shippedQty,jdbcType=VARCHAR},
            ship_time              = #{shipTime,jdbcType=TIMESTAMP},
            delivery_time          = #{deliveryTime,jdbcType=TIMESTAMP},
            last_update_date       = #{lastUpdateDate,jdbcType=TIMESTAMP},
            site_id                = #{siteId,jdbcType=VARCHAR},
            release_status         = #{releaseStatus,jdbcType=VARCHAR},
            edi_location           = #{ediLocation,jdbcType=VARCHAR},
            erp_party_site_id      = #{erpPartySiteId,jdbcType=VARCHAR},
            business_type          = #{businessType,jdbcType=VARCHAR},
            cust_item_revision     = #{custItemRevision,jdbcType=VARCHAR},
            rel_id                 = #{relId,jdbcType=VARCHAR},
            org_id                 = #{orgId,jdbcType=VARCHAR},
            price                  = #{price,jdbcType=VARCHAR},
            source                 = #{source,jdbcType=VARCHAR},
            ebs_item_id            = #{ebsItemId,jdbcType=VARCHAR},
            ship_to_site_use_id    = #{shipToSiteUseId,jdbcType=VARCHAR},
            back_order_qty         = #{backOrderQty,jdbcType=VARCHAR},
            original_ship_time     = #{originalShipTime,jdbcType=TIMESTAMP},
            original_delivery_time = #{originalDeliveryTime,jdbcType=TIMESTAMP},
            qty                    = #{qty,jdbcType=VARCHAR},
            ebs_customer_id        = #{ebsCustomerId,jdbcType=VARCHAR},
            submission_type        = #{submissionType,jdbcType=VARCHAR},
            import_type            = #{importType,jdbcType=VARCHAR},
            enabled                = #{enabled,jdbcType=VARCHAR},
            modifier               = #{modifier,jdbcType=VARCHAR},
            modify_time            = #{modifyTime,jdbcType=TIMESTAMP},
            version_value              = version_value + 1
            oem_code               = #{oemCode,jdbcType=VARCHAR},
            serial_num             = #{serialNum,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective"
            parameterType="com.yhl.scp.dfp.originDemand.infrastructure.po.FdpOriginDemandInterfaceLogPO">
        update fdp_origin_demand_interface_log
        <set>
            <if test="item.buId != null and item.buId != ''">
                bu_id = #{item.buId,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationCode != null and item.organizationCode != ''">
                organization_code = #{item.organizationCode,jdbcType=VARCHAR},
            </if>
            <if test="item.organizationName != null and item.organizationName != ''">
                organization_name = #{item.organizationName,jdbcType=VARCHAR},
            </if>
            <if test="item.accountNumber != null and item.accountNumber != ''">
                account_number = #{item.accountNumber,jdbcType=VARCHAR},
            </if>
            <if test="item.partyName != null and item.partyName != ''">
                party_name = #{item.partyName,jdbcType=VARCHAR},
            </if>
            <if test="item.partyId != null and item.partyId != ''">
                party_id = #{item.partyId,jdbcType=VARCHAR},
            </if>
            <if test="item.siteName != null and item.siteName != ''">
                site_name = #{item.siteName,jdbcType=VARCHAR},
            </if>
            <if test="item.itemNum != null and item.itemNum != ''">
                item_num = #{item.itemNum,jdbcType=VARCHAR},
            </if>
            <if test="item.custItemNum != null and item.custItemNum != ''">
                cust_item_num = #{item.custItemNum,jdbcType=VARCHAR},
            </if>
            <if test="item.shipQty != null">
                ship_qty = #{item.shipQty,jdbcType=VARCHAR},
            </if>
            <if test="item.orderedQty != null">
                ordered_qty = #{item.orderedQty,jdbcType=VARCHAR},
            </if>
            <if test="item.availableQty != null">
                available_qty = #{item.availableQty,jdbcType=VARCHAR},
            </if>
            <if test="item.shippedQty != null">
                shipped_qty = #{item.shippedQty,jdbcType=VARCHAR},
            </if>
            <if test="item.shipTime != null">
                ship_time = #{item.shipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.deliveryTime != null">
                delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.lastUpdateDate != null">
                last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.siteId != null and item.siteId != ''">
                site_id = #{item.siteId,jdbcType=VARCHAR},
            </if>
            <if test="item.releaseStatus != null and item.releaseStatus != ''">
                release_status = #{item.releaseStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.ediLocation != null and item.ediLocation != ''">
                edi_location = #{item.ediLocation,jdbcType=VARCHAR},
            </if>
            <if test="item.erpPartySiteId != null and item.erpPartySiteId != ''">
                erp_party_site_id = #{item.erpPartySiteId,jdbcType=VARCHAR},
            </if>
            <if test="item.businessType != null and item.businessType != ''">
                business_type = #{item.businessType,jdbcType=VARCHAR},
            </if>
            <if test="item.custItemRevision != null and item.custItemRevision != ''">
                cust_item_revision = #{item.custItemRevision,jdbcType=VARCHAR},
            </if>
            <if test="item.relId != null and item.relId != ''">
                rel_id = #{item.relId,jdbcType=VARCHAR},
            </if>
            <if test="item.orgId != null and item.orgId != ''">
                org_id = #{item.orgId,jdbcType=VARCHAR},
            </if>
            <if test="item.price != null and item.price != ''">
                price = #{item.price,jdbcType=VARCHAR},
            </if>
            <if test="item.source != null and item.source != ''">
                source = #{item.source,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsItemId != null and item.ebsItemId != ''">
                ebs_item_id = #{item.ebsItemId,jdbcType=VARCHAR},
            </if>
            <if test="item.shipToSiteUseId != null and item.shipToSiteUseId != ''">
                ship_to_site_use_id = #{item.shipToSiteUseId,jdbcType=VARCHAR},
            </if>
            <if test="item.backOrderQty != null and item.backOrderQty != ''">
                back_order_qty = #{item.backOrderQty,jdbcType=VARCHAR},
            </if>
            <if test="item.originalShipTime != null">
                original_ship_time = #{item.originalShipTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.originalDeliveryTime != null">
                original_delivery_time = #{item.originalDeliveryTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.qty != null">
                qty = #{item.qty,jdbcType=VARCHAR},
            </if>
            <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
            </if>
            <if test="item.submissionType != null and item.submissionType != ''">
                submission_type = #{item.submissionType,jdbcType=VARCHAR},
            </if>
            <if test="item.importType != null and item.importType != ''">
                import_type = #{item.importType,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.serialNum != null and item.serialNum != ''">
                serial_num = #{item.serialNum,jdbcType=VARCHAR},
            </if>
            version_value = version_value + 1,
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
        and version_value = #{item.versionValue,jdbcType=INTEGER}

    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_origin_demand_interface_log
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="bu_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.buId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="organization_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="account_number = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.accountNumber,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="party_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partyName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="party_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.partyId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="site_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.siteName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="item_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.itemNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_item_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custItemNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ordered_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orderedQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="available_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.availableQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shipped_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shippedQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.deliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="last_update_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.lastUpdateDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.siteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="release_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.releaseStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="edi_location = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ediLocation,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="erp_party_site_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.erpPartySiteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="business_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.businessType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="cust_item_revision = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.custItemRevision,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rel_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.relId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="org_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.orgId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="price = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.price,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="source = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.source,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_item_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsItemId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ship_to_site_use_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shipToSiteUseId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="back_order_qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.backOrderQty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="original_ship_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalShipTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="original_delivery_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.originalDeliveryTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="qty = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.qty,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="ebs_customer_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ebsCustomerId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="submission_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.submissionType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="import_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.importType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then
                    version_value + 1
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="serial_num = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.serialNum,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_origin_demand_interface_log
            <set>
                <if test="item.buId != null and item.buId != ''">
                    bu_id = #{item.buId,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationCode != null and item.organizationCode != ''">
                    organization_code = #{item.organizationCode,jdbcType=VARCHAR},
                </if>
                <if test="item.organizationName != null and item.organizationName != ''">
                    organization_name = #{item.organizationName,jdbcType=VARCHAR},
                </if>
                <if test="item.accountNumber != null and item.accountNumber != ''">
                    account_number = #{item.accountNumber,jdbcType=VARCHAR},
                </if>
                <if test="item.partyName != null and item.partyName != ''">
                    party_name = #{item.partyName,jdbcType=VARCHAR},
                </if>
                <if test="item.partyId != null and item.partyId != ''">
                    party_id = #{item.partyId,jdbcType=VARCHAR},
                </if>
                <if test="item.siteName != null and item.siteName != ''">
                    site_name = #{item.siteName,jdbcType=VARCHAR},
                </if>
                <if test="item.itemNum != null and item.itemNum != ''">
                    item_num = #{item.itemNum,jdbcType=VARCHAR},
                </if>
                <if test="item.custItemNum != null and item.custItemNum != ''">
                    cust_item_num = #{item.custItemNum,jdbcType=VARCHAR},
                </if>
                <if test="item.shipQty != null">
                    ship_qty = #{item.shipQty,jdbcType=VARCHAR},
                </if>
                <if test="item.orderedQty != null">
                    ordered_qty = #{item.orderedQty,jdbcType=VARCHAR},
                </if>
                <if test="item.availableQty != null">
                    available_qty = #{item.availableQty,jdbcType=VARCHAR},
                </if>
                <if test="item.shippedQty != null">
                    shipped_qty = #{item.shippedQty,jdbcType=VARCHAR},
                </if>
                <if test="item.shipTime != null">
                    ship_time = #{item.shipTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deliveryTime != null">
                    delivery_time = #{item.deliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.lastUpdateDate != null">
                    last_update_date = #{item.lastUpdateDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.siteId != null and item.siteId != ''">
                    site_id = #{item.siteId,jdbcType=VARCHAR},
                </if>
                <if test="item.releaseStatus != null and item.releaseStatus != ''">
                    release_status = #{item.releaseStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.ediLocation != null and item.ediLocation != ''">
                    edi_location = #{item.ediLocation,jdbcType=VARCHAR},
                </if>
                <if test="item.erpPartySiteId != null and item.erpPartySiteId != ''">
                    erp_party_site_id = #{item.erpPartySiteId,jdbcType=VARCHAR},
                </if>
                <if test="item.businessType != null and item.businessType != ''">
                    business_type = #{item.businessType,jdbcType=VARCHAR},
                </if>
                <if test="item.custItemRevision != null and item.custItemRevision != ''">
                    cust_item_revision = #{item.custItemRevision,jdbcType=VARCHAR},
                </if>
                <if test="item.relId != null and item.relId != ''">
                    rel_id = #{item.relId,jdbcType=VARCHAR},
                </if>
                <if test="item.orgId != null and item.orgId != ''">
                    org_id = #{item.orgId,jdbcType=VARCHAR},
                </if>
                <if test="item.price != null and item.price != ''">
                    price = #{item.price,jdbcType=VARCHAR},
                </if>
                <if test="item.source != null and item.source != ''">
                    source = #{item.source,jdbcType=VARCHAR},
                </if>
                <if test="item.ebsItemId != null and item.ebsItemId != ''">
                    ebs_item_id = #{item.ebsItemId,jdbcType=VARCHAR},
                </if>
                <if test="item.shipToSiteUseId != null and item.shipToSiteUseId != ''">
                    ship_to_site_use_id = #{item.shipToSiteUseId,jdbcType=VARCHAR},
                </if>
                <if test="item.backOrderQty != null and item.backOrderQty != ''">
                    back_order_qty = #{item.backOrderQty,jdbcType=VARCHAR},
                </if>
                <if test="item.originalShipTime != null">
                    original_ship_time = #{item.originalShipTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.originalDeliveryTime != null">
                    original_delivery_time = #{item.originalDeliveryTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.qty != null">
                    qty = #{item.qty,jdbcType=VARCHAR},
                </if>
                <if test="item.ebsCustomerId != null and item.ebsCustomerId != ''">
                    ebs_customer_id = #{item.ebsCustomerId,jdbcType=VARCHAR},
                </if>
                <if test="item.submissionType != null and item.submissionType != ''">
                    submission_type = #{item.submissionType,jdbcType=VARCHAR},
                </if>
                <if test="item.importType != null and item.importType != ''">
                    import_type = #{item.importType,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.serialNum != null and item.serialNum != ''">
                    serial_num = #{item.serialNum,jdbcType=VARCHAR},
                </if>
                version_value = version_value + 1,
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_origin_demand_interface_log
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_origin_demand_interface_log where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
