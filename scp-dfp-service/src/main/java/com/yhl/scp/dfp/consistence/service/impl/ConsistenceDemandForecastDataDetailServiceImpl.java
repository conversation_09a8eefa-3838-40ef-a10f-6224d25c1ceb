package com.yhl.scp.dfp.consistence.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.clean.infrastructure.dao.CleanForecastDataDetailDao;
import com.yhl.scp.dfp.clean.infrastructure.po.CleanForecastDataDetailPO;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.convertor.ConsistenceDemandForecastDataDetailConvertor;
import com.yhl.scp.dfp.consistence.domain.entity.ConsistenceDemandForecastDataDetailDO;
import com.yhl.scp.dfp.consistence.domain.service.ConsistenceDemandForecastDataDetailDomainService;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastCheckDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDetailDTO;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDetailDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataDetailPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandForecastEstablishmentVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionDetailConvertor;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>ConsistenceDemandForecastDataDetailServiceImpl</code>
 * <p>
 * 一致性业务预测数据明细应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-01 19:56:39
 */
@Slf4j
@Service
public class ConsistenceDemandForecastDataDetailServiceImpl extends AbstractService
        implements ConsistenceDemandForecastDataDetailService {

    @Resource
    private ConsistenceDemandForecastDataDetailDao consistenceDemandForecastDataDetailDao;

    @Resource
    private ConsistenceDemandForecastDataDetailDomainService consistenceDemandForecastDataDetailDomainService;
    
    @Resource
    private OriginDemandVersionService originDemandVersionService;
    
    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;
    
    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;
    
    @Resource
    private DemandVersionService demandVersionService;
    
    @Resource
    private CleanForecastDataService cleanForecastDataService;
    
    @Resource
    private CleanForecastDataDetailDao cleanForecastDataDetailDao;
    
    @Resource
    private DemandForecastVersionService demandForecastVersionService;
    
    @Resource
    private DemandForecastEstablishmentDao demandForecastEstablishmentDao;

    @Override
    public BaseResponse<Void> doCreate(ConsistenceDemandForecastDataDetailDTO consistenceDemandForecastDataDetailDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDetailDO consistenceDemandForecastDataDetailDO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDetailDTO);
        ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDetailDTO);
        // 1.数据校验
        consistenceDemandForecastDataDetailDomainService.validation(consistenceDemandForecastDataDetailDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(consistenceDemandForecastDataDetailPO);
        consistenceDemandForecastDataDetailDao.insert(consistenceDemandForecastDataDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ConsistenceDemandForecastDataDetailDTO consistenceDemandForecastDataDetailDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDetailDO consistenceDemandForecastDataDetailDO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDetailDTO);
        ConsistenceDemandForecastDataDetailPO consistenceDemandForecastDataDetailPO =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDetailDTO);
        // 1.数据校验
        consistenceDemandForecastDataDetailDomainService.validation(consistenceDemandForecastDataDetailDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(consistenceDemandForecastDataDetailPO);
        consistenceDemandForecastDataDetailDao.update(consistenceDemandForecastDataDetailPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ConsistenceDemandForecastDataDetailDTO> list) {
        List<ConsistenceDemandForecastDataDetailPO> newList =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        consistenceDemandForecastDataDetailDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ConsistenceDemandForecastDataDetailDTO> list) {
        List<ConsistenceDemandForecastDataDetailPO> newList =
                ConsistenceDemandForecastDataDetailConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        consistenceDemandForecastDataDetailDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return consistenceDemandForecastDataDetailDao.deleteBatch(idList);
        }
        return consistenceDemandForecastDataDetailDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ConsistenceDemandForecastDataDetailVO selectByPrimaryKey(String id) {
        ConsistenceDemandForecastDataDetailPO po = consistenceDemandForecastDataDetailDao.selectByPrimaryKey(id);
        return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL")
    public List<ConsistenceDemandForecastDataDetailVO> selectByPage(Pagination pagination, String sortParam,
                                                                    String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL")
    public List<ConsistenceDemandForecastDataDetailVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ConsistenceDemandForecastDataDetailVO> dataList =
                consistenceDemandForecastDataDetailDao.selectByCondition(sortParam, queryCriteriaParam);
        ConsistenceDemandForecastDataDetailServiceImpl target =
                SpringBeanUtils.getBean(ConsistenceDemandForecastDataDetailServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByParams(Map<String, Object> params) {
        List<ConsistenceDemandForecastDataDetailPO> list = consistenceDemandForecastDataDetailDao.selectByParams(params);
        return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectVOByParams(Map<String, Object> params) {
        return consistenceDemandForecastDataDetailDao.selectVOByParams(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CONSISTENCE_DEMAND_FORECAST_DATA_DETAIL.getCode();
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> invocation(List<ConsistenceDemandForecastDataDetailVO> dataList,
                                                                  Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return consistenceDemandForecastDataDetailDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectDemandForecastBystockPointId(String id) {
        return consistenceDemandForecastDataDetailDao.selectDemandForecastByStockPointId(id);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByConsistenceDemandForecastDataId() {
        return Collections.emptyList();
    }


    @Override
    public List<ConsistenceDemandForecastDataDetailVO> getLatestPublishedVersionData(List<String> oemCodeScope,
                                                                                     List<String> productScope) {
        // 查询最新已发布的预测版本号
        String versionCode = consistenceDemandForecastDataDetailDao.selectLatestPublishedVersionCode();
        if (StringUtils.isEmpty(versionCode)) {
            return new ArrayList<>();
        }
        // 业务预测数据
        return consistenceDemandForecastDataDetailDao.selectVOByParams(ImmutableMap
                .of("versionCode", versionCode, "oemCodeList", oemCodeScope, "productCodeList", productScope));
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectForecastQuantitySumByOemCodesAndMonths(Map<String, Object> params) {
        return consistenceDemandForecastDataDetailDao.selectForecastQuantitySumByOemCodesAndMonths(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataDetailVO> selectByParentIdsAndMonth(List<String> ids, Date month) {
        List<ConsistenceDemandForecastDataDetailPO> poList =
                consistenceDemandForecastDataDetailDao.selectByParentIdsAndMonth(ids, month);
        if (CollectionUtils.isNotEmpty(poList)) {
            return ConsistenceDemandForecastDataDetailConvertor.INSTANCE.po2Vos(poList);
        }
        return Collections.emptyList();
    }

	@Override
	public void doBatchUapdatForecastQuantity(List<ConsistenceDemandForecastCheckDTO> dtoList) {
		//更新一致性业务预测数据
		List<String> ids = dtoList.stream().map(ConsistenceDemandForecastCheckDTO::getId).collect(Collectors.toList());
		List<ConsistenceDemandForecastDataDetailPO> currList = consistenceDemandForecastDataDetailDao.selectByPrimaryKeys(ids);
		Map<String,ConsistenceDemandForecastCheckDTO> CheckDTOMap = dtoList.stream()
				.collect(Collectors.toMap(ConsistenceDemandForecastCheckDTO::getId,e->e,(v1, v2) -> v1));
		for (ConsistenceDemandForecastDataDetailPO curr : currList) {
			ConsistenceDemandForecastCheckDTO checkDTO = CheckDTOMap.get(curr.getId());
			curr.setForecastQuantity(checkDTO.getDeliveryQty().add(checkDTO.getWaitDeliveryQty()));
		}
		BasePOUtils.updateBatchFiller(currList);
		consistenceDemandForecastDataDetailDao.updateBatch(currList);
		
		List<String> oemCodes = dtoList.stream().map(ConsistenceDemandForecastCheckDTO::getOemCode)
				.distinct().collect(Collectors.toList());
		List<String> productCodes = dtoList.stream().map(ConsistenceDemandForecastCheckDTO::getProductCode)
				.distinct().collect(Collectors.toList());
		List<String> oemProductCodes = dtoList.stream().map(e ->String.join("&", e.getOemCode(), e.getProductCode()))
				.distinct().collect(Collectors.toList());
		Map<String,ConsistenceDemandForecastCheckDTO> checkOemProductMap = dtoList.stream()
				.collect(Collectors.toMap(e ->String.join("&", e.getOemCode(), e.getProductCode()), e->e,(v1, v2) -> v1));
		
		//更新装车需求提报数据,获取最新版本，量产，只处理月份的数据
		String demandVersionId = originDemandVersionService.selectLatestVersionId();
		List<LoadingDemandSubmissionVO> submissionList = loadingDemandSubmissionService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"versionId" , demandVersionId,
				"demandCategory", ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
				"oemCodes", oemCodes,
				"productCodes", productCodes))
				.stream().filter( e -> oemProductCodes.contains(String.join("&", e.getOemCode(), e.getProductCode())))
				.collect(Collectors.toList());
		List<String> submissionIds = submissionList.stream().map(LoadingDemandSubmissionVO::getId)
				.collect(Collectors.toList());
		Map<String,String> submissionMap =  submissionList.stream()
				.collect(Collectors.toMap(LoadingDemandSubmissionVO::getId, 
						e-> String.join("&", e.getOemCode(), e.getProductCode()),(value1, value2) -> value1));
		if(CollectionUtils.isNotEmpty(submissionIds)) {
			List<LoadingDemandSubmissionDetailVO> submissionDetails = loadingDemandSubmissionDetailService.selectByParams(ImmutableMap.of(
	    			"enabled", YesOrNoEnum.YES.getCode(), 
	    			"submissionIds" , submissionIds,
					"demandTime", DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH)));
			if(CollectionUtils.isNotEmpty(submissionDetails)) {
				for (LoadingDemandSubmissionDetailVO detailVO : submissionDetails) {
					String submissionId = detailVO.getSubmissionId();
					String unkey = submissionMap.get(submissionId);
					ConsistenceDemandForecastCheckDTO checkDTO = checkOemProductMap.get(unkey);
					detailVO.setDemandQuantity(checkDTO.getDeliveryQty().add(checkDTO.getWaitDeliveryQty()));
				}
				loadingDemandSubmissionDetailService
					.doUpdateBatch(LoadingDemandSubmissionDetailConvertor.INSTANCE.vo2Dtos(submissionDetails));
			}
		}
		//更新滚动预测数据
		DemandVersionVO cleanDemandVersion = demandVersionService.selectLastVersionByVersionTypeAndPlanPeriod(
				VersionTypeEnum.CLEAN_FORECAST.getCode(), null);
		List<CleanForecastDataVO> cleanForecastDataList = cleanForecastDataService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"versionId" , cleanDemandVersion.getId(),
				"demandCategory", ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
				"oemCodeList", oemCodes,
				"productCodeList", productCodes))
				.stream().filter( e -> oemProductCodes.contains(String.join("&", e.getOemCode(), e.getProductCode())))
				.collect(Collectors.toList());
		List<String> cleanForecastDataIds = cleanForecastDataList.stream().map(CleanForecastDataVO::getId)
				.collect(Collectors.toList());
		Map<String,String> cleanForecastDataMap =  cleanForecastDataList.stream()
				.collect(Collectors.toMap(CleanForecastDataVO::getId, 
						e-> String.join("&", e.getOemCode(), e.getProductCode()),(value1, value2) -> value1));
		if(CollectionUtils.isNotEmpty(cleanForecastDataIds)) {
			List<CleanForecastDataDetailPO> details = cleanForecastDataDetailDao.selectByParams(ImmutableMap.of(
	    			"enabled", YesOrNoEnum.YES.getCode(), 
	    			"cleanForecastDataIdList" , cleanForecastDataIds,
					"forecastTimeStr", DateUtils.dateToString(DateUtils.getMonthFirstDay(new Date()))));
			if(CollectionUtils.isNotEmpty(details)) {
				for (CleanForecastDataDetailPO detailPO : details) {
					String cleanForecastDataId = detailPO.getCleanForecastDataId();
					String unkey = cleanForecastDataMap.get(cleanForecastDataId);
					ConsistenceDemandForecastCheckDTO checkDTO = checkOemProductMap.get(unkey);
					detailPO.setForecastQuantity(checkDTO.getDeliveryQty().add(checkDTO.getWaitDeliveryQty()));
				}
				BasePOUtils.updateBatchFiller(details);
				cleanForecastDataDetailDao.updateBatchSelective(details);
			}
		}
		//更新需求预测编制数据
		DemandForecastVersionVO demandForecastVersion = demandForecastVersionService.selectLastVersionByPlanPeriod(null);
		List<DemandForecastEstablishmentPO> establishmentList = demandForecastEstablishmentDao.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"forecastVersionId" , demandForecastVersion.getId(),
				"demandCategory", ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
				"forecastTimeStr", DateUtils.dateToString(DateUtils.getMonthFirstDay(new Date())),
				"oemCodes", oemCodes,
				"productCodeList", productCodes))
			.stream().filter( e -> oemProductCodes.contains(String.join("&", e.getOemCode(), e.getProductCode())))
			.collect(Collectors.toList());
		for (DemandForecastEstablishmentPO e : establishmentList) {
			ConsistenceDemandForecastCheckDTO checkDTO = checkOemProductMap.get(String.join("&", e.getOemCode(), e.getProductCode()));
			e.setDemandForecast(checkDTO.getDeliveryQty().add(checkDTO.getWaitDeliveryQty()));
		}
		if(CollectionUtils.isNotEmpty(establishmentList)) {
			BasePOUtils.updateBatchFiller(establishmentList);
			demandForecastEstablishmentDao.updateBatchSelective(establishmentList);
		}
		
	}
}
