package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.consignmentProduct.service.ConsignmentProductService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 委托产品关系定时任务
 * author：李杰
 * email: <EMAIL>
 * date: 2024/12/12
 */

@Component
@Slf4j
public class ConsignmentProductJob {

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Resource
    private ConsignmentProductService consignmentProductService;

    @XxlJob("consignmentProductJob")
    public ReturnT<String> consignmentProductJob() {
        XxlJobHelper.log("开始调用委托产品关系定时任务。");
        List<Scenario> data = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (data.isEmpty()) {
            XxlJobHelper.log("租户下不存在该数据");
            return ReturnT.FAIL;
        }
        for (Scenario scenario : data) {
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            try {
                if (!StringUtils.isEmpty(scenario.getTenantId())) {
                    consignmentProductService.syncData(scenario.getDataBaseName(),scenario.getTenantId());
                }
            } catch (Exception e) {
                XxlJobHelper.log("委托产品关系定时任务报错{}。", e.getMessage());
                return ReturnT.FAIL;
            }
        }
        XxlJobHelper.log("委托产品关系定时任务结束。");
        return ReturnT.SUCCESS;
    }
}
