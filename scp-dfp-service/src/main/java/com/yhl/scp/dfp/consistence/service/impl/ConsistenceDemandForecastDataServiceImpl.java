package com.yhl.scp.dfp.consistence.service.impl;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.excel.RangeMergeStrategy;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.common.utils.BulkOperationUtils;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dcp.apiLog.vo.ExtApiLogVO;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.ShowLevelEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.common.vo.DynamicDataDetailVO;
import com.yhl.scp.dfp.consistence.convertor.ConsistenceDemandForecastDataConvertor;
import com.yhl.scp.dfp.consistence.convertor.ConsistenceDemandForecastDataDetailConvertor;
import com.yhl.scp.dfp.consistence.domain.entity.ConsistenceDemandForecastDataDO;
import com.yhl.scp.dfp.consistence.domain.service.ConsistenceDemandForecastDataDomainService;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDetailDTO;
import com.yhl.scp.dfp.consistence.dto.ExecutionMonitorDTO;
import com.yhl.scp.dfp.consistence.dto.ForecastSummerReportDataDTO;
import com.yhl.scp.dfp.consistence.infrastructure.dao.ConsistenceDemandForecastDataDao;
import com.yhl.scp.dfp.consistence.infrastructure.po.ConsistenceDemandForecastDataPO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastVersionService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastVersionVO;
import com.yhl.scp.dfp.consistence.vo.ExecutionMonitorResultVO;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataVO;
import com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanPublishedDao;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2;
import com.yhl.scp.dfp.demand.dto.DemandForecastReplayDTO;
import com.yhl.scp.dfp.demand.enums.RowTypeEnum;
import com.yhl.scp.dfp.demand.vo.DemandForecastReplayDetailVO;
import com.yhl.scp.dfp.demand.vo.DemandForecastReplayVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionAndOemVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.demand.vo.DynamicCellVO;
import com.yhl.scp.dfp.demand.vo.ForecastReplayDetailVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.excel.handler.CustomColumnWidthHandler;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.oem.enums.OemTradeTypeEnum;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleService;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.dfp.release.dto.ReleaseLineChartDTO;
import com.yhl.scp.dfp.release.dto.ReleaseOemDTO;
import com.yhl.scp.dfp.release.dto.ReleaseProductItemDTO;
import com.yhl.scp.dfp.release.vo.ReleaseExportVO;
import com.yhl.scp.dfp.release.vo.ReleaseLineChartMonthVO;
import com.yhl.scp.dfp.release.vo.ReleaseOemVO;
import com.yhl.scp.dfp.release.vo.ReleaseVO;
import com.yhl.scp.dfp.switchrelation.service.DfpSwitchRelationBetweenProductService;
import com.yhl.scp.dfp.switchrelation.vo.SwitchRelationVO;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseMonthVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.User;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.ips.warning.vo.WarningSqlSettingVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mrp.inventory.vo.InventoryFloatGlassShippedDetailVO;

import io.swagger.annotations.ApiModelProperty;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>ConsistenceDemandForecastDataServiceImpl</code>
 * <p>
 * 一致性业务预测数据应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class ConsistenceDemandForecastDataServiceImpl extends AbstractService implements ConsistenceDemandForecastDataService {

    @Resource
    private ConsistenceDemandForecastDataDao consistenceDemandForecastDataDao;
    @Resource
    private ConsistenceDemandForecastDataDomainService consistenceDemandForecastDataDomainService;
    @Resource
    private ConsistenceDemandForecastDataDetailService dataDetailService;
    @Resource
    private ConsistenceDemandForecastVersionService consistenceDemandForecastVersionService;

    @Resource
    private OemService oemService;
    @Resource
    private PartRelationMapService partRelationMapService;
    @Resource
    private OemVehicleModelService oemVehicleModelService;
    @Resource
    private PassengerCarSaleService passengerCarSaleService;

    @Resource
    private LoadingDemandSubmissionService loadingDemandSubmissionService;
    @Resource
    private DeliveryPlanPublishedService deliveryPlanPublishedService;
    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    @Resource
    private DfpSwitchRelationBetweenProductService switchRelationBetweenProductService;

    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private NewDcpFeign newDcpFeign;
    @Resource
    private NewMdsFeign newMdsFeign;
    
    @Resource
    private DeliveryPlanPublishedDao deliveryPlanPublishedDao;

    public static final String PARAM_VERSION_ID = "versionId";
    public static final String PARAM_OEM_CODE = "oemCode";
    public static final String PARAM_OEM_CODE_LIST = "oemCodeList";
    public static final String PARAM_PRODUCT_CODES = "productCodes";
    public static final String PARAM_PRODUCT_CODE_LIST = "productCodeList";
    public static final String PARAM_BEGIN_DATE = "beginDate";
    public static final String PARAM_END_DATE = "endDate";
    public static final String PARAM_ENABLED = "enabled";

    public static final String YEARMONTH = "yyyyMM";

    private static Function<ConsistenceDemandForecastDataPO, String> productShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER,
                item.getOemCode(), item.getVehicleModelCode(), item.getProductCode());
    }

    private static Function<ConsistenceDemandForecastDataPO, String> vehicleShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER,
                item.getOemCode(), item.getVehicleModelCode());
    }

    private static Function<ConsistenceDemandForecastDataPO, String> oemShowLevelFunction() {
        return item -> String.join(Constants.DELIMITER, item.getOemCode());
    }

    @Override
    public BaseResponse<Void> doCreate(ConsistenceDemandForecastDataDTO consistenceDemandForecastDataDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDO consistenceDemandForecastDataDO =
                ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDTO);
        ConsistenceDemandForecastDataPO consistenceDemandForecastDataPO =
                ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDTO);
        // 1.数据校验
        consistenceDemandForecastDataDomainService.validation(consistenceDemandForecastDataDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(consistenceDemandForecastDataPO);
        consistenceDemandForecastDataDao.insertWithPrimaryKey(consistenceDemandForecastDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(ConsistenceDemandForecastDataDTO consistenceDemandForecastDataDTO) {
        // 0.数据转换
        ConsistenceDemandForecastDataDO consistenceDemandForecastDataDO =
                ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Do(consistenceDemandForecastDataDTO);
        ConsistenceDemandForecastDataPO consistenceDemandForecastDataPO =
                ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Po(consistenceDemandForecastDataDTO);
        // 1.数据校验
        consistenceDemandForecastDataDomainService.validation(consistenceDemandForecastDataDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(consistenceDemandForecastDataPO);
        consistenceDemandForecastDataDao.update(consistenceDemandForecastDataPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ConsistenceDemandForecastDataDTO> list) {
        List<ConsistenceDemandForecastDataPO> newList = ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        consistenceDemandForecastDataDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<ConsistenceDemandForecastDataDTO> list) {
        List<ConsistenceDemandForecastDataPO> newList = ConsistenceDemandForecastDataConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        consistenceDemandForecastDataDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return consistenceDemandForecastDataDao.deleteBatch(idList);
        }
        return consistenceDemandForecastDataDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ConsistenceDemandForecastDataVO selectByPrimaryKey(String id) {
        ConsistenceDemandForecastDataPO po = consistenceDemandForecastDataDao.selectByPrimaryKey(id);
        return ConsistenceDemandForecastDataConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "fdp_consistence_demand_forecast_data")
    public List<ConsistenceDemandForecastDataVO> selectByPage(Pagination pagination, String sortParam,
                                                              String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "fdp_consistence_demand_forecast_data")
    public List<ConsistenceDemandForecastDataVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ConsistenceDemandForecastDataVO> dataList =
                consistenceDemandForecastDataDao.selectByCondition(sortParam, queryCriteriaParam);
        ConsistenceDemandForecastDataServiceImpl target =
                SpringBeanUtils.getBean(ConsistenceDemandForecastDataServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> selectByParams(Map<String, Object> params) {
        List<ConsistenceDemandForecastDataPO> list = consistenceDemandForecastDataDao.selectByParams(params);
        return ConsistenceDemandForecastDataConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.CONSISTENCE_DEMAND_FORECAST_DATA.getCode();
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> invocation(List<ConsistenceDemandForecastDataVO> dataList,
                                                            Map<String, Object> params, String invocation) {
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return consistenceDemandForecastDataDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<ExecutionMonitorResultVO> executionMonitor(ExecutionMonitorDTO monitorDTO) {
        // 获取一致性业务预测数据
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put(PARAM_VERSION_ID, monitorDTO.getVersionId());
        queryMap.put(PARAM_OEM_CODE, monitorDTO.getOemCode());
        queryMap.put("demandCategory", monitorDTO.getDemandType());
        queryMap.put("oemNameLike", monitorDTO.getOemName());
        queryMap.put("productCodeLike", monitorDTO.getProductCode());
        queryMap.put("vehicleModelCodeLike", monitorDTO.getVehicleModelCode());
        // 处理主机厂数据
        List<ConsistenceDemandForecastDataVO> forecastDataList =
                consistenceDemandForecastDataDao.selectVOByParams(queryMap);
        if (CollectionUtils.isEmpty(forecastDataList)) {
            return Collections.emptyList();
        }
        // 汇总发货计划数据
        String saleDate = monitorDTO.getSaleDate();
        if (StringUtils.isBlank(saleDate)) {
            return Collections.emptyList();
        }
        saleDate = DateUtils.dateToString(DateUtils.stringToDate(saleDate + "01", "yyyyMMdd"),
                DateUtils.YEAR_MONTH);
        Date now = new Date();
        String current = DateUtils.dateToString(now, DateUtils.YEAR_MONTH);

        Map<String, BigDecimal> releaseQtyMap = new HashMap<>();
        Map<String, Object> releaseQueryMap = new HashMap<>();
        Map<String, BigDecimal> toWarehouseQtyMap = new HashMap<>();

        List<String> oemCodes = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getOemCode)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<String> productCodes = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getProductCode)
                .distinct().filter(Objects::nonNull).collect(Collectors.toList());
        releaseQueryMap.put("oemCodes", oemCodes);

        SwitchRelationVO switchRelation = switchRelationBetweenProductService.getSwitchRelation(oemCodes, productCodes);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        Map<String, String> newOldMap = switchRelation.getNewOldMap();
        Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        Map<String, String> toNewOldMap = switchRelation.getNewOldMap();
        Map<String, String> toOldNewMap = switchRelation.getOldNewMap();
        releaseQueryMap.put(PARAM_PRODUCT_CODES, allProductCodes);
        String planPeriodType;
        if (current.compareTo(saleDate) < 0) {
            planPeriodType = "FUTURE";
        } else {
            if (current.compareTo(saleDate) > 0) {
                planPeriodType = "HISTORICAL";
                String beginDate = saleDate + "-01";
                releaseQueryMap.put(PARAM_BEGIN_DATE, beginDate);
                String endDate = DateUtils.dateToString(DateUtils.moveCalendar(DateUtils.stringToDate(beginDate),
                                Calendar.MONTH, 1), DateUtils.COMMON_DATE_STR3);
                releaseQueryMap.put(PARAM_END_DATE, endDate);
            } else {
                planPeriodType = "CURRENT";
                String currentMonthFirstDay = DateUtils.dateToString(DateUtils.getCurrentMonthFirstDay(), 
                        DateUtils.COMMON_DATE_STR3);
                releaseQueryMap.put(PARAM_BEGIN_DATE, currentMonthFirstDay);
                String endDate = DateUtils.dateToString(DateUtils.moveCalendar(now, Calendar.DAY_OF_YEAR, 1),
                        DateUtils.COMMON_DATE_STR3);
                releaseQueryMap.put(PARAM_END_DATE, endDate);
            }
            // 获取仓库发货计划数据
            releaseQtyMap = assembleQtyMap(releaseQueryMap, newOldMap, oldNewMap);
            // 获取仓库发货至中转库的发货数据
            toWarehouseQtyMap = assembleToWarehouseQtyMap(releaseQueryMap, toNewOldMap, toOldNewMap);
        }

        // 获取预测详情数据
        List<String> parentIdList = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getId)
                .collect(Collectors.toList());
        Map<String, Object> detailQueryMap = new HashMap<>();
        detailQueryMap.put("consistenceDemandForecastDataIdList", parentIdList);
        String finalSaleDate = saleDate;
        List<ConsistenceDemandForecastDataDetailVO> forecastDataDetailList =
                dataDetailService.selectByParams(detailQueryMap).stream().filter(p ->
                        null != p.getForecastTime()
                                && DateUtils.dateToString(p.getForecastTime(), DateUtils.YEAR_MONTH).equals(finalSaleDate))
                        .collect(Collectors.toList());
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> parentIdGroup = forecastDataDetailList.stream()
                .filter(k -> StringUtils.isNotEmpty(k.getConsistenceDemandForecastDataId()))
                .collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId));

        // 发布的发货计划数据
        Date startTime = DateUtils.truncateTimeOfDate(now);
        Date endTime = DateUtils.moveCalendar(DateUtils.truncateTimeOfDate(DateUtils.getMonthLastDay(now)), Calendar.DAY_OF_YEAR, 1);
        Map<String, Integer> deliveryQtyMap = deliveryPlanPublishedService.selectByParams(ImmutableMap
                .of(PARAM_PRODUCT_CODES, productCodes)).stream().filter(p ->
                p.getDemandTime().getTime() >= startTime.getTime()
                        && p.getDemandTime().getTime() <= endTime.getTime()).collect(Collectors
                .groupingBy(item -> String.join(Constants.DELIMITER, item.getOemCode(),
                        item.getProductCode()), Collectors.summingInt(DeliveryPlanPublishedVO::getDemandQuantity)));

        // 获取物料属性信息
        String scenario = SystemHolder.getScenario();
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = 
                ipsNewFeign.getScenarioBusinessRange(scenario, "SALE_ORGANIZATION",
                        "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of(PARAM_ENABLED, YesOrNoEnum.YES.getCode(),
                        "stockPointCode", rangeData, PARAM_PRODUCT_CODES, productCodes));
        Map<String, NewProductStockPointVO> productMap = productList.stream().collect(Collectors
                .toMap(NewProductStockPointVO::getProductCode, Function.identity(), (v1, v2) -> v1));
        // 获取计划员名称
        List<String> userIds = productList.stream().map(NewProductStockPointVO::getOrderPlanner)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, String> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIds)) {
            BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.IPS.getCode(),
                    TenantCodeEnum.FYQB.getCode());
            userMap = ipsNewFeign.selectUserByParams(defaultScenario.getData(), ImmutableMap.of("ids", userIds))
                    .stream().collect(Collectors.toMap(User::getId, User::getCnName, (t1, t2) -> t2));
        }
        // 装车需求提报数据
        Map<String, BigDecimal> productDemandQuantityMap = loadingDemandSubmissionService.getProductDemandQuantity(saleDate);
        // 主机厂名称
        Map<String, String> oemMap = oemService.selectByParams(ImmutableMap.of(PARAM_OEM_CODE_LIST, oemCodes))
                .stream().filter(oem -> oem.getOemCode() != null && oem.getOemName() != null)
                .collect(Collectors.toMap(OemVO::getOemCode, OemVO::getOemName, (t1, t2) -> t2));

        // 结果集合
        List<ExecutionMonitorResultVO> resultList = new ArrayList<>();
        for (ConsistenceDemandForecastDataVO demandForecastDataVO : forecastDataList) {
            ExecutionMonitorResultVO resultVO = new ExecutionMonitorResultVO();
            resultVO.setId(demandForecastDataVO.getId());
            resultVO.setOemCode(demandForecastDataVO.getOemCode());
            resultVO.setOemName(oemMap.get(demandForecastDataVO.getOemCode()));
            resultVO.setDemandType(demandForecastDataVO.getDemandCategory());
            resultVO.setVehicleModelCode(demandForecastDataVO.getVehicleModelCode());
            resultVO.setProductCode(demandForecastDataVO.getProductCode());
            NewProductStockPointVO productInfo = productMap.get(demandForecastDataVO.getProductCode());
            if (productInfo != null) {
                resultVO.setProductName(productInfo.getProductName());
                resultVO.setLoadingPositionSub(productInfo.getLoadingPositionSub());
                String poCategory = productInfo.getPoCategory();
                if (StringUtils.isNotEmpty(poCategory) && poCategory.contains(".")) {
                    resultVO.setFactoryCode(poCategory.split("\\.")[1]);
                }
                String orderPlanner = productInfo.getOrderPlanner();
                if (StringUtils.isNotEmpty(orderPlanner)) {
                    resultVO.setSalesmanName(userMap.get(orderPlanner));
                }
            }
            BigDecimal demandForecast = BigDecimal.ZERO;
            if (parentIdGroup.containsKey(demandForecastDataVO.getId())) {
                demandForecast = parentIdGroup.get(demandForecastDataVO.getId()).get(0).getForecastQuantity();
            }
            resultVO.setDemandForecastQuantity(demandForecast);
            BigDecimal releaseQty = releaseQtyMap.getOrDefault(String.join(Constants.DELIMITER, 
                    resultVO.getOemCode(), resultVO.getProductCode()), BigDecimal.ZERO);
            BigDecimal toReleaseQty = toWarehouseQtyMap.getOrDefault(String.join(Constants.DELIMITER,
                    resultVO.getOemCode(), resultVO.getProductCode()), BigDecimal.ZERO);
            int deliveryQty = deliveryQtyMap.getOrDefault(String.join(Constants.DELIMITER, 
                    resultVO.getOemCode(), resultVO.getProductCode()), 0);
            if ("CURRENT".equals(planPeriodType)) {
                resultVO.setDeliveredQuantity(releaseQty.add(toReleaseQty));
                resultVO.setWaitDeliveryQuantity(new BigDecimal(deliveryQty));
            } else if ("HISTORICAL".equals(planPeriodType)) {
                resultVO.setDeliveredQuantity(releaseQty.add(toReleaseQty));
                resultVO.setWaitDeliveryQuantity(BigDecimal.ZERO);
            } else {
                // FUTURE
                resultVO.setWaitDeliveryQuantity(demandForecast);
                resultVO.setDeliveredQuantity(BigDecimal.ZERO);
            }
            BigDecimal waitDelivery = deliveryQty == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(deliveryQty);
            resultVO.setWaitDeliveryQuantity(waitDelivery);
            resultVO.setDeviationQuantity(resultVO.getDeliveredQuantity().add(waitDelivery).subtract(demandForecast));

            resultVO.setDeviationRate(getDeviationRate(demandForecast, resultVO.getDeviationQuantity()));
            // 当需求预测值为0时，当月已发+待发不为0时，偏差率默认100%
            if (BigDecimal.ZERO.compareTo(resultVO.getDemandForecastQuantity()) == 0 
                    && BigDecimal.ZERO.compareTo(resultVO.getDeliveredQuantity().add(resultVO.getWaitDeliveryQuantity())) != 0) {
                resultVO.setDeviationRate(BigDecimal.valueOf(100));
            }
            resultVO.setAchievementRate(getAchievingRate(demandForecast, resultVO.getDeliveredQuantity()));
            resultVO.setExpectedAchievementRate(getAchievingRate(demandForecast, resultVO.getDeliveredQuantity().add(waitDelivery)));

            BigDecimal loadingSubmissionQuantity = productDemandQuantityMap.getOrDefault(resultVO.getProductCode(), BigDecimal.ZERO);
            resultVO.setLoadingDemandSubmission(loadingSubmissionQuantity);
            resultList.add(resultVO);
        }
        // 数据过滤
        if (monitorDTO.getDeviationRate() != null) {
            resultList = resultList.stream().filter(e -> e.getDeviationRate() != null
                    && e.getDeviationRate().abs().compareTo(monitorDTO.getDeviationRate()) > 0).collect(Collectors.toList());
        }
        // 偏差率百分号处理
        resultList.forEach(item -> {
            if (item.getDeviationRate() != null) {
                item.setDeviationRateStr(item.getDeviationRate() + "%");
            }
        });
        // 按需求类型、主机厂编码、车型编码、产品编码顺序排序
        resultList.sort(Comparator.comparing(ExecutionMonitorResultVO::getDemandType)
                .thenComparing(ExecutionMonitorResultVO::getOemCode)
                .thenComparing(ExecutionMonitorResultVO::getVehicleModelCode)
                .thenComparing(ExecutionMonitorResultVO::getProductCode));
        return resultList;
    }

    private Map<String, BigDecimal> assembleToWarehouseQtyMap(Map<String, Object> releaseQueryMap,
                                                              Map<String, String> toNewOldMap,
                                                              Map<String, String> toOldNewMap) {
        Map<String, BigDecimal> releaseToQtyMap;
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses =
                warehouseReleaseToWarehouseService.selectMonthVOByParams(releaseQueryMap);
        Map<String, BigDecimal> toQtyMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                .groupingBy(item -> String.join(Constants.DELIMITER,
                                item.getOemCode(), item.getItemCode()),
                        Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseToWarehouseMonthVO::getSumQty,
                                BigDecimal::add)));
        for (WarehouseReleaseToWarehouseMonthVO item : warehouseReleaseToWarehouses) {
            BigDecimal sumQty = item.getSumQty();
            String oemCode = item.getOemCode();
            String itemCode = item.getItemCode();
            if (toNewOldMap.containsKey(itemCode)) {
                String xItemCode = toNewOldMap.get(itemCode);
                String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode);
                sumQty = sumQty.add(toQtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (toOldNewMap.containsKey(itemCode)) {
                String yItemCode = toOldNewMap.get(itemCode);
                String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode);
                sumQty = sumQty.add(toQtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumQty(sumQty);
        }
        releaseToQtyMap = warehouseReleaseToWarehouses.stream().collect(Collectors
                .groupingBy(item -> String.join(Constants.DELIMITER,
                        item.getOemCode(), item.getItemCode()), Collectors.reducing(BigDecimal.ZERO,
                        WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
        return releaseToQtyMap;
    }

    private Map<String, BigDecimal> assembleQtyMap(Map<String, Object> releaseQueryMap,
                                                   Map<String, String> newOldMap,
                                                   Map<String, String> oldNewMap) {
        Map<String, BigDecimal> releaseQtyMap;
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecords =
                warehouseReleaseRecordService.selectMonthVOByParams(releaseQueryMap);
        Map<String, BigDecimal> qtyMap = warehouseReleaseRecords.stream().collect(Collectors
                .groupingBy(item -> String.join(Constants.DELIMITER,
                        item.getOemCode(), item.getItemCode()),
                        Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
        for (WarehouseReleaseRecordMonthVO item : warehouseReleaseRecords) {
            BigDecimal sumQty = item.getSumQty();
            String oemCode = item.getOemCode();
            String itemCode = item.getItemCode();
            if (newOldMap.containsKey(itemCode)) {
                String xItemCode = newOldMap.get(itemCode);
                String xKey = String.join(Constants.DELIMITER, oemCode, xItemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yItemCode = oldNewMap.get(itemCode);
                String yKey = String.join(Constants.DELIMITER, oemCode, yItemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumQty(sumQty);
        }
        releaseQtyMap = warehouseReleaseRecords.stream().collect(Collectors.groupingBy(item ->
                String.join(Constants.DELIMITER, item.getOemCode(), item.getItemCode()),
                Collectors.reducing(BigDecimal.ZERO, WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
        return releaseQtyMap;
    }

    /**
     * 实际达成率
     *
     * @param demandForecast 业务预测量
     * @param delivered      销量
     * @return java.lang.String
     */
    private String getAchievingRate(BigDecimal demandForecast, BigDecimal delivered) {
        if (BigDecimal.ZERO.compareTo(demandForecast) >= 0)
            return 0 + DfpConstants.PER_CENT;
        else {
            return delivered.multiply(BigDecimal.valueOf(100)).divide(demandForecast, 0, 
                    RoundingMode.HALF_DOWN).intValue() + DfpConstants.PER_CENT;
        }
    }

    private BigDecimal getDeviationRate(BigDecimal demandForecast, BigDecimal delivered) {
        if (BigDecimal.ZERO.compareTo(demandForecast) >= 0)
            return BigDecimal.ZERO;
        else {
            return BigDecimal.valueOf(delivered.multiply(BigDecimal.valueOf(100)).divide(demandForecast, 0, 
                    RoundingMode.HALF_DOWN).intValue());
        }
    }

    @Override
    public List<DeliveryPlanVO2> selectVO2ByPlanPeriod(String planPeriod, String startTime, String endTime) {
        return consistenceDemandForecastDataDao.selectVO2ByPlanPeriod(planPeriod, startTime, endTime);
    }

    @Override
    public List<DemandForecastReplayDetailVO> selectDataByVersionList(List<DemandVersionVO> versionList, 
                                                                      String showLevel, 
                                                                      DemandForecastReplayDTO replayDTO, 
                                                                      List<String> vehicleCodeList, 
                                                                      List<String> dateListSort) {
        List<DemandForecastReplayDetailVO> replayDetailVOList = new ArrayList<>();
        // 获取滚动数据
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("versionIdList", versionList.stream().map(DemandVersionVO::getId).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(replayDTO.getOemCodeList()) 
                && ShowLevelEnum.OEM.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put(PARAM_OEM_CODE, replayDTO.getOemCodeList());
        }
        if (CollectionUtils.isNotEmpty(replayDTO.getVehicleCodeList()) 
                && ShowLevelEnum.VEHICLE_MODEL.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put("vehicleCodeList", vehicleCodeList);
        }
        if (CollectionUtils.isNotEmpty(replayDTO.getProductCodeList()) 
                && ShowLevelEnum.PART.getCode().equals(replayDTO.getShowLevel())) {
            queryMap.put(PARAM_PRODUCT_CODE_LIST, replayDTO.getProductCodeList());
        }
        List<ConsistenceDemandForecastDataVO> cleanForecastDataVOS = this.selectByParams(queryMap);
        // 滚动预测父id集合
        List<String> parentIdList = cleanForecastDataVOS.stream().map(ConsistenceDemandForecastDataVO::getId)
                .collect(Collectors.toList());
        // 计划版本id，周期map
        Map<String, String> versionIdMap = versionList.stream().collect(Collectors
                .toMap(BaseVO::getId, DemandVersionVO::getPlanPeriod));

        // 获取滚动数据详情
        List<ConsistenceDemandForecastDataDetailVO> forecastDataDetailVOS = 
                dataDetailService.selectByParams(ImmutableMap.of("consistenceDemandForecastDataId", parentIdList));
        // 详情根据主表id分组
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> detailGroup = 
                forecastDataDetailVOS.stream().collect(Collectors
                        .groupingBy(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId));
        for (DemandVersionVO demandVersionVO : versionList) {
            List<ConsistenceDemandForecastDataVO> dataVOList = cleanForecastDataVOS.stream()
                    .filter(k -> k.getVersionId().equals(demandVersionVO.getId()))
                    .collect(Collectors.toList());
            // 根据评级+版本分组
            Map<String, List<String>> levelGroup = new HashMap<>();
            if (ShowLevelEnum.OEM.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k -> 
                        StringUtils.isNotEmpty(k.getOemCode())).collect(Collectors.groupingBy(k -> 
                        k.getOemCode() + Constants.DELIMITER + k.getVersionId(), Collectors
                        .mapping(ConsistenceDemandForecastDataVO::getId, Collectors.toList())));
            }
            if (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k -> 
                        StringUtils.isNotEmpty(k.getVehicleModelCode())).collect(Collectors.groupingBy(k ->
                        k.getVehicleModelCode() + Constants.DELIMITER + k.getVersionId(), Collectors
                        .mapping(ConsistenceDemandForecastDataVO::getId, Collectors.toList())));
            }
            if (ShowLevelEnum.PART.getCode().equals(showLevel)) {
                levelGroup = dataVOList.stream().filter(k ->
                        StringUtils.isNotEmpty(k.getProductCode())).collect(Collectors.groupingBy(k -> 
                        k.getProductCode() + Constants.DELIMITER + k.getVersionId(), Collectors
                        .mapping(ConsistenceDemandForecastDataVO::getId, Collectors.toList())));
            }
            // 分组统计返回
            for (Map.Entry<String, List<String>> entry : levelGroup.entrySet()) {
                String key = entry.getKey();
                List<String> ids = entry.getValue();
                List<ConsistenceDemandForecastDataDetailVO> dataDetailVOList = new ArrayList<>();
                // 获取对应周期
                String planPeriod = versionIdMap.get(key.split(Constants.DELIMITER)[1]);
                // 收集组内所有详情数据
                for (String id : ids) {
                    if (detailGroup.containsKey(id)) {
                        dataDetailVOList.addAll(detailGroup.get(id));
                    }
                }
                // 根据年月分组统计数量
                Map<String, BigDecimal> monthQuantityMap = dataDetailVOList.stream().collect(Collectors
                        .groupingBy(k -> DateUtils.dateToString(k.getForecastTime(), 
                                DateUtils.YEAR_MONTH), Collectors.reducing(BigDecimal.ZERO,
                                ConsistenceDemandForecastDataDetailVO::getForecastQuantity, BigDecimal::add)));
                // 转为动态列信息
                DemandForecastReplayDetailVO detailVO = new DemandForecastReplayDetailVO();
                List<DynamicDataDetailVO> details = new ArrayList<>();
                detailVO.setForecastVersion(key);
                for (String date : dateListSort) {
                    DynamicDataDetailVO dataDetailVO = new DynamicDataDetailVO();
                    dataDetailVO.setId(UUIDUtil.getUUID());
                    dataDetailVO.setSaleDate(date);
                    // 如果在计划周期前，不展示
                    if (planPeriod.compareTo(date) >= 0) {
                        dataDetailVO.setQuantity(null);
                    } else
                        dataDetailVO.setQuantity(monthQuantityMap.getOrDefault(date, BigDecimal.ZERO));
                    details.add(dataDetailVO);
                }
                detailVO.setDetails(details);
                replayDetailVOList.add(detailVO);
            }
        }
        return replayDetailVOList;
    }

    @Override
    public List<DemandVersionAndOemVO> getVersionAndOem() {
        List<DemandVersionAndOemVO> demandVersionAndOemList = new ArrayList<>();
        List<ConsistenceDemandForecastVersionVO> demandVersionList = 
                consistenceDemandForecastVersionService.selectPublishVersionDetail();
        if (CollectionUtils.isNotEmpty(demandVersionList)) {
            log.info("分组组装返回值");
            // 二层根据id分组
            Map<String, ConsistenceDemandForecastVersionVO> secondLevelVersionMap = demandVersionList.stream()
                    .filter(k -> StringUtils.isEmpty(k.getOemCode()))
                    .collect(Collectors.toMap(BaseVO::getId, Function.identity(), (v1, v2) -> v1));
            // 三层根据parentId分组
            Map<String, List<ConsistenceDemandForecastVersionVO>> sonVersionDetailGroup = demandVersionList.stream()
                    .filter(k -> StringUtils.isNotEmpty(k.getOemCode()))
                    .collect(Collectors.groupingBy(ConsistenceDemandForecastVersionVO::getParentVersionId));
            for (Map.Entry<String, ConsistenceDemandForecastVersionVO> entry : secondLevelVersionMap.entrySet()) {
                DemandVersionAndOemVO demandVersionAndOemVO = new DemandVersionAndOemVO();
                String key = entry.getKey();
                ConsistenceDemandForecastVersionVO value = entry.getValue();
                demandVersionAndOemVO.setVersionCode(value.getVersionCode());
                demandVersionAndOemVO.setVersionId(key);
                if (sonVersionDetailGroup.containsKey(key)) {
                    demandVersionAndOemVO.setOemCodeList(sonVersionDetailGroup.get(key).stream()
                            .map(ConsistenceDemandForecastVersionVO::getOemCode).collect(Collectors.toSet()));
                }
                demandVersionAndOemList.add(demandVersionAndOemVO);
            }
        }
        demandVersionAndOemList.sort((o1, o2) -> 
                o2.getVersionCode().compareTo(o1.getVersionCode()));
        return demandVersionAndOemList;
    }

    @Override
    public List<DeliveryPlanVO2> selectVO2ByVersionId(List<String> versionIds) {
        if (CollectionUtils.isEmpty(versionIds)) {
            return new ArrayList<>();
        }
        return consistenceDemandForecastDataDao.selectVO2ByVersionId(versionIds);
    }

    @Override
    public List<DemandForecastReplayVO> selectForecastReplay(Map<String, Object> basicParams, String showLevel, 
                                                             String planPeriod, List<String> planPeriods) {
        /*
         * 1.查询一致性需求测版本，根据一致性需求预测版本获取版本下的数据（主机厂，物料）级别，
         * 2.再根据版本下物料级别数据获取对应明细结果，进行日期汇总，最终汇总到顶层版本（一致性需求预测版本）
         */
        Map<String, Object> params = new HashMap<>();
        params.put("versionStatus", VersionStatusEnum.PUBLISHED.getCode());
        params.put("planPeriods", planPeriods);
        List<ConsistenceDemandForecastVersionVO> list = 
                consistenceDemandForecastVersionService.selectMaxVersionByParams(params);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> versionIds = list.stream().map(ConsistenceDemandForecastVersionVO::getId).collect(Collectors.toList());
        Map<String, ConsistenceDemandForecastVersionVO> versionMap = list.stream().collect(Collectors
                .toMap(ConsistenceDemandForecastVersionVO::getId, Function.identity(), (t1, t2) -> t2));
        basicParams.put("versionIdList", versionIds);
        // 查询一致性需求预测版本下的数据，主机厂+车型+本厂编码级别数据
        List<ConsistenceDemandForecastDataPO> dataList = consistenceDemandForecastDataDao.selectByParams(basicParams);

        List<String> productCodes = dataList.stream().map(ConsistenceDemandForecastDataPO::getProductCode)
                .filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        if (CollectionUtils.isNotEmpty(dataList)) {
            // 零件映射关系
            Map<String, String> partMap = 
                    partRelationMapService.selectByParams(ImmutableMap.of(PARAM_PRODUCT_CODE_LIST, productCodes))
                            .stream().filter(item -> StringUtils.isNotEmpty(item.getProductCode()) 
                                    && StringUtils.isNotEmpty(item.getPartName())).collect(Collectors
                                    .toMap(PartRelationMapVO::getProductCode, PartRelationMapVO::getPartName, 
                                            (t1, t2) -> t2));
            // 主机厂信息
            Map<String, String> oemMap = oemService.selectAll().stream().filter(item -> 
                    StringUtils.isNotEmpty(item.getOemCode()) && StringUtils.isNotEmpty(item.getOemName()))
                    .collect(Collectors.toMap(OemVO::getOemCode, OemVO::getOemName, (t1, t2) -> t2));
            Map<String, List<ConsistenceDemandForecastDataPO>> dataGroup;
            // 根据不同维度分组版本数据，每个组可能含有多个版本的数据
            if (ShowLevelEnum.OEM.getCode().equals(showLevel)) {
                dataGroup = dataList.stream().collect(Collectors.groupingBy(oemShowLevelFunction()));
            } else if (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel)) {
                dataGroup = dataList.stream().collect(Collectors.groupingBy(vehicleShowLevelFunction()));
            } else {
                dataGroup = dataList.stream().collect(Collectors.groupingBy(productShowLevelFunction()));
            }

            List<String> consistenceDemandForecastDataIdList = 
                    dataList.stream().map(ConsistenceDemandForecastDataPO::getId).collect(Collectors.toList());
            // 查询一致性需求预测版本下的预测详情数据，并且根据版本id分组
            Map<String, List<ConsistenceDemandForecastDataDetailVO>> detailGroup = 
                    dataDetailService.selectByParams(ImmutableMap
                            .of("consistenceDemandForecastDataIdList", consistenceDemandForecastDataIdList))
                            .stream().collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId));
            List<DemandForecastReplayVO> replayList = new ArrayList<>();
            for (Map.Entry<String, List<ConsistenceDemandForecastDataPO>> entry : dataGroup.entrySet()) {
                String key = entry.getKey();
                List<ConsistenceDemandForecastDataPO> subList = entry.getValue();
                String[] split = key.split(Constants.DELIMITER);
                String oemCode = split[0];
                String vehicleModelCode = (ShowLevelEnum.VEHICLE_MODEL.getCode().equals(showLevel) 
                        || ShowLevelEnum.PART.getCode().equals(showLevel)) ? split[1] : null;
                String productCode = ShowLevelEnum.PART.getCode().equals(showLevel) ? split[2] : null;

                Map<String, Map<String, BigDecimal>> collect = new HashMap<>(16);
                Map<String, List<ConsistenceDemandForecastDataPO>> versionGroup = subList.stream()
                        .collect(Collectors.groupingBy(ConsistenceDemandForecastDataPO::getVersionId));
                for (Map.Entry<String, List<ConsistenceDemandForecastDataPO>> versionEntry : versionGroup.entrySet()) {
                    String versionId = versionEntry.getKey();
                    List<String> dataIds = versionEntry.getValue().stream().map(ConsistenceDemandForecastDataPO::getId)
                            .collect(Collectors.toList());
                    List<ConsistenceDemandForecastDataDetailVO> tempList = new ArrayList<>();
                    for (String dataId : dataIds) {
                        if (detailGroup.containsKey(dataId)) {
                            tempList.addAll(detailGroup.get(dataId));
                        }
                    }
                    collect.put(versionId, tempList.stream().filter(Objects::nonNull) // 过滤掉 null 的 item
                            .filter(item -> item.getForecastTime() != null 
                                    && item.getForecastQuantity() != null).collect(Collectors
                                    .groupingBy(item -> 
                                            DateUtils.dateToString(item.getForecastTime(), DateUtils.YEAR_MONTH), 
                                            Collectors.reducing(BigDecimal.ZERO, 
                                                    ConsistenceDemandForecastDataDetailVO::getForecastQuantity, 
                                                    BigDecimal::add))));
                }

                DemandForecastReplayVO replayVO = DemandForecastReplayVO.builder()
                        .id(UUIDUtil.getUUID()).oemCode(oemCode).oemName(oemMap.getOrDefault(oemCode, ""))
                        .vehicleModelCode(vehicleModelCode).productCode(productCode)
                        .partName(partMap.getOrDefault(productCode, ""))
                        .demandType(VersionTypeEnum.CLEAN_ALGORITHM.getCode())
                        .build();
                List<ForecastReplayDetailVO> detailList = new ArrayList<>();
                for (Map.Entry<String, Map<String, BigDecimal>> entry1 : collect.entrySet()) {
                    String versionId = entry1.getKey();
                    ConsistenceDemandForecastVersionVO versionVO = versionMap.get(versionId);
                    ForecastReplayDetailVO detail = ForecastReplayDetailVO.builder()
                            .versionId(versionId).versionCode(versionVO.getVersionCode())
                            .planPeriod(versionVO.getPlanPeriod()).rowType(RowTypeEnum.DEMAND_FORECAST)
                            .build();
                    Map<String, BigDecimal> dynamicMap = entry1.getValue();
                    List<DynamicCellVO> cellList = new ArrayList<>();
                    for (Map.Entry<String, BigDecimal> entry2 : dynamicMap.entrySet()) {
                        DynamicCellVO cell = DynamicCellVO.builder()
                                .planPeriod(entry2.getKey()).quantity(entry2.getValue())
                                .build();
                        cellList.add(cell);
                    }
                    cellList.sort(Comparator.comparing(DynamicCellVO::getPlanPeriod));
                    detail.setCells(cellList);
                    detailList.add(detail);
                }
                detailList.sort(Comparator.comparing(ForecastReplayDetailVO::getVersionCode));
                replayVO.setDetailList(detailList);
                replayList.add(replayVO);
            }
            return replayList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<ReleaseOemVO> selectOemInfoByVersionId(ReleaseOemDTO releaseOemDTO) {
        return consistenceDemandForecastDataDao.selectOemInfoByVersionId(releaseOemDTO);
    }

    @Override
    public List<ReleaseVO> selectByVersionAndOem(String versionId, String demandCategory, String oemCode, String stockPointCode) {
        return consistenceDemandForecastDataDao.selectByVersionAndOem(versionId, demandCategory, oemCode, stockPointCode);
    }

    @Override
    public int updateForecastValueByIds(List<ReleaseProductItemDTO> detailDTOS) {
        if (CollectionUtils.isEmpty(detailDTOS)) {
            return 0;
        }
        int result = 0;
        for (ReleaseProductItemDTO detailDTO : detailDTOS) {
            result += consistenceDemandForecastDataDao.updateForecastValueById(detailDTO);
        }
        return result;
    }

    @Override
    public List<ReleaseLineChartMonthVO> selectLineChartByYearAndOem(int calcYear, String demandType, String oemCode) {
        List<ReleaseLineChartMonthVO> result = Lists.newArrayList();
        List<ReleaseLineChartDTO> releaseLineCharts = 
                consistenceDemandForecastDataDao.selectLineChartByYearAndOem(calcYear, demandType, oemCode);
        if (CollectionUtils.isEmpty(releaseLineCharts)) {
            return result;
        }
        releaseLineCharts.stream().collect(Collectors.groupingBy(ReleaseLineChartDTO::getForecastTime, 
                Collectors.mapping(ReleaseLineChartDTO::getDemandForecast, Collectors.summingInt(Integer::intValue))))
                .forEach((key, value) -> {
            ReleaseLineChartMonthVO chartMonthVO = new ReleaseLineChartMonthVO();
            chartMonthVO.setColumn(key);
            chartMonthVO.setQuantity(value);
            result.add(chartMonthVO);
        });
        return result;
    }

    @Override
    public void monitorUpdateDemandForecast(List<ExecutionMonitorResultVO> list, String month) {
        // TODO: 前端应该过滤偏差率在 ±15% 范围外的数据；
        //  或者后端查询符合数据权限且偏差率在 ±15% 范围外的数据
        // 取百分比大于15%和小于-15%的数据
        Map<String, ExecutionMonitorResultVO> monitorMap = list.stream().filter(item -> 
                {
                    if (item == null || StringUtils.isEmpty(item.getId()) 
                            || item.getDeviationRate().compareTo(BigDecimal.ZERO) == 0) {
                        // 百分比为0的, 不需要更新
                        return false;
                    }
                    // 判断是否大于15%或小于-15%
                    return isDeviation(item.getDeviationRate());
                }) // 确保对象不为 null
                .collect(Collectors.toMap(vo -> vo.getId() == null ? "" : vo.getId(),
                        Function.identity(), (t1, t2) -> t1));

        List<String> ids = new ArrayList<>(monitorMap.keySet());
        if (CollectionUtils.isEmpty(ids)) {
            throw new BusinessException("没有需要更新的数据");
        }
        Date date = StringUtils.isEmpty(month) ? new Date() : DateUtils.stringToDate(month, DateUtils.YEAR_MONTH);
        Date monthFirstDay = DateUtils.getMonthFirstDay(date);
        List<ConsistenceDemandForecastDataDetailVO> detailList =
                dataDetailService.selectByParentIdsAndMonth(ids, monthFirstDay);
        Map<String, ConsistenceDemandForecastDataDetailVO> dataMap = detailList.stream().collect(Collectors
                .toMap(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId,
                        Function.identity(), (t1, t2) -> t1));
        List<ConsistenceDemandForecastDataDetailDTO> updateList = new ArrayList<>();
        ConsistenceDemandForecastDataDetailConvertor instance = ConsistenceDemandForecastDataDetailConvertor.INSTANCE;
        ids.forEach(id -> {
            ConsistenceDemandForecastDataDetailVO detailVO = dataMap.get(id);
            if (detailVO == null) {
                return;
            }
            ExecutionMonitorResultVO monitorVO = monitorMap.get(id);
            if (monitorVO == null) {
                return;
            }
            detailVO.setForecastQuantity(monitorVO.getDeliveredQuantity().add(monitorVO.getWaitDeliveryQuantity()));
            ConsistenceDemandForecastDataDetailDTO dto = instance.vo2dto(detailVO);
            updateList.add(dto);
        });
        BulkOperationUtils.bulkUpdateOrCreate(updateList, dataDetailService::doUpdateBatch, 2000);
    }

    /**
     * 偏差是否大于正负15%
     */
    public boolean isDeviation(BigDecimal deviationRate) {
        BigDecimal top = new BigDecimal(15);
        BigDecimal lf = new BigDecimal(-15);
        return deviationRate.compareTo(top) >= 0 || deviationRate.compareTo(lf) <= 0;
    }

    @Override
    public List<Map<String, String>> selectDistinctVersionCode(String versionStatus) {
        return consistenceDemandForecastDataDao.selectDistinctVersionCode(versionStatus);
    }

    @Override
    public List<Map<String, String>> selectDistinctNewVersionCode() {
        return consistenceDemandForecastDataDao.selectDistinctNewVersionCode();
    }

    @Override
    public List<ReleaseExportVO> selectReleaseExportVO(Map<String, Object> params) {
        return consistenceDemandForecastDataDao.selectReleaseExportVO(params);
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> selectByPrimaryKeys(List<String> ids) {
        List<ConsistenceDemandForecastDataPO> result = consistenceDemandForecastDataDao.selectByPrimaryKeys(ids);
        return ConsistenceDemandForecastDataConvertor.INSTANCE.po2Vos(result);
    }

    @Override
    public BaseResponse<String> executionSend() {
        // 获取最新的时间和版本
        ExecutionMonitorDTO executionMonitor = consistenceDemandForecastVersionService.getExecutionMonitorDTO();
        // 获取数据
        List<ExecutionMonitorResultVO> executionMonitorResults = this.executionMonitor(executionMonitor);
        // 过滤出偏差率的绝对值超过15的数据
        executionMonitorResults = executionMonitorResults.stream().filter(item -> 
                item.getDeviationRate().compareTo(new BigDecimal(15)) > 0 
                        || item.getDeviationRate().compareTo(new BigDecimal(-15)) < 0).collect(Collectors.toList());
        if (executionMonitorResults.isEmpty()) {
            return BaseResponse.success("不存在偏差率绝对值大于15的数据。");
        }
        executionMonitorResults.forEach(item -> {
            if (ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode().equals(item.getDemandType())) {
                item.setDemandType(ProductionDemandTypeEnum.OUTPUT_DEMAND.getDesc());
            } else {
                item.setDemandType(ProductionDemandTypeEnum.PROJECT_DEMAND.getDesc());
            }
        });
        // 获取ExecutionMonitorResultVO存在的字段
        Class<? extends ExecutionMonitorResultVO> aClass = executionMonitorResults.get(0).getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        // 设置所有字段可访问
        for (Field field : declaredFields) {
            field.setAccessible(true);
        }
        List<Map<String, Object>> excelList = excelDataProcessor(executionMonitorResults, declaredFields);
        // 获取预警配置文件
        List<WarningSqlSettingVO> warningSqlSettingVo = ipsNewFeign.getWarningSql("BPIM-YJ-A00300");
        if (warningSqlSettingVo.isEmpty()) {
            log.error("匹配不到预警编码为BPIM-YJ-A00300的预警配置数据，请让管理员进行配置。");
            return BaseResponse.error("匹配不到预警编码为BPIM-YJ-A00300的预警配置数据，请让管理员进行配置。");
        } else {
            WarningSqlSettingVO warningSqlSettingVO = warningSqlSettingVo.get(0);
            Map<String, Object> map = new HashMap<>();
            map.put("data", excelList);
            map.put("mainData", warningSqlSettingVO);
            BaseResponse<String> dcpBaseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), 
                    ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.EMAIL_MESSAGE.getCode(), map);
            if (Boolean.TRUE.equals(dcpBaseResponse.getSuccess())) {
                return BaseResponse.success("发送成功.");
            } else {
                return dcpBaseResponse;
            }
        }
    }

    private static List<Map<String, Object>> excelDataProcessor(List<ExecutionMonitorResultVO> executionMonitorResults, 
                                                                Field[] declaredFields) {
        List<Map<String, Object>> excelList = new ArrayList<>();
        try {
            for (ExecutionMonitorResultVO vo : executionMonitorResults) {
                Map<String, Object> rowMap = new HashMap<>();
                for (Field field : declaredFields) {
                    Object value = field.get(vo);
                    if (!"serialVersionUID".equals(field.getName()) && value != null) {
                            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                            rowMap.put(annotation != null ? annotation.value() : field.getName(), value);
                        }

                }
                excelList.add(rowMap);
            }
            return excelList;
        } catch (IllegalAccessException e) {
            throw new BusinessException("反射访问字段失败：" + e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<String> executionQuayErrorSend() {
        HashMap<String, Object> apiLogMap = new HashMap<>();
        apiLogMap.put("configId", "53");
        List<ExtApiLogVO> extApiLog = newDcpFeign.getByParamsExtApiLog(apiLogMap);
        List<ExtApiLogVO> nonNullList = extApiLog.stream().filter(config -> 
                config.getModifyTime() != null).collect(Collectors.toList());
        ExtApiLogVO extApiLogVO = Collections.max(nonNullList, Comparator.comparing(ExtApiLogVO::getModifyTime));
        boolean sameDay = DateUtils.isSameDay(extApiLogVO.getModifyTime(), new Date());
        if (!sameDay) {
            return BaseResponse.success("今日无错误码头数据");
        }
        String responseBody = extApiLogVO.getResponseBody();
        List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOList = JSON.parseArray(responseBody,
                InventoryFloatGlassShippedDetailVO.class);
        // 获取ExecutionMonitorResultVO存在的字段
        Class<? extends InventoryFloatGlassShippedDetailVO> aClass = floatGlassShippedDetailVOList.get(0).getClass();
        Field[] declaredFields = aClass.getDeclaredFields();
        // 设置所有字段可访问
        for (Field field : declaredFields) {
            field.setAccessible(true);
        }
        List<Map<String, Object>> excelList = excelDataShippedProcessor(floatGlassShippedDetailVOList, declaredFields);
        // 获取预警配置文件
        List<WarningSqlSettingVO> warningSqlSettingVo = ipsNewFeign.getWarningSql("BPIM-YJ-A00500");
        if (warningSqlSettingVo.isEmpty()) {
            log.error("匹配不到预警编码为BPIM-YJ-A00500的预警配置数据，请让管理员进行配置。");
            return BaseResponse.error("匹配不到预警编码为BPIM-YJ-A00500的预警配置数据，请让管理员进行配置。");
        } else {
            WarningSqlSettingVO warningSqlSettingVO = warningSqlSettingVo.get(0);
            Map<String, Object> map = new HashMap<>();
            map.put("data", excelList);
            map.put("mainData", warningSqlSettingVO);
            BaseResponse<String> dcpBaseResponse = newDcpFeign.callExternalApi(TenantCodeEnum.FYQB.getCode(), 
                    ApiSourceEnum.FONE.getCode(), ApiCategoryEnum.EMAIL_MESSAGE.getCode(), map);
            if (Boolean.TRUE.equals(dcpBaseResponse.getSuccess())) {
                return BaseResponse.success("发送成功.");
            } else {
                return dcpBaseResponse;
            }
        }
    }

    private static List<Map<String, Object>> excelDataShippedProcessor(List<InventoryFloatGlassShippedDetailVO> floatGlassShippedDetailVOList,
                                                                Field[] declaredFields) {
        List<Map<String, Object>> excelList = new ArrayList<>();
        try {
            for (InventoryFloatGlassShippedDetailVO vo : floatGlassShippedDetailVOList) {
                Map<String, Object> rowMap = new HashMap<>();
                for (Field field : declaredFields) {
                    if(vo==null){
                        continue;
                    }
                    Object value = field.get(vo);
                    if (!"serialVersionUID".equals(field.getName()) && value != null) {
                        ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                        rowMap.put(annotation != null ? annotation.value() : field.getName(), value);
                    }
                }
                excelList.add(rowMap);
            }
            return excelList;
        } catch (IllegalAccessException e) {
            throw new BusinessException("反射访问字段失败：" + e.getMessage());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @Override
    public List<ConsistenceDemandForecastDataVO> selectForecastQuantityByVersionId(String versionId, String yearMonthStr) {
        return consistenceDemandForecastDataDao.selectForecastQuantityByVersionId(versionId, yearMonthStr);
    }

    @Override
    public List<ForecastSummerReportDataVO> queryForecastSummaryReport(ForecastSummerReportDataDTO dto) {
        // 1.获取最新已发布已发布的“是否为评审版本”为“是”的一致性需求预测版本
        ConsistenceDemandForecastVersionVO versionInfo = 
                consistenceDemandForecastVersionService.selectOneMaxVersionByParams(ImmutableMap.of(PARAM_ENABLED, 
                        YesOrNoEnum.YES.getCode(), "versionStatus", PublishStatusEnum.PUBLISHED.getCode(), 
                        "reviewVersionFlag", YesOrNoEnum.YES.getCode()));
        if (versionInfo == null) {
            throw new BusinessException("未获取到最新已发布且【是否为评审版本】为是的一致性需求预测版本!");
        }
        // 2.获取一致性业务预测数据及对应的详情数据
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put(PARAM_ENABLED, YesOrNoEnum.YES.getCode());
        queryParams.put(PARAM_VERSION_ID, versionInfo.getId());
        if (StringUtils.isNotEmpty(dto.getOemCode())) {
            queryParams.put(PARAM_OEM_CODE, dto.getOemCode());
        }
        if (StringUtils.isNotEmpty(dto.getVehicleModelCode())) {
            queryParams.put("vehicleModelCode", dto.getVehicleModelCode());
        }
        List<ConsistenceDemandForecastDataVO> forecastDataList = this.selectByParams(queryParams);
        List<ForecastSummerReportDataVO> reportResult = new ArrayList<>();
        if (CollectionUtils.isEmpty(forecastDataList)) {
            return reportResult;
        }
        // 处理时间轴
        String planPeriod = versionInfo.getPlanPeriod();
        String currentYear = planPeriod.substring(0, 4);
        List<String> monthsList = getYearMonthHeaderList(planPeriod, currentYear);

        // 按照主机厂+车型进行分组
        Map<String, List<ConsistenceDemandForecastDataVO>> forecastDataMap = forecastDataList.stream()
                .collect(Collectors.groupingBy(e -> e.getOemCode() + "&" + e.getVehicleModelCode()));
        // 查询一致性需求预测版本下的预测详情数据,按照主机厂，车型，需求时间统计分组
        List<ConsistenceDemandForecastDataVO> currVersionCollectList = 
                consistenceDemandForecastDataDao.selectCollectByVersionId(queryParams);
        //维护装车位置小类
        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), 
                TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = 
                ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(), "SALE_ORGANIZATION",
                        "INTERNAL", null);
        String rangeData = scenarioBusinessRange.getData().getRangeData();
        List<String> productCodes = currVersionCollectList.stream().map(ConsistenceDemandForecastDataVO::getProductCode)
        		.distinct().collect(Collectors.toList());
        List<NewProductStockPointVO> productList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), ImmutableMap.of(PARAM_ENABLED, 
                YesOrNoEnum.YES.getCode(), "stockPointCode", rangeData, 
                "productCodes", productCodes));
        Map<String, String> loadingPositionSubMap = productList.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, 
        		NewProductStockPointVO::getLoadingPositionSub,(v1, v2) -> v1));
        currVersionCollectList.forEach( e-> e.setLoadingPositionSub(loadingPositionSubMap.get(e.getProductCode())));
		Map<String, BigDecimal> currVersionCustomerCollectMap = currVersionCollectList.stream()
                .filter( e-> StringUtils.isNotEmpty(e.getLoadingPositionSub()))
                .collect(Collectors.groupingBy(e -> String.join("&", 
                        e.getOemCode(), e.getVehicleModelCode(), e.getLoadingPositionSub() ,e.getForecastTimeStr()),
                                Collectors.reducing(BigDecimal.ZERO, ConsistenceDemandForecastDataVO::getCustomerForecastQuantity, BigDecimal::add)));
        Map<String, BigDecimal> currVersionBussinessCollectMap = currVersionCollectList.stream()
                .filter( e-> StringUtils.isNotEmpty(e.getLoadingPositionSub()))
                .collect(Collectors.groupingBy(e -> String.join("&", 
                        e.getOemCode(), e.getVehicleModelCode(), e.getLoadingPositionSub() ,e.getForecastTimeStr()),
                                Collectors.reducing(BigDecimal.ZERO, ConsistenceDemandForecastDataVO::getForecastQuantity, BigDecimal::add)));
        // 获取主机厂信息，维护主机厂名称,预测评审备注
        List<String> oemCodes = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getOemCode)
                .distinct().collect(Collectors.toList());
        Map<String, OemVO> oemMap = oemService.selectByParams(ImmutableMap.of(PARAM_OEM_CODE_LIST, oemCodes)).stream()
                .collect(Collectors.toMap(OemVO::getOemCode, Function.identity(), (t1, t2) -> t2));
        // 获取主机厂车型信息，维护取数装车位置
        List<String> vehicleModelCodes = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getVehicleModelCode)
                .distinct().collect(Collectors.toList());
        List<OemVehicleModelVO> oemVehicleModelList = oemVehicleModelService.selectByParams(ImmutableMap
                .of(PARAM_OEM_CODE_LIST, oemCodes, "oemVehicleModelCodeList", vehicleModelCodes));
        Map<String, String> accessPositionMap = oemVehicleModelList.stream().filter(e -> 
                StringUtils.isNotEmpty(e.getOemCode()) && StringUtils.isNotEmpty(e.getOemVehicleModelCode()) 
                        && StringUtils.isNotEmpty(e.getAccessPosition())).collect(Collectors
                .toMap(e -> e.getOemCode() + "&" + e.getOemVehicleModelCode(), 
                        OemVehicleModelVO::getAccessPosition, (t1, t2) -> t2));
        // 获取SOP,EOP时间
        Map<String, NewProductStockPointVO> maxEopMinSopMap = newMdsFeign.selectMaxEopMinSop(ImmutableMap
                .of(PARAM_ENABLED, YesOrNoEnum.YES.getCode(), "vehicleCodeList", vehicleModelCodes, 
                        "stockPointCode", rangeData));
        // 外部车型
        List<PassengerCarSaleVO> passengerCarSaleList = passengerCarSaleService.selectByParams(ImmutableMap
                .of(PARAM_ENABLED, YesOrNoEnum.YES.getCode(), 
                        "startSaleTime", DateUtils.getMonthFirstDay(DateUtils.stringToDate(monthsList.get(0),
                                YEARMONTH)), 
                        "endSaleTime", DateUtils.getMonthLastDay(DateUtils.stringToDate(planPeriod, YEARMONTH)), 
                        "vehicleModelCodeList", vehicleModelCodes, 
                        PARAM_OEM_CODE_LIST, oemCodes));
        Map<String, List<PassengerCarSaleVO>> passengerCarSaleMap = passengerCarSaleList.stream().collect(Collectors
                .groupingBy(e -> e.getOemCode() + "&" + e.getVehicleModelCode()));
        // 获取指定时间的已发+待发
        List<Map<String, BigDecimal>> deliveryMap = getDeliveryMap(forecastDataList, planPeriod, oemCodes, 
                vehicleModelCodes, oemVehicleModelList, rangeData);
        Map<String, BigDecimal> wrrMap = deliveryMap.get(0);//仓库发货
        Map<String, BigDecimal> wrtwMap = deliveryMap.get(1);//中转库发货
        Map<String, BigDecimal> waitDeliveryQtyMap = deliveryMap.get(2);

        // 获取乘用车销量数据，同期销量
        List<PassengerCarSaleVO> sameTimeCarSaleList = passengerCarSaleService.selectByParams(ImmutableMap
                .of(PARAM_ENABLED, YesOrNoEnum.YES.getCode(), 
                        "startSaleTime", DateUtils.moveYear(DateUtils.getMonthFirstDay(DateUtils.stringToDate(monthsList.get(0), YEARMONTH)), -1), 
                        "endSaleTime", DateUtils.moveYear(DateUtils.getMonthFirstDay(DateUtils.stringToDate(monthsList.get(monthsList.size() - 1), YEARMONTH)), -1), 
                        "vehicleModelCodeList", vehicleModelCodes,
                        PARAM_OEM_CODE_LIST, oemCodes));
        Map<String, Integer> sameTimeQtyMap = CollectionUtils.isEmpty(sameTimeCarSaleList) 
                ? new HashMap<>() : sameTimeCarSaleList.stream().collect(Collectors.groupingBy(x ->
                String.join("&", x.getOemCode(), x.getVehicleModelCode(), 
                        DateUtils.dateToString(x.getSaleTime(), YEARMONTH)), Collectors.summingInt(PassengerCarSaleVO::getSaleQuantity)));

        // 获取历史业务预测版本id，及对应版本对应月份的数据
        List<String> beforeYearMonthList = monthsList.stream().filter(e -> 
                new BigDecimal(e).compareTo(new BigDecimal(planPeriod)) <= 0).collect(Collectors.toList());
        List<String> historyVersionIds = 
                consistenceDemandForecastVersionService.selectHistoryVersionByPlanPeriods(beforeYearMonthList);
        Map<String, BigDecimal> historyVersionCustomerCollectMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(historyVersionIds)) {
            // 查询这些版本的明细数据
            queryParams.put("versionIds", historyVersionIds);
            List<ConsistenceDemandForecastDataVO> historyVersionCollectList = 
                    consistenceDemandForecastDataDao.selectCollectByVersionIds(queryParams);
            historyVersionCustomerCollectMap = historyVersionCollectList.stream().collect(Collectors
                    .toMap(e -> String.join("&", e.getPlanPeriod(), 
                            e.getOemCode(), e.getVehicleModelCode(), e.getForecastTimeStr()), 
                            ConsistenceDemandForecastDataVO::getForecastQuantity, (v1, v2) -> v1));
        }

        for (Entry<String, List<ConsistenceDemandForecastDataVO>> forecastDataEntrySet : forecastDataMap.entrySet()) {
            String oemVehicleKey = forecastDataEntrySet.getKey();
            String[] oemVehicleModelSplit = oemVehicleKey.split("&");
            String oemCode = oemVehicleModelSplit[0];
            String vehicleModelCode = oemVehicleModelSplit[1];
            ForecastSummerReportDataVO add = new ForecastSummerReportDataVO();
            add.setOemCode(oemCode);
            OemVO oemVO = oemMap.get(oemCode);
            if (oemVO != null) {
                add.setOemName(oemVO.getOemName());
                add.setOemRemark(oemVO.getRemark());
            }
            add.setVehicleModelCode(vehicleModelCode);
            add.setAccessPosition(accessPositionMap.get(oemVehicleKey));
            NewProductStockPointVO newProductStockPointVO = maxEopMinSopMap.get(vehicleModelCode);
            if (newProductStockPointVO != null && newProductStockPointVO.getProductSop() != null 
                    && newProductStockPointVO.getProductEop() != null) {
                add.setSopEopTime(DateUtils.dateToString(newProductStockPointVO.getProductSop()) 
                        + "—" + DateUtils.dateToString(newProductStockPointVO.getProductEop()));
            }

            List<PassengerCarSaleVO> carSaleList = passengerCarSaleMap.getOrDefault(oemVehicleKey, new ArrayList<>());
            List<String> externalVehicleModelCodes = carSaleList.stream().map(PassengerCarSaleVO::getVehicleModelName)
                    .distinct().collect(Collectors.toList());
            add.setExternalVehicleModelCode(String.join(",", externalVehicleModelCodes));
            // 按照月份进行汇总
            Map<String, Integer> detailSumMap = carSaleList.stream().collect(Collectors.groupingBy(e -> 
                    DateUtils.dateToString(e.getSaleTime(), YEARMONTH), Collectors.summingInt(PassengerCarSaleVO::getSaleQuantity)));
            // 维护时间轴数据
            List<ForecastSummerReportDataDetailVO> details = new ArrayList<>();
            for (String yearMonth : monthsList) {
                String oemVehicleMonthKey = oemVehicleKey + "&" + yearMonth;
                ForecastSummerReportDataDetailVO detailAdd = new ForecastSummerReportDataDetailVO();
                detailAdd.setYearMonth(yearMonth);
                // 客户预测
                if (new BigDecimal(yearMonth).compareTo(new BigDecimal(planPeriod)) < 0) {
                    // 早于计划年月的取乘用车销售数量
                    detailAdd.setCustomerForecastQty(detailSumMap.getOrDefault(yearMonth, 0).toString());
                    if(oemVO != null && OemTradeTypeEnum.OUT.getDesc().equals(oemVO.getMarketType())) {
                    	//主机厂的市场属性如果为“出口”,客户销量/客户预测当前月以前的月份展示”实际出货“数据
                    	BigDecimal deliveryQuantity = wrrMap.getOrDefault(oemVehicleMonthKey, BigDecimal.ZERO)
                    			.add(wrtwMap.getOrDefault(oemVehicleMonthKey, BigDecimal.ZERO));
                    	detailAdd.setCustomerForecastQty(deliveryQuantity.stripTrailingZeros().toPlainString());
                    }
                    // 业务预测
                    String beforeYearMonth = DateUtils.dateToString(DateUtils.moveMonth(DateUtils
                            .stringToDate(yearMonth, YEARMONTH), -1), YEARMONTH);
                    String businessForecastQty = historyVersionCustomerCollectMap.getOrDefault(String.join("&", 
                            beforeYearMonth, oemVehicleKey, yearMonth), BigDecimal.ZERO).stripTrailingZeros().toPlainString();
                    detailAdd.setBussinessForecastQty(businessForecastQty);
                } else if (Objects.equals(yearMonth, planPeriod)) {
                    // 早于计划年月的取 已发+待发
                    BigDecimal wrrQuantity = wrrMap.getOrDefault(oemVehicleMonthKey, BigDecimal.ZERO);
                    BigDecimal wrtrQuantity = wrtwMap.getOrDefault(oemVehicleMonthKey, BigDecimal.ZERO);
                    BigDecimal waitDeliveryQuantity = waitDeliveryQtyMap.getOrDefault(oemVehicleKey, BigDecimal.ZERO);
                    detailAdd.setCustomerForecastQty(wrrQuantity.add(wrtrQuantity)
                    		.add(waitDeliveryQuantity).stripTrailingZeros().toPlainString());
                    // 业务预测
                    String beforeYearMonth = DateUtils.dateToString(DateUtils.moveMonth(DateUtils
                            .stringToDate(yearMonth, YEARMONTH), -1), YEARMONTH);
                    String businessForecastQty = historyVersionCustomerCollectMap.getOrDefault(String.join("&", 
                            beforeYearMonth, oemVehicleKey, yearMonth), BigDecimal.ZERO).stripTrailingZeros().toPlainString();
                    detailAdd.setBussinessForecastQty(businessForecastQty);
                } else {
                	String forecastKey = String.join("&", oemVehicleKey, add.getAccessPosition(), yearMonth);
                    String customerForecastQty = currVersionCustomerCollectMap
                            .getOrDefault(forecastKey, BigDecimal.ZERO).stripTrailingZeros().toPlainString();
                    detailAdd.setCustomerForecastQty(customerForecastQty);
                    // 业务预测
                    String businessForecastQty = currVersionBussinessCollectMap
                            .getOrDefault(forecastKey, BigDecimal.ZERO).stripTrailingZeros().toPlainString();
                    detailAdd.setBussinessForecastQty(businessForecastQty);
                }
                // 同期销量
                String beforeYearStr = DateUtils.dateToString(DateUtils.moveYear(DateUtils
                        .stringToDate(yearMonth, YEARMONTH), -1), YEARMONTH);
                String sameTimeQty = sameTimeQtyMap.getOrDefault(oemVehicleKey + "&" + beforeYearStr, 0).toString();
                detailAdd.setSameTimeQty(sameTimeQty);
                details.add(detailAdd);
            }
            add.setDetails(details);
            reportResult.add(add);
        }
        // 数据排序，过滤
        if (StringUtils.isNotEmpty(dto.getExternalVehicleModelCode())) {
            reportResult = reportResult.stream().filter(e -> 
                    StringUtils.isNotEmpty(e.getExternalVehicleModelCode()) 
                            && e.getExternalVehicleModelCode().contains(dto.getExternalVehicleModelCode()))
                    .collect(Collectors.toList());
        }
        reportResult.sort(Comparator.comparing(ForecastSummerReportDataVO::getOemCode)
        		.thenComparing(ForecastSummerReportDataVO::getVehicleModelCode)
                .thenComparing(Comparator.comparing(ForecastSummerReportDataVO::getExternalVehicleModelCode).reversed()));
        return reportResult;
    }

    private List<Map<String, BigDecimal>> getDeliveryMap(List<ConsistenceDemandForecastDataVO> forecastDataList, 
                                                         String planPeriod, List<String> oemCodes, 
                                                         List<String> vehicleModelCodes, 
                                                         List<OemVehicleModelVO> oemVehicleModelList, 
                                                         String rangeData) {
        List<String> productCodes = forecastDataList.stream().map(ConsistenceDemandForecastDataVO::getProductCode)
                .distinct().collect(Collectors.toList());
        SwitchRelationVO switchRelation = switchRelationBetweenProductService.getSwitchRelation(oemCodes, productCodes);
        List<String> allProductCodes = switchRelation.getAllProductCodes();
        // 汇总车型下的零件编码数据
        Map<String, Object> productParams = new HashMap<>();
        productParams.put(PARAM_PRODUCT_CODE_LIST, productCodes);
        Map<String, String> productVehicleModelMap = newMdsFeign.selectProductVehicleModel(SystemHolder.getScenario(), productParams);
        // 获取物料主数据
        List<NewProductStockPointVO> newProductStockPoints = 
                newMdsFeign.selectByVehicleModelCode(SystemHolder.getScenario(), vehicleModelCodes);
        // 获取车型下有哪些零件编码
        Map<String, List<String>> vehicle2ProductAccessPositionMap = assembleAccessPositionMap(newProductStockPoints
                .stream().filter(e -> rangeData.equals(e.getStockPointCode()))
                .collect(Collectors.toList()), oemVehicleModelList, OemVehicleModelVO::getOemVehicleModelCode);
        // 获取仓库发货记录
        String beginDate = DateUtils.dateToString(DateUtils.getYearFirstDay(Integer.valueOf(planPeriod.substring(0, 4))));
        Date monthFirstDay = DateUtils.getMonthFirstDay(DateUtils.stringToDate(planPeriod, YEARMONTH));
        String endDate = DateUtils.dateToString(DateUtils.getMonthLastDay(monthFirstDay));
        Map<String, Object> warehouseQueryMap = Maps.newHashMap();
        warehouseQueryMap.put(PARAM_BEGIN_DATE, beginDate);
        warehouseQueryMap.put(PARAM_END_DATE, endDate);
        warehouseQueryMap.put(PARAM_PRODUCT_CODES, allProductCodes);
        warehouseQueryMap.put("oemCodes", oemCodes);
        List<WarehouseReleaseRecordMonthVO> warehouseReleaseRecordMonthList = 
                warehouseReleaseRecordService.selectMonthVOByParams(warehouseQueryMap);
        warehouseReleaseRecordMonthList.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setVehicleCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode) 
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, BigDecimal> warehouseReleaseRecordByMonthMap = 
                getWarehouseReleaseRecordDetailMonthly(warehouseReleaseRecordMonthList);
        // 获取中转库发货记录
        List<WarehouseReleaseToWarehouseMonthVO> warehouseReleaseToWarehouses = 
                warehouseReleaseToWarehouseService.selectMonthVOByParams(warehouseQueryMap);
        // 处理中转库数据
        warehouseReleaseToWarehouses.forEach(item -> {
            String itemCode = item.getItemCode();
            String vehicleModelCode = productVehicleModelMap.getOrDefault(itemCode, itemCode);
            item.setVehicleModelCode(vehicleModelCode);
            item.setVehicleCountFlag(false);
            if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode) 
                    && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(itemCode)) {
                item.setVehicleCountFlag(true);
            }
        });
        Map<String, BigDecimal> warehouseReleaseToRecordByMonthMap = 
                getWarehouseReleaseToRecordDetailMonthly(warehouseReleaseToWarehouses);
        
        //获取当月待发货数据
        Map<String, BigDecimal> deliveryPlanPublishedCurrentMonthMap = new HashMap<>();
        if(Objects.equals(planPeriod, DateUtils.dateToString(new Date(), YEARMONTH))) {
            Date startDemandTime = DateUtils.getDayFirstTime(new Date());
            Date endDemandTime = DateUtils.getMonthLastDay(startDemandTime);
            List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList = deliveryPlanPublishedDao
                    .selectCurrentMonthVOByItemCodes(oemCodes, allProductCodes,
                            startDemandTime, endDemandTime);
            
            deliveryPlanPublishedCurrentMonthList.forEach(item -> {
            	String productCode = item.getProductCode();
                String vehicleModelCode = productVehicleModelMap.getOrDefault(productCode, productCode);
                item.setVehicleModelCode(vehicleModelCode);
                item.setCountFlag(false);
                if (vehicle2ProductAccessPositionMap.containsKey(vehicleModelCode)
                        && vehicle2ProductAccessPositionMap.getOrDefault(vehicleModelCode, new ArrayList<>()).contains(productCode)) {
                    item.setCountFlag(true);
                }
            });
            deliveryPlanPublishedCurrentMonthMap = getDeliveryPlanPublishedCurrentMonthly(switchRelation, 
            		deliveryPlanPublishedCurrentMonthList);
        }
        
        return Arrays.asList(warehouseReleaseRecordByMonthMap, warehouseReleaseToRecordByMonthMap,
        		deliveryPlanPublishedCurrentMonthMap);
    }
    
    private Map<String, BigDecimal> getDeliveryPlanPublishedCurrentMonthly(SwitchRelationVO switchRelation,
    		List<DeliveryPlanPublishedMonthVO> deliveryPlanPublishedCurrentMonthList) {
    	Map<String, String> newOldMap = switchRelation.getNewOldMap();
    	Map<String, String> oldNewMap = switchRelation.getOldNewMap();
        Map<String, BigDecimal> qtyMap = deliveryPlanPublishedCurrentMonthList.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedMonthVO::getProductCode,
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
        for (DeliveryPlanPublishedMonthVO item : deliveryPlanPublishedCurrentMonthList) {
            BigDecimal sumQty = item.getSumDemandQuantity();
            String itemCode = item.getProductCode();
            if (newOldMap.containsKey(itemCode)) {
                String xKey = newOldMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(xKey, BigDecimal.ZERO));
            }
            if (oldNewMap.containsKey(itemCode)) {
                String yKey = oldNewMap.get(itemCode);
                sumQty = sumQty.add(qtyMap.getOrDefault(yKey, BigDecimal.ZERO));
            }
            item.setSumDemandQuantity(sumQty);
        }
        return deliveryPlanPublishedCurrentMonthList
                .stream().filter(DeliveryPlanPublishedMonthVO::getCountFlag)
                .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getVehicleModelCode()),
                        Collectors.reducing(BigDecimal.ZERO, DeliveryPlanPublishedMonthVO::getSumDemandQuantity,
                                BigDecimal::add)));
    }

    /**
     * 获取动态时间轴
     *
     * @param planPeriod  计划时段
     * @param currentYear 当前年
     * @return java.util.List<java.lang.String>
     */
    private List<String> getYearMonthHeaderList(String planPeriod, String currentYear) {
        List<String> monthsList = new ArrayList<>();
        // 遍历从1月到当前月份
        for (int month = 1; month <= 12; month++) {
            // 将月份转换为字符串并添加到列表中
            String yearMonthStr = currentYear + String.format("%02d", month);
            if (Objects.equals(yearMonthStr, planPeriod)) {
                monthsList.add(yearMonthStr);
                Date planPeriodTime = DateUtils.stringToDate(planPeriod, YEARMONTH);
                for (int moveMonth = 1; moveMonth <= 12; moveMonth++) {
                    monthsList.add(DateUtils.dateToString(DateUtils.moveMonth(planPeriodTime, moveMonth), YEARMONTH));
                }
                break;
            }
            monthsList.add(yearMonthStr);
        }
        return monthsList;
    }

    /**
     * 根据物料主数据和主机厂车型获取车型下的零件编码
     * @param newProductStockPointVOS 库存点物品列表
     * @param vehicleModelVOList      主机厂车型列表
     * @param function                主机厂车型函数
     * @return java.util.Map<java.lang.String, java.util.List < java.lang.String>>
     */
    private Map<String, List<String>> assembleAccessPositionMap(List<NewProductStockPointVO> newProductStockPointVOS, 
                                                                List<OemVehicleModelVO> vehicleModelVOList, 
                                                                Function<OemVehicleModelVO, String> function) {
        return vehicleModelVOList.stream().filter(x -> StringUtils.isNotBlank(x.getAccessPosition()))
                .collect(Collectors.toMap(function, x -> newProductStockPointVOS.stream()
                                .filter(y -> y.getVehicleModelCode().equals(x.getOemVehicleModelCode()) 
                                        && StringUtils.equals(x.getAccessPosition(), y.getLoadingPositionSub()))
                                .map(NewProductStockPointVO::getProductCode).collect(Collectors.toList()),
                // 如果有重复的key,合并新旧值
                (oldValue, newValue) -> {
                    oldValue.addAll(newValue);
                    return oldValue;
                }));
    }

    private Map<String, BigDecimal> getWarehouseReleaseRecordDetailMonthly(List<WarehouseReleaseRecordMonthVO> records) {
        return records.parallelStream().filter(x -> x != null 
                && x.getYearMonth() != null && StringUtils.isNotBlank(x.getItemCode()) && x.getSumQty() != null)
                .filter(WarehouseReleaseRecordMonthVO::getVehicleCountFlag).collect(Collectors
                        .groupingBy(e-> String.join("&", e.getOemCode(), e.getVehicleModelCode(), e.getYearMonth().replace("-", "")), Collectors.reducing(BigDecimal.ZERO, 
                                WarehouseReleaseRecordMonthVO::getSumQty, BigDecimal::add)));
    }

    private Map<String, BigDecimal> getWarehouseReleaseToRecordDetailMonthly(List<WarehouseReleaseToWarehouseMonthVO> records) {
        // 获取每个月的发货量数据
        return records.parallelStream().filter(x -> x != null 
                && x.getYearMonth() != null && StringUtils.isNotBlank(x.getItemCode()) 
                && x.getSumQty() != null).filter(WarehouseReleaseToWarehouseMonthVO::getVehicleCountFlag)
                .collect(Collectors.groupingBy(e-> String.join("&", e.getOemCode(), e.getVehicleModelCode(), e.getYearMonth().replace("-", "")), Collectors.reducing(BigDecimal.ZERO, 
                        WarehouseReleaseToWarehouseMonthVO::getSumQty, BigDecimal::add)));
    }

    @Override
    public BaseResponse<List<LabelValue<String>>> selectPartLevelProductDropDown(ConsistenceDemandForecastDataDTO dto) {
        List<ConsistenceDemandForecastDataPO> forecastDataList = 
                consistenceDemandForecastDataDao.selectByParams(ImmutableMap.of(PARAM_ENABLED, YesOrNoEnum.YES.getCode(), 
                        PARAM_VERSION_ID, dto.getVersionId(), "demandCategory", dto.getDemandCategory()));
        List<LabelValue<String>> dataList = forecastDataList.stream().map(ConsistenceDemandForecastDataPO::getProductCode)
                .distinct().map(x -> new LabelValue<>(x, x)).collect(Collectors.toList());
        return BaseResponse.success(dataList);
    }

    @SneakyThrows
    @Override
    public void forecastSummaryReportExport(HttpServletResponse response, ForecastSummerReportDataDTO dto) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "预测汇总表");

        List<String> forecastMonths = getForecastMonthMap();
        List<List<String>> headers = initHeaders(forecastMonths);
        List<String> timeSeries = forecastMonths.stream().sorted().collect(Collectors.toList());
        List<List<Object>> dataList = new ArrayList<>();
        List<CellRangeAddress> mergeRegions = new ArrayList<>();
        List<ForecastSummerReportDataVO> originList = this.queryForecastSummaryReport(dto);
        assembleDataList(originList, timeSeries, dataList, mergeRegions);
        boolean hasData = CollectionUtils.isNotEmpty(dataList);
        ExcelWriterBuilder excelWriterBuilder = EasyExcel.write(out).head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler());
        if (hasData) {
            excelWriterBuilder.registerWriteHandler(new RangeMergeStrategy(mergeRegions));
        }
        // 设置单元格合并
        excelWriterBuilder.sheet("Sheet1").doWrite(dataList);
    }

    private void assembleDataList(List<ForecastSummerReportDataVO> originList, List<String> timeSeries,
                                  List<List<Object>> dataList, List<CellRangeAddress> mergeRegions) {
        Collections.sort(timeSeries);
        Map<String, List<ForecastSummerReportDataVO>> dataGroup = originList.stream().collect(Collectors
                .groupingBy(x ->
                        String.join(Constants.DELIMITER, x.getOemCode(), x.getVehicleModelCode())));
        List<Entry<String, List<ForecastSummerReportDataVO>>> entries = new ArrayList<>(dataGroup.entrySet());
        entries.sort(Map.Entry.comparingByKey());
        int i = 0;
        for (ForecastSummerReportDataVO item : originList) {
            List<ForecastSummerReportDataDetailVO> details = item.getDetails();
            Map<String, String> customerForecastQtyMap = details.stream().collect(Collectors
                    .toMap(ForecastSummerReportDataDetailVO::getYearMonth,
                            ForecastSummerReportDataDetailVO::getCustomerForecastQty, (t1, t2) -> t2));
            Map<String, String> businessForecastQtyMap = details.stream().collect(Collectors
                    .toMap(ForecastSummerReportDataDetailVO::getYearMonth,
                            ForecastSummerReportDataDetailVO::getBussinessForecastQty, (t1, t2) -> t2));
            Map<String, String> sameTimeQtyMap = details.stream().collect(Collectors
                    .toMap(ForecastSummerReportDataDetailVO::getYearMonth,
                            ForecastSummerReportDataDetailVO::getSameTimeQty, (t1, t2) -> t2));

            List<Object> subList1 = Lists.newArrayList(item.getOemCode(), item.getOemName(), item.getVehicleModelCode(),
                    item.getExternalVehicleModelCode(), "客户销量/客户预测");
            for (String yearMonth : timeSeries) {
                subList1.add(customerForecastQtyMap.getOrDefault(yearMonth, null));
            }
            subList1.add(item.getOemRemark());
            subList1.add(item.getSopEopTime());
            subList1.add(item.getAccessPosition());
            dataList.add(subList1);

            List<Object> subList2 = Lists.newArrayList(item.getOemCode(), item.getOemName(), item.getVehicleModelCode(),
                    item.getExternalVehicleModelCode(), "业务预测");
            for (String yearMonth : timeSeries) {
                subList2.add(businessForecastQtyMap.getOrDefault(yearMonth, null));
            }
            subList2.add(item.getOemRemark());
            subList2.add(item.getSopEopTime());
            subList2.add(item.getAccessPosition());
            dataList.add(subList2);

            List<Object> subList3 = Lists.newArrayList(item.getOemCode(), item.getOemName(), item.getVehicleModelCode(),
                    item.getExternalVehicleModelCode(), "同期销量");
            for (String yearMonth : timeSeries) {
                subList3.add(sameTimeQtyMap.getOrDefault(yearMonth, null));
            }
            subList3.add(item.getOemRemark());
            subList3.add(item.getSopEopTime());
            subList3.add(item.getAccessPosition());
            dataList.add(subList3);

            CellRangeAddress entity1 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 0, 0);
            mergeRegions.add(entity1);
            CellRangeAddress entity2 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 1, 1);
            mergeRegions.add(entity2);
            CellRangeAddress entity3 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 2, 2);
            mergeRegions.add(entity3);
            CellRangeAddress entity4 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 3, 3);
            mergeRegions.add(entity4);

            CellRangeAddress entity5 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 5 + timeSeries.size(),
                    5 + timeSeries.size());
            mergeRegions.add(entity5);
            CellRangeAddress entity6 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 6 + timeSeries.size(),
                    6 + timeSeries.size());
            mergeRegions.add(entity6);
            CellRangeAddress entity7 = new CellRangeAddress(i * 3 + 1, i * 3 + 3, 7 + timeSeries.size(),
                    7 + timeSeries.size());
            mergeRegions.add(entity7);
            i++;
        }
    }

    private List<List<String>> initHeaders(List<String> forecastMonths) {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Lists.newArrayList("主机厂编码"));
        headers.add(Lists.newArrayList("主机厂名称"));
        headers.add(Lists.newArrayList("内部车型"));
        headers.add(Lists.newArrayList("外部车型"));
        headers.add(Lists.newArrayList("类别"));
        for (String forecastMonth : forecastMonths) {
            headers.add(Lists.newArrayList(forecastMonth));
        }
        headers.add(Lists.newArrayList("预测评审备注"));
        headers.add(Lists.newArrayList("SOP~EOP时间"));
        headers.add(Lists.newArrayList("取数装车位置"));
        return headers;
    }

    private List<String> getForecastMonthMap() {
        ConsistenceDemandForecastVersionVO versionInfo =
                consistenceDemandForecastVersionService.selectOneMaxVersionByParams(ImmutableMap.of(PARAM_ENABLED,
                        YesOrNoEnum.YES.getCode(), "versionStatus", PublishStatusEnum.PUBLISHED.getCode(),
                        "reviewVersionFlag", YesOrNoEnum.YES.getCode()));
        if (versionInfo == null) {
            throw new BusinessException("未获取到最新已发布且【是否为评审版本】为是的一致性需求预测版本!");
        }
        // 处理时间轴
        String planPeriod = versionInfo.getPlanPeriod();
        String currentYear = planPeriod.substring(0, 4);
        return getYearMonthHeaderList(planPeriod, currentYear);
    }

}