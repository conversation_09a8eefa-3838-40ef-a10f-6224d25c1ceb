package com.yhl.scp.dfp.loading.service.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ZipUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.IOUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dcp.apiConfig.enums.ApiCategoryEnum;
import com.yhl.scp.dcp.apiConfig.enums.ApiSourceEnum;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.dcp.apiConfig.feign.NewDcpFeign;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.*;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanVersionService;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.excel.handler.CustomColumnWidthHandler;
import com.yhl.scp.dfp.excel.listener.ExcelDynamicDataListener;
import com.yhl.scp.dfp.loading.convertor.LoadingDemandSubmissionConvertor;
import com.yhl.scp.dfp.loading.domain.entity.LoadingDemandSubmissionDO;
import com.yhl.scp.dfp.loading.domain.service.LoadingDemandSubmissionDomainService;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionDetailDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDTO;
import com.yhl.scp.dfp.loading.dto.LoadingDemandSubmissionReqDetailDTO;
import com.yhl.scp.dfp.loading.handler.LoadingComponentHandler;
import com.yhl.scp.dfp.loading.infrastructure.dao.LoadingDemandSubmissionDao;
import com.yhl.scp.dfp.loading.infrastructure.po.LoadingDemandSubmissionPO;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionTemp;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionVO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemVehicleModelService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.origin.vo.OriginDemandVersionVO;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandForecastInterfaceLogService;
import com.yhl.scp.dfp.originDemand.service.FdpOriginDemandInterfaceLogService;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandForecastInterfaceLogVO;
import com.yhl.scp.dfp.originDemand.vo.FdpOriginDemandInterfaceLogVO;
import com.yhl.scp.dfp.part.service.PartRelationMapService;
import com.yhl.scp.dfp.part.vo.PartRelationMapVO;
import com.yhl.scp.dfp.utils.BpimDateUtil;
import com.yhl.scp.dfp.utils.EasyExcelUtil;
import com.yhl.scp.dfp.verison.dto.VersionCreateDTO;
import com.yhl.scp.dfp.version.service.IDemandVersionCreate;
import com.yhl.scp.dfp.version.service.factory.DemandVersionCreateFactory;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.RzzSystemModuleEnum;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.rbac.entity.Dept;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.enums.ObjectTypeEnum;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.productroutestepbase.vo.MdsProductStockPointBaseVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>LoadingDemandSubmissionServiceImpl</code>
 * <p>
 * 装车需求提报应用实现
 * </p>
 *
 * @version 1.0
 * @since 2024-08-02 10:12:19
 */
@Slf4j
@Service
public class LoadingDemandSubmissionServiceImpl extends AbstractService implements LoadingDemandSubmissionService {

    @Resource
    private LoadingDemandSubmissionDao loadingDemandSubmissionDao;

    @Resource
    private LoadingDemandSubmissionDomainService loadingDemandSubmissionDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;

    @Resource
    @Lazy
    private LoadingComponentHandler loadingComponentHandler;

    @Resource
    private OriginDemandVersionService originDemandVersionService;
    @Resource
    private OemService oemService;

    @Resource
    private DemandVersionService demandVersionService;

    @Resource
    private DeliveryPlanVersionService deliveryPlanVersionService;

    @Resource
    private DemandVersionCreateFactory demandVersionCreateFactory;

    @Resource
    private NewDcpFeign newDcpFeign;

    @Resource
    private NewMdsFeign mdsFeign;

    @Resource
    private IpsNewFeign ipsNewFeign;

    @Value("${loadSubmission.file.path:/usr/local/loadSubmission}")
    private String filePath;

    @Resource
    private FdpOriginDemandInterfaceLogService originDemandService;

    @Resource
    private FdpOriginDemandForecastInterfaceLogService originDemandForecastService;

    @Resource
    private PartRelationMapService partRelationMapService;

    @Resource
    private DemandForecastVersionService demandForecastVersionService;

    @Resource
    private OemVehicleModelService oemVehicleModelService;

    @Override
    public BaseResponse<Void> doCreate(LoadingDemandSubmissionDTO loadingDemandSubmissionDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDO loadingDemandSubmissionDO =
                LoadingDemandSubmissionConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDTO);
        LoadingDemandSubmissionPO loadingDemandSubmissionPO =
                LoadingDemandSubmissionConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDomainService.validation(loadingDemandSubmissionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(loadingDemandSubmissionPO);
        if (StringUtils.isBlank(loadingDemandSubmissionPO.getId())) {
            loadingDemandSubmissionPO.setId(UUIDUtil.getUUID());
        }
        loadingDemandSubmissionDao.insert(loadingDemandSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(LoadingDemandSubmissionDTO loadingDemandSubmissionDTO) {
        // 0.数据转换
        LoadingDemandSubmissionDO loadingDemandSubmissionDO =
                LoadingDemandSubmissionConvertor.INSTANCE.dto2Do(loadingDemandSubmissionDTO);
        LoadingDemandSubmissionPO loadingDemandSubmissionPO =
                LoadingDemandSubmissionConvertor.INSTANCE.dto2Po(loadingDemandSubmissionDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        loadingDemandSubmissionDomainService.validation(loadingDemandSubmissionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(loadingDemandSubmissionPO);
        loadingDemandSubmissionDao.update(loadingDemandSubmissionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<LoadingDemandSubmissionDTO> list) {
        List<LoadingDemandSubmissionPO> newList = LoadingDemandSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        loadingDemandSubmissionDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<LoadingDemandSubmissionDTO> list) {
        List<LoadingDemandSubmissionPO> newList = LoadingDemandSubmissionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        loadingDemandSubmissionDao.updateBatchSelective(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return loadingDemandSubmissionDao.deleteBatch(idList);
        }
        return loadingDemandSubmissionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public LoadingDemandSubmissionVO selectByPrimaryKey(String id) {
        LoadingDemandSubmissionPO po = loadingDemandSubmissionDao.selectByPrimaryKey(id);
        return LoadingDemandSubmissionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION")
    public List<LoadingDemandSubmissionVO> selectByPage(Pagination pagination, String sortParam,
                                                        String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "LOADING_DEMAND_SUBMISSION")
    public List<LoadingDemandSubmissionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<LoadingDemandSubmissionVO> dataList = loadingDemandSubmissionDao.selectByCondition(sortParam,
                queryCriteriaParam);
        LoadingDemandSubmissionServiceImpl target = springBeanUtils.getBean(LoadingDemandSubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectByParams(Map<String, Object> params) {
        List<LoadingDemandSubmissionPO> list = loadingDemandSubmissionDao.selectByParams(params);
        return LoadingDemandSubmissionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.LOADING_DEMAND_SUBMISSION.getCode();
    }

    @Override
    public List<LoadingDemandSubmissionVO> invocation(List<LoadingDemandSubmissionVO> dataList,
                                                      Map<String, Object> params, String invocation) {
        if (CollectionUtils.isEmpty(dataList)) {
            return dataList;
        }
        List<String> submissionIds =
                dataList.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(submissionIds)) {
            return dataList;
        }
        Map<String, Object> detailParam = MapUtil.newHashMap();
        detailParam.put("submissionIds", submissionIds);
        List<LoadingDemandSubmissionDetailVO> submissionDetailVOS =
                loadingDemandSubmissionDetailService.selectByParams(detailParam);
        if (CollectionUtils.isEmpty(submissionDetailVOS)) {
            return dataList;
        }
        Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap = submissionDetailVOS.stream()
                .collect(
                        Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId, Collectors.toList()));

        for (LoadingDemandSubmissionVO submissionVO : dataList) {
            List<LoadingDemandSubmissionDetailVO> detailVOS = submissionDetailMap.get(submissionVO.getId());
            if (CollectionUtils.isEmpty(detailVOS)) {
                continue;
            }
            submissionVO.setDayData(
                    detailVOS.stream()
                            .filter(x -> GranularityEnum.DAY.getCode().equals(x.getSubmissionType()))
                            .sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime))
                            .collect(Collectors.toList()));
            submissionVO.setMonthData(
                    detailVOS.stream()
                            .filter(x -> GranularityEnum.MONTH.getCode().equals(x.getSubmissionType()))
                            .sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime))
                            .collect(Collectors.toList()));
            // 添加日期汇总数据计算

            Map<String, List<LoadingDemandSubmissionDetailVO>> loadingDemandSubmissionDetailMapOfTime = submissionVO.getDayData().stream().collect(Collectors.groupingBy(
                    detail -> DateUtils.dateToString(DateUtils.stringToDate(detail.getDemandTime()), DateUtils.COMMON_DATE_STR3)));

            Map<String, BigDecimal> daySummary = getDemandTimeQuantityMap(loadingDemandSubmissionDetailMapOfTime);

            //            Map<String, BigDecimal> daySummary = submissionVO.getDayData().stream()
            //                    .collect(Collectors.groupingBy(
            //                            detail -> DateUtils.dateToString(DateUtils.stringToDate(detail.getDemandTime()), DateUtils.COMMON_DATE_STR3),
            //                            Collectors.reducing(
            //                                    BigDecimal.ZERO,
            //                                    LoadingDemandSubmissionDetailVO::getDemandQuantity,
            //                                    BigDecimal::add
            //                            )
            //                    ));

            List<LoadingDemandSubmissionDetailVO> daySummaryList = daySummary.entrySet().stream()
                    .map(entry -> {
                        LoadingDemandSubmissionDetailVO vo = new LoadingDemandSubmissionDetailVO();
                        vo.setDemandTime(entry.getKey());
                        vo.setDemandQuantity(entry.getValue());
                        return vo;
                    })
                    .sorted(Comparator.comparing(LoadingDemandSubmissionDetailVO::getDemandTime))
                    .collect(Collectors.toList());

            submissionVO.setDaySummaryData(daySummaryList);

        }
        return dataList;
    }

    private Map<String, BigDecimal> getDemandTimeQuantityMap(Map<String, List<LoadingDemandSubmissionDetailVO>> listMap) {
        Map<String, BigDecimal> daySummary = new HashMap<>();
        for (Map.Entry<String, List<LoadingDemandSubmissionDetailVO>> entry : listMap.entrySet()) {
            List<LoadingDemandSubmissionDetailVO> value = entry.getValue();
            List<LoadingDemandSubmissionDetailVO> demandQuantityNullList = value.stream().filter(s -> null == s.getDemandQuantity()).collect(Collectors.toList());
            if (demandQuantityNullList.size() == value.size()) {
                daySummary.put(entry.getKey(), null);
                continue;
            }

            BigDecimal dayQuantitySum = BigDecimal.ZERO;
            for (LoadingDemandSubmissionDetailVO detail : value) {
                if (detail.getDemandQuantity() != null) {
                    dayQuantitySum = dayQuantitySum.add(detail.getDemandQuantity());
                }
            }
            daySummary.put(entry.getKey(), dayQuantitySum);
        }
        return daySummary;
    }

    @SneakyThrows
    @Override
    public void exportTemplate(HttpServletResponse response, String templateType) {
        ServletOutputStream out = response.getOutputStream();
        EasyExcelUtil.initResponse(response, "装车需求提报模板");
        List<List<String>> headers = Lists.newArrayList();
        headers.add(Collections.singletonList("主机厂编码"));
        headers.add(Collections.singletonList("产品编码"));
        headers.add(Collections.singletonList("客户零件号"));
        if ("ROW".equals(templateType)) {

            BpimDateUtil.getDatesForDays(31).forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3))));
            BpimDateUtil.getDatesForMonths(13).forEach(day -> headers.add(Collections.singletonList(DateUtils.dateToString(day, DateUtils.YEAR_MONTH))));

        } else if ("COL".equals(templateType)) {
            headers.add(Collections.singletonList("日期"));
            headers.add(Collections.singletonList("数量"));

            // 新增代码：生成最近30天和12个月的数据
            List<List<String>> dataRows = new ArrayList<>();

            // 生成最近30天的数据
            BpimDateUtil.getDatesForNextNDays(30).forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, DateUtils.dateToString(day, DateUtils.COMMON_DATE_STR3)); // 日期放在第4列
                dataRows.add(row);
            });

            // 生成12个月的数据
            BpimDateUtil.getDatesForNextNMonths(12).forEach(day -> {
                List<String> row = new ArrayList<>(Collections.nCopies(4, "")); // 初始化4列
                row.set(3, DateUtils.dateToString(day, DateUtils.YEAR_MONTH)); // 日期放在第4列
                dataRows.add(row);
            });

        }

        EasyExcel.write(out)
                .sheet("装车需求提报")
                .head(headers)
                .registerWriteHandler(new CustomColumnWidthHandler())
                .doWrite(Collections.emptyList());
    }

    @Override
    public BaseResponse<Void> submissionData(LoadingDemandSubmissionReqDTO reqDTO) {
        try {
            String submissionType = reqDTO.getSubmissionType();
            String originVersionId = reqDTO.getOriginVersionId();
            List<LoadingDemandSubmissionReqDetailDTO> detailDTOList = reqDTO.getDetailDTOList();
            if (CollectionUtils.isEmpty(detailDTOList)) {
                return BaseResponse.success("请提报相关数据");
            }
            if (SubmissionTypeEnum.API.getCode().equals(submissionType)) {
                List<String> oemCodeList = detailDTOList.stream().map(LoadingDemandSubmissionReqDetailDTO::getOemCode).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(oemCodeList)) {
                    return BaseResponse.success("请提报相关数据");
                }
                BaseResponse<List<Scenario>> scenario = ipsNewFeign.getScenariosByModuleCode(RzzSystemModuleEnum.DFP.getCode());
                if (CollectionUtils.isEmpty(scenario.getData())) {
                    return BaseResponse.error("未找到对应的场景");
                }
                List<OemVO> oemVOS = oemService.selectProductEdiFlag(YesOrNoEnum.YES.getCode(), YesOrNoEnum.YES.getCode());
                if (CollectionUtils.isEmpty(oemVOS)) {
                    return BaseResponse.error("未找到对应的主机厂");
                }
                List<OemVO> ediOemList = oemVOS.stream().filter(oemVO -> oemCodeList.stream().anyMatch(t -> t.equals(oemVO.getOemCode()))).collect(Collectors.toList());

                if (CollectionUtils.isEmpty(ediOemList)) {
                    return BaseResponse.error("所选主机厂不支持EDI接口同步");
                }
                return apiSubmission(ediOemList, originVersionId, scenario.getData().get(0));
            } else if (SubmissionTypeEnum.FILE.getCode().equals(submissionType)) {
                StringBuilder totalErrMsg = new StringBuilder();
                for (LoadingDemandSubmissionReqDetailDTO detailDTO : detailDTOList) {
                    String oemCode = detailDTO.getOemCode();
                    String contentType = detailDTO.getContentType();
                    File submissionFile = getSubmissionFile(originVersionId, oemCode, contentType);
                    if (StringUtils.isBlank(contentType)) {
                        return BaseResponse.error("参数异常");
                    }
                    if (Objects.isNull(submissionFile)) {
                        return BaseResponse.error("文件为空");
                    }
                    // todo 更新原始版本的文件下载链接
                    originDemandVersionService.updateSubmissionType(originVersionId, oemCode,
                            SubmissionTypeEnum.FILE.getCode());
                    // 增量和全量进行数据新增
                    // 初始化监听器
                    Map<String, String> extMap = MapUtil.newHashMap();
                    extMap.put("originVersionId", originVersionId);
                    extMap.put("oemCode", oemCode);
                    extMap.put("importType", reqDTO.getImportType());
                    extMap.put("contentType", contentType);
                    ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 2, 0,
                            extMap);
                    // 解析数据
                    try (FileInputStream inputStream = new FileInputStream(submissionFile)) {
                        EasyExcel.read(inputStream, excelDynamicDataListener).sheet().doReadSync();
                    }
                    String errMsg = excelDynamicDataListener.getErrMsg();
                    if (StringUtils.isNotBlank(errMsg)) {
                        totalErrMsg.append(errMsg);
                        totalErrMsg.append("\n");
                    }
                }
                if (StringUtils.isBlank(totalErrMsg.toString())) {
                    return BaseResponse.success("模板提报成功");
                } else {
                    return BaseResponse.error(String.format("模板提报成功，部分失败：%s", totalErrMsg));
                }
            } else {
                return BaseResponse.error("提报类型错误");
            }
        } catch (Exception ex) {
            String errMsg = String.format("提报失败:%s", ex.getMessage());
            log.error(errMsg);
            return BaseResponse.error(errMsg);
        }
    }

    private void deleteData(String originVersionId, String oemCode) {
        loadingDemandSubmissionDao.deleteData(originVersionId, oemCode);
    }

    private File getSubmissionFile(String originVersionId, String oemCode, String contentType) {
        String directoryStr =
                filePath + File.separator + originVersionId + File.separator + oemCode + File.separator + contentType;
        File directory = new File(directoryStr);
        File latestFile = null;
        long latestModTime = 0;
        // 确保路径存在且是一个目录
        if (directory.exists() && directory.isDirectory()) {
            // 遍历目录下的所有文件和文件夹
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    // 只考虑文件，忽略目录
                    if (file.isFile()) {
                        try {
                            BasicFileAttributes attr = Files.readAttributes(file.toPath(), BasicFileAttributes.class);
                            long modTime = attr.lastModifiedTime().toMillis();
                            if (modTime > latestModTime) {
                                latestModTime = modTime;
                                latestFile = file;
                            }
                        } catch (IOException e) {
                            // 处理读取属性时可能发生的异常，这里只是简单地打印出来
                            log.error(e.getMessage());
                        }
                    }
                }
            }
        }
        return latestFile;
    }

    @Override
    public BaseResponse<Void> uploadFiles(String originVersionId, String oemCode, String contentType,
                                          MultipartFile originFile, MultipartFile submissionFile) {
        if (submissionFile.isEmpty()) {
            return BaseResponse.error(String.format("主机厂：%s提报文件不存在", oemCode));
        }
        String directoryStr = filePath + File.separator + originVersionId + File.separator + oemCode;
        File directory = new File(directoryStr);
        if (directory.mkdirs()) {
            log.info("oemCode:{}创建目录成功", oemCode);
        } else {
            log.error("oemCode:{}创建目录失败或目录已存在", oemCode);
        }
        try {
            // 构建文件保存路径
            String submissionPathStr = directoryStr + File.separator + contentType;
            File submissionDirectory = new File(submissionPathStr);
            submissionDirectory.delete();
            if (submissionDirectory.mkdirs()) {
                log.info("contentType:{}创建目录成功", contentType);
            } else {
                log.error("contentType:{}创建目录失败或目录已存在", contentType);
            }
            String submissionFileStr = submissionPathStr + File.separator + submissionFile.getOriginalFilename();
            Path submissionPath = Paths.get(submissionFileStr);
            submissionFile.transferTo(submissionPath);
            if (!originFile.isEmpty()) {
                String originPathStr = directoryStr + File.separator + "ORIGIN";
                File originDirectory = new File(originPathStr);
                if (originDirectory.mkdirs()) {
                    log.info("contentType:{}创建目录成功", "ORIGIN");
                } else {
                    log.error("contentType:{}创建目录失败或目录已存在", "ORIGIN");
                }
                String originFileStr = originPathStr + File.separator + originFile.getOriginalFilename();
                Path originPath = Paths.get(originFileStr);
                originFile.transferTo(originPath);
            }
        } catch (IOException ex) {
            return BaseResponse.error("文件上传失败");
        }
        return BaseResponse.success("文件上传成功");
    }

    @Override
    public void doImport(Map<Integer, String> headers, List<Map<Integer, String>> data, int fixedColumns,
                         Map<String, String> extMap) {
        String scenario = SystemHolder.getScenario();
        List<String> collect = new ArrayList<>(headers.values());
        if (!collect.contains("主机厂编码")
                || !collect.contains("产品编码")
                || !collect.contains("客户零件号")) {
            throw new BusinessException("导入数据模板错误，请检查");
        }
        if (CollectionUtils.isEmpty(data)) {
            throw new BusinessException("模板数据为空,没有数据");
        }
        String originVersionId = extMap.get("originVersionId");
        String contentType = extMap.get("contentType");
        String templateType = extMap.get("templateType");
        String demandCategory = extMap.get("projectDemandType");
        String checkFlag = extMap.get("checkFlag");

        // 处理第二种模板格式
        if ("COL".equals(templateType)) {
            Map<Integer, String> newHeaders = new LinkedHashMap<>();
            Map<String, Map<Integer, String>> newRowMap = new LinkedHashMap<>();
            // 复制固定列
            for (int i = 0; i < 3; i++) {
                newHeaders.put(i, headers.get(i));
            }
            // 创建日期列
            Set<String> uniqueDates = new TreeSet<>();
            for (Map<Integer, String> row : data) {
                uniqueDates.add(row.get(3));
            }
            // 添加日期列到新的headers
            int columnIndex = 3;
            for (String date : uniqueDates) {
                newHeaders.put(columnIndex++, date);
            }

            for (Map<Integer, String> row : data) {
                String key = (row.get(0) + "|" + row.get(1) + "|" + row.get(2)).trim().toLowerCase();
                Map<Integer, String> newRow = newRowMap.computeIfAbsent(key, k -> {
                    Map<Integer, String> r = new LinkedHashMap<>();
                    for (int i = 0; i < newHeaders.size(); i++) {
                        r.put(i, i < 3 ? row.get(i).trim() : null);
                    }
                    return r;
                });

                String date = row.get(3).trim();
                String quantity = row.get(4);
                int dateColumnIndex = new ArrayList<>(newHeaders.values()).indexOf(date);
                newRow.put(dateColumnIndex, StringUtils.isBlank(quantity) ? null : quantity.trim());
            }
            // 更新headers和data
            headers.clear();
            headers.putAll(newHeaders);
            data.clear();
            data.addAll(newRowMap.values());
        }
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("enabled", YesOrNoEnum.YES.getCode());
        List<OemVO> oemVOList = oemService.selectByParams(queryMap);

        Set<String> oemCodes = getOemNamesFromData(data);
        if (CollectionUtils.isEmpty(oemCodes)) {
            throw new BusinessException("未能从数据中获取有效的主机厂编码");
        }

        Map<String, String> oemCodeToNameMap = oemVOList.stream().filter(Objects::nonNull)
                .filter(oemVO -> oemVO.getOemCode() != null && oemVO.getOemName() != null)
                .collect(Collectors.toMap(OemVO::getOemCode, OemVO::getOemName, (v1, v2) -> v1));
        Set<String> notFindOemCodes = oemCodes.stream()
        		.filter( e-> !oemCodeToNameMap.containsKey(e))
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(notFindOemCodes)) {
            throw new BusinessException("未能匹配到有效的主机厂名称:" + String.join(",", notFindOemCodes));
        }

        //校验主机厂，产品绑定关系
        List<String> productCodes = data.stream()
        		.map(row -> row.get(1))
        		.filter(StringUtils::isNotBlank)
        		.collect(Collectors.toList());

        BaseResponse<String> defaultScenario = ipsNewFeign.getDefaultScenario(RzzSystemModuleEnum.DFP.getCode(), TenantCodeEnum.FYQB.getCode());
        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "SALE_ORGANIZATION",
                "INTERNAL", null);
        String saleRangeData = scenarioBusinessRange.getData().getRangeData();
        List<NewProductStockPointVO> productInfoList = mdsFeign.selectProductStockPointByParams(scenario, ImmutableMap.of(
//    			"enabled", YesOrNoEnum.YES.getCode(),
//    			"stockPointCode", rangeData,
    			"productCodes" , productCodes));

        Map<String, NewProductStockPointVO> productInfoMap =  productInfoList.stream()
        		.collect(Collectors.toMap( e -> e.getProductCode() + "&" +  e.getStockPointCode(),
        				Function.identity(),(v1, v2) -> v1));

        Map<String, List<String>> fg2SaMap = mdsFeign.selectFg2SaMap(scenario, productCodes);
        List<String> saCodes = fg2SaMap.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        List<String> allProductCodes = new ArrayList<>();
        allProductCodes.addAll(saCodes);
        allProductCodes.addAll(productCodes);
        //  提取 productCode 并记录对应的行号
        Map<String, Integer> productCodeRowMap = new HashMap<>();
        for (int i = 0; i < data.size(); i++) {
            Map<Integer, String> rowMap = data.get(i);
            String productCode = rowMap.get(1);
            if (StringUtils.isNotBlank(productCode)) {
                productCodeRowMap.put(productCode, i + 2);
            }
        }
        List<String> productStockPointBaseCodes = mdsFeign.selectProductStockPointBaseByParams(scenario,
                        ImmutableMap.of("productCodeList", allProductCodes)).stream()
                .map(MdsProductStockPointBaseVO::getProductCode).distinct()
                .collect(Collectors.toList());

        Set<String> unmatchedProducts = new HashSet<>();
        for (String productCode : productCodes) {
            if (!productStockPointBaseCodes.contains(productCode)) {
                unmatchedProducts.add(productCode);
            }
        }

        if (!unmatchedProducts.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder();
            for (String productCode : unmatchedProducts) {
                Integer rowNum = productCodeRowMap.get(productCode);
                errorMsg.append(String.format("行数：%d，产品编码%s未能匹配到有效的产品工艺数据，请联系工艺维护%n", rowNum, productCode));
            }
            throw new BusinessException(errorMsg.toString());
        }
        List<OemVehicleModelVO> oemVehicleModelList = oemVehicleModelService.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(),
    			"oemCodeList" , new ArrayList<String>(oemCodes)));
        Map<String, List<String>> vehicleOmesMap = oemVehicleModelList.stream()
                .collect(Collectors.groupingBy(
                    OemVehicleModelVO::getOemVehicleModelCode,
                    Collectors.mapping(OemVehicleModelVO::getOemCode, Collectors.toList())
                ));

        //获取当前月的下标和今天到月底的下标
        List<Integer> monthDayIndexList = getMonthDayIndexList(headers);
        Integer currentMonthIndex = monthDayIndexList.get(monthDayIndexList.size() -1);
        monthDayIndexList.remove(monthDayIndexList.size() -1);
        List<Integer> errorIndex = new ArrayList<>();
        List<Integer> checkIndex = new ArrayList<>();
        List<String> checkProductCodes = new ArrayList<>();
        List<String> checkOemCodes = new ArrayList<>();

        //获取生产组织库存点
        BaseResponse<ScenarioBusinessRangeVO> productOrgRange = ipsNewFeign.getScenarioBusinessRange(defaultScenario.getData(),
                "PRODUCT_ORGANIZATION",
                "INTERNAL", null);
        String productOrgRangeDate = productOrgRange.getData().getRangeData();
        List<String> productOrgStockPointCodes = Arrays.asList(productOrgRangeDate.split(","));
        List<String> productOrgProductIds = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
        	Map<Integer, String> rowMap = data.get(i);
        	String omeCode = rowMap.get(0);
        	String productCode = rowMap.get(1);
        	NewProductStockPointVO productInfo = productInfoMap.get(productCode + "&" + saleRangeData);
        	int rowNum = i + 2;
        	if (productInfo == null) {
        		throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "物料未维护，请联系工艺维护");
        	}
        	if (!YesOrNoEnum.YES.getCode().equals(productInfo.getEnabled())) {
        		throw new BusinessException("行数：" + rowNum + ",产品编码不是有效的，请联系工艺维护");
        	}
            if (!productStockPointBaseCodes.contains(productCode)) {
                throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "不存在工艺基础数据，请联系工艺维护");
            }
            if (fg2SaMap.containsKey(productCode)) {
                List<String> saCodeList = fg2SaMap.get(productCode);
                List<String> diffSection = new ArrayList<>(com.yhl.platform.common.utils.CollectionUtils
                        .getDiffSection(saCodeList, productStockPointBaseCodes));
                if (CollectionUtils.isNotEmpty(diffSection)) {
                    String join = String.join(",", diffSection);
                    throw new BusinessException("行数：" + rowNum + ",供应类型为制造的物料编码" + join + "不存在工艺基础数据，请联系工艺维护");
                }
            }
        	String vehicleModelCode = productInfo.getVehicleModelCode();
        	if(StringUtils.isEmpty(vehicleModelCode)) {
        		throw new BusinessException("行数：" + rowNum + ",产品编码:" + productCode + "对应车型为空,请检查");
        	}
        	List<String> omeCodes = vehicleOmesMap.get(vehicleModelCode);
        	if(CollectionUtils.isEmpty(omeCodes) || !omeCodes.contains(omeCode)) {
        		throw new BusinessException("行数：" + rowNum + ",产品编码 "+ productCode 
        				+ "对应车型与主机厂:" + omeCode + "(" + oemCodeToNameMap.get(omeCode)  + ")关联为空,请检查");
        	}
        	//判断采购计划类别的库存点是否在生产组织库存点
        	String stockPointCode = productInfo.getPoCategory().split("\\.")[1];
        	if(productOrgStockPointCodes.contains(stockPointCode)
        			&& productInfoMap.containsKey(productCode + "&" + stockPointCode)
        			&& !productOrgProductIds.contains(productInfo.getId())) {
        		productOrgProductIds.add(productInfoMap.get(productCode + "&" + stockPointCode).getId());
        	}

        	String currMonthQty = rowMap.get(currentMonthIndex);
    		if(StringUtils.isEmpty(currMonthQty)) {
    			//校验存量数据
    			if(!checkOemCodes.contains(omeCode)) {
    				checkOemCodes.add(omeCode);
    			}
    			if(!checkProductCodes.contains(productCode)) {
    				checkProductCodes.add(productCode);
    			}
    			checkIndex.add(i);
    			continue;
    		}
    		//校验当月发货的汇总量（如今天是5月15日，汇总量为2025-05-15 至2025-05-31）是否大于当月预测量
    		BigDecimal totalQty = BigDecimal.ZERO;
    		for (Integer dayIndex : monthDayIndexList) {
    			String dayQty = rowMap.get(dayIndex);
    			if(StringUtils.isNotEmpty(dayQty)) {
    				totalQty = totalQty.add(new BigDecimal(dayQty));
    			}
    		}
    		if(totalQty.compareTo(new BigDecimal(currMonthQty)) > 0) {
    			errorIndex.add(rowNum);
    		}
		}

        //校验产品工艺路径
        Map<String, String> checkPorductRoutingMap = new HashMap<>();
        if(CollectionUtils.isNotEmpty(productOrgProductIds)) {
        	long startTime = System.currentTimeMillis();
        	checkPorductRoutingMap = mdsFeign.checkPorductRouting(scenario, productOrgProductIds);
        	log.info("装车需求提报导入-校验工艺路径数据耗时：", (System.currentTimeMillis() - startTime));
        }
        List<String> errorMsgList = new ArrayList<>();
        for (int i = 0; i < data.size(); i++) {
        	Map<Integer, String> rowMap = data.get(i);
        	String productCode = rowMap.get(1);
        	int rowNum = i + 2;
        	if(checkPorductRoutingMap.containsKey(productCode)) {
        		errorMsgList.add("行数：" + rowNum + ",产品编码:" +  productCode + ","
        				+ checkPorductRoutingMap.get(productCode));
        		checkPorductRoutingMap.remove(productCode);
        	}
		}
        //如果MAP中还有元素，那就是输入物品中的工艺类型为制造的产品
        Boolean firstFalg = true;
    	for (Entry<String, String> checkPorductEntry : checkPorductRoutingMap.entrySet()) {
    		if(firstFalg) {
    			errorMsgList.add("供应类型为制造的物料, 产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
    		}else {
    			errorMsgList.add("产品编码:" + checkPorductEntry.getKey() + "," + checkPorductEntry.getValue());
    		}
    		firstFalg = false;
		}
    	if(CollectionUtils.isNotEmpty(errorMsgList)) {
    		throw new BusinessException("导入失败：" + String.join("<br/>", errorMsgList));
    	}

    	//进行发货数量和预测校验
    	if(CollectionUtils.isNotEmpty(checkOemCodes) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
    		//校验存量数据
	        Map<String, Object> loadingParamMap = new HashMap<>();
	        loadingParamMap.put("versionId", originVersionId);
	        loadingParamMap.put("enabled", YesOrNoEnum.YES.getCode());
	        loadingParamMap.put("productCodes", checkProductCodes);
	        loadingParamMap.put("oemCodes", checkOemCodes);
	        loadingParamMap.put("demandCategory", demandCategory);
	        List<LoadingDemandSubmissionVO> oldLodLoadingDemandSubmissionVOS = this.selectByParams(loadingParamMap);
	        Map<String, String> oldLodLoadingDemandSubmissionMap =  oldLodLoadingDemandSubmissionVOS.stream()
	        		.collect(Collectors.toMap(e -> e.getOemCode() + "&" + e.getProductCode(),LoadingDemandSubmissionVO::getId,(v1, v2) -> v1));
	        //查询获取历史的装车需求详细信息（只差月份）
	        Map<String, BigDecimal> submissionDetailVOMap = new HashMap<>();
	        if(CollectionUtils.isNotEmpty(oldLodLoadingDemandSubmissionVOS)) {
	        	List<String> oldSubmissionIds = oldLodLoadingDemandSubmissionVOS.stream().map(LoadingDemandSubmissionVO::getId).collect(Collectors.toList());
	        	List<LoadingDemandSubmissionDetailVO> oldLodLoadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService
	        			.selectByParams(ImmutableMap.of("enabled", YesOrNoEnum.YES.getCode(),
	        			"submissionType" , GranularityEnum.MONTH.getCode(),
	        			"demandTime" , DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH),
	        			"submissionIds" , oldSubmissionIds));
	        	submissionDetailVOMap = oldLodLoadingDemandSubmissionDetailVOS.stream()
	        			.filter(e -> e.getDemandQuantity() != null)
	        			.collect(Collectors.toMap(LoadingDemandSubmissionDetailVO::getSubmissionId,LoadingDemandSubmissionDetailVO::getDemandQuantity,
	        					(v1, v2) -> v1));
	        }
	        for (int i = 0; i < data.size(); i++) {
	        	if(!checkIndex.contains(i)) {
	        		continue;
	        	}
	        	Integer rowNum = i + 2;
	        	Map<Integer, String> rowMap = data.get(i);
	        	String omeCode = rowMap.get(0);
	        	String productCode = rowMap.get(1);
	        	String submissionId = oldLodLoadingDemandSubmissionMap.get(omeCode + "&" + productCode);
	        	if(submissionId == null) {
	        		continue;
	        	}
	        	BigDecimal totalQty = submissionDetailVOMap.getOrDefault(submissionId, BigDecimal.ZERO);
	        	BigDecimal totalDayQty = BigDecimal.ZERO;
	    		for (Integer dayIndex : monthDayIndexList) {
	    			String dayQty = rowMap.get(dayIndex);
	    			if(StringUtils.isNotEmpty(dayQty)) {
	    				totalDayQty = totalDayQty.add(new BigDecimal(dayQty));
	    			}
	    		}
	        	if(totalDayQty.compareTo(totalQty) > 0) {
	    			errorIndex.add(rowNum);
	    		}
	        }
    	}
    	if(CollectionUtils.isNotEmpty(errorIndex) && !YesOrNoEnum.YES.getCode().equals(checkFlag)) {
    		Collections.sort(errorIndex);
    		String errorIndexMsg = errorIndex.stream().map(Object::toString).collect(Collectors.joining(", "));
    		throw new BusinessException("行数"+ errorIndexMsg +"，发货数量大于预测数量，如确认请点击确定按钮");
    	}

        if ("full".equals(extMap.get("importType"))) {
            // 全量导入 先删除需求提报全部数据
            List<String> waitDeleteIds = loadingDemandSubmissionDetailService
                    .selectByOriginIdAndOemCode(originVersionId, null, contentType);
            this.deleteData(originVersionId, null);
            // 删除历史提报详情数据
            loadingDemandSubmissionDetailService.doDelete(waitDeleteIds);
        }

        // 对每个oemCode分别处理数据
        extMap.put("oemCode", String.join(",", oemCodes));
        loadingComponentHandler.handle(contentType, headers, data, extMap);
    }



    /**
     * 获取当前月的下标和今天到月底的下标
     * @param headers
     * @param currYearMonth
     */
	private List<Integer> getMonthDayIndexList(Map<Integer, String> headers) {
		LocalDate today = LocalDate.now();
        // 获取本月的最后一天
        LocalDate lastDayOfMonth = today.withDayOfMonth(today.lengthOfMonth());
        // 定义日期格式
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        // 创建列表存储每天的字符串格式
        List<String> currMonthDayList = new ArrayList<>();
        // 从今天开始遍历到最后一天
        for (LocalDate date = today; !date.isAfter(lastDayOfMonth); date = date.plusDays(1)) {
        	currMonthDayList.add(date.format(formatter));
        }
        String currYearMonth = DateUtils.dateToString(new Date(), DateUtils.YEAR_MONTH);
        Integer currYearMonthIndex = 0;
    	List<Integer> indexList = new ArrayList<>();
    	for (Entry<Integer, String> headerEntry : headers.entrySet()) {
    		if(currMonthDayList.contains(headerEntry.getValue())) {
    			indexList.add(headerEntry.getKey());
    		}
    		if(currYearMonth.equals(headerEntry.getValue())) {
    			currYearMonthIndex = headerEntry.getKey();
    		}
		}
    	indexList.add(currYearMonthIndex);
		return indexList;
	}

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isEmpty(versionDTOList)) {
            return 0;
        }
        List<String> submissionIds = versionDTOList.stream().map(RemoveVersionDTO::getId).distinct().collect(Collectors.toList());
        // 删除主数据
        loadingDemandSubmissionDao.deleteBatch(submissionIds);
        // 删除明细数据
        loadingDemandSubmissionDetailService.deleteBySubmissionIds(submissionIds);
        return 1;
    }

    @Override
    public List<LoadingDemandSubmissionVO> selectByProductCode(List<String> productCodeList) {
        List<LoadingDemandSubmissionVO> dataList = loadingDemandSubmissionDao.selectByProductCode(productCodeList);
        LoadingDemandSubmissionServiceImpl target = springBeanUtils.getBean(LoadingDemandSubmissionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public void downloadFile(String originVersionId, String oemCode, HttpServletResponse response) {
        String versionOemFilePath = filePath + File.separator + originVersionId + File.separator + oemCode;
        try {
            versionOemFilePath = IOUtils.cleanseDir(false, versionOemFilePath);
            if (versionOemFilePath.endsWith("/")) {
                versionOemFilePath = versionOemFilePath.substring(0, versionOemFilePath.length() - 1);
            }
            log.info("versionOemFilePath is {}", versionOemFilePath);
            File file = new File(versionOemFilePath);
            if (file.exists()) {
                response.setContentType("application/zip");
                response.setHeader("Content-Disposition", "attachment; filename=" + oemCode + ".zip");
                OutputStream os = response.getOutputStream();
                ZipUtil.zip(os, Charset.defaultCharset(), false, null, file);
            } else {
                log.error("文件目录不存在！");
                throw new Exception("文件目录不存在！");
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    @Override
    public BaseResponse<Void> updateLoadingSubmissionDetail(List<LoadingDemandSubmissionDetailDTO> loadingDemandSubmissionDetailDTOs) {
        if (CollectionUtils.isEmpty(loadingDemandSubmissionDetailDTOs)) {
            return BaseResponse.error("请至少选择一条数据");
        }
        // for (LoadingDemandSubmissionDetailDTO dtos : loadingDemandSubmissionDetailDTOs) {
        //     if (dtos.getDemandQuantity() == null) {
        //         return BaseResponse.error("数据不能为空");
        //     }
        // }
        loadingDemandSubmissionDetailDTOs.forEach(x -> loadingDemandSubmissionDetailService.updateQuantityById(x.getId(), x.getDemandQuantity()));
        return BaseResponse.success("修改成功");
    }

    private Set<String> getOemNamesFromData(List<Map<Integer, String>> data) {
        return data.stream()
                .map(row -> row.get(0))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
    }

    private List<Map<Integer, String>> filterDataByOemCode(List<Map<Integer, String>> data, String oemCode) {
        return data.stream()
                .filter(row -> oemCode.equals(row.get(0)))
                .collect(Collectors.toList());
    }

    @Override
    public BaseResponse<Void> importDemand(String originVersionId, String importType, String templateType,
                                                               String projectDemandType,
                                                               String checkFlag,
                                                               MultipartFile submissionFile) {
        Map<String, String> extMap = MapUtil.newHashMap();
        extMap.put("originVersionId", originVersionId);
        extMap.put("importType", importType);
        extMap.put("checkFlag", checkFlag);
        extMap.put("projectDemandType", projectDemandType);
        try {
            if (submissionFile.isEmpty()) {
                throw new BusinessException("文件为空");
            }
            ExcelDynamicDataListener excelDynamicDataListener = new ExcelDynamicDataListener(this, 2, 0,
                    extMap);
            EasyExcel.read(submissionFile.getInputStream(), excelDynamicDataListener).sheet().doReadSync();
        } catch (Exception e) {
            log.error("需求提报失败:", e);
            if(e.getLocalizedMessage().contains("发货数量大于预测数量")) {
            	throw new BusinessException("{0}", e.getLocalizedMessage());
            }
            throw new BusinessException("导入失败, {0}", e.getLocalizedMessage());
        }

        String successInfo = extMap.get("successInfo");
        String errorInfo = extMap.get("errorInfo");
        if (StringUtils.isBlank(errorInfo)){
            return BaseResponse.success(successInfo);
        }else {
            return BaseResponse.error(successInfo+errorInfo);
        }

    }

    List<String> doCreateOrigin(String scenario, String dayPeriod, String generateType) {
        VersionCreateDTO versionCreateDTO = new VersionCreateDTO();
        versionCreateDTO.setScenario(scenario);
        versionCreateDTO.setPlanPeriod(dayPeriod);
        versionCreateDTO.setVersionType(VersionTypeEnum.ORIGIN_DEMAND.getCode());
        versionCreateDTO.setGenerateType(generateType);
        versionCreateDTO.setOemCodeResource(new ArrayList<>());
        // 获取最新的原始需求数据
        String originDemandVersionId = originDemandVersionService.selectLatestVersionId();
        if (StringUtils.isNotBlank(originDemandVersionId)) {
            versionCreateDTO.setTargetOriginDemandVersionId(originDemandVersionId);
        }
        IDemandVersionCreate demandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(versionCreateDTO.getVersionType());
        // 生成原始需求版本, 只包含版本号和二级id, list元素0是版本号、元素1是二级版本ID
        return demandVersionCreate.doDemandVersionCreateNew(versionCreateDTO, null);
    }

    @Override
    public synchronized BaseResponse<Void> doCreateNewVersion(String scenario, String generateType) {
        log.info("开始新建版本++++++++++++++++++++++++++++++++++++++++++++++++");
        // 基础数据
        SimpleDateFormat dayFormat = new SimpleDateFormat("yyyyMMdd");
        SimpleDateFormat monthFormat = new SimpleDateFormat("yyyyMM");
        Date date = new Date();
        String dayPeriod = dayFormat.format(date);
        String monthPeriod = monthFormat.format(date);

        // 拼装原始需求版本数据
        VersionCreateDTO versionCreateDTO = new VersionCreateDTO();
        versionCreateDTO.setScenario(scenario);
        versionCreateDTO.setPlanPeriod(dayPeriod);
        versionCreateDTO.setVersionType(VersionTypeEnum.ORIGIN_DEMAND.getCode());
        versionCreateDTO.setGenerateType(generateType);
        versionCreateDTO.setOemCodeResource(new ArrayList<>());
        // 获取最新的原始需求数据
        String originDemandVersionId = originDemandVersionService.selectLatestVersionId();
        if (StringUtils.isNotBlank(originDemandVersionId)) {
            versionCreateDTO.setTargetOriginDemandVersionId(originDemandVersionId);
        }
        IDemandVersionCreate demandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(versionCreateDTO.getVersionType());
        // 生成原始需求版本, 只包含版本号和二级id, list元素0是版本号、元素1是二级版本ID
        List<String> list = demandVersionCreate.doDemandVersionCreateNew(versionCreateDTO, null);
        String originVersionCode = list.get(0);
        String originVersionId = list.get(1);

        // 创建版本ID和版本号
        String dailyVersionId = UUIDUtil.getUUID();
        String dailyVersionCode = getVersionCode(VersionTypeEnum.CLEAN_DEMAND.getCode(), originVersionCode);
        String deliveryVersionId = UUIDUtil.getUUID();
        String deliveryVersionCode = getVersionCode(VersionTypeEnum.DELIVERY_PLAN.getCode(), dailyVersionCode);
        String rollingVersionId = UUIDUtil.getUUID();
        String rollingVersionCode = getVersionCode(VersionTypeEnum.CLEAN_FORECAST.getCode(), originVersionCode);
        String forecastVersionId = UUIDUtil.getUUID();
        String forecastVersionCode = getVersionCode(VersionTypeEnum.DEMAND_FORECAST.getCode(), rollingVersionCode);
        String userId = SystemHolder.getUserId();
        CompletableFuture<Void> dailyFuture = CompletableFuture.runAsync(() -> {
            // 日需求版本创建
            // 拼装日需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO dailyVersionCreateDTO = new VersionCreateDTO();
            dailyVersionCreateDTO.setCreator(userId);
            dailyVersionCreateDTO.setScenario(scenario);
            dailyVersionCreateDTO.setVersionType(VersionTypeEnum.CLEAN_DEMAND.getCode());
            dailyVersionCreateDTO.setPlanPeriod(dayPeriod);
            dailyVersionCreateDTO.setGenerateType(generateType);
            dailyVersionCreateDTO.setOemCodeResource(new ArrayList<>());
            // 目标原始需求版本ID
            dailyVersionCreateDTO.setPreviousOriginVersionId(originDemandVersionId);
            dailyVersionCreateDTO.setTargetOriginDemandVersionId(originVersionId);
            dailyVersionCreateDTO.setCurrentVersionId(dailyVersionId);
            dailyVersionCreateDTO.setCurrentVersionCode(dailyVersionCode);
            // 生成日需求版本
            IDemandVersionCreate dayDemandVersionCreate = demandVersionCreateFactory.getDemandVersionCreate(dailyVersionCreateDTO.getVersionType());
            dayDemandVersionCreate.doDemandVersionCreateParallel(dailyVersionCreateDTO);
        });

        CompletableFuture<Void> deliveryFuture = CompletableFuture.runAsync(() -> {
            // 发货计划版本创建
            // 拼装发货计划需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO deliveryPlanVersionCreateDTO = new VersionCreateDTO();
            deliveryPlanVersionCreateDTO.setCreator(userId);
            deliveryPlanVersionCreateDTO.setScenario(scenario);
            deliveryPlanVersionCreateDTO.setVersionType(VersionTypeEnum.DELIVERY_PLAN.getCode());
            deliveryPlanVersionCreateDTO.setPlanPeriod(dayPeriod);
            deliveryPlanVersionCreateDTO.setGenerateType(generateType);
            deliveryPlanVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            deliveryPlanVersionCreateDTO.setTargetCleanDemandVersionId(dailyVersionId);
            deliveryPlanVersionCreateDTO.setCurrentVersionId(deliveryVersionId);
            deliveryPlanVersionCreateDTO.setCurrentVersionCode(deliveryVersionCode);
            // 获取最新的发货计划需求数据
            Map<String, Object> deliveryPlanMap = new HashMap<>();
            DeliveryPlanVersionVO deliveryPlanVersionVO = deliveryPlanVersionService.selectLatestVersionByParams(deliveryPlanMap);
            if (null != deliveryPlanVersionVO) {
                log.info("获取最新的发货计划版本: {}", deliveryPlanVersionVO.getVersionCode());
                deliveryPlanVersionCreateDTO.setTargetDeliveryPlanVersionId(deliveryPlanVersionVO.getId());
                deliveryPlanVersionCreateDTO.setTargetVersionCode(deliveryPlanVersionVO.getVersionCode());
            }
            // 生成发货计划需求版本
            IDemandVersionCreate deliveryPlanVersionCreate =
                    demandVersionCreateFactory.getDemandVersionCreate(deliveryPlanVersionCreateDTO.getVersionType());
            deliveryPlanVersionCreate.doDemandVersionCreateParallel(deliveryPlanVersionCreateDTO);
        });

        CompletableFuture<Void> rollingFuture = CompletableFuture.runAsync(() -> {
            // 滚动需求版本创建
            // 拼装滚动需求版本数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO rollingVersionCreateDTO = new VersionCreateDTO();
            rollingVersionCreateDTO.setCreator(userId);
            rollingVersionCreateDTO.setScenario(scenario);
            rollingVersionCreateDTO.setVersionType(VersionTypeEnum.CLEAN_FORECAST.getCode());
            rollingVersionCreateDTO.setPlanPeriod(monthPeriod);
            rollingVersionCreateDTO.setGenerateType(generateType);
            rollingVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            // 目标原始需求版本ID
            rollingVersionCreateDTO.setPreviousOriginVersionId(originDemandVersionId);
            rollingVersionCreateDTO.setTargetOriginDemandVersionId(originVersionId);
            rollingVersionCreateDTO.setCurrentVersionId(rollingVersionId);
            rollingVersionCreateDTO.setCurrentVersionCode(rollingVersionCode);
            // 生成滚动需求版本
            IDemandVersionCreate monthDemandVersionCreate =
                    demandVersionCreateFactory.getDemandVersionCreate(rollingVersionCreateDTO.getVersionType());
            monthDemandVersionCreate.doDemandVersionCreateParallel(rollingVersionCreateDTO);
        });

        CompletableFuture<Void> forecastFuture = CompletableFuture.runAsync(() -> {
            // 需求预测版本创建
            // 拼装业务预测数据
            DynamicDataSourceContextHolder.setDataSource(scenario);
            VersionCreateDTO demandForecastVersionCreateDTO = new VersionCreateDTO();
            demandForecastVersionCreateDTO.setCreator(userId);
            demandForecastVersionCreateDTO.setScenario(scenario);
            demandForecastVersionCreateDTO.setVersionType(VersionTypeEnum.DEMAND_FORECAST.getCode());
            demandForecastVersionCreateDTO.setPlanPeriod(monthPeriod);
            demandForecastVersionCreateDTO.setGenerateType(generateType);
            demandForecastVersionCreateDTO.setOemCodeResource(new ArrayList<>());

            demandForecastVersionCreateDTO.setTargetCleanForecastVersionId(rollingVersionId);
            demandForecastVersionCreateDTO.setTargetCleanAlgorithmVersionId(getLatestAlgorithmVersionId());
            demandForecastVersionCreateDTO.setCurrentVersionId(forecastVersionId);
            demandForecastVersionCreateDTO.setCurrentVersionCode(forecastVersionCode);

            String targetDemandForecastVersionId = demandForecastVersionService.selectLatestVersionId();
            demandForecastVersionCreateDTO.setTargetDemandForecastVersionId(targetDemandForecastVersionId);
            // 生成业务预测数据
            IDemandVersionCreate demandForecastVersionCreate =
                    demandVersionCreateFactory.getDemandVersionCreate(demandForecastVersionCreateDTO.getVersionType());
            demandForecastVersionCreate.doDemandVersionCreateParallel(demandForecastVersionCreateDTO);
        });

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(dailyFuture, deliveryFuture, rollingFuture, forecastFuture);
        allFutures.join();
        return BaseResponse.success();
    }

    private String getLatestAlgorithmVersionId() {
        Map<String, Object> params = new HashMap<>();
        params.put("versionStatus", VersionStatusEnum.PUBLISHED.getCode());
        params.put("versionType", VersionTypeEnum.CLEAN_ALGORITHM.getCode());
        List<DemandVersionVO> demandVersionVOS = demandVersionService.selectMaxVersionByParams(params);
        if (CollectionUtils.isNotEmpty(demandVersionVOS)) {
            return demandVersionVOS.get(0).getId();
        }
        return "";
    }

    @Override
    public BaseResponse<Void> syncSubmission(Scenario scenario) {
        Map<String, Object> queryParams = MapUtil.newHashMap();
        queryParams.put("organizeType", StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode());
        queryParams.put("enabled", YesOrNoEnum.YES.getCode());

        List<NewStockPointVO> newStockPointVOList = mdsFeign.selectStockPointByParams(scenario.getDataBaseName(), queryParams);
        List<String> grpStockList = newStockPointVOList.stream().filter(t -> Objects.equals(t.getEdiMode(), ApiSourceEnum.GRP.getCode()))
                .map(NewStockPointVO::getOrganizeId).distinct().collect(Collectors.toList());
        List<String> mesStockList = newStockPointVOList.stream().filter(t -> Objects.equals(t.getEdiMode(), ApiSourceEnum.MES.getCode()))
                .map(NewStockPointVO::getOrganizeId).distinct().collect(Collectors.toList());
        log.info("开始同步GRP接口，库存点数据，库存点参数：{}",grpStockList);
        if (CollectionUtils.isNotEmpty(grpStockList)) {
            for (String grpStock : grpStockList) {
                List<CompletableFuture<Void>> grpFutures = new ArrayList<>();

                Map demandParam = MapUtil.builder("organizeId", grpStock).build();
                grpFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(),
                        ApiSourceEnum.GRP.getCode(),ApiCategoryEnum.LOADING_DEMAND.getCode(), demandParam)));
                grpFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(),
                        ApiSourceEnum.GRP.getCode(),ApiCategoryEnum.LOADING_FORECAST.getCode(), demandParam)));
                CompletableFuture.allOf(grpFutures.toArray(new CompletableFuture[0])).join();
            }

        }else{
            log.info("grp接口库存点数据为空");
        }
        log.info("同步GRP接口完成");
        log.info("开始同步MES接口，库存点数据，库存点参数：{}",mesStockList);
        if (CollectionUtils.isNotEmpty(mesStockList)) {

            for (String mesStock : mesStockList) {
                List<CompletableFuture<Void>> mesFutures = new ArrayList<>();

                Map demandParam = MapUtil.builder("organizeId", mesStock).build();
                mesFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(),
                        ApiSourceEnum.MES.getCode(),ApiCategoryEnum.MES_SHIP_DEMAND.getCode(), demandParam)));
                mesFutures.add(CompletableFuture.runAsync(() -> newDcpFeign.callExternalApi(scenario.getTenantId(),
                        ApiSourceEnum.MES.getCode(),ApiCategoryEnum.MES_FORECAST_DEMAND.getCode(), demandParam)));
                CompletableFuture.allOf(mesFutures.toArray(new CompletableFuture[0])).join();
            }

        }else{
            log.info("mes接口库存点数据为空");
        }
        log.info("同步MES接口完成");

        return BaseResponse.success("同步成功");
    }

    /**
     * 获取新版本代码
     *
     * @param versionType 需求类型
     * @param versionCode 需求代码
     * @return java.lang.String
     */
    public static String getVersionCode(String versionType, String versionCode) {
        String newVersionCode;
        switch (versionType) {
            case "CLEAN_DEMAND":
                newVersionCode = "STD-" + versionCode;
                break;
            case "CLEAN_FORECAST":
                newVersionCode = "LTD-" + versionCode;
                break;
            case "DEMAND_FORECAST":
                newVersionCode = "DFC-" + versionCode;
                break;
            case "DELIVERY_PLAN":
                newVersionCode = "DLP-" + versionCode;
                break;
            default:
                newVersionCode = "";
        }
        return newVersionCode;
    }
    @Override
    public BaseResponse<Void> apiSubmission(List<OemVO> ediOemList, String originVersionId, Scenario scenario) {
        //此处调用接口同步EDI数据
        syncSubmission(scenario);
        OriginDemandVersionVO demandVersionVO = originDemandVersionService.selectByPrimaryKey(originVersionId);
        if (Objects.isNull(demandVersionVO)) {
            return BaseResponse.success("无原始需求版本");
        }
        String planPeriod = demandVersionVO.getPlanPeriod();
        Date currentDate = DateUtils.stringToDate(planPeriod, "yyyyMMdd");
        int period = 30;
        Date startDate = org.apache.commons.lang3.time.DateUtils.addDays(currentDate, 0);
        Date endDate = org.apache.commons.lang3.time.DateUtils.addDays(currentDate, period);

        int monthPeriod = 12;
        // 设置月份起始日期为下月1号0点
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentDate);
//        cal.add(Calendar.MONTH, 1);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        Date monthStartDate = cal.getTime();

        // 设置月份结束日期为12个月后的1号0点
        cal.add(Calendar.MONTH, monthPeriod);
        Date monthEndDate = cal.getTime();

        Map<String, Object> params = MapUtil.newHashMap();
        params.put("enabled", YesOrNoEnum.YES.getCode());
        params.put("versionId", originVersionId);
        List<LoadingDemandSubmissionVO> submissionVOS = this.selectByParams(params);
        List<LoadingDemandSubmissionVO> submissionTree = invocation(submissionVOS, null, null);
        StringBuilder totalErrMsg = new StringBuilder();
        for (OemVO oem : ediOemList) {
            String oemCode = oem.getOemCode();
            List<LoadingDemandSubmissionDTO> insertSubmissionList = Lists.newArrayList();
            List<LoadingDemandSubmissionDetailDTO> insertDetailList = Lists.newArrayList();
            List<String> deleteDetailIdList = Lists.newArrayList();

            //todo 这里要把返回的装车需求报文转换到oemData里面
            // 这里通过主机厂编码去查询对应的最新装车需求报文，会有多个零件号，主机厂编码和零件号为一个组合
            HashMap<String, Object> queryParams = MapUtil.newHashMap();
            queryParams.put("oemCode", oemCode);
            List<FdpOriginDemandInterfaceLogVO> originDemandVOS = originDemandService.selectVOByParams(queryParams);
            List<FdpOriginDemandForecastInterfaceLogVO> originDemandForecastVOS = originDemandForecastService
                    .selectVOByParams(queryParams);
            List<LoadingDemandSubmissionTemp> allList = new ArrayList<>();
            List<LoadingDemandSubmissionTemp> dayList = new ArrayList<>();
            List<LoadingDemandSubmissionTemp> monthList = new ArrayList<>();
            Map<String, Object> partRelationParam = MapUtil.newHashMap();
            partRelationParam.put("enabled", YesOrNoEnum.YES.getCode());
            List<PartRelationMapVO> relationMapVOS = partRelationMapService.selectByParams(partRelationParam);

            for (FdpOriginDemandInterfaceLogVO fdpOriginDemandInterfaceLogVO : originDemandVOS) {
                LoadingDemandSubmissionTemp loadingDemandSubmissionTemp = new LoadingDemandSubmissionTemp();
                loadingDemandSubmissionTemp.setOemCode(oemCode);
                loadingDemandSubmissionTemp.setPartNumber(fdpOriginDemandInterfaceLogVO.getCustItemNum());
                loadingDemandSubmissionTemp.setProductCode(fdpOriginDemandInterfaceLogVO.getItemNum());
                if(Objects.equals(YesOrNoEnum.NO.getCode(), fdpOriginDemandInterfaceLogVO.getProductEdiFlag())){
                    log.info("该物料没有EDI标识，需求同步跳过：{}", fdpOriginDemandInterfaceLogVO.getItemNum());
                    continue;
                }else if(Objects.isNull(fdpOriginDemandInterfaceLogVO.getProductEdiFlag())){
                    if(!Objects.equals(oem.getEdiFlag(), YesOrNoEnum.YES.getCode())){
                        log.info("该OEM厂没有EDI标识，需求同步跳过：{}", fdpOriginDemandInterfaceLogVO.getItemNum());
                        continue;
                    }
                }
                loadingDemandSubmissionTemp.setSubmissionType(fdpOriginDemandInterfaceLogVO.getSubmissionType());
                loadingDemandSubmissionTemp.setDemandQuantity(fdpOriginDemandInterfaceLogVO.getShipQty());
                loadingDemandSubmissionTemp.setDemandTime(DateUtils.dateToString
                        (fdpOriginDemandInterfaceLogVO.getOriginalShipTime(), DateUtils.COMMON_DATE_STR1));
                loadingDemandSubmissionTemp.setReleaseStatus(fdpOriginDemandInterfaceLogVO.getReleaseStatus());
                dayList.add(loadingDemandSubmissionTemp);
            }
            for (FdpOriginDemandForecastInterfaceLogVO fdpOriginDemandForecastInterfaceLogVO : originDemandForecastVOS) {
                if(Objects.equals(YesOrNoEnum.NO.getCode(), fdpOriginDemandForecastInterfaceLogVO.getProductEdiFlag())){
                    log.info("该物料没有EDI标识，预测同步跳过：{}", fdpOriginDemandForecastInterfaceLogVO.getItemNum());
                    continue;
                }else if(Objects.isNull(fdpOriginDemandForecastInterfaceLogVO.getProductEdiFlag())){
                    if(!Objects.equals(oem.getEdiFlag(), YesOrNoEnum.YES.getCode())){
                        log.info("该OEM厂没有EDI标识，预测同步跳过：{}", fdpOriginDemandForecastInterfaceLogVO.getItemNum());
                        continue;
                    }
                }

                LoadingDemandSubmissionTemp loadingDemandSubmissionTemp = new LoadingDemandSubmissionTemp();

                loadingDemandSubmissionTemp.setOemCode(oemCode);
                loadingDemandSubmissionTemp.setSubmissionType(fdpOriginDemandForecastInterfaceLogVO.getSubmissionType());
                loadingDemandSubmissionTemp.setProductCode(fdpOriginDemandForecastInterfaceLogVO.getItemNum());
                loadingDemandSubmissionTemp.setDemandTime(DateUtils.dateToString(fdpOriginDemandForecastInterfaceLogVO.
                        getOriginalShipTime(), DateUtils.YEAR_MONTH));
                loadingDemandSubmissionTemp.setDemandQuantity(fdpOriginDemandForecastInterfaceLogVO.getQty());
                if (ApiSourceEnum.MES.getCode().equals(fdpOriginDemandForecastInterfaceLogVO.getImportType())) {
                    relationMapVOS.stream().filter(t -> t.getProductCode().equals(fdpOriginDemandForecastInterfaceLogVO.
                            getItemNum())).findFirst().ifPresent(t ->
                            loadingDemandSubmissionTemp.setPartNumber(t.getPartNumber()));
                    if (Objects.isNull(loadingDemandSubmissionTemp.getPartNumber())) {
                        log.error("没有找到对应的客户零件号，产品编码：{}", fdpOriginDemandForecastInterfaceLogVO.getItemNum());
                        totalErrMsg.append("主机厂编码："+oemCode+"产品编码："+fdpOriginDemandForecastInterfaceLogVO.getItemNum()+":没有找到对应的客户零件号");
                        totalErrMsg.append("</br>");
                        continue;
                    }
                } else {
                    loadingDemandSubmissionTemp.setPartNumber(fdpOriginDemandForecastInterfaceLogVO.getCustomerItemNum());
                }
                monthList.add(loadingDemandSubmissionTemp);
            }
            Map<String, List<LoadingDemandSubmissionTemp>> monthGroup = monthList.stream().collect(
                    Collectors.groupingBy(item -> item.getPartNumber() + "_" +
                            item.getProductCode() + "_" + item.getDemandTime(), Collectors.toList()));
            monthList.clear();
            // 处理合并后的月度数据
            List<LoadingDemandSubmissionTemp> mergedMonthList = new ArrayList<>();
            monthGroup.forEach((key, groupList) -> {
                // 取第一条记录作为基础数据
                LoadingDemandSubmissionTemp mergedTemp = new LoadingDemandSubmissionTemp();
                LoadingDemandSubmissionTemp firstItem = groupList.get(0);

                // 复制基础信息
                mergedTemp.setOemCode(firstItem.getOemCode());
                mergedTemp.setPartNumber(firstItem.getPartNumber());
                mergedTemp.setProductCode(firstItem.getProductCode());
                mergedTemp.setSubmissionType(firstItem.getSubmissionType());
                mergedTemp.setDemandTime(firstItem.getDemandTime());

                // 累加数量
                BigDecimal totalQuantity = groupList.stream()
                        .map(LoadingDemandSubmissionTemp::getDemandQuantity)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                mergedTemp.setDemandQuantity(totalQuantity);
                mergedMonthList.add(mergedTemp);
            });
            allList.addAll(dayList);
            allList.addAll(mergedMonthList);
            List<LoadingDemandSubmissionVO> submissionTreeList = submissionTree.stream().filter(t -> t.getOemCode()
                    .equals(oemCode)).collect(Collectors.toList());
            List<LoadingDemandSubmissionTemp> newList = new ArrayList<>();

            if (CollectionUtils.isNotEmpty(submissionTreeList)) {

                Set<String> existingCombinations = submissionTreeList.stream()
                        .map(submission -> submission.getPartNumber() + "|" + submission.getProductCode())
                        .collect(Collectors.toSet());

                // 过滤出在allList中存在但在submissionTreeList中不存在的记录
                List<LoadingDemandSubmissionTemp> nonExistList = allList.stream()
                        .filter(temp -> !existingCombinations.contains(temp.getPartNumber() + "|" + temp.getProductCode()))
                        .collect(Collectors.toList());

                if (CollectionUtils.isNotEmpty(nonExistList)) {
                    newList.addAll(nonExistList);
                }
                submissionTreeList.forEach(submission -> {
                    // 更新已制单数
                    String currentPartNumber = submission.getPartNumber(); //找到客户零件号
                    String currentItemNum = submission.getProductCode(); //找到物料编码
                    // 从EDI返回的需求数据找到匹配的零件号本厂编码数据，进行插入 如果没匹配到，需要新增提报
                    if (CollectionUtils.isNotEmpty(submission.getDayData())) {

                        List<LoadingDemandSubmissionTemp> existSubmissionList = dayList.stream()
                                .filter(day -> Objects.equals(currentItemNum, day.getProductCode()) && Objects
                                        .equals(currentPartNumber, day.getPartNumber())).collect(Collectors.toList());

                        existSubmissionList.forEach(day -> {

                            Date demandDate = DateUtils.stringToDate(day.getDemandTime(), DateUtils.COMMON_DATE_STR3);

                            // 如果日期在计划周期内，则插入
                            if (Objects.nonNull(demandDate) && (demandDate.compareTo(startDate) >= 0
                                    && demandDate.compareTo(endDate) <= 0)) {
                                LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                                detailDTO.setDemandTime(day.getDemandTime());
                                //                                detailDTO.setDemandQuantity(day.getDemandQuantity());
                                detailDTO.setDemandQuantity(Objects.equals(day.getReleaseStatus(),
                                        StatusEnum.CLOSED.getCode()) ? BigDecimal.ZERO : day.getDemandQuantity());
                                detailDTO.setSubmissionType(GranularityEnum.DAY.getCode());
                                detailDTO.setSubmissionId(submission.getId());
                                detailDTO.setId(UUIDUtil.getUUID());
                                detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                                insertDetailList.add(detailDTO);
                            }
                        });
                        deleteDetailIdList.addAll(submission.getDayData().stream().map(BaseVO::getId).
                                collect(Collectors.toList()));

                    }
                    if (CollectionUtils.isNotEmpty(submission.getMonthData())) {
                        // 从monthList中找到匹配的零件号本厂编码数据，进行插入 如果没有匹配到，需要新增提报头
                        mergedMonthList.stream().filter(month -> Objects.equals(currentItemNum, month.getProductCode())
                                && Objects.equals(currentPartNumber, month.getPartNumber())).forEach(month -> {
                            Date demandMonth = DateUtils.stringToDate(month.getDemandTime(), DateUtils.YEAR_MONTH);

                            // 如果月份在计划周期内，则插入
                            if (Objects.nonNull(demandMonth) && (demandMonth.compareTo(monthStartDate) >= 0
                                    && demandMonth.compareTo(monthEndDate) <= 0)) {
                                LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                                detailDTO.setDemandTime(month.getDemandTime());
                                detailDTO.setDemandQuantity(month.getDemandQuantity());
                                detailDTO.setSubmissionType(GranularityEnum.MONTH.getCode());
                                detailDTO.setSubmissionId(submission.getId());
                                detailDTO.setId(UUIDUtil.getUUID());
                                detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                                insertDetailList.add(detailDTO);
                            }
                        });
                        deleteDetailIdList.addAll(submission.getMonthData().stream().map(BaseVO::getId).collect(Collectors.toList()));
                    }
                });
            }
            if (CollectionUtils.isEmpty(submissionTreeList) || CollectionUtils.isNotEmpty(newList)) {
                if (CollectionUtils.isNotEmpty(newList)) {
                    allList = newList;
                }
                // 如果没有现有数据，需要创建新的提报记录
                Map<String, List<LoadingDemandSubmissionTemp>> groupedData = allList.stream()
                        .collect(Collectors.groupingBy(temp -> temp.getProductCode() + "|" + temp.getPartNumber()));
                // 为每个分组创建新的提报记录
                groupedData.forEach((key, tempList) -> {
                    LoadingDemandSubmissionTemp first = tempList.get(0);
                    // 创建主记录
                    LoadingDemandSubmissionDTO submission = new LoadingDemandSubmissionDTO();
                    submission.setId(UUIDUtil.getUUID());
                    submission.setOemCode(first.getOemCode());
                    submission.setPartNumber(first.getPartNumber());
                    submission.setProductCode(first.getProductCode());
                    submission.setVersionId(originVersionId);
                    submission.setEnabled(YesOrNoEnum.YES.getCode());
                    submission.setDemandCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode());
                    insertSubmissionList.add(submission);

                    // 填充数据
                    tempList.forEach(temp -> {
                        try {
                            LoadingDemandSubmissionDetailDTO detail = new LoadingDemandSubmissionDetailDTO();
                            detail.setSubmissionId(submission.getId());
                            detail.setDemandTime(temp.getDemandTime());
                            detail.setDemandQuantity(temp.getDemandQuantity());
                            detail.setSubmissionType(temp.getSubmissionType());
                            detail.setId(UUIDUtil.getUUID());
                            detail.setEnabled(YesOrNoEnum.YES.getCode());
                            if (Objects.equals(GranularityEnum.DAY.getCode(), temp.getSubmissionType())) {
                                Date demandDate = DateUtils.stringToDate(detail.getDemandTime(), DateUtils.COMMON_DATE_STR1);
                                if (demandDate != null &&
                                        (demandDate.compareTo(startDate) >= 0 && demandDate.compareTo(endDate) <= 0)) {
                                    detail.setDemandQuantity(Objects.equals(StatusEnum.CLOSED.getCode(),
                                            temp.getReleaseStatus()) ? BigDecimal.ZERO : temp.getDemandQuantity());

                                    insertDetailList.add(detail);
                                }
                            } else {
                                Date demandMonth = DateUtils.stringToDate(detail.getDemandTime(), DateUtils.YEAR_MONTH);
                                if (Objects.nonNull(demandMonth)&& (demandMonth.compareTo(monthStartDate) >= 0
                                        && demandMonth.compareTo(monthEndDate) <= 0)) {
                                    insertDetailList.add(detail);
                                }
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("处理提报数据时出错, demandTime={}, error={}", temp.getDemandTime(), e.getMessage());
                            totalErrMsg.append("主机厂编码："+oemCode+"处理提报数据时出错, error="+e.getMessage());
                            totalErrMsg.append("</br>");
                        }
                    });
                });
            }
            if (CollectionUtils.isNotEmpty(insertSubmissionList)) {
                doCreateBatch(insertSubmissionList);
            }
            if (CollectionUtils.isNotEmpty(deleteDetailIdList)) {
                loadingDemandSubmissionDetailService.doDelete(deleteDetailIdList);
            }
            insertSubmissionList.forEach(t -> {
                List<LoadingDemandSubmissionDetailDTO> detailList = insertDetailList.stream()
                        .filter(x -> Objects.equals(x.getSubmissionId(), t.getId())).collect(Collectors.toList());
                // 补充缺失的日期 yyyy-MM-dd
                for (int i = 0; i < period; i++) {
                    Date dayDate = org.apache.commons.lang3.time.DateUtils.addDays(startDate, i);
                    String dayStr = DateUtils.dateToString(dayDate, DateUtils.COMMON_DATE_STR3);
                    if (detailList.stream().filter(day -> GranularityEnum.DAY.getCode().equals(day.getSubmissionType())).
                            noneMatch(day -> DateUtils.dateToString(DateUtils.stringToDate(day.getDemandTime()),
                                    DateUtils.COMMON_DATE_STR3).equals(dayStr))) {
                        LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                        detailDTO.setSubmissionId(t.getId());
                        detailDTO.setDemandTime(dayStr);
                        detailDTO.setDemandQuantity(BigDecimal.ZERO);
                        detailDTO.setSubmissionType(GranularityEnum.DAY.getCode());
                        detailDTO.setId(UUIDUtil.getUUID());
                        detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                        insertDetailList.add(detailDTO);
                    }
                }
                // 补充缺失的月份
                for (int i = 0; i < monthPeriod; i++) {
                    Date currentMonth = org.apache.commons.lang3.time.DateUtils.addMonths(monthStartDate, i);
                    String monthStr = DateUtils.dateToString(currentMonth, DateUtils.YEAR_MONTH);

                    if (detailList.stream().filter(month -> GranularityEnum.MONTH.getCode().equals(month.getSubmissionType())).
                            noneMatch(month -> month.getDemandTime().equals(monthStr))) {

                        LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                        detailDTO.setSubmissionId(t.getId());
                        detailDTO.setDemandTime(monthStr);
                        detailDTO.setDemandQuantity(BigDecimal.ZERO);
                        detailDTO.setSubmissionType(GranularityEnum.MONTH.getCode());
                        detailDTO.setId(UUIDUtil.getUUID());
                        detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                        insertDetailList.add(detailDTO);
                    }
                }
            });
            submissionTree.stream().filter(t -> Objects.equals(t.getOemCode(), oemCode)).forEach(t -> {
                List<LoadingDemandSubmissionDetailDTO> detailList = insertDetailList.stream()
                        .filter(x -> Objects.equals(x.getSubmissionId(), t.getId())).collect(Collectors.toList());
                // 补充缺失的日期 yyyy-MM-dd
                for (int i = 0; i < period; i++) {
                    Date dayDate = org.apache.commons.lang3.time.DateUtils.addDays(startDate, i);
                    String dayStr = DateUtils.dateToString(dayDate, DateUtils.COMMON_DATE_STR3);
                    if (detailList.stream().filter(day -> GranularityEnum.DAY.getCode().equals(day.getSubmissionType())).
                            noneMatch(day -> DateUtils.dateToString(DateUtils.stringToDate(day.getDemandTime()),
                                    DateUtils.COMMON_DATE_STR3).equals(dayStr))) {
                        LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                        detailDTO.setSubmissionId(t.getId());
                        detailDTO.setDemandTime(dayStr);
                        detailDTO.setDemandQuantity(BigDecimal.ZERO);
                        detailDTO.setSubmissionType(GranularityEnum.DAY.getCode());
                        detailDTO.setId(UUIDUtil.getUUID());
                        detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                        insertDetailList.add(detailDTO);
                    }
                }
                // 补充缺失的月份
                for (int i = 0; i < monthPeriod; i++) {
                    Date currentMonth = org.apache.commons.lang3.time.DateUtils.addMonths(monthStartDate, i);
                    String monthStr = DateUtils.dateToString(currentMonth, DateUtils.YEAR_MONTH);

                    if (detailList.stream().filter(month -> GranularityEnum.MONTH.getCode().equals(month.getSubmissionType())).
                            noneMatch(month -> Objects.equals(month.getDemandTime(), monthStr))) {

                        LoadingDemandSubmissionDetailDTO detailDTO = new LoadingDemandSubmissionDetailDTO();
                        detailDTO.setSubmissionId(t.getId());
                        detailDTO.setDemandTime(monthStr);
                        detailDTO.setDemandQuantity(BigDecimal.ZERO);
                        detailDTO.setSubmissionType(GranularityEnum.MONTH.getCode());
                        detailDTO.setId(UUIDUtil.getUUID());
                        detailDTO.setEnabled(YesOrNoEnum.YES.getCode());
                        insertDetailList.add(detailDTO);
                    }
                }
            });
            if (CollectionUtils.isNotEmpty(insertDetailList)) {
                loadingDemandSubmissionDetailService.doCreateBatch(insertDetailList);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isBlank(totalErrMsg.toString())) {
            return BaseResponse.success("装车需求提报成功");
        } else{
            return BaseResponse.error(String.format("装车需求提报成功，部分失败：%s", totalErrMsg));
        }
    }

    //获取最新版本需求提报月度预测数量
    @Override
    public Map<String,BigDecimal> getProductDemandQuantity(String demandTime){
        // 将 demandTime 从年月格式（例如 202412）转换为年-月格式（例如 2024-12）
        if (demandTime != null && demandTime.length() == 6) {
            demandTime = demandTime.substring(0, 4) + "-" + demandTime.substring(4);
        }
        // 获取数据
        String latestVersionId = originDemandVersionService.selectLatestVersionId();
        Map<String, Object> param = new HashMap<>(2);
        param.put("versionId", latestVersionId);
        List<LoadingDemandSubmissionVO> loadingDemandSubmissionVOS = selectByParams(param);

        Map<String,LoadingDemandSubmissionVO> submissionMap =
                loadingDemandSubmissionVOS.stream().collect(Collectors.toMap(LoadingDemandSubmissionVO::getId,
                        Function.identity()));
        List<String> submissionIds = new ArrayList<>(submissionMap.keySet());

        //查询月度详情数据
        Map<String, Object> detailQueryMap = MapUtil.newHashMap();
        detailQueryMap.put("submissionIds", submissionIds);
        detailQueryMap.put("submissionType", GranularityEnum.MONTH.getCode());
        detailQueryMap.put("demandTime", demandTime);
        List<LoadingDemandSubmissionDetailVO> loadingDemandSubmissionDetailVOS = loadingDemandSubmissionDetailService.selectByParams(detailQueryMap);
        Map<String, List<LoadingDemandSubmissionDetailVO>> detailMap = loadingDemandSubmissionDetailVOS.stream()
                .collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getSubmissionId));
        //汇总数量
        Map<String,BigDecimal> productDemandQuantityMap = new HashMap<>();
        for (String submissionId : submissionIds) {
            LoadingDemandSubmissionVO loadingDemandSubmissionVO = submissionMap.get(submissionId);
            if (null == loadingDemandSubmissionVO){
                continue;
            }
            List<LoadingDemandSubmissionDetailVO> detailList = detailMap.get(submissionId);
            if (CollectionUtils.isEmpty(detailList)) {
                continue;
            }
            BigDecimal demandQuantity = BigDecimal.ZERO;
            for (LoadingDemandSubmissionDetailVO detail : detailList) {
                demandQuantity = demandQuantity.add(detail.getDemandQuantity()==null?BigDecimal.ZERO:detail.getDemandQuantity());
            }

            productDemandQuantityMap.put(loadingDemandSubmissionVO.getProductCode(),demandQuantity);
        }
        return productDemandQuantityMap;
    }

    @Override
    public BaseResponse<Object> getDemandTypeEnum() {
        String userId = SystemHolder.getUserId();
        if (StringUtils.isEmpty(userId)){
            return  BaseResponse.error("获取不到该用户数据");
        }
        //获取该用户的部门
        List<Dept> dept = ipsNewFeign.getDeptByCurrentUserId(userId);
        if (dept.isEmpty()){
            return BaseResponse.error("当前用户没有匹配到组织，请进行维护。");
        }else{
            Optional<Dept> first = dept.stream().filter(x -> StringUtils.isEmpty(x.getDeptPath())).findFirst();
            if (first.isPresent()){
                return BaseResponse.error("该用户对应的组织数据没有路径字段为空，请维护组织表。");
            }
        }
        //判断是否属于物流还是项目
        List<Dept> list1 = dept.stream().filter(x -> x.getDeptPath().contains("物流")).collect(Collectors.toList());
        List<Dept> list2 = dept.stream().filter(x -> x.getDeptPath().contains("项目")).collect(Collectors.toList());
        List<LabelValue<String>> objectLabelValue = new ArrayList<>();
        //物流部则返回量产需求
        if (!list1.isEmpty()){
            objectLabelValue.add(new LabelValue<>(ProductionDemandTypeEnum.OUTPUT_DEMAND.getDesc(),ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode()));
        }
        //项目部则返回项目需求
        if (!list2.isEmpty()){
            objectLabelValue.add(new LabelValue<>(ProductionDemandTypeEnum.PROJECT_DEMAND.getDesc(),ProductionDemandTypeEnum.PROJECT_DEMAND.getCode()));
        }
        if (objectLabelValue.isEmpty()){
            return BaseResponse.error("该用户对应的组织没有需求类型的权限");
        } else{
            return BaseResponse.success(objectLabelValue);
        }
    }

}
