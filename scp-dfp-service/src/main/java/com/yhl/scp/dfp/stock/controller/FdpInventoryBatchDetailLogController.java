package com.yhl.scp.dfp.stock.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.stock.dto.FdpInventoryBatchDetailLogDTO;
import com.yhl.scp.dfp.stock.service.FdpInventoryBatchDetailLogService;
import com.yhl.scp.dfp.stock.vo.FdpInventoryBatchDetailLogVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <code>FdpInventoryBatchDetailLogController</code>
 * <p>
 * 控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:07
 */
@Slf4j
@Api(tags = "控制器")
@RestController
@RequestMapping("fdpInventoryBatchDetailLog")
public class FdpInventoryBatchDetailLogController extends BaseController {

    @Resource
    private FdpInventoryBatchDetailLogService fdpInventoryBatchDetailLogService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<FdpInventoryBatchDetailLogVO>> page() {
        List<FdpInventoryBatchDetailLogVO> fdpInventoryBatchDetailLogList = fdpInventoryBatchDetailLogService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<FdpInventoryBatchDetailLogVO> pageInfo = new PageInfo<>(fdpInventoryBatchDetailLogList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody FdpInventoryBatchDetailLogDTO fdpInventoryBatchDetailLogDTO) {
        return fdpInventoryBatchDetailLogService.doCreate(fdpInventoryBatchDetailLogDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody FdpInventoryBatchDetailLogDTO fdpInventoryBatchDetailLogDTO) {
        return fdpInventoryBatchDetailLogService.doUpdate(fdpInventoryBatchDetailLogDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        fdpInventoryBatchDetailLogService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }
    @ApiOperation("下载原始数据")
    @GetMapping(value = "downloadOriginalData")
    public void downloadOriginalData(@RequestParam(value = "stockPointCode") String stockPointCode, HttpServletResponse response) {
        fdpInventoryBatchDetailLogService.downloadOriginalData(stockPointCode,response);
    }
}
