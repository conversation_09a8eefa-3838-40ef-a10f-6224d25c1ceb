package com.yhl.scp.dfp.consistence.controller;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.biz.common.util.PageUtils;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.consistence.dto.ConsistenceDemandForecastDataDTO;
import com.yhl.scp.dfp.consistence.dto.ExecutionMonitorDTO;
import com.yhl.scp.dfp.consistence.dto.ForecastSummerReportDataDTO;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.consistence.vo.ExecutionMonitorResultVO;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ForecastSummerReportDataVO;
import com.yhl.scp.dfp.consistence.vo.MonitorResultUpdate;
import com.yhl.scp.dfp.demand.vo.DemandVersionAndOemVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>ConsistenceDemandForecastDataController</code>
 * <p>
 * 一致性业务预测数据控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Api(tags = "一致性业务预测数据控制器")
@RestController
@RequestMapping("consistenceDemandForecastData")
public class ConsistenceDemandForecastDataController extends BaseController {

    @Resource
    private ConsistenceDemandForecastDataService consistenceDemandForecastDataService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<ConsistenceDemandForecastDataVO>> page() {
        List<ConsistenceDemandForecastDataVO> consistenceDemandForecastDataList =
                consistenceDemandForecastDataService.selectByPage(getPagination(), getSortParam(), getQueryCriteriaParam());
        PageInfo<ConsistenceDemandForecastDataVO> pageInfo = new PageInfo<>(consistenceDemandForecastDataList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody ConsistenceDemandForecastDataDTO consistenceDemandForecastDataDTO) {
        return consistenceDemandForecastDataService.doCreate(consistenceDemandForecastDataDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody ConsistenceDemandForecastDataDTO consistenceDemandForecastDataDTO) {
        return consistenceDemandForecastDataService.doUpdate(consistenceDemandForecastDataDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        consistenceDemandForecastDataService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<ConsistenceDemandForecastDataVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, consistenceDemandForecastDataService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "根据版本删除")
    @PostMapping(value = "deleteByVersion")
    public BaseResponse<Void> deleteByVersion(@RequestBody List<RemoveVersionDTO> versionDTOList) {
        consistenceDemandForecastDataService.doDeleteByVersion(versionDTOList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "执行监控版本下拉")
    @GetMapping("getVersionAndOem")
    public BaseResponse<List<DemandVersionAndOemVO>> getVersionAndOem() {
        return BaseResponse.success(consistenceDemandForecastDataService.getVersionAndOem());
    }

    @ApiOperation(value = "执行监控查询")
    @PostMapping(value = "executionMonitor")
    public BaseResponse<List<ExecutionMonitorResultVO>> executionMonitor(@RequestBody ExecutionMonitorDTO monitorDTO) {
        return BaseResponse.success(consistenceDemandForecastDataService.executionMonitor(monitorDTO));
    }

    @ApiOperation(value = "执行监控一键更新")
    @PostMapping(value = "updateConsistence")
    public BaseResponse<Void> executionMonitor(@RequestBody MonitorResultUpdate monitorResultUpdate) {
        consistenceDemandForecastDataService.monitorUpdateDemandForecast(monitorResultUpdate.getList(),
                monitorResultUpdate.getMonth());
        return BaseResponse.success();
    }

    @ApiOperation(value = "执行监控查询并发送邮件")
    @PostMapping(value = "executionSend")
    public BaseResponse<String> executionSend() {
        return consistenceDemandForecastDataService.executionSend();
    }

    @ApiOperation(value = "预测汇总报表分页查询")
    @PostMapping(value = "queryForecastSummaryReport")
    public BaseResponse<PageInfo<ForecastSummerReportDataVO>> queryForecastSummaryReport(
            @RequestBody ForecastSummerReportDataDTO dto) {
        List<ForecastSummerReportDataVO> list = consistenceDemandForecastDataService.queryForecastSummaryReport(dto);
        PageInfo<ForecastSummerReportDataVO> pageInfo = PageUtils.getPageInfo(list, dto.getPageNum(), dto.getPageSize());
        if (CollectionUtils.isEmpty(list)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
        }
        List<ForecastSummerReportDataVO> pageList = pageInfo.getList();
        if (pageList.get(0) != null) {
            pageList.get(0).setHeaderList(pageList.get(0).getDetails().stream()
                    .map(ForecastSummerReportDataDetailVO::getYearMonth).collect(Collectors.toList()));
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "预测汇总导出")
    @PostMapping(value = "exportData")
    public void forecastSummaryReportExport(@RequestBody ForecastSummerReportDataDTO dto) {
        consistenceDemandForecastDataService.forecastSummaryReportExport(this.response, dto);
    }

    @ApiOperation(value = "获取零件层级产品下拉框")
    @PostMapping(value = "selectPartLevelProductDropDown")
    public BaseResponse<List<LabelValue<String>>> selectPartLevelProductDropDown(
            @RequestBody ConsistenceDemandForecastDataDTO consistenceDemandForecastDataDTO) {
        return consistenceDemandForecastDataService.selectPartLevelProductDropDown(consistenceDemandForecastDataDTO);
    }

}