package com.yhl.scp.dfp.report.service.impl;

import com.github.pagehelper.PageInfo;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.StringConvertUtils;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDateUtils;
import com.yhl.scp.dfp.delivery.service.DeliveryPlanPublishedService;
import com.yhl.scp.dfp.delivery.service.impl.DeliveryPlanServiceImpl;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import com.yhl.scp.dfp.feign.DfpFeign;
import com.yhl.scp.dfp.loading.service.LoadingDemandSubmissionDetailService;
import com.yhl.scp.dfp.loading.vo.LoadingDemandSubmissionDetailVO;
import com.yhl.scp.dfp.oem.enums.OemBusinessTypeEnum;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.service.OemStockPointMapService;
import com.yhl.scp.dfp.oem.service.OemTransportTimeService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.oem.vo.OemStockPointMapVO;
import com.yhl.scp.dfp.oem.vo.OemTransportTimeVO;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.origin.service.OriginDemandVersionService;
import com.yhl.scp.dfp.report.dto.DemandDeliveryProductionDetailDTO;
import com.yhl.scp.dfp.report.service.DemandDeliveryProductionService;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionDetailVO;
import com.yhl.scp.dfp.report.vo.DemandDeliveryProductionVO;
import com.yhl.scp.dfp.safety.vo.SafetyStockLevelVO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryBatchDetailDao;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao;
import com.yhl.scp.dfp.stock.infrastructure.po.InventoryShiftPO;
import com.yhl.scp.dfp.stock.vo.InventoryBatchDetailVO;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.vo.ScenarioBusinessRangeVO;
import com.yhl.scp.mds.basic.routing.vo.RoutingBasicVO;
import com.yhl.scp.mds.basic.routing.vo.StandardStepBasicVO;
import com.yhl.scp.mds.extension.routing.vo.RoutingVO;
import com.yhl.scp.mds.extension.routing.vo.StandardStepVO;
import com.yhl.scp.mds.extension.time.vo.PlanningHorizonVO;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;
import com.yhl.scp.mds.stock.enums.StockPointOrganizeTypeEnum;
import com.yhl.scp.mds.stock.enums.StockPointTypeEnum;
import com.yhl.scp.mds.stock.vo.NewStockPointVO;
import com.yhl.scp.mps.feign.MpsFeign;
import com.yhl.scp.mps.plan.req.MasterPlanReq;
import com.yhl.scp.mps.plan.vo.MasterPlanTaskVO;
import com.yhl.scp.mps.subInventoryCargoLocation.vo.SubInventoryCargoLocationVO;
import io.seata.common.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DemandDeliveryProductionServiceImpl</code>
 * <p>
 * 需求发货生产报表服务实现类 - 性能优化版本
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-15 14:27:02
 */
@Service
@Slf4j
public class DemandDeliveryProductionServiceImpl implements DemandDeliveryProductionService {

    @Resource
    DeliveryPlanPublishedService deliveryPlanPublishedService;

    @Resource
    InventoryShiftDao inventoryShiftDao;
    @Resource
    OriginDemandVersionService originDemandVersionService;
    @Resource
    LoadingDemandSubmissionDetailService loadingDemandSubmissionDetailService;
    @Resource
    private NewMdsFeign newMdsFeign;
    @Resource
    private DfpFeign dfpFeign;
    @Resource
    private MpsFeign mpsFeign;
    @Resource
    private IpsNewFeign ipsNewFeign;
    @Resource
    private InventoryBatchDetailDao inventoryBatchDetailDao;
    @Resource
    private OemTransportTimeService oemTransportTimeService;
    @Resource
    private OemStockPointMapService oemStockPointMapService;
    @Resource
    private OemInventorySubmissionService oemInventorySubmissionService;
    @Resource
    private OemService oemService;
    @Resource
    private WarehouseReleaseRecordService warehouseReleaseRecordService;
    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;
    // 常量定义
    public static final String PARAM_STOCK_POINT_TYPE = "stockPointType";
    public static final String PAINTING_OPERATION = "印刷";
    public static final String FORMING_OPERATION = "成型";
    public static final String MERGING_OPERATION = "合片";
    public static final String PACKAGING_OPERATION = "包装";




    @Override
//    @Cacheable(value = "demandDeliveryProductionReport",
//               key = "#dto.productCode + '_' + #dto.vehicleModelCode + '_' + #dto.pageNum + '_' + #dto.pageSize",
//               unless = "#result.isEmpty()")
    public List<DemandDeliveryProductionVO> queryDemandDeliveryProductionReport(DemandDeliveryProductionDetailDTO dto) {
        String scenario = SystemHolder.getScenario();

        // 1. 提前计算时间范围
        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveDay(planStartTime, 14);
        List<Date> intervalDates = DateUtils.getIntervalDates(planStartTime, planEndTime);

        // 2.分页查询产品编码
        List<String> queryProductCodes = getQueryProductCodesWithPagination(scenario, dto);
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new ArrayList<>();
        }

        ExecutorService executorService = Executors.newFixedThreadPool(10);

        try {
            CompletableFuture<List<DeliveryPlanPublishedVO>> deliveryPlanFuture =
                CompletableFuture.supplyAsync(() -> getDeliveryPlanData(queryProductCodes, planStartTime, planEndTime), executorService);

            CompletableFuture<List<MasterPlanTaskVO>> masterPlanFuture =
                CompletableFuture.supplyAsync(() -> getMasterPlanData(scenario, queryProductCodes, planStartTime, planEndTime), executorService);

            CompletableFuture<Map<String, List<InventoryBatchDetailVO>>> inventoryFuture =
                CompletableFuture.supplyAsync(() -> getInventoryData(queryProductCodes), executorService);

            CompletableFuture<Map<String, List<LoadingDemandSubmissionDetailVO>>> loadingDemandFuture =
                CompletableFuture.supplyAsync(() -> getLoadingDemandData(planStartTime, planEndTime), executorService);

            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS = deliveryPlanFuture.get();
            List<MasterPlanTaskVO> operationTasks = masterPlanFuture.get();
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap = inventoryFuture.get();
            Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap = loadingDemandFuture.get();

            if(CollectionUtils.isEmpty(deliveryPlanPublishedVOS)){
                throw new BusinessException("找不到发货计划");
            }

            return processReportData(scenario, deliveryPlanPublishedVOS, operationTasks,
                inventoryBatchDetailMap, submissionDetailMap, intervalDates, planStartTime);

        } catch (InterruptedException | ExecutionException e) {
            log.error("异步查询数据失败", e);
            Thread.currentThread().interrupt();
            throw new BusinessException("查询数据失败: " + e.getMessage());
        } finally {
            if (executorService != null && !executorService.isShutdown()) {
                executorService.shutdown();
                log.debug("线程池已关闭");
            }
        }
    }



    /**
     * 分页获取产品编码 - 分页实现
     */
    private List<String> getQueryProductCodesWithPagination(String scenario, DemandDeliveryProductionDetailDTO dto) {
        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveDay(planStartTime, 14);

        Map<String, Object> queryParams = new HashMap<>();
        boolean hasQueryCondition = false;

        if (StringUtils.isNotEmpty(dto.getProductCode())) {
            queryParams.put("productCodeLike", StringConvertUtils.convertToLike(dto.getProductCode()));
            hasQueryCondition = true;
        }

        if (StringUtils.isNotEmpty(dto.getVehicleModelCode())) {
            queryParams.put("vehicleModelCodeLike", StringConvertUtils.convertToLike(dto.getVehicleModelCode()));
            hasQueryCondition = true;
        }

        if (hasQueryCondition) {
            // 如果有查询条件，则根据条件查询
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(
                scenario, queryParams);

            if (CollectionUtils.isEmpty(newProductStockPointVOS)) {
                throw new BusinessException("找不到符合条件的物料");
            }

            List<String> allMatchedProductCodes = newProductStockPointVOS.stream()
                .map(NewProductStockPointVO::getProductCode)
                .distinct()
                .collect(Collectors.toList());

            // 对匹配的产品编码进行分页
            return paginateProductCodes(allMatchedProductCodes, dto.getPageNum(), dto.getPageSize());
        } else {
            // 如果没有查询条件，则从发货计划中获取特定时间范围内的产品编码
            return getProductCodesFromDeliveryPlanWithPagination(planStartTime, planEndTime, dto.getPageNum(), dto.getPageSize());
        }
    }

    /**
     * 从发货计划中获取特定时间范围内的产品编码并分页
     */
    private List<String> getProductCodesFromDeliveryPlanWithPagination(Date planStartTime, Date planEndTime, int pageNum, int pageSize) {
        // 1. 先从发货计划中获取特定时间范围内的所有产品编码
        List<DeliveryPlanPublishedVO> allDeliveryPlans = deliveryPlanPublishedService.selectByParams(ImmutableMap.of(
            "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
            "endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
        ));

        if (CollectionUtils.isEmpty(allDeliveryPlans)) {
            return new ArrayList<>();
        }

        // 2. 提取所有唯一的产品编码
        List<String> allProductCodes = allDeliveryPlans.stream()
            .map(DeliveryPlanPublishedVO::getProductCode)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toList());

        // 3. 对产品编码进行分页
        return paginateProductCodes(allProductCodes, pageNum, pageSize);
    }

    /**
     * 对产品编码列表进行内存分页
     */
    private List<String> paginateProductCodes(List<String> productCodes, int pageNum, int pageSize) {
        if (CollectionUtils.isEmpty(productCodes)) {
            return new ArrayList<>();
        }

        int startIndex = (pageNum - 1) * pageSize;
        int endIndex = Math.min(startIndex + pageSize, productCodes.size());

        if (startIndex >= productCodes.size()) {
            return new ArrayList<>();
        }

        return productCodes.subList(startIndex, endIndex);
    }
    
    /**
     * 优化：异步获取发货计划数据 - 只查询当前页面需要的产品
     */
    private List<DeliveryPlanPublishedVO> getDeliveryPlanData(List<String> queryProductCodes, Date planStartTime, Date planEndTime) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new ArrayList<>();
        }

        return deliveryPlanPublishedService.selectByParams(ImmutableMap.of(
            "productCodes", queryProductCodes,
            "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
            "endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
        ));
    }
    
    /**
     * 优化：异步获取主生产计划数据 - 只查询当前页面需要的产品
     */
    private List<MasterPlanTaskVO> getMasterPlanData(String scenario, List<String> queryProductCodes, Date planStartTime, Date planEndTime) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new ArrayList<>();
        }

        PlanningHorizonVO planningHorizon = newMdsFeign.selectPlanningHorizon(scenario);
        MasterPlanReq masterPlanReq = new MasterPlanReq();

        Date historyRetrospectStartTime = planningHorizon.getHistoryRetrospectStartTime();
        masterPlanReq.setHistoryRetrospectStartTime(historyRetrospectStartTime);
        masterPlanReq.setProductCodes(queryProductCodes);

        Date startTime = DateUtils.stringToDate(DateUtils.dateToString(planStartTime)
                + " " + DateUtils.dateToString(planningHorizon.getPlanStartTime(), "HH:mm:ss"),
            DateUtils.COMMON_DATE_STR1);
        Date endTime = DateUtils.stringToDate(DateUtils.dateToString(planEndTime)
                + " " + DateUtils.dateToString(planningHorizon.getPlanEndTime(), "HH:mm:ss"),
            DateUtils.COMMON_DATE_STR1);
        masterPlanReq.setDeliverStartTime(startTime);
        masterPlanReq.setDeliverEndTime(endTime);

        return mpsFeign.selectByMasterReq(scenario, masterPlanReq);
    }
    
    /**
     * 优化：异步获取库存数据 - 只查询当前页面需要的产品
     */
    private Map<String, List<InventoryBatchDetailVO>> getInventoryData(List<String> queryProductCodes) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new HashMap<>();
        }
        List<InventoryBatchDetailVO> inventoryBatchDetails =inventoryBatchDetailDao.selectByProductCodes(queryProductCodes, StockPointTypeEnum.BC.getCode());

        return inventoryBatchDetails.stream().collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
    }
    /**
     * 获取并组装中转库存
     *
     * @param saleOrganizations 销售组织列表
     * @param oemProductMap 主机厂物料映射
     * @param oemProductMap 物料实时库存映射
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getTransitStockMap(List<String> saleOrganizations,Map<String, String> oemProductMap,Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        /*
         * ①主机厂编码查询mds_oem_stock_point_map中非SJ的库存点，再去fdp_inventory_batch_detail(库存批次明细)用库存点+产品编码获取数量的汇总；
         * ②主机厂+产品编码去fdp_oem_inventory_submission中匹配小于等于当天日期的submission_date，最接近的那天，取stock_inventory_quantity；
         * ③同②，取oem_inventory_quantity，为空视为0，三者相加。
         */
        // TODO
        Date currentDate = DateUtils.getCurrentDateTruncateTime();
        List<String> oemCodes = new ArrayList<>(oemProductMap.keySet());
        List<String> productCodes = new ArrayList<>(oemProductMap.values());
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS = oemInventorySubmissionService.selectByParams(ImmutableMap.of("oemCodes", oemCodes,
                "productCodes", productCodes));
        Map<String, List<OemInventorySubmissionVO>> oemInventorySubmissionMap = oemInventorySubmissionVOS.stream().collect(Collectors.groupingBy(t -> t.getOemCode() + "|" + t.getProductCode()));
        List<OemStockPointMapVO> oemStockPointMapVOS = oemStockPointMapService.selectByParams(ImmutableMap.of("oemCodeList", oemCodes,
                "stockPointCodesNotInList", saleOrganizations));
        List<String> stockPointCodes = oemStockPointMapVOS.stream().map(OemStockPointMapVO::getStockPointCode).distinct().collect(Collectors.toList());
        Map<String,BigDecimal> transitStockMap = new HashMap<>();
        for(String oemCode: oemCodes){
            String productCode = oemProductMap.get(oemCode);
            String oemInventorySubmissionKey=oemCode+"|"+productCode;
            List<InventoryBatchDetailVO> inventoryBatchDetailVOS = inventoryBatchDetailMap.get(productCode);
            BigDecimal inventoryQty=BigDecimal.ZERO;
            BigDecimal stockInventoryQty=BigDecimal.ZERO;
            BigDecimal oemInventoryQty=BigDecimal.ZERO;
            BigDecimal totalQty;
            if(CollectionUtils.isNotEmpty(inventoryBatchDetailVOS)){
                 inventoryQty = inventoryBatchDetailVOS.stream()
                        .filter(t -> stockPointCodes.contains(t.getStockPointCode()))
                        .map(InventoryBatchDetailVO::getCurrentQuantity)
                        .filter(Objects::nonNull)
                        .map(BigDecimal::new)
                        .reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO);
            }
            if(oemInventorySubmissionMap.containsKey(oemInventorySubmissionKey)){
                List<OemInventorySubmissionVO> oemInventorySubmissionVOList = oemInventorySubmissionMap.get(oemInventorySubmissionKey);
                OemInventorySubmissionVO oemInventorySubmissionVO =oemInventorySubmissionVOList.stream().filter(t->Objects.nonNull(t.getSubmissionDate()))
                        .filter(t->!DateUtils.formatDate(t.getSubmissionDate(), DateUtils.COMMON_DATE_STR3).after(currentDate))
                        .max(Comparator.comparing(OemInventorySubmissionVO::getSubmissionDate))
                        .orElse(null);
                if(Objects.nonNull(oemInventorySubmissionVO)){
                    stockInventoryQty = oemInventorySubmissionVO.getStockInventoryQuantity() != null ?
                        oemInventorySubmissionVO.getStockInventoryQuantity() : BigDecimal.ZERO;
                    oemInventoryQty = oemInventorySubmissionVO.getOemInventoryQuantity() != null ?
                        oemInventorySubmissionVO.getOemInventoryQuantity() : BigDecimal.ZERO;
                }
            }
            totalQty=inventoryQty.add(stockInventoryQty).add(oemInventoryQty);
            transitStockMap.put(productCode,totalQty);
        }
        return transitStockMap;
    }

    /**
     * 获取并组装在途库存
     *
     * @param oemProductMap 主机厂产品映射
     * @return java.util.Map<java.lang.String,java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getTransportingQtyMap(Map<String, String> oemProductMap) {
        Date currentDate = DateUtils.getCurrentDateTruncateTime();

        List<String> oemCodes = new ArrayList<>(oemProductMap.keySet());
        List<String> productCodes = new ArrayList<>(oemProductMap.values());
        /*
         * 取主机厂编码+产品编码，主机厂编码去mds_oem，匹配到目标货位，去fdp_oem_transport_time，匹配到优先级为1的transportation_time，
         * transportation_time 如果小于4，则当天到，如果大于4小于24，视为第二天到，后续每24小时，多一天到，换算出的结果为运输时间(天）,
         * 用目标货位+产品编码去fdp_warehouse_release_record，fdp_warehouse_release_to_warehouse查询数据，
         * 用当前日期减去运输时间(天),得到一个日期，查询"is_receive"不为"Y"且发货时间creation_date"在该日期到当前日期之间(包含该日期和当前日期)的数据，将得到的数量汇总，记为在途。
         */
        // TODO
        // 获取主机厂的运输时间
        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of("oemCodes", oemCodes));
        List<String> targetLocations = oemList.stream()
            .map(OemVO::getTargetStockLocation)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        Map<String, String> oemStockMap = oemList.stream()
            .filter(o -> o.getTargetStockLocation() != null)
            .collect(Collectors.toMap(
                OemVO::getOemCode,
                OemVO::getTargetStockLocation,
                (existing, replacement) -> existing
            ));

        List<OemTransportTimeVO> oemTransportTimeVOList = oemTransportTimeService.selectByParams(ImmutableMap.of(
                "oemCodes", oemCodes,"priority",1));
        // 获取最小运输时间
        BigDecimal maxTransportTime = oemTransportTimeVOList.stream()
                .map(OemTransportTimeVO::getTransportationTime)
                .max(BigDecimal::compareTo)
                .orElse(BigDecimal.ZERO);
        int maxTransportDays = DeliveryPlanServiceImpl.calculateTransportDays(maxTransportTime.doubleValue());
        Date minDate = DateUtils.moveDay(currentDate, -maxTransportDays);
        // 根据主机厂分组
        Map<String, List<OemTransportTimeVO>> oemTransportTimeMap= oemTransportTimeVOList.stream().collect(Collectors.groupingBy(OemTransportTimeVO::getOemCode));
        Map<String, Object> warehouseParams = new HashMap<>();
        warehouseParams.put("productCodes", productCodes);
        warehouseParams.put("shipmentLocatorCodes", targetLocations);
        warehouseParams.put("isReceiveNotEq", "Y");
        warehouseParams.put("beginDate", DateUtils.dateToString(minDate));
        warehouseParams.put("endDate", DateUtils.dateToString(currentDate));
        List<WarehouseReleaseRecordVO> warehouseReleaseRecordVOS = warehouseReleaseRecordService.selectByParams(warehouseParams);
        Map<String, List<WarehouseReleaseRecordVO>>  warehouseReleaseRecordMap = warehouseReleaseRecordVOS.stream()
                .collect(Collectors.groupingBy(t->t.getItemCode()+"|"+t.getShipmentLocatorCode()));
        Map<String, Object> warehouseToParams = new HashMap<>();
        warehouseToParams.put("productCodes", productCodes);
        warehouseToParams.put("shipmentLocatorCodes", targetLocations);
        warehouseToParams.put("isReceiveNotEq", "Y");
        warehouseToParams.put("beginDate", DateUtils.dateToString(minDate));
        warehouseToParams.put("endDate", DateUtils.dateToString(currentDate));
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseVOS = warehouseReleaseToWarehouseService.selectByParams(warehouseToParams);
        Map<String, List<WarehouseReleaseToWarehouseVO>> warehouseReleaseToWarehouseMap = warehouseReleaseToWarehouseVOS
                .stream().collect(Collectors.groupingBy(t->t.getItemCode()+"|"+t.getShipmentLocatorCode()));
        Map<String, BigDecimal> transportingQtyMap=new HashMap<>();
        for(String oemCode: oemCodes){
            String productCode = oemProductMap.get(oemCode);

            BigDecimal wareHouseReleaseQty=BigDecimal.ZERO;
            BigDecimal wareHouseReleaseToQty=BigDecimal.ZERO;
            BigDecimal oemInventoryQty=BigDecimal.ZERO;

            if(oemTransportTimeMap.containsKey(oemCode)){
                List<OemTransportTimeVO> oemTransportTimeVOS = oemTransportTimeMap.get(oemCode);
                OemTransportTimeVO oemTransportTimeVO = oemTransportTimeVOS.get(0);
                int transportDays = DeliveryPlanServiceImpl.calculateTransportDays(null == oemTransportTimeVO.getTransportationTime() ?
                        0 : oemTransportTimeVO.getTransportationTime().doubleValue());
                Date beginDate = DateUtils.moveDay(currentDate, -transportDays);
                if(oemStockMap.containsKey(oemCode)){
                    String targetStock = oemStockMap.get(oemCode);
                    String warehouseKey = productCode+"|"+targetStock;
                    if(warehouseReleaseRecordMap.containsKey(warehouseKey)){
                        List<WarehouseReleaseRecordVO> warehouseReleaseRecordList = warehouseReleaseRecordMap.get(warehouseKey);
                        wareHouseReleaseQty = warehouseReleaseRecordList.stream().filter(t -> !t.getCreationDate().before(beginDate))
                                .map(WarehouseReleaseRecordVO::getSumQty)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

                    }
                    if(warehouseReleaseToWarehouseMap.containsKey(warehouseKey)){
                        List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseList = warehouseReleaseToWarehouseMap.get(warehouseKey);
                        wareHouseReleaseToQty= warehouseReleaseToWarehouseList.stream().filter(t -> !t.getCreationDate().before(beginDate))
                                .map(WarehouseReleaseToWarehouseVO::getSumQty)
                                .filter(Objects::nonNull)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                    }

                }
            }
            transportingQtyMap.put(productCode, wareHouseReleaseQty.add(wareHouseReleaseToQty).add(oemInventoryQty));
        }
        return transportingQtyMap;
    }
    /**
     * 优化：异步获取库存转移数据 - 只查询当前页面需要的产品
     */
    private Map<String, List<InventoryShiftVO>> getInventoryShiftData(List<String> queryProductCodes, Date planStartTime, Date planEndTime) {
        if (CollectionUtils.isEmpty(queryProductCodes)) {
            return new HashMap<>();
        }

        List<InventoryShiftVO> inventoryShiftVOS = inventoryShiftDao.selectLatestDataByPlanDate(ImmutableMap.of(
            "productCodes", queryProductCodes,
            "plannedDateStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
            "plannedDateEndYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
        ));

        return CollectionUtils.isEmpty(inventoryShiftVOS) ? new HashMap<>() :
            inventoryShiftVOS.stream().collect(Collectors.groupingBy(InventoryShiftVO::getProductCode));
    }
    
    /**
     * 优化：异步获取装载需求数据
     */
    private Map<String, List<LoadingDemandSubmissionDetailVO>> getLoadingDemandData(Date planStartTime, Date planEndTime) {
        String versionId = originDemandVersionService.selectLatestVersionId();
        if (Objects.isNull(versionId)) {
            log.info("原始需求版本不存在");
            return new HashMap<>();
        }
        
        List<LoadingDemandSubmissionDetailVO> submissionDetailVOS = loadingDemandSubmissionDetailService
            .selectVOByParams(ImmutableMap.of(
                "versionId", versionId,
                "startTimeStr", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR1),
                "endTimeStr", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR1)
            ));
        
        return CollectionUtils.isEmpty(submissionDetailVOS) ? new HashMap<>() :
            submissionDetailVOS.stream().collect(Collectors.groupingBy(LoadingDemandSubmissionDetailVO::getProductCode));
    }

    /**
     * 优化：处理报表数据的主要逻辑
     */
    private List<DemandDeliveryProductionVO> processReportData(String scenario,
            List<DeliveryPlanPublishedVO> deliveryPlanPublishedVOS, List<MasterPlanTaskVO> operationTasks,
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap,
            Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap,
            List<Date> intervalDates, Date planStartTime) {

        List<DemandDeliveryProductionVO> dataList = new ArrayList<>();

        // 获取销售组织信息
        List<String> saleOrganizations = getSaleOrganizations(scenario);

        // 批量获取其他必要数据
        Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap = deliveryPlanPublishedVOS.stream()
                .collect(Collectors.groupingBy(DeliveryPlanPublishedVO::getProductCode));
        Set<String> productCodeSets = deliveryPlanMap.keySet();
        List<String> productCodes = new ArrayList<>(productCodeSets);
        Map<String, String> oemProductMap = deliveryPlanPublishedVOS.stream()
            .filter(vo -> vo.getOemCode() != null && vo.getProductCode() != null)
            .collect(Collectors.toMap(
                DeliveryPlanPublishedVO::getOemCode,
                DeliveryPlanPublishedVO::getProductCode,
                (existing, replacement) -> existing
            ));
        Map<String, BigDecimal> transitStockMap = getTransitStockMap(saleOrganizations, oemProductMap, inventoryBatchDetailMap);
        Map<String, BigDecimal> transportingQtyMap = getTransportingQtyMap(oemProductMap);
        // 批量获取产品映射信息
        Map<String, String> productMap = getProductVehicleModelMap(scenario, saleOrganizations, productCodes);
        Map<String, String> productStockMap = getProductStockMap(scenario, productCodes);
        Map<String, SubInventoryCargoLocationVO> cargoLocationMap = getCargoLocationMap(scenario, inventoryBatchDetailMap);
        Map<String, String> standardStepMap = getStandardStepMap(scenario);
        Map<String, List<InventoryBatchDetailVO>> finishInventoryMap = getFinishInventoryMap(scenario, inventoryBatchDetailMap);
        Map<String, List<InventoryBatchDetailVO>> operationInventoryMap = getOperationInventoryMap(scenario, inventoryBatchDetailMap);

        // 处理每个产品的数据
        for (String productCode : productCodeSets) {
            DemandDeliveryProductionVO demandDeliveryProductionVO = processProductData(
                productCode, deliveryPlanMap, submissionDetailMap, finishInventoryMap, operationTasks, productMap, productStockMap,
                cargoLocationMap, standardStepMap, operationInventoryMap, intervalDates, planStartTime,transitStockMap,transportingQtyMap);

            dataList.add(demandDeliveryProductionVO);
        }
        // 根据vehicleModelCode和productCode进行排序
        dataList.sort(Comparator.comparing(DemandDeliveryProductionVO::getVehicleModelCode, Comparator.nullsLast(String::compareTo))
                .thenComparing(DemandDeliveryProductionVO::getProductCode, Comparator.nullsLast(String::compareTo)));
        // 设置表头
        if (!dataList.isEmpty() && !dataList.get(0).getDetails().isEmpty()) {
            dataList.get(0).setHeader(dataList.get(0).getDetails().stream()
                .map(DemandDeliveryProductionDetailVO::getTitleDate)
                .collect(Collectors.toList()));
        }



        return dataList;
    }

    private Map<String, List<InventoryBatchDetailVO>> getFinishInventoryMap(String scenario, Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {

        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
            .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                    && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
            .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        BaseResponse<ScenarioBusinessRangeVO> scenarioBusinessRange = ipsNewFeign.getScenarioBusinessRange(
            scenario, "SUB_INVENTORY", "INTERNAL", null);
        String subInventory = scenarioBusinessRange.getData().getRangeData();

        if (StringUtils.isBlank(subInventory)) {
            log.error("未找到对应的成品子库存信息！");
            throw new BusinessException("未找到对应的成品子库存信息！");
        }
        // 成品
        return inventoryBatchDetailMap.values().stream().flatMap(List::stream)
                .filter(t -> StringUtils.isEmpty(t.getOperationCode())
                        && saleOrganizations.contains(t.getStockPointCode())
                        && subInventory.equals(t.getSubinventory()))
                .collect(Collectors.groupingBy(InventoryBatchDetailVO::getProductCode));
    }

    /**
     * 获取销售组织信息
     */
    private List<String> getSaleOrganizations(String scenario) {
        List<String> saleOrganizations = newMdsFeign.selectStockPointByParams(scenario, ImmutableMap
                .of("stockPointType", StockPointTypeEnum.BC.getCode())).stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.SALE_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        if(CollectionUtils.isEmpty(saleOrganizations)){
            throw new BusinessException("销售组织信息为空");
        }
        return saleOrganizations;
    }

    /**
     * 获取产品车型映射
     */
    private Map<String, String> getProductVehicleModelMap(String scenario, List<String> saleOrganizations, List<String> productCodes) {
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(scenario,
                ImmutableMap.of("stockPointCodes", saleOrganizations, "productCodes", productCodes));
        return newProductStockPointVOS.stream()
            .filter(vo -> vo.getProductCode() != null && vo.getVehicleModelCode() != null)
            .collect(Collectors.toMap(NewProductStockPointVO::getProductCode,
                NewProductStockPointVO::getVehicleModelCode, (a, b) -> a));
    }

    /**
     * 获取产品库存点映射
     */
    private Map<String, String> getProductStockMap(String scenario, List<String> productCodes) {
        List<RoutingVO> routingVOS = newMdsFeign.selectRoutingByParams(scenario,
            ImmutableMap.of("routingCodes", productCodes));
        return CollectionUtils.isEmpty(routingVOS) ? new HashMap<>() :
            routingVOS.stream()
                .filter(vo -> vo.getRoutingCode() != null && vo.getStockPointId() != null)
                .collect(Collectors.toMap(RoutingBasicVO::getRoutingCode,
                    RoutingBasicVO::getStockPointId, (v1, v2) -> v1));
    }

    /**
     * 获取货位映射
     */
    private Map<String, SubInventoryCargoLocationVO> getCargoLocationMap(String scenario,
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        List<String> spaceList = inventoryBatchDetailMap.values().stream()
            .flatMap(List::stream)
            .map(InventoryBatchDetailVO::getFreightSpace)
            .distinct()
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(spaceList)) {
            return new HashMap<>();
        }

        List<SubInventoryCargoLocationVO> subInventoryCargoLocations =
            mpsFeign.queryByFreightSpaces(scenario, spaceList, StockPointTypeEnum.BC.getCode());

        return CollectionUtils.isEmpty(subInventoryCargoLocations) ? new HashMap<>() :
            subInventoryCargoLocations.stream()
                .filter(vo -> vo.getFreightSpaceCode() != null)
                .collect(Collectors.toMap(
                    SubInventoryCargoLocationVO::getFreightSpaceCode, Function.identity(), (v1, v2) -> v1));
    }

    /**
     * 获取标准步骤映射
     */
    private Map<String, String> getStandardStepMap(String scenario) {
        List<StandardStepVO> standardStepVOS = newMdsFeign.selectStandardStepAll(scenario);
        return standardStepVOS.stream()
            .filter(vo -> vo.getStockPointCode() != null && vo.getStandardStepName() != null && vo.getStandardStepCode() != null)
            .collect(Collectors.toMap(
                p -> p.getStockPointCode() + p.getStandardStepName(),
                StandardStepBasicVO::getStandardStepCode, (v1, v2) -> v1));
    }

    /**
     * 获取工序库存映射
     */
    private Map<String, List<InventoryBatchDetailVO>> getOperationInventoryMap(String scenario,
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        // 查询库存点数据，用于过滤非本厂库存
        List<NewStockPointVO> newStockPoints = newMdsFeign.selectStockPointByParams(scenario,
                ImmutableMap.of(PARAM_STOCK_POINT_TYPE, StockPointTypeEnum.BC.getCode()));

        // 获取本厂生产组织类型的仓库(半成品库存点)
        List<String> productOrganizations = newStockPoints.stream()
                .filter(e -> StringUtils.isNotEmpty(e.getOrganizeType())
                        && StockPointOrganizeTypeEnum.PRODUCT_ORGANIZATION.getCode().equals(e.getOrganizeType()))
                .map(NewStockPointVO::getStockPointCode).collect(Collectors.toList());

        // 工序在制量
        return inventoryBatchDetailMap.values().stream()
                .flatMap(List::stream)
                .filter(t -> productOrganizations.contains(t.getStockPointCode()))
                .collect(Collectors.groupingBy(p -> String.join("-", p.getStockPointCode(),
                        p.getProductCode(), p.getOperationCode())));
    }

    /**
     * 优化：处理单个产品的数据
     */
    private DemandDeliveryProductionVO processProductData(String productCode,
            Map<String, List<DeliveryPlanPublishedVO>> deliveryPlanMap,
            Map<String, List<LoadingDemandSubmissionDetailVO>> submissionDetailMap,
            Map<String, List<InventoryBatchDetailVO>> fgInventoryBatchDetailMap,
            List<MasterPlanTaskVO> operationTasks,
            Map<String, String> productMap,
            Map<String, String> productStockMap,
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap,
            Map<String, String> standardStepMap,
            Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
            List<Date> intervalDates, Date planStartTime,
            Map<String, BigDecimal> transitStockMap,
            Map<String, BigDecimal> transportingQtyMap) {

        DemandDeliveryProductionVO demandDeliveryProductionVO = new DemandDeliveryProductionVO();
        List<DemandDeliveryProductionDetailVO> details = new ArrayList<>();

        // 处理发货计划动态数据
        List<Map<String, Object>> dynamicData = processDynamicData(deliveryPlanMap.get(productCode), intervalDates);

        // 处理装车需求动态数据
        List<Map<String, Object>> loadingDynamicData = processLoadingDynamicData(submissionDetailMap.get(productCode), intervalDates);

        // 处理库存数据
        processInventoryData(demandDeliveryProductionVO,productCode, transitStockMap,transportingQtyMap);

        // 处理工序库存数据
        processOperationInventoryData(demandDeliveryProductionVO, productCode, productStockMap,
            standardStepMap, operationInventoryMap, cargoLocationMap);

        // 处理成品库存数据
        processFinishedGoodsInventory(demandDeliveryProductionVO, productCode, fgInventoryBatchDetailMap);

        // 处理生产计划数据
        processProductionPlanData(demandDeliveryProductionVO, productCode, operationTasks);

        // 设置基本信息
        demandDeliveryProductionVO.setDynamicData(dynamicData);
        demandDeliveryProductionVO.setLoadingDynamicData(loadingDynamicData);
        demandDeliveryProductionVO.setProductCode(productCode);
        demandDeliveryProductionVO.setVehicleModelCode(productMap.get(productCode));

        // 处理详情数据
        processDetailData(details, dynamicData, loadingDynamicData, intervalDates, demandDeliveryProductionVO);
        demandDeliveryProductionVO.setDetails(details);

        return demandDeliveryProductionVO;
    }

    /**
     * 处理发货计划动态数据
     */
    private List<Map<String, Object>> processDynamicData(List<DeliveryPlanPublishedVO> deliveryPlanList, List<Date> intervalDates) {
        List<Map<String, Object>> dynamicData = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(deliveryPlanList)) {
            Map<String, Integer> deliveryMap = deliveryPlanList.stream()
                    .collect(Collectors.toMap(
                            o -> DateUtils.dateToString(o.getDemandTime(), DateUtils.COMMON_DATE_STR3),
                            o -> o.getDemandQuantity(),
                            Integer::sum
                    ));

            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, deliveryMap.getOrDefault(deliveryDate, 0));
                dynamicData.add(dataMap);
            }
        } else {
            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, 0);
                dynamicData.add(dataMap);
            }
        }

        return dynamicData;
    }

    /**
     * 处理装车需求动态数据
     */
    private List<Map<String, Object>> processLoadingDynamicData(List<LoadingDemandSubmissionDetailVO> loadingDemandList, List<Date> intervalDates) {
        List<Map<String, Object>> loadingDynamicData = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(loadingDemandList)) {
            Map<String, BigDecimal> submissionMap = loadingDemandList.stream()
                    .filter(o -> o != null && o.getDemandTime() != null)
                    .collect(Collectors.toMap(
                            o -> {
                                Date date = DateUtils.stringToDate(o.getDemandTime());
                                if (date == null) {
                                    throw new IllegalArgumentException("非法 demandTime：" + o.getDemandTime());
                                }
                                return DateUtils.dateToString(date, DateUtils.COMMON_DATE_STR3);
                            },
                            o -> o.getDemandQuantity() != null ? o.getDemandQuantity() : BigDecimal.ZERO,
                            BigDecimal::add
                    ));

            for (Date intervalDate : intervalDates) {
                Map<String, Object> dataMap = new HashMap<>();
                String deliveryDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
                dataMap.put(deliveryDate, submissionMap.getOrDefault(deliveryDate, BigDecimal.ZERO));
                loadingDynamicData.add(dataMap);
            }
        }

        return loadingDynamicData;
    }

    /**
     * 处理库存数据
     */
    private void processInventoryData(DemandDeliveryProductionVO demandDeliveryProductionVO,String productCode,Map<String, BigDecimal> transitStockMap,
                                      Map<String, BigDecimal> transportingQtyMap) {
        if(transitStockMap.containsKey(productCode)){
            demandDeliveryProductionVO.setBohStock(transitStockMap.get(productCode));
        }else{
            demandDeliveryProductionVO.setBohStock(BigDecimal.ZERO);
        }
        if(transportingQtyMap.containsKey(productCode)){
            demandDeliveryProductionVO.setTransportingQty(transportingQtyMap.get(productCode));
        }else{
            demandDeliveryProductionVO.setTransportingQty(BigDecimal.ZERO);
        }
    }

    /**
     * 处理工序库存数据
     */
    private void processOperationInventoryData(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
            Map<String, String> productStockMap, Map<String, String> standardStepMap,
            Map<String, List<InventoryBatchDetailVO>> operationInventoryMap,
            Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (productStockMap.containsKey(productCode)) {
            String stockPointCode = productStockMap.get(productCode);
            String cx = getInventory(FORMING_OPERATION, stockPointCode, productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
            String hp = getInventory(MERGING_OPERATION, stockPointCode, productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
            String bz = getInventory(PACKAGING_OPERATION, stockPointCode, productCode, standardStepMap, operationInventoryMap, cargoLocationMap);
            demandDeliveryProductionVO.setAfterShape(new BigDecimal(cx));
            demandDeliveryProductionVO.setAfterPacking(new BigDecimal(hp));
            demandDeliveryProductionVO.setAfterLamination(new BigDecimal(bz));
        } else {
            demandDeliveryProductionVO.setAfterShape(BigDecimal.ZERO);
            demandDeliveryProductionVO.setAfterPacking(BigDecimal.ZERO);
            demandDeliveryProductionVO.setAfterLamination(BigDecimal.ZERO);
        }
    }

    /**
     * 处理成品库存数据
     */
    private void processFinishedGoodsInventory(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
            Map<String, List<InventoryBatchDetailVO>> inventoryBatchDetailMap) {
        List<InventoryBatchDetailVO> inventoryBatchDetailVOList = inventoryBatchDetailMap.get(productCode);
        if (CollectionUtils.isNotEmpty(inventoryBatchDetailVOList)) {
            BigDecimal inventoryQuantity = inventoryBatchDetailVOList.stream()
                    .map(t -> Optional.ofNullable(t.getCurrentQuantity())
                            .map(BigDecimal::new)
                            .orElse(BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            demandDeliveryProductionVO.setFgStock(Objects.isNull(inventoryQuantity)?BigDecimal.ZERO:inventoryQuantity);
        }
    }

    /**
     * 处理生产计划数据
     */
    private void processProductionPlanData(DemandDeliveryProductionVO demandDeliveryProductionVO, String productCode,
            List<MasterPlanTaskVO> operationTasks) {
        Map<String, List<MasterPlanTaskVO>> productTaskGroup = operationTasks.stream()
                .filter(x -> StringUtils.isNotBlank(x.getProductStockPointCode()))
                .collect(Collectors.groupingBy(MasterPlanTaskVO::getProductStockPointCode));

        List<MasterPlanTaskVO> masterPlanTasks = productTaskGroup.getOrDefault(productCode, new ArrayList<>())
                .stream().filter(x -> StringUtils.isNotBlank(x.getStandardStepName())
                        && PACKAGING_OPERATION.equals(x.getStandardStepName()))
                .sorted(Comparator.comparing(MasterPlanTaskVO::getStartTime))
                .collect(Collectors.toList());

        int totalPlanQuantity = masterPlanTasks.stream()
                .filter(x -> Objects.nonNull(x.getOperationVO()) && Objects.nonNull(x.getOperationVO().getQuantity()))
                .map(x -> x.getOperationVO().getQuantity())
                .reduce(BigDecimal.ZERO, BigDecimal::add).intValue();

        demandDeliveryProductionVO.setScheduleQty(BigDecimal.valueOf(totalPlanQuantity));
    }

    /**
     * 处理详情数据 - 累积扣减库存，装车和需求分别处理
     */
    private void processDetailData(List<DemandDeliveryProductionDetailVO> details,
            List<Map<String, Object>> dynamicData, List<Map<String, Object>> loadingDynamicData,
            List<Date> intervalDates, DemandDeliveryProductionVO demandDeliveryProductionVO) {

        BigDecimal fgStock = Objects.isNull(demandDeliveryProductionVO.getFgStock()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getFgStock();
        BigDecimal bohStock = Objects.isNull(demandDeliveryProductionVO.getBohStock()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getBohStock();
        BigDecimal transportingQty = Objects.isNull(demandDeliveryProductionVO.getTransportingQty()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getTransportingQty();
        BigDecimal afterPacking = Objects.isNull(demandDeliveryProductionVO.getAfterPacking()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterPacking();
        BigDecimal afterLamination = Objects.isNull(demandDeliveryProductionVO.getAfterLamination()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterLamination();
        BigDecimal afterShape = Objects.isNull(demandDeliveryProductionVO.getAfterShape()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getAfterShape();
        BigDecimal scheduleQty = Objects.isNull(demandDeliveryProductionVO.getScheduleQty()) ? BigDecimal.ZERO : demandDeliveryProductionVO.getScheduleQty();

        BigDecimal producingQty = afterPacking.add(afterLamination).add(afterShape).add(scheduleQty);
        BigDecimal transferAndTransportQty = bohStock.add(transportingQty);

        // 累积扣减变量 :分别为装车和需求的库存
        // 装车数量的累积扣减库存
        BigDecimal loadingRemainingTransferAndTransportQty = transferAndTransportQty;
        BigDecimal loadingRemainingFgStock = fgStock;
        BigDecimal loadingRemainingProducingQty = producingQty;

        // 需求数量的累积扣减库存
        BigDecimal demandRemainingTransferAndTransportQty = transferAndTransportQty;
        BigDecimal demandRemainingFgStock = fgStock;
        BigDecimal demandRemainingProducingQty = producingQty;
        final BigDecimal[] beforeLoadingQty = {BigDecimal.ZERO};
        final BigDecimal[] beforeDemandQty = {BigDecimal.ZERO};
        // 一旦出现红色，后续都为红色
        final boolean[] loadingIsRed = {false};
        final boolean[] demandIsRed = {false};

        for (Date intervalDate : intervalDates) {
            String titleDate = DateUtils.dateToString(intervalDate, DateUtils.COMMON_DATE_STR3);
            DemandDeliveryProductionDetailVO detail = new DemandDeliveryProductionDetailVO();

            // 处理装车数量和颜色
            loadingDynamicData.stream().filter(o -> o.containsKey(titleDate)).findFirst().ifPresent(t -> {
                BigDecimal loadingQty = new BigDecimal(String.valueOf(t.get(titleDate)));
                detail.setLoadingQty(loadingQty);

                // 装车数量颜色判断（基于累积扣减后的库存）
                if (loadingIsRed[0]) {
                    // 如果之前已经是红色，后续都为红色
                    detail.setLoadingColor("4"); // 红色：无法满足
                } else if (loadingQty.compareTo(loadingRemainingTransferAndTransportQty.subtract(beforeLoadingQty[0])) <= 0) {
                    detail.setLoadingColor("1"); // 绿色：中转在途库存能满足
                } else if (loadingQty.compareTo(loadingRemainingTransferAndTransportQty.add(loadingRemainingFgStock).subtract(beforeLoadingQty[0])) <= 0) {
                    detail.setLoadingColor("2"); // 浅绿色：中转在途+成品库存能满足
                } else if (loadingQty.compareTo(loadingRemainingTransferAndTransportQty.add(loadingRemainingFgStock).add(loadingRemainingProducingQty).subtract(beforeLoadingQty[0])) <= 0) {
                    detail.setLoadingColor("3"); // 蓝色：中转在途+成品+在制品能满足
                } else {
                    detail.setLoadingColor("4"); // 红色：无法满足
                    loadingIsRed[0] = true; // 标记为红色，后续都为红色
                }
                // 装车数量的累积扣减
                if (loadingQty.compareTo(BigDecimal.ZERO) > 0) {
                    beforeLoadingQty[0] = beforeLoadingQty[0].add(loadingQty);

//                    deductFromInventory(loadingQty,
//                        new BigDecimal[]{loadingRemainingTransferAndTransportQty, loadingRemainingFgStock, loadingRemainingProducingQty});
                }
            });

            // 处理需求数量和颜色
            dynamicData.stream().filter(o -> o.containsKey(titleDate)).findFirst().ifPresent(t -> {
                BigDecimal demandQty = new BigDecimal(String.valueOf(t.get(titleDate)));
                detail.setDemandQty(demandQty);

                // 需求数量颜色判断（基于累积扣减后的库存）
                if (demandIsRed[0]) {
                    // 如果之前已经是红色，后续都为红色
                    detail.setDemandColor("4"); // 红色：无法满足
                } else if (demandQty.compareTo(demandRemainingTransferAndTransportQty.subtract(beforeDemandQty[0])) <= 0) {
                    detail.setDemandColor("1"); // 绿色：中转在途库存能满足
                } else if (demandQty.compareTo(demandRemainingTransferAndTransportQty.add(demandRemainingFgStock).subtract(beforeDemandQty[0])) <= 0) {
                    detail.setDemandColor("2"); // 浅绿色：中转在途+成品库存能满足
                } else if (demandQty.compareTo(demandRemainingTransferAndTransportQty.add(demandRemainingFgStock).add(demandRemainingProducingQty).subtract(beforeDemandQty[0])) <= 0) {
                    detail.setDemandColor("3"); // 蓝色：中转在途+成品+在制品能满足
                } else {
                    detail.setDemandColor("4"); // 红色：无法满足
                    demandIsRed[0] = true; // 标记为红色，后续都为红色
                }

                // 需求数量的累积扣减
                if (demandQty.compareTo(BigDecimal.ZERO) > 0) {
                    beforeDemandQty[0] = beforeDemandQty[0].add(demandQty);
//                    deductFromInventory(demandQty,
//                        new BigDecimal[]{demandRemainingTransferAndTransportQty, demandRemainingFgStock, demandRemainingProducingQty});
                }
            });

            detail.setTitleDate(titleDate);
            details.add(detail);
        }
    }

    /**
     * 从库存中扣减数量
     * @param qtyToDeduct 要扣减的数量
     * @param inventories 库存数组，按优先级顺序排列
     */
    private void deductFromInventory(BigDecimal qtyToDeduct, BigDecimal[] inventories) {
        BigDecimal remainingQty = qtyToDeduct;

        for (int i = 0; i < inventories.length && remainingQty.compareTo(BigDecimal.ZERO) > 0; i++) {
            if (inventories[i].compareTo(remainingQty) >= 0) {
                inventories[i] = inventories[i].subtract(remainingQty);
                remainingQty = BigDecimal.ZERO;
            } else {
                remainingQty = remainingQty.subtract(inventories[i]);
                inventories[i] = BigDecimal.ZERO;
            }
        }
    }

    /**
     * 获取库存数量
     */
    private String getInventory(String op, String stockPointCode, String productCode, Map<String, String> stepMap,
                                Map<String, List<InventoryBatchDetailVO>> inventoryMap,
                                Map<String, SubInventoryCargoLocationVO> cargoLocationMap) {
        if (Objects.isNull(productCode)) {
            return "0";
        }

        String stockPoint = "S1";
        String stockPoint2 = "S2";
        if (stockPoint.equals(stockPointCode) && op.equals(PAINTING_OPERATION)) {
            op = "镀膜";
        }
        if (stockPoint2.equals(stockPointCode) && op.equals(FORMING_OPERATION)) {
            op = "钢化";
        }

        // 工序代码
        String op1 = stepMap.get(stockPointCode + op);
        String opMainKey = String.join("-", stockPointCode, productCode, op1);
        String opMainKey1 = String.join("-", stockPointCode, productCode, "");
        String finalOp = op;

        List<InventoryBatchDetailVO> inventoryBatchDetails = inventoryMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(opMainKey)
                        || (PACKAGING_OPERATION.equals(finalOp) && entry.getKey().equals(opMainKey1)))
                .map(Map.Entry::getValue).flatMap(List::stream).filter(p -> {
                    String freightSpace = p.getFreightSpace();
                    SubInventoryCargoLocationVO subInventoryCargoLocationVO = cargoLocationMap.get(freightSpace);
                    if (null == subInventoryCargoLocationVO) {
                        return false;
                    }
                    return subInventoryCargoLocationVO.getEnabled().equals(YesOrNoEnum.YES.getCode());
                }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(inventoryBatchDetails)) {
            return "0";
        }

        OptionalDouble sum = inventoryBatchDetails.stream()
                .mapToDouble(t -> Double.parseDouble(t.getCurrentQuantity())).reduce(Double::sum);
        double totalQuantity = sum.orElse(0);
        return String.valueOf(totalQuantity);
    }

    @Override
//    @Cacheable(value = "demandDeliveryProductionReportPage",
//               key = "#dto.productCode + '_' + #dto.vehicleModelCode + '_' + #dto.pageNum + '_' + #dto.pageSize",
//               unless = "#result.list.isEmpty()")
    public PageInfo<DemandDeliveryProductionVO> queryDemandDeliveryProductionReportWithPagination(DemandDeliveryProductionDetailDTO dto) {
        String scenario = SystemHolder.getScenario();

        // 1. 先获取总数（用于分页信息）
        long totalCount = getTotalProductCount(scenario, dto);

        // 2. 如果总数为0，直接返回空的分页结果
        if (totalCount == 0) {
            return new PageInfo<>(new ArrayList<>());
        }

        // 3. 查询当前页的数据
        List<DemandDeliveryProductionVO> dataList = queryDemandDeliveryProductionReport(dto);

        // 4. 构建分页信息
        PageInfo<DemandDeliveryProductionVO> pageInfo = new PageInfo<>(dataList);
        pageInfo.setPageNum(dto.getPageNum());
        pageInfo.setPageSize(dto.getPageSize());
        pageInfo.setTotal(totalCount);
        pageInfo.setPages((int) Math.ceil((double) totalCount / dto.getPageSize()));
        pageInfo.setHasPreviousPage(dto.getPageNum() > 1);
        pageInfo.setHasNextPage(dto.getPageNum() < pageInfo.getPages());

        return pageInfo;
    }

    /**
     * 获取产品总数（用于分页）
     */
    private long getTotalProductCount(String scenario, DemandDeliveryProductionDetailDTO dto) {
        Date planStartTime = DateUtils.getCurrentDateTruncateTime();
        Date planEndTime = DateUtils.moveDay(planStartTime, 14);

        // 构建查询参数
        Map<String, Object> queryParams = new HashMap<>();
        boolean hasQueryCondition = false;

        if (StringUtils.isNotEmpty(dto.getProductCode())) {
            queryParams.put("productCodeLike", StringConvertUtils.convertToLike(dto.getProductCode()));
            hasQueryCondition = true;
        }

        if (StringUtils.isNotEmpty(dto.getVehicleModelCode())) {
            queryParams.put("vehicleModelCodeLike", StringConvertUtils.convertToLike(dto.getVehicleModelCode()));
            hasQueryCondition = true;
        }

        if (hasQueryCondition) {
            // 如果有查询条件，查询匹配的产品总数
            List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(
                scenario, queryParams);

            return newProductStockPointVOS.stream()
                .map(NewProductStockPointVO::getProductCode)
                .distinct()
                .count();
        } else {
            // 如果没有查询条件，从发货计划中获取特定时间范围内的产品总数
            return getTotalProductCountFromDeliveryPlan(planStartTime, planEndTime);
        }
    }

    /**
     * 从发货计划中获取特定时间范围内的产品总数
     */
    private long getTotalProductCountFromDeliveryPlan(Date planStartTime, Date planEndTime) {
        List<DeliveryPlanPublishedVO> allDeliveryPlans = deliveryPlanPublishedService.selectByParams(ImmutableMap.of(
            "startTimeStrYMD", DateUtils.dateToString(planStartTime, DateUtils.COMMON_DATE_STR3),
            "endTimeStrYMD", DateUtils.dateToString(planEndTime, DateUtils.COMMON_DATE_STR3)
        ));

        if (CollectionUtils.isEmpty(allDeliveryPlans)) {
            return 0;
        }

        return allDeliveryPlans.stream()
            .map(DeliveryPlanPublishedVO::getProductCode)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .count();
    }
}
