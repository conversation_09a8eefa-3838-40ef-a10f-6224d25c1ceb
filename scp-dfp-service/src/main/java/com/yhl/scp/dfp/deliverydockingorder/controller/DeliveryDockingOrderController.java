package com.yhl.scp.dfp.deliverydockingorder.controller;

import java.io.IOException;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.yhl.scp.biz.common.annotation.BusinessMonitorLog;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.common.ExcelListener;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderDTO;
import com.yhl.scp.dfp.deliverydockingorder.dto.DeliveryDockingOrderExcelDTO;
import com.yhl.scp.dfp.deliverydockingorder.service.DeliveryDockingOrderService;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderDetailVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryDockingOrderVO;
import com.yhl.scp.dfp.deliverydockingorder.vo.DeliveryReturnVO;
import com.yhl.scp.dfp.utils.LabelValueFour;
import com.yhl.scp.dfp.utils.LabelValueThree;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>DeliveryDockingOrderController</code>
 * <p>
 * 发货对接单管理控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-10-30 10:34:51
 */
@Slf4j
@Api(tags = "发货对接单管理控制器")
@RestController
@RequestMapping("deliveryDockingOrder")
public class DeliveryDockingOrderController extends BaseController {

    @Resource
    private DeliveryDockingOrderService deliveryDockingOrderService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<DeliveryDockingOrderVO>> page() {
        List<DeliveryDockingOrderVO> deliveryDockingOrderList =
                deliveryDockingOrderService.selectByPage(getPagination(),
                        getSortParam(), getQueryCriteriaParam());
        PageInfo<DeliveryDockingOrderVO> pageInfo = new PageInfo<>(deliveryDockingOrderList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody DeliveryDockingOrderDTO deliveryDockingOrderDTO) {
        return deliveryDockingOrderService.doCreate(deliveryDockingOrderDTO);
    }

    @ApiOperation(value = "批量新增")
    @PostMapping(value = "doCreateBatch")
    public BaseResponse<Void> doCreateBatch(@RequestBody List<DeliveryDockingOrderDTO> deliveryDockingOrderDTO) {
        deliveryDockingOrderService.doCreateBatch(deliveryDockingOrderDTO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody DeliveryDockingOrderDTO deliveryDockingOrderDTO) {
        return deliveryDockingOrderService.updateAndSync(deliveryDockingOrderDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        deliveryDockingOrderService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<DeliveryDockingOrderVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "自动生成发货对接单号")
    @GetMapping(value = "generateDeliveryDockingNumber")
    public BaseResponse<String> generateDeliveryDockingNumber() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.generateDeliveryDockingNumber());
    }

    @ApiOperation(value = "通过版本号与主机厂编码获取发货计划表中本厂编码")
    @GetMapping(value = "getProductCodeByOemCode")
    public BaseResponse<List<String>> getProductCodeByOemCode(@RequestParam(value = "oemCode") String oemCode
            , @RequestParam(value = "VersionCode") String VersionCode) {

        List<String> productCodeList = deliveryDockingOrderService.getProductCodeByOemCodeAndVresionCode(oemCode,
                VersionCode);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productCodeList);

    }

    @ApiOperation(value = "通过版本号与主机厂编码获取装车需求表中本厂编码")
    @GetMapping(value = "getProductCodeByOemCodeAndVersionCodeFromLoadDemand")
    public BaseResponse<List<String>> getProductCodeByOemCodeAndVersionCodeFromLoadDemand(@RequestParam(value =
                                                                                                  "oemCode") String oemCode
            , @RequestParam(value = "VersionCode") String VersionCode) {

        List<String> productCodeList =
                deliveryDockingOrderService.getProductCodeByOemCodeAndVersionCodeFromLoadDemand(oemCode, VersionCode);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, productCodeList);
    }

    @ApiOperation(value = "通过本厂编码取物料信息字段")
    @GetMapping(value = "getNewProductStockPoint")
    public List<LabelValue<String>> getProductInfoByProductCodeList(@RequestParam(value = "productCodeList") List<String> productCodeList) {
        return deliveryDockingOrderService.getNewProductStockPoint(productCodeList);
    }

    @ApiOperation(value = "获取原始需求版本号")
    @GetMapping(value = "getOriginDemandVersionCode")
    public List<String> getOriginDemandVersionCode() {
        return deliveryDockingOrderService.getOriginDemandVersionCode();
    }

    @ApiOperation(value = "获取发货计划版本号")
    @GetMapping(value = "getDeliveryPlanVersionCode")
    public List<String> getDeliveryPlanVersionCode() {
        return deliveryDockingOrderService.getDeliveryPlanVersionCode();
    }

    @ApiOperation(value = "获取线路编码")
    @GetMapping(value = "getRoutingCode")
    public List<LabelValue<String>> getRoutingCode() {
        return deliveryDockingOrderService.getRoutingCode();
    }

    @ApiOperation(value = "获取运输方式")
    @GetMapping(value = "getTransportMode")
    public List<String> getTransportMode() {
        return deliveryDockingOrderService.getTransportMode();
    }

    @ApiOperation(value = "获取子库存-根据主机厂对应库存点")
    @GetMapping(value = "getSubInventory")
    public List<LabelValue<String>> getSubInventory(@RequestParam(value = "oemCode") String oemCode) {
        return deliveryDockingOrderService.getSubInventory(oemCode);
    }

    @ApiOperation(value = "获取货位-根据子库存")
    @GetMapping(value = "getLocation")
    public List<LabelValue<String>> getLocation(@RequestParam(value = "stashCode") String stashCode) {
        return deliveryDockingOrderService.getLocation(stashCode);
    }

    @ApiOperation(value = "获取供应类型/贸易类型")
    @GetMapping(value = "getBusinessAndMarketType")
    public List<LabelValue<String>> getBusinessAndMarketType(@RequestParam(value = "oemCode") String oemCode) {
        return deliveryDockingOrderService.getBusinessAndMarketType(oemCode);
    }

    @ApiOperation(value = "子信息详情查询")
    @GetMapping(value = "selectDockingOrderDetail")
    public BaseResponse<List<DeliveryDockingOrderDetailVO>> selectDockingOrderDetail(@RequestParam(value =
            "deliveryDockingNumber") String deliveryDockingNumber) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.selectDockingOrderDetail(deliveryDockingNumber));
    }

    @ApiOperation(value = "发布")
    @PostMapping(value = "publish")
    @BusinessMonitorLog(businessCode = "理货单下发", moduleCode = "DFP", businessFrequency = "DAY")
    public BaseResponse<String> publish(@RequestParam(value = "deliveryDockingNumber") String deliveryDockingNumber,
                                        @RequestBody List<DeliveryDockingOrderDetailVO> detailVOS) {
        return deliveryDockingOrderService.isPublish(deliveryDockingNumber, true, detailVOS);
    }

    @ApiOperation(value = "撤回发布")
    @GetMapping(value = "doWithDrawPublish")
    public BaseResponse<Void> doWithDrawPublish(@RequestParam(value = "deliveryDockingNumber") String deliveryDockingNumber) {
        deliveryDockingOrderService.isPublish(deliveryDockingNumber, false, null);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "变更")
    @GetMapping(value = "change")
    public BaseResponse<Void> doChange(@RequestParam(name = "status", required = false) String status
            , @RequestParam(value = "deliveryDockingNumber") String deliveryDockingNumber) {
        deliveryDockingOrderService.doChange(status);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "手动同步")
    @GetMapping(value = "sync")
    public BaseResponse<Void> sync() {
        return deliveryDockingOrderService.sync();
    }

    @ApiOperation(value = "对接单号下拉")
    @GetMapping(value = "dropdown")
    public BaseResponse<List<String>> dropdown() {
        return BaseResponse.success(deliveryDockingOrderService.dropdown());
    }

    @ApiOperation(value = "对接单状态查询")
    @GetMapping(value = "getStatusByDeliveryDockingNumber")
    public BaseResponse<List<String>> getStatusByDeliveryDockingNumber(@RequestParam(value = "deliveryDockingNumber") String deliveryDockingNumber) {
        return BaseResponse.success(deliveryDockingOrderService.getStatusByDeliveryDockingNumber(deliveryDockingNumber));
    }

    @ApiOperation(value = "获取客户地址")
    @GetMapping(value = "getCustomerAddress")
    public BaseResponse<List<LabelValue<String>>> getCustomerAddress(@RequestParam(value = "oemCode") String oemCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.getCustomerAddress(oemCode));
    }

    @ApiOperation(value = "新-获取线路编码")
    @GetMapping(value = "getLineRoutingCode")
    public BaseResponse<List<LabelValue<String>>> getLineRoutingCode() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.getLineRoutingCode());
    }

    @ApiOperation(value = "获取运输方式通过线路编码")
    @GetMapping(value = "getTransportModelByLineCode")
    public BaseResponse<String> getTransportModelByLineCode(@RequestParam(value = "routingDetailCode") String routingDetailCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getTransportModelByLineCode(routingDetailCode));
    }

    @ApiOperation(value = "获取箱子类型(器具)通过本厂编码")
    @GetMapping(value = "getBoxType")
    public BaseResponse<String> getBoxType(@RequestParam(value = "productCode") String productCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getBoxTypeByProductCode(productCode));
    }

    @ApiOperation(value = "获取物料器具通过本厂编码ID")
    @GetMapping(value = "getBoxTypeByProductCode")
    public BaseResponse<String> getBoxTypeByProductCode(@RequestParam(value = "productStockPointId") String productStockPointId) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getBoxTypeByProductCodeId(productStockPointId));
    }

    @ApiOperation(value = "获取本厂编码-通过主机厂EDI为Y/N")
    @GetMapping(value = "getProductCodeByOemCodeEdi")
    public BaseResponse<List<DeliveryReturnVO>> getProductCodeByOemCodeEdi(@RequestParam(value = "oemCode") String oemCode,
                                                                           @RequestParam(value = "selectTime") String selectTime,
                                                                           @RequestParam(value = "currentDayFlag", required = false) String currentDayFlag) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getProductCodeByOemCodeEdi(oemCode, selectTime, currentDayFlag));
    }

    @ApiOperation(value = "获取车长(米)")
    @GetMapping(value = "getVehicleLength")
    public BaseResponse<List<String>> getVehicleLength() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.getVehicleLength());
    }

    @ApiOperation(value = "获取本厂编码-通过主机厂")
    @GetMapping(value = "getProductCodeByOemCodeToVehicleCode")
    public BaseResponse<List<LabelValue<String>>> getProductCodeByOemCodeToVehicleCode(@RequestParam(value = "oemCode"
    ) String oemCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getProductCodeByOemCode(oemCode));
    }

    @ApiOperation(value = "承运人下拉")
    @GetMapping(value = "carrierDataDropDown")
    public BaseResponse<List<LabelValueFour<String>>> carrierDataDropDown() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.carrierDataDropDown());
    }

    @ApiOperation(value = "获取运输工具-通过承运人对应id")
    @GetMapping(value = "getConveyanceByCode")
    public BaseResponse<List<LabelValueThree<String>>> getConveyanceByCode(@RequestParam(value = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, deliveryDockingOrderService.getConveyanceByCode(id));
    }

    @ApiOperation(value = "获取运输条款-通过主机厂编码")
    @GetMapping(value = "getTransitClauseByCode")
    public BaseResponse<List<LabelValue<String>>> getTransitClauseByCode(@RequestParam(value = "oemCode") String oemCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getTransitClauseByCode(oemCode));
    }

    @ApiOperation(value = "获取主信息通过对接单号")
    @GetMapping(value = "getMainDetailByDockingNumber")
    public BaseResponse<DeliveryDockingOrderVO> getMainDetailByDockingNumber(@RequestParam(value =
            "deliveryDockingNumber") String deliveryDockingNumber) {
        return BaseResponse.success(deliveryDockingOrderService.getMainDetailByDockingNumber(deliveryDockingNumber));
    }
    
    @ApiOperation("导出发货对接单模板")
    @GetMapping(value = "/exportTemplate")
    public void exportTemplate(HttpServletResponse response) throws IOException {
        try {
        	deliveryDockingOrderService.exportTemplate(response);
        } catch (Exception e) {
            throw new IOException(e.getMessage());
        }
    }

    @ApiOperation("导入发货对接单数据")
    @PostMapping(value = "/importExcel")
    public BaseResponse importExcel(@RequestPart MultipartFile file, String deliveryDockingNumber) throws Exception {
    	ExcelListener listener = new ExcelListener();
        // 执行导入操作
        InputStream inputStream = null;
        int importNum = 1;
        try {
            inputStream = file.getInputStream();
            EasyExcel.read(inputStream, DeliveryDockingOrderExcelDTO.class, listener).sheet().doRead();
            List<Object> datas = listener.getDatas();
            List<DeliveryDockingOrderExcelDTO> deliveryDockingOrderList = BeanUtil.copyToList(datas, DeliveryDockingOrderExcelDTO.class);
            importNum = deliveryDockingOrderService.importExcel(deliveryDockingOrderList, deliveryDockingNumber);
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            inputStream.close();
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, importNum);
    }
    
    @ApiOperation(value = "获取物料器具（多产品）")
    @GetMapping(value = "getBoxTypeDropDown")
    public BaseResponse<Map<String,List<LabelValueThree<String>>>> getBoxTypeDropDown(@RequestParam(value = "productCode") String productCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS,
                deliveryDockingOrderService.getBoxTypeDropDown(productCode));
    }

}
