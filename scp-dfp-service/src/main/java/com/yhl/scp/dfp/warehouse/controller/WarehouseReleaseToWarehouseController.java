package com.yhl.scp.dfp.warehouse.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.warehouse.dto.WarehouseReleaseToWarehouseDTO;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseToWarehouseService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseToWarehouseVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "仓库发货至中转库控制器")
@RestController
@RequestMapping("warehouseReleaseToWarehouse")
public class WarehouseReleaseToWarehouseController extends BaseController {

    @Resource
    private WarehouseReleaseToWarehouseService warehouseReleaseToWarehouseService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<WarehouseReleaseToWarehouseVO>> page() {
        List<WarehouseReleaseToWarehouseVO> warehouseReleaseToWarehouseList = warehouseReleaseToWarehouseService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<WarehouseReleaseToWarehouseVO> pageInfo = new PageInfo<>(warehouseReleaseToWarehouseList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        return warehouseReleaseToWarehouseService.doCreate(warehouseReleaseToWarehouseDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody WarehouseReleaseToWarehouseDTO warehouseReleaseToWarehouseDTO) {
        return warehouseReleaseToWarehouseService.doUpdate(warehouseReleaseToWarehouseDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        warehouseReleaseToWarehouseService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<WarehouseReleaseToWarehouseVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, warehouseReleaseToWarehouseService.selectByPrimaryKey(id));
    }

}
