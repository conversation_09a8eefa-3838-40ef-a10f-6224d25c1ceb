package com.yhl.scp.dfp.job;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.yhl.platform.common.datasource.DynamicDataSourceContextHolder;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.scp.common.enums.SystemModuleEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.ips.feign.common.IpsNewFeign;
import com.yhl.scp.ips.system.entity.Scenario;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * description: 每日海运错误数据发送给指定人员
 * author：yihang.zhu
 * email:
 * date: 2025/05/28
 */
@Component
@Slf4j
public class ConsistenceDemandForecastQuayErrorJob {
    @Resource
    private SpringBeanUtils springBeanUtils;

    @XxlJob("consistenceDemandForecastQuayErrorJob")
    private ReturnT<String> consistenceDemandForecastQuayErrorJob() {
        IpsNewFeign ipsNewFeign=springBeanUtils.getBean(IpsNewFeign.class);
        ConsistenceDemandForecastDataService consistenceDemandForecastDataService=springBeanUtils.getBean(ConsistenceDemandForecastDataService.class);
        List<Scenario> scenarios = ipsNewFeign.getScenariosByModuleCode(SystemModuleEnum.DFP.getCode()).getData();
        if (CollectionUtils.isEmpty(scenarios)) {
            XxlJobHelper.log("租户下不存在DFP模块信息");
            return ReturnT.SUCCESS;
        }
        for (Scenario scenario : scenarios) {
            XxlJobHelper.log("开始处理执行监控每日海运错误数据", scenario.getDataBaseName());
            DynamicDataSourceContextHolder.setDataSource(scenario.getDataBaseName());
            consistenceDemandForecastDataService.executionQuayErrorSend();
            DynamicDataSourceContextHolder.clearDataSource();
            XxlJobHelper.log("结束处理执行监控每日海运错误数据", scenario);
        }
        return ReturnT.SUCCESS;
    }
}
