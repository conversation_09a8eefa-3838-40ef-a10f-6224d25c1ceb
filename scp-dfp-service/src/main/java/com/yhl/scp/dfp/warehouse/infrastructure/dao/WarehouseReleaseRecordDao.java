package com.yhl.scp.dfp.warehouse.infrastructure.dao;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.warehouse.dto.WareHouseReleaseInTransitTotalDTO;
import com.yhl.scp.dfp.warehouse.infrastructure.po.WarehouseReleaseRecordPO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordDayVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <code>WarehouseReleaseRecordDao</code>
 * <p>
 * 仓库发货记录DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-31 10:17:29
 */
public interface WarehouseReleaseRecordDao extends BaseDao<WarehouseReleaseRecordPO, WarehouseReleaseRecordVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link WarehouseReleaseRecordVO}
     */
    List<WarehouseReleaseRecordVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据版本批量删除
     *
     * @param versionDTOList
     * @return
     */
    int deleteBatchVersion(@Param("list") List<RemoveVersionDTO> versionDTOList);

    /**
     * 根据本厂编码批量查询
     *
     * @param productCodeList
     * @return
     */
    List<WarehouseReleaseRecordPO> selectByItemCodes(@Param("productCodeList") List<String> productCodeList);

    /**
     * 根据本厂编码批量查询
     *
     * @param oemCodeList
     * @param productCodeList
     * @param startYearMonth
     * @param endYearMonth
     * @return
     */
    List<WarehouseReleaseRecordMonthVO> selectMonthVOByItemCodes(@Param("oemCodeList") List<String> oemCodeList,
                                                                 @Param("productCodeList") List<String> productCodeList,
                                                                 @Param("startYearMonth") String startYearMonth,
                                                                 @Param("endYearMonth") String endYearMonth);

    /**
     * 按月聚合查询
     *
     * @param params 查询条件
     * @return java.util.List<com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordMonthVO>
     */
    List<WarehouseReleaseRecordMonthVO> selectMonthVOByParams(@Param("params") Map<String, Object> params);

    List<WarehouseReleaseRecordMonthVO> selectMonthVOByParamsGroupOem(@Param("params") Map<String, Object> params);

    List<WarehouseReleaseRecordVO> actualDelivery(@Param("beginTime") Date beginTime, @Param("endTime") Date endTime,
                                                  @Param("type") String type);

    /**
     * 查询发货时间是三个月内且未被接收的数据
     *
     * @param productCode
     * @return
     */
    List<WarehouseReleaseRecordPO> selectThreeMonthDataByProductCode(@Param("productCode") String productCode);

    /**
     * 根据本厂编码查询在途数据
     *
     * @param productCode
     * @return
     */
    WareHouseReleaseInTransitTotalDTO selectInTransitTotal(@Param("productCode") String productCode);

    List<WarehouseReleaseRecordPO> selectGroup();

    //在途数据用新的方式判断条件
    //List<WarehouseReleaseRecordVO> selectInRoad(@Param("productCodeList") List<String> demandProductCodeList);

    List<WarehouseReleaseRecordVO> selectInRoadWarehouse(@Param("productCodeList") List<String> demandProductCodeList
            ,@Param("shipmentLocatorCodes") List<String> shipmentLocatorCodes);

    List<String> selectTargetStockLocation();

	List<WarehouseReleaseRecordPO> selectGroupByItemCodes(@Param("itemCodes") List<String> itemCodes);

    List<WarehouseReleaseRecordVO> selectWarehouseReleaseRecordSumQtyByDate(@Param("productCodes") List<String> productCodes, @Param("startDate") String startDate, @Param("endDate") String endDate);


	List<WarehouseReleaseRecordDayVO> selectDayVOByParams(@Param("params") Map<String, Object> params);
}
