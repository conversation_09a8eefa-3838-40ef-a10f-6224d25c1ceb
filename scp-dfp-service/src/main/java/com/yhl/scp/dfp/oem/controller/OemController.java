package com.yhl.scp.dfp.oem.controller;

import com.google.common.collect.ImmutableMap;
import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.oem.dto.OemDTO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.util.LabelValueThree;
import com.yhl.scp.ips.common.SystemHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>OemController</code>
 * <p>
 * 主机厂档案控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:48:08
 */
@Slf4j
@Api(tags = "主机厂档案控制器")
@RestController
@RequestMapping("oem")
public class OemController extends BaseController {

    @Resource
    private OemService oemService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<OemVO>> page() {
        List<OemVO> oemList = oemService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<OemVO> pageInfo = new PageInfo<>(oemList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody OemDTO oemDTO) {
        return oemService.doCreate(oemDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody OemDTO oemDTO) {
        return oemService.doUpdate(oemDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    public BaseResponse<Void> delete(@RequestBody List<RemoveVersionDTO> removeVersionDTOS) {
        oemService.deleteBatchVersion(removeVersionDTOS);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    public BaseResponse<OemVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.selectByPrimaryKey(id));
    }
    
    @ApiOperation(value = "详情查询(通过主机厂编码)")
    @GetMapping(value = "selectByOemCode")
    public BaseResponse<OemVO> selectByOemCode(@RequestParam(name = "oemCode") String oemCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.selectByOemCode(oemCode));
    }

    @ApiOperation(value = "主机厂下拉")
    @GetMapping(value = "dropdown")
    public BaseResponse<List<LabelValue<String>>> queryOemInfo() {
        return BaseResponse.success(oemService.queryOemInfo());
    }

    @ApiOperation(value = "主机厂下拉(非拼接版)")
    @GetMapping(value = "oemDropdown")
    public BaseResponse<List<LabelValue<String>>> queryNameAndCode() {
        return BaseResponse.success(oemService.queryNameAndCode());
    }

    @ApiOperation(value = "客户下拉")
    @GetMapping(value = "customerDropdown")
    public BaseResponse<List<LabelValue<String>>> queryCustomerInfo() {
        return BaseResponse.success(oemService.queryCustomerInfo());
    }

    @ApiOperation(value = "根据用户权限获取主机厂编码集合")
    @GetMapping(value = "getOemCodeByUserPermission")
    public BaseResponse<List<OemVO>> getOemCodeByUserPermission() {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.getOemCodeByUserPermission());
    }

    @ApiOperation(value = "同步主机厂客户信息")
    @PostMapping(value = "sync")
    public BaseResponse<Void> syncSaleOrganize(@RequestParam(value = "organizeId", required = true) String organizeId) {
        return oemService.syncCustomers(organizeId, SystemHolder.getTenantCode());
    }

    @ApiOperation(value = "根据客户编码获取主机厂编码")
    @GetMapping(value = "queryOemCodeByCustomerCode")
    public BaseResponse<List<LabelValue<String>>> queryOemCodeByCustomerCode(@RequestParam(name = "customerCode") String customerCode) {
        return BaseResponse.success(oemService.queryOemCodeByCustomerCode(customerCode));
    }

    @ApiOperation(value = "根据主机厂获取对应地址")
    @GetMapping(value = "getAddressByOemCode")
    public BaseResponse<String> getAddressByOemCode(@RequestParam(value = "oemCode") String oemCode) {
        return BaseResponse.success(oemService.getAddressByOemCode(oemCode));
    }

    @ApiOperation(value = "客户编码下拉-非EDI直连")
    @GetMapping(value = "getCustomerByNoEdi")
    public BaseResponse<List<LabelValue<String>>> getCustomerByNoEdi() {
        return BaseResponse.success(oemService.getCustomerByNoEdi());
    }

    @ApiOperation(value = "地址二下拉(模糊搜索)")
    @GetMapping(value = "getAddressTwoLike")
    public BaseResponse<List<String>> getAddressTwoLike(@RequestParam(value = "locationArea2", required = false) String locationArea2) {
        return BaseResponse.success(oemService.getAddressTwo(locationArea2));
    }

    @ApiOperation(value = "获取地址一三四-通过客户编码")
    @GetMapping(value = "getAddressOneThreeFourByCustomer")
    public BaseResponse<List<LabelValueThree<String>>> getAddressOneThreeFourByCustomer(@RequestParam(value =
            "customerCode") String customerCode) {
        List<OemVO> oemVOS = oemService.getAddressOneThreeFourByCustomer(customerCode);
        List<LabelValueThree<String>> result = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(oemVOS)) {
            oemVOS.forEach(x -> {
                LabelValueThree<String> labelValueTree = new LabelValueThree<>();
                labelValueTree.setLabel(x.getLocationArea1());
                labelValueTree.setValue(x.getLocationArea3());
                labelValueTree.setName(x.getLocationArea4());
                result.add(labelValueTree);
            });
        }
        return BaseResponse.success(result);
    }

    @ApiOperation(value = "生成主机厂编码通过客户编码")
    @GetMapping(value = "getOemCodeByCustomerCode")
    public BaseResponse<String> getOemCodeByCustomerCode(@RequestParam(value = "customerCode") String customerCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.getOemCodeByCustomerCode(customerCode));
    }

    @ApiOperation(value = "获取付款方式通过客户编码")
    @GetMapping(value = "getPayment")
    public BaseResponse<List<String>> getPayment(@RequestParam(value = "customerCode") String customerCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.getPayment(customerCode));
    }

    @ApiOperation(value = "根据主机厂编码查询详情")
    @GetMapping(value = "getOemDetailByCode")
    public BaseResponse<OemVO> getOemDetailByCode(@RequestParam(value = "oemCode") String oemCode) {
        List<OemVO> oemList = oemService.selectByParams(ImmutableMap.of("oemCode", oemCode));
        if (CollectionUtils.isNotEmpty(oemList)) {
            return BaseResponse.success(BaseResponse.OP_SUCCESS, oemList.get(0));
        }
        return BaseResponse.success(BaseResponse.OP_SUCCESS, null);
    }
    
    @ApiOperation(value = "根据主机厂编码查询运输条款")
    @GetMapping(value = "getTransitClauseByCodes")
    public BaseResponse<String> getTransitClauseByCodes(@RequestParam(value = "oemCode") String oemCode,
    		@RequestParam(value = "productCode", required = false) String productCode) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, oemService.getTransitClauseByCodes(oemCode, null));
    }

}