package com.yhl.scp.dfp.delivery.infrastructure.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import com.yhl.platform.common.ddd.BaseDao;
import com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPublishedPO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedDayVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedMonthVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanPublishedVO;
import org.apache.ibatis.annotations.Select;

/**
 * <code>DeliveryPlanPublishedDao</code>
 * <p>
 * 发货计划发布表DAO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-17 16:00:39
 */
public interface DeliveryPlanPublishedDao extends BaseDao<DeliveryPlanPublishedPO, DeliveryPlanPublishedVO> {

    /**
     * 组合查询
     *
     * @param params 查询条件
     * @return list {@link DeliveryPlanPublishedVO}
     */
    List<DeliveryPlanPublishedVO> selectVOByParams(@Param("params") Map<String, Object> params);

    /**
     * 通过本厂编码，产品编码获取当月代发货数量
     * @param oemCodeList
     * @param productCodeList
     * @param endDemandTime
     * @param startDemandTime
     */
    List<DeliveryPlanPublishedMonthVO> selectCurrentMonthVOByItemCodes(@Param("oemCodeList") List<String> oemCodeList,
            @Param("productCodeList") List<String> productCodeList,
            @Param("startDemandTime") Date startDemandTime, @Param("endDemandTime") Date endDemandTime);

    Integer selectMaxKID();

    /**
     * 通过本厂编码，开始时间，结束时间统计产品发货数量
     * @param params
     * @return
     */
	List<DeliveryPlanPublishedVO> selectSumDeliveryPlanPublished(@Param("params")Map<String, Object> params);

    /**
     * 获取发货计划视图所有列
     *
     * @return column
     */
    @Select("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'v_fdp_delivery_plan_published'")
    List<String> getAllColumns();

    List<DeliveryPlanPublishedVO> selectProductListByParamOnDynamicColumns(@Param("dynamicColumn") String dynamicColumn, @Param("params") Map<String, Object> params);

    /**
     * 通过本厂编码，产品编码获取当月每天的代发货数量
     * @param oemCodeList
     * @param productCodeList
     * @param endDemandTime
     * @param startDemandTime
     */
    List<DeliveryPlanPublishedDayVO> selectDayVOByItemCodes(@Param("oemCodeList") List<String> oemCodeList,
            @Param("productCodeList") List<String> productCodeList,
            @Param("startDemandTime") Date startDemandTime, @Param("endDemandTime") Date endDemandTime);
    List<DeliveryPlanPublishedVO> selectDeliveryPlan14days();
}
