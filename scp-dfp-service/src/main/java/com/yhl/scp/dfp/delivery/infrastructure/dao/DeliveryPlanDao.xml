<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.delivery.infrastructure.dao.DeliveryPlanDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO">
        <!--@Table fdp_delivery_plan-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="version_id" jdbcType="VARCHAR" property="versionId"/>
        <result column="demand_category" jdbcType="VARCHAR" property="demandCategory"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="supply_type" jdbcType="VARCHAR" property="supplyType"/>
        <result column="trade_type" jdbcType="VARCHAR" property="tradeType"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="transportation_route_id" jdbcType="VARCHAR" property="transportationRouteId"/>
        <result column="publish_status" jdbcType="VARCHAR" property="publishStatus"/>
        <result column="in_transit_total" jdbcType="INTEGER" property="inTransitTotal"/>
        <result column="piece_per_box" jdbcType="INTEGER" property="piecePerBox"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="version_value" jdbcType="INTEGER" property="versionValue"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <result column="version_code" jdbcType="VARCHAR" property="versionCode"/>
        <result column="version_status" jdbcType="VARCHAR" property="versionStatus"/>
        <result column="loading_position" jdbcType="VARCHAR" property="loadingPosition"/>
        <result column="routing_name" jdbcType="VARCHAR" property="routingName"/>
        <result column="demand_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="vehicle_model_code" jdbcType="VARCHAR" property="vehicleModelCode"/>
        <result column="order_planner" jdbcType="VARCHAR" property="orderPlanner"/>
    </resultMap>
    <resultMap id="VO2ResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2">
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="product_code" jdbcType="VARCHAR" property="productCode"/>
        <result column="demand_time" jdbcType="TIMESTAMP" property="demandTime"/>
        <result column="demand_quantity" jdbcType="INTEGER" property="demandQuantity"/>
        <result column="delivery_plan_detail_id" jdbcType="VARCHAR" property="deliveryPlanDetailId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id,version_id,demand_category,oem_code,supply_type,trade_type,product_code,transportation_route_id,publish_status,in_transit_total,piece_per_box,
        remark,enabled,creator,create_time,modifier,modify_time,version_value
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List"/>,version_code,oem_name,version_status,loading_position,routing_name,
        demand_quantity,vehicle_model_code
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.ids != null and params.ids.size() > 0">
                and id in
                <foreach collection="params.ids" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.versionId != null and params.versionId != ''">
                and version_id = #{params.versionId,jdbcType=VARCHAR}
            </if>
            <if test="params.demandCategory != null and params.demandCategory != ''">
                and demand_category = #{params.demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="params.versionCode != null and params.versionCode != ''">
                and version_code = #{params.versionCode,jdbcType=VARCHAR}
            </if>
            <if test="params.versionIds != null and params.versionIds.size() > 0">
                and version_id in
                <foreach collection="params.versionIds" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.supplyType != null and params.supplyType != ''">
                and supply_type = #{params.supplyType,jdbcType=VARCHAR}
            </if>
            <if test="params.tradeType != null and params.tradeType != ''">
                and trade_type = #{params.tradeType,jdbcType=VARCHAR}
            </if>
            <if test="params.productCode != null and params.productCode != ''">
                and product_code = #{params.productCode,jdbcType=VARCHAR}
            </if>
            <if test="params.productCodes != null and params.productCodes.size() > 0">
                and product_code in
                <foreach collection="params.productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="params.loadingPosition != null and params.loadingPosition != ''">
                and loading_position = #{params.loadingPosition,jdbcType=VARCHAR}
            </if>
            <if test="params.transportationRouteId != null and params.transportationRouteId != ''">
                and transportation_route_id = #{params.transportationRouteId,jdbcType=VARCHAR}
            </if>
            <if test="params.publishStatus != null and params.publishStatus != ''">
                and publish_status = #{params.publishStatus,jdbcType=VARCHAR}
            </if>
            <if test="params.inTransitTotal != null">
                and in_transit_total = #{params.inTransitTotal,jdbcType=INTEGER}
            </if>
            <if test="params.piecePerBox != null">
                and piece_per_box = #{params.piecePerBox,jdbcType=INTEGER}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.versionValue != null">
                and version_value = #{params.versionValue,jdbcType=INTEGER}
            </if>
            <if test="params.productCodeLike != null and params.productCodeLike != ''">
                and product_code like concat('%', #{params.productCodeLike,jdbcType=VARCHAR}, '%')
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_delivery_plan
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List"/>
        from v_fdp_delivery_plan
        <include refid="Base_Where_Condition"/>
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into fdp_delivery_plan(
        id,
        version_id,
        demand_category,
        oem_code,
        supply_type,
        trade_type,
        product_code,
        transportation_route_id,
        publish_status,
        in_transit_total,
        piece_per_box,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values (
        #{id,jdbcType=VARCHAR},
        #{versionId,jdbcType=VARCHAR},
        #{demandCategory,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{supplyType,jdbcType=VARCHAR},
        #{tradeType,jdbcType=VARCHAR},
        #{productCode,jdbcType=VARCHAR},
        #{transportationRouteId,jdbcType=VARCHAR},
        #{publishStatus,jdbcType=VARCHAR},
        #{inTransitTotal,jdbcType=INTEGER},
        #{piecePerBox,jdbcType=INTEGER},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO">
        insert into fdp_delivery_plan(id,
                                      version_id,
                                      demand_category,
                                      oem_code,
                                      supply_type,
                                      trade_type,
                                      product_code,
                                      transportation_route_id,
                                      publish_status,
                                      in_transit_total,
                                      piece_per_box,
                                      remark,
                                      enabled,
                                      creator,
                                      create_time,
                                      modifier,
                                      modify_time,
                                      version_value)
        values (#{id,jdbcType=VARCHAR},
                #{versionId,jdbcType=VARCHAR},
                #{demandCategory,jdbcType=VARCHAR},
                #{oemCode,jdbcType=VARCHAR},
                #{supplyType,jdbcType=VARCHAR},
                #{tradeType,jdbcType=VARCHAR},
                #{productCode,jdbcType=VARCHAR},
                #{transportationRouteId,jdbcType=VARCHAR},
                #{publishStatus,jdbcType=VARCHAR},
                #{inTransitTotal,jdbcType=INTEGER},
                #{piecePerBox,jdbcType=INTEGER},
                #{remark,jdbcType=VARCHAR},
                #{enabled,jdbcType=VARCHAR},
                #{creator,jdbcType=VARCHAR},
                #{createTime,jdbcType=TIMESTAMP},
                #{modifier,jdbcType=VARCHAR},
                #{modifyTime,jdbcType=TIMESTAMP},
                #{versionValue,jdbcType=INTEGER})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into fdp_delivery_plan(
        id,
        version_id,
        demand_category,
        oem_code,
        supply_type,
        trade_type,
        product_code,
        transportation_route_id,
        publish_status,
        in_transit_total,
        piece_per_box,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            ((select md5(uuid()) from dual),
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.tradeType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.transportationRouteId,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.inTransitTotal,jdbcType=INTEGER},
            #{entity.piecePerBox,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into fdp_delivery_plan(
        id,
        version_id,
        demand_category,
        oem_code,
        supply_type,
        trade_type,
        product_code,
        transportation_route_id,
        publish_status,
        in_transit_total,
        piece_per_box,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        version_value)
        values
        <foreach collection="list" item="entity" separator=",">
            (
            #{entity.id,jdbcType=VARCHAR},
            #{entity.versionId,jdbcType=VARCHAR},
            #{entity.demandCategory,jdbcType=VARCHAR},
            #{entity.oemCode,jdbcType=VARCHAR},
            #{entity.supplyType,jdbcType=VARCHAR},
            #{entity.tradeType,jdbcType=VARCHAR},
            #{entity.productCode,jdbcType=VARCHAR},
            #{entity.transportationRouteId,jdbcType=VARCHAR},
            #{entity.publishStatus,jdbcType=VARCHAR},
            #{entity.inTransitTotal,jdbcType=INTEGER},
            #{entity.piecePerBox,jdbcType=INTEGER},
            #{entity.remark,jdbcType=VARCHAR},
            #{entity.enabled,jdbcType=VARCHAR},
            #{entity.creator,jdbcType=VARCHAR},
            #{entity.createTime,jdbcType=TIMESTAMP},
            #{entity.modifier,jdbcType=VARCHAR},
            #{entity.modifyTime,jdbcType=TIMESTAMP},
            #{entity.versionValue,jdbcType=INTEGER})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO">
        update fdp_delivery_plan
        set version_id       = #{versionId,jdbcType=VARCHAR},
            demand_category       = #{demandCategory,jdbcType=VARCHAR},
            oem_code         = #{oemCode,jdbcType=VARCHAR},
            supply_type      = #{supplyType,jdbcType=VARCHAR},
            trade_type       = #{tradeType,jdbcType=VARCHAR},
            product_code     = #{productCode,jdbcType=VARCHAR},
            transportation_route_id = #{transportationRouteId,jdbcType=VARCHAR},
            publish_status   = #{publishStatus,jdbcType=VARCHAR},
            in_transit_total = #{inTransitTotal,jdbcType=INTEGER},
            piece_per_box    = #{piecePerBox,jdbcType=INTEGER},
            remark           = #{remark,jdbcType=VARCHAR},
            enabled          = #{enabled,jdbcType=VARCHAR},
            modifier         = #{modifier,jdbcType=VARCHAR},
            modify_time      = #{modifyTime,jdbcType=TIMESTAMP},
            version_value    = #{versionValue,jdbcType=INTEGER}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.delivery.infrastructure.po.DeliveryPlanPO">
        update fdp_delivery_plan
        <set>
            <if test="item.versionId != null and item.versionId != ''">
                version_id = #{item.versionId,jdbcType=VARCHAR},
            </if>
            <if test="item.demandCategory != null and item.demandCategory != ''">
                demand_category = #{item.demandCategory,jdbcType=VARCHAR},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplyType != null and item.supplyType != ''">
                supply_type = #{item.supplyType,jdbcType=VARCHAR},
            </if>
            <if test="item.tradeType != null and item.tradeType != ''">
                trade_type = #{item.tradeType,jdbcType=VARCHAR},
            </if>
            <if test="item.productCode != null and item.productCode != ''">
                product_code = #{item.productCode,jdbcType=VARCHAR},
            </if>
            <if test="item.transportationRouteId != null and item.transportationRouteId != ''">
                transportation_route_id = #{item.transportationRouteId,jdbcType=VARCHAR},
            </if>
            <if test="item.publishStatus != null and item.publishStatus != ''">
                publish_status = #{item.publishStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.inTransitTotal != null">
                in_transit_total = #{item.inTransitTotal,jdbcType=INTEGER},
            </if>
             <if test="item.piecePerBox != null">
                piece_per_box = #{item.piecePerBox,jdbcType=INTEGER},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.creator != null and item.creator != ''">
                creator = #{item.creator,jdbcType=VARCHAR},
            </if>
            <if test="item.createTime != null">
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.versionValue != null">
                version_value = #{item.versionValue,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER};
        update fdp_delivery_plan set
        version_value = version_value + 1
        where id = #{item.id,jdbcType=VARCHAR}
          and version_value = #{item.versionValue,jdbcType=INTEGER}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update fdp_delivery_plan
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="version_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="demand_category = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.demandCategory,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="supply_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.supplyType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="trade_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.tradeType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.productCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="transportation_route_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.transportationRouteId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="publish_status = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.publishStatus,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="in_transit_total = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.inTransitTotal,jdbcType=INTEGER}
                </foreach>
            </trim>
             <trim prefix="piece_per_box = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.piecePerBox,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="creator = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.creator,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="create_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.createTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="version_value = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.versionValue,jdbcType=INTEGER}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>;
        update fdp_delivery_plan set
        version_value = version_value + 1
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
          and version_value in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            update fdp_delivery_plan
            <set>
                <if test="item.versionId != null and item.versionId != ''">
                    version_id = #{item.versionId,jdbcType=VARCHAR},
                </if>
                <if test="item.demandCategory != null and item.demandCategory != ''">
                    demand_category = #{item.demandCategory,jdbcType=VARCHAR},
                </if>
                <if test="item.oemCode != null and item.oemCode != ''">
                    oem_code = #{item.oemCode,jdbcType=VARCHAR},
                </if>
                <if test="item.supplyType != null and item.supplyType != ''">
                    supply_type = #{item.supplyType,jdbcType=VARCHAR},
                </if>
                <if test="item.tradeType != null and item.tradeType != ''">
                    trade_type = #{item.tradeType,jdbcType=VARCHAR},
                </if>
                <if test="item.productCode != null and item.productCode != ''">
                    product_code = #{item.productCode,jdbcType=VARCHAR},
                </if>
                <if test="item.transportationRouteId != null and item.transportationRouteId != ''">
                    transportation_route_id = #{item.transportationRouteId,jdbcType=VARCHAR},
                </if>
                <if test="item.publishStatus != null and item.publishStatus != ''">
                    publish_status = #{item.publishStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.inTransitTotal != null">
                    in_transit_total = #{item.inTransitTotal,jdbcType=INTEGER},
                </if>
                <if test="item.piecePerBox != null">
                    piece_per_box = #{item.piecePerBox,jdbcType=INTEGER},
                </if>
                <if test="item.remark != null and item.remark != ''">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.enabled != null and item.enabled != ''">
                    enabled = #{item.enabled,jdbcType=VARCHAR},
                </if>
                <if test="item.creator != null and item.creator != ''">
                    creator = #{item.creator,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifier != null and item.modifier != ''">
                    modifier = #{item.modifier,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.versionValue != null">
                    version_value = #{item.versionValue,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER};
            update fdp_delivery_plan set
            version_value = version_value + 1
            where id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete
        from fdp_delivery_plan
        where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from fdp_delivery_plan where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
    <delete id="deleteBatchByVersionIds" parameterType="java.util.List">
        delete from fdp_delivery_plan where  in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>

	<!-- 批量id+版本删除 -->
	<delete id="deleteBatchVersion" parameterType="java.util.List">
        <foreach collection="list" index="index" item="item" separator=";">
            delete from fdp_delivery_plan where
            id = #{item.id,jdbcType=VARCHAR}
            and version_value = #{item.versionValue,jdbcType=INTEGER}
        </foreach>
    </delete>

    <select id="selectVO2ByPlanPeriod" resultMap="VO2ResultMap">
        SELECT
            b.oem_code,
            b.product_code,
            fdpd.demand_time,
            fdpd.id as delivery_plan_detail_id,
            sum( fdpd.demand_quantity ) AS demand_quantity
        FROM
            fdp_delivery_plan_detail fdpd,
            (
                SELECT
                    fdp.id,
                    fdp.oem_code,
                    fdp.product_code
                FROM
                    fdp_delivery_plan fdp
                        join (
                        SELECT id
                        FROM `fdp_delivery_plan_version`
                        where parent_version_id = (	SELECT id
                                                       FROM `fdp_delivery_plan_version`
                                                       WHERE  oem_code IS NULL
                                                         AND version_status = 'PUBLISHED'
                                                       order by modify_time desc
                                                       limit 1)
                    )t on fdp.version_id = t.id
            ) b
        WHERE
            fdpd.delivery_plan_data_id = b.id
        <if test="startTime != null">
            and fdpd.demand_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            and fdpd.demand_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY
            b.oem_code,
            b.product_code,
            fdpd.demand_time,
            fdpd.id
    </select>
    <select id="selectVO2ByDeliveryVersionId" resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanVO2">
        SELECT
            b.id as deliveryPlanId,
            b.oem_code as oemCode,
            b.product_code as productCode,
            dpd.id as deliveryPlanDetailId,
            dpd.demand_time as demandTime,
            dpd.demand_quantity AS demandQuantity
        FROM
            fdp_delivery_plan b
                INNER JOIN fdp_delivery_plan_detail dpd ON b.id = dpd.delivery_plan_data_id
        WHERE
            <choose>
                <when test="deliveryVersionId != null and deliveryVersionId != ''">
                    b.version_id = #{deliveryVersionId,jdbcType=VARCHAR}
                </when>
                <otherwise>
                    b.version_id = (
                    SELECT
                    id
                    FROM
                    `fdp_delivery_plan_version`
                    ORDER BY create_time desc LIMIT 1
                )
                </otherwise>
            </choose>
    </select>
    <select id="selectByDeliveryPlanDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        fdp_delivery_plan
        <where>
            <if test="versionId !=null and versionId !=''">
                AND version_id = #{versionId,jdbcType=VARCHAR}
            </if>
            <if test="demandCategory !=null and demandCategory !=''">
                AND demand_category = #{demandCategory,jdbcType=VARCHAR}
            </if>
            <if test="oemCode != null and oemCode != ''">
                AND oem_code = #{oemCode,jdbcType=VARCHAR}
            </if>
            <if test="tradeType != null and tradeType !=''">
                AND trade_type = #{tradeType,jdbcType=VARCHAR}
            </if>
            <if test="supplyType != null and supplyType !=''">
                AND supply_type = #{supplyType,jdbcType=VARCHAR}
            </if>
            <if test="productCode != null and productCode != ''">
                AND product_code = #{productCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <select id="selectLatestPublishedVersionCode" resultType="java.lang.String">
        select  max(version_code)  from v_fdp_delivery_plan
        where version_status = 'PUBLISHED'
    </select>

    <select id="selectCopy" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from fdp_delivery_plan_copy
    </select>
    <select id="selectExportData" parameterType="java.lang.String" resultType="com.yhl.scp.dfp.delivery.vo.DeliveryPlanExportVO">
        SELECT
            tt.version_code AS versionCode,
            tt.order_planner AS orderPlanner,
            tt.oem_code AS oemCode,
            tt.oem_name AS oemName,
            tt.part_number AS partName,
            tt.product_code AS productCode,
            tt.location_area2 AS receiveAddress,
            tt.sale_type AS salesType,
            tt.vehicle_model_code AS vehicleModelCode,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( NOW(), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day0,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 1 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day1,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 2 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day2,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 3 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day3,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 4 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day4,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 5 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day5,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 6 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day6,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 7 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day7,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 8 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day8,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 9 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day9,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 10 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day10,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 11 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day11,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 12 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day12,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 13 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day13,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 14 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day14,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 15 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day15,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 16 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day16,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 17 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day17,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 18 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day18,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 19 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day19,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 20 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day20,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 21 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day21,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 22 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day22,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 23 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day23,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 24 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day24,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 25 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day25,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 26 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day26,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 27 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day27,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 28 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day28,
            SUM( CASE WHEN tt.demand_time = DATE_FORMAT( DATE_ADD( NOW(), INTERVAL 29 DAY ), '%Y-%m-%d' ) THEN tt.sum_qty ELSE 0 END ) AS day29
        FROM
            (
                SELECT
                    dpv.version_code,
                    psp.order_planner,
                    dp.oem_code,
                    o.oem_name,
                    prm.part_number,
                    dp.product_code,
                    o.location_area2,
                    psp.sale_type,
                    DATE_FORMAT( dpd.demand_time, '%Y-%m-%d' ) AS demand_time,
                    dpd.demand_quantity AS sum_qty,
                    psp.vehicle_model_code
                FROM
                    fdp_delivery_plan dp
                        LEFT JOIN fdp_delivery_plan_detail dpd ON dp.id = dpd.delivery_plan_data_id
                        LEFT JOIN fdp_delivery_plan_version dpv ON dpv.id = dp.version_id
                        LEFT JOIN mds_oem o ON dp.oem_code = o.oem_code
                        LEFT JOIN (select * from (SELECT
                                                      pp.product_code AS product_code,
                                                      pp.part_number AS part_number,
                                                      pp.part_name AS part_name,
                                                      row_number() OVER (PARTITION BY pp.product_code ORDER BY pp.effective_start_time DESC, pp.create_time DESC) AS row_num
                                                  FROM fdp_part_relation_map pp) temp
                                   WHERE temp.row_num = 1) prm ON prm.product_code = dp.product_code
                        LEFT JOIN mds_product_stock_point psp ON dp.product_code = psp.product_code
                        LEFT JOIN mds_stock_point sp on psp.stock_point_code=sp.stock_point_code
                WHERE
                    dpv.version_code = #{versionCode,jdbcType=VARCHAR}
                  and sp.organize_type='SALE_ORGANIZATION'
                  and DATE_FORMAT( dpd.demand_time, '%Y-%m-%d' ) &gt;= #{beginDate,jdbcType=VARCHAR}
                  and DATE_FORMAT( dpd.demand_time, '%Y-%m-%d' ) &lt;= #{endDate,jdbcType=VARCHAR}
            ) tt
        GROUP BY
            tt.version_code,
            tt.order_planner,
            tt.oem_code,
            tt.oem_name,
            tt.part_number,
            tt.product_code,
            tt.location_area2,
            tt.sale_type,
            tt.vehicle_model_code
        ORDER BY
            tt.oem_code,
            tt.vehicle_model_code,
            tt.product_code
    </select>
    <select id="selectOemLabelValue" resultType="com.yhl.platform.common.LabelValue">
        select distinct
            o.oem_name as label,
            dp.oem_code as value
        from fdp_delivery_plan dp
            left join fdp_delivery_plan_version dpv on dp.version_id=dpv.id
            left join mds_oem o on dp.oem_code=o.oem_code
        where dpv.version_code=#{versionCode,jdbcType=VARCHAR}
        order by dp.oem_code,o.oem_name
    </select>
    
    <select id="selectDeliveryReport" resultMap="VOResultMap">
        select
	        id,
			version_id,
			version_code,
			demand_category,
			oem_code,
			oem_name,
			product_code,
			order_planner
        from v_fdp_delivery_plan_report
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
</mapper>
