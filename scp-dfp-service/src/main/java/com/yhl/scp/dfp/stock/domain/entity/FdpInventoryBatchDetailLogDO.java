package com.yhl.scp.dfp.stock.domain.entity;

import com.yhl.platform.common.ddd.BaseDO;
import lombok.AllArgsConstructor;
import lombok.experimental.SuperBuilder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>FdpInventoryBatchDetailLogDO</code>
 * <p>
 * DO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-04-18 22:55:12
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class FdpInventoryBatchDetailLogDO extends BaseDO implements Serializable {

    private static final long serialVersionUID = -87272219645705580L;

    /**
     * 主键id
     */
    private String id;
    /**
     * 库存点代码
     */
    private String stockPointCode;
    /**
     * 物料编码
     */
    private String productCode;
    /**
     * 子库存
     */
    private String subinventory;
    /**
     * 子库存描述
     */
    private String subinventoryDescription;
    /**
     * 货位
     */
    private String freightSpace;
    /**
     * 货位描述
     */
    private String freightSpaceDescription;
    /**
     * 批次
     */
    private String batch;
    /**
     * 条码号
     */
    private String barCode;
    /**
     * 现有量
     */
    private String currentQuantity;
    /**
     * 客户号
     */
    private String customerNum;
    /**
     * 零件号
     */
    private String partNum;
    /**
     * 入库时间
     */
    private String assignedTime;
    /**
     * 最后更新时间
     */
    private String lastUpdateDate;
    /**
     * 库龄
     */
    private String stockAge;
    /**
     * 库龄天数
     */
    private String stockAgeDay;
    /**
     * 保质期
     */
    private String warrantyDate;
    /**
     * 距离失效时间
     */
    private String distanceEnableDate;
    /**
     * 数据来源（ERP/MES）
     */
    private String sourceType;
    /**
     * 原始报文组织ID
     */
    private String originalOrgId;
    /**
     * 分配状态
     */
    private String allocationStatus;
    /**
     * 版本号
     */
    private Integer versionValue;

}
