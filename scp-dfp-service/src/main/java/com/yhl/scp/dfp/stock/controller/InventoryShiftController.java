package com.yhl.scp.dfp.stock.controller;

import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dfp.stock.dto.InventoryShiftDTO;
import com.yhl.scp.dfp.stock.service.InventoryShiftService;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>InventoryShiftController</code>
 * <p>
 * 库存推移表控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-26 10:26:59
 */
@Slf4j
@Api(tags = "库存推移表控制器")
@RestController
@RequestMapping("inventoryShift")
public class InventoryShiftController extends BaseController {

    @Resource
    private InventoryShiftService inventoryShiftService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    public BaseResponse<PageInfo<InventoryShiftVO>> page(@RequestParam(value = "weeklySummary") boolean weeklySummary) {
        List<InventoryShiftVO> inventoryShiftList = inventoryShiftService.selectByNewPage(getPagination(),
                getSortParam(), request.getParameter("queryCriteriaParam"), weeklySummary).stream().sorted(Comparator
                .comparing(InventoryShiftVO::getPlannedDate)).collect(Collectors.toList());
        // if (inventoryShiftList != null) {
        //     List<Date> dateList = inventoryShiftList.stream().map(InventoryShiftVO::getPlannedDate).distinct().collect(Collectors.toList());
        //     inventoryShiftList.get(0).setDateList(dateList);
        // }
        PageInfo<InventoryShiftVO> pageInfo = new PageInfo<>(inventoryShiftList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody InventoryShiftDTO inventoryShiftDTO) {
        return inventoryShiftService.doCreate(inventoryShiftDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody InventoryShiftDTO inventoryShiftDTO) {
        return inventoryShiftService.doUpdate(inventoryShiftDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        inventoryShiftService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<InventoryShiftVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryShiftService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "库存推移数据")
    @PostMapping(value = "queryInventoryShiftData")
    public BaseResponse<List<InventoryShiftVO>> queryInventoryShiftInfo(@RequestBody InventoryShiftDTO inventoryShiftDTO) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, inventoryShiftService.queryInventoryShiftData(inventoryShiftDTO));
    }



}
