<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.switchrelation.infrastructure.dao.DfpSwitchRelationBetweenProductDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO">
        <!--@Table dfp_switch_relation_between_product-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
        <result column="old_product_code" jdbcType="VARCHAR" property="oldProductCode"/>
        <result column="new_product_code" jdbcType="VARCHAR" property="newProductCode"/>
        <result column="switch_code_group" jdbcType="VARCHAR" property="switchCodeGroup"/>
        <result column="switch_mode" jdbcType="VARCHAR" property="switchMode"/>
        <result column="switch_quantity" jdbcType="INTEGER" property="switchQuantity"/>
        <result column="switch_time" jdbcType="TIMESTAMP" property="switchTime"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.switchrelation.vo.DfpSwitchRelationBetweenProductVO">
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
        <!-- TODO -->
    </resultMap>
    <sql id="Base_Column_List">
id,oem_code,old_product_code,new_product_code,switch_code_group,switch_mode,switch_quantity,switch_time,enabled,creator,create_time,modifier,modify_time
    </sql>
    <sql id="VO_Column_List">
        <!-- TODO -->
        <include refid="Base_Column_List" />,
        oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oldProductCode != null and params.oldProductCode != ''">
                and old_product_code = #{params.oldProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.newProductCode != null and params.newProductCode != ''">
                and new_product_code = #{params.newProductCode,jdbcType=VARCHAR}
            </if>
            <if test="params.switchCodeGroup != null and params.switchCodeGroup != ''">
                and switch_code_group = #{params.switchCodeGroup,jdbcType=VARCHAR}
            </if>
            <if test="params.switchMode != null and params.switchMode != ''">
                and switch_mode = #{params.switchMode,jdbcType=VARCHAR}
            </if>
            <if test="params.switchQuantity != null">
                and switch_quantity = #{params.switchQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.switchTime != null">
                and switch_time = #{params.switchTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.oldOrNewProduct != null and params.oldOrNewProduct != ''">
                and (
                	old_product_code = #{params.oldOrNewProduct,jdbcType=VARCHAR} 
                	or
                	new_product_code = #{params.oldOrNewProduct,jdbcType=VARCHAR} 
                	)
            </if>
            <if test="params.oemCodes != null and params.oemCodes.size() > 0">
                and oem_code in
                <foreach collection="params.oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_switch_relation_between_product
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_switch_relation_between_product
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_dfp_switch_relation_between_product
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_switch_relation_between_product
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_dfp_switch_relation_between_product
        <include refid="Base_Where_Condition" />
    </select>
    <select id="selectByOemAndProduct" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_switch_relation_between_product
        <where>
            enabled='YES'
            <if test="oemCodes != null and oemCodes.size() > 0">
                and oem_code in
                <foreach collection="oemCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="productCodes != null and productCodes.size() > 0">
                and (
                old_product_code in
                <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                or
                new_product_code in
                <foreach collection="productCodes" item="item" index="index" open="(" separator="," close=")">
                    #{item,jdbcType=VARCHAR}
                </foreach>
                )
            </if>
        </where>
    </select>

    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into dfp_switch_relation_between_product(
        id,
        oem_code,
        old_product_code,
        new_product_code,
        switch_code_group,
        switch_mode,
        switch_quantity,
        switch_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{oldProductCode,jdbcType=VARCHAR},
        #{newProductCode,jdbcType=VARCHAR},
        #{switchCodeGroup,jdbcType=VARCHAR},
        #{switchMode,jdbcType=VARCHAR},
        #{switchQuantity,jdbcType=INTEGER},
        #{switchTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO">
        insert into dfp_switch_relation_between_product(
        id,
        oem_code,
        old_product_code,
        new_product_code,
        switch_code_group,
        switch_mode,
        switch_quantity,
        switch_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values (
        #{id,jdbcType=VARCHAR},
        #{oemCode,jdbcType=VARCHAR},
        #{oldProductCode,jdbcType=VARCHAR},
        #{newProductCode,jdbcType=VARCHAR},
        #{switchCodeGroup,jdbcType=VARCHAR},
        #{switchMode,jdbcType=VARCHAR},
        #{switchQuantity,jdbcType=INTEGER},
        #{switchTime,jdbcType=TIMESTAMP},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into dfp_switch_relation_between_product(
        id,
        oem_code,
        old_product_code,
        new_product_code,
        switch_code_group,
        switch_mode,
        switch_quantity,
        switch_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.oldProductCode,jdbcType=VARCHAR},
        #{entity.newProductCode,jdbcType=VARCHAR},
        #{entity.switchCodeGroup,jdbcType=VARCHAR},
        #{entity.switchMode,jdbcType=VARCHAR},
        #{entity.switchQuantity,jdbcType=INTEGER},
        #{entity.switchTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into dfp_switch_relation_between_product(
        id,
        oem_code,
        old_product_code,
        new_product_code,
        switch_code_group,
        switch_mode,
        switch_quantity,
        switch_time,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.oemCode,jdbcType=VARCHAR},
        #{entity.oldProductCode,jdbcType=VARCHAR},
        #{entity.newProductCode,jdbcType=VARCHAR},
        #{entity.switchCodeGroup,jdbcType=VARCHAR},
        #{entity.switchMode,jdbcType=VARCHAR},
        #{entity.switchQuantity,jdbcType=INTEGER},
        #{entity.switchTime,jdbcType=TIMESTAMP},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO">
        update dfp_switch_relation_between_product set
        oem_code = #{oemCode,jdbcType=VARCHAR},
        old_product_code = #{oldProductCode,jdbcType=VARCHAR},
        new_product_code = #{newProductCode,jdbcType=VARCHAR},
        switch_code_group = #{switchCodeGroup,jdbcType=VARCHAR},
        switch_mode = #{switchMode,jdbcType=VARCHAR},
        switch_quantity = #{switchQuantity,jdbcType=INTEGER},
        switch_time = #{switchTime,jdbcType=TIMESTAMP},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.switchrelation.infrastructure.po.DfpSwitchRelationBetweenProductPO">
        update dfp_switch_relation_between_product
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oldProductCode != null and item.oldProductCode != ''">
                old_product_code = #{item.oldProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.newProductCode != null and item.newProductCode != ''">
                new_product_code = #{item.newProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.switchCodeGroup != null and item.switchCodeGroup != ''">
                switch_code_group = #{item.switchCodeGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.switchMode != null and item.switchMode != ''">
                switch_mode = #{item.switchMode,jdbcType=VARCHAR},
            </if>
            <if test="item.switchQuantity != null">
                switch_quantity = #{item.switchQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.switchTime != null">
                switch_time = #{item.switchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update dfp_switch_relation_between_product
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="old_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oldProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="new_product_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.newProductCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="switch_code_group = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.switchCodeGroup,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="switch_mode = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.switchMode,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="switch_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.switchQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="switch_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.switchTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update dfp_switch_relation_between_product 
        <set>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
            <if test="item.oldProductCode != null and item.oldProductCode != ''">
                old_product_code = #{item.oldProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.newProductCode != null and item.newProductCode != ''">
                new_product_code = #{item.newProductCode,jdbcType=VARCHAR},
            </if>
            <if test="item.switchCodeGroup != null and item.switchCodeGroup != ''">
                switch_code_group = #{item.switchCodeGroup,jdbcType=VARCHAR},
            </if>
            <if test="item.switchMode != null and item.switchMode != ''">
                switch_mode = #{item.switchMode,jdbcType=VARCHAR},
            </if>
            <if test="item.switchQuantity != null">
                switch_quantity = #{item.switchQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.switchTime != null">
                switch_time = #{item.switchTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
        </set>  
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from dfp_switch_relation_between_product where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from dfp_switch_relation_between_product where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
