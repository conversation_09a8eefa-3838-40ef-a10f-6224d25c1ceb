<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yhl.scp.dfp.calendar.infrastructure.dao.DfpCalendarRuleDao">
    <resultMap id="BaseResultMap" type="com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO">
        <!--@Table dfp_cal_calendar_rule-->
        <id column="id" jdbcType="VARCHAR" property="id"/>
        <result column="organization_id" jdbcType="VARCHAR" property="organizationId"/>
        <result column="standard_resource_ids" jdbcType="VARCHAR" property="standardResourceIds"/>
        <result column="physical_resource_ids" jdbcType="VARCHAR" property="physicalResourceIds"/>
        <result column="shift_ids" jdbcType="VARCHAR" property="shiftIds"/>
        <result column="rule_name" jdbcType="VARCHAR" property="ruleName"/>
        <result column="priority" jdbcType="INTEGER" property="priority"/>
        <result column="repeat_frequency" jdbcType="VARCHAR" property="repeatFrequency"/>
        <result column="frequency_pattern" jdbcType="VARCHAR" property="frequencyPattern"/>
        <result column="work_hours" jdbcType="VARCHAR" property="workHours"/>
        <result column="overtime_hours" jdbcType="VARCHAR" property="overtimeHours"/>
        <result column="calendar_type" jdbcType="VARCHAR" property="calendarType"/>
        <result column="calendar_rule_type" jdbcType="VARCHAR" property="calendarRuleType"/>
        <result column="efficiency" jdbcType="INTEGER" property="efficiency"/>
        <result column="resource_quantity" jdbcType="INTEGER" property="resourceQuantity"/>
        <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
        <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="enabled" jdbcType="VARCHAR" property="enabled"/>
        <result column="creator" jdbcType="VARCHAR" property="creator"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="modifier" jdbcType="VARCHAR" property="modifier"/>
        <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime"/>
        <result column="oem_code" jdbcType="VARCHAR" property="oemCode"/>
    </resultMap>
    <resultMap id="VOResultMap" extends="BaseResultMap" type="com.yhl.scp.dfp.calendar.vo.CalendarRuleVO">
        <result column="standard_resource_codes" jdbcType="VARCHAR" property="standardResourceCodes"/>
        <result column="standard_resource_names" jdbcType="VARCHAR" property="standardResourceNames"/>
        <result column="physical_resource_codes" jdbcType="VARCHAR" property="physicalResourceCodes"/>
        <result column="physical_resource_names" jdbcType="VARCHAR" property="physicalResourceNames"/>
        <result column="organization_code" jdbcType="VARCHAR" property="organizationCode"/>
        <result column="organization_name" jdbcType="VARCHAR" property="organizationName"/>
        <result column="shift_names" jdbcType="VARCHAR" property="shiftNames"/>
        <result column="oem_name" jdbcType="VARCHAR" property="oemName"/>
    </resultMap>
    <sql id="Base_Column_List">
id,organization_id,standard_resource_ids,physical_resource_ids,shift_ids,rule_name,priority,repeat_frequency,frequency_pattern,work_hours,overtime_hours,calendar_type,calendar_rule_type,efficiency,resource_quantity,start_date,end_date,remark,enabled,creator,create_time,modifier,modify_time,oem_code
    </sql>
    <sql id="VO_Column_List">
        <include refid="Base_Column_List" />,standard_resource_codes,standard_resource_names,physical_resource_codes,physical_resource_names,organization_code,organization_name,shift_names,oem_name
    </sql>
    <sql id="Base_Where_Condition">
        <where>
            <if test="params.id != null and params.id != ''">
                and id = #{params.id,jdbcType=VARCHAR}
            </if>
            <if test="params.organizationId != null and params.organizationId != ''">
                and organization_id = #{params.organizationId,jdbcType=VARCHAR}
            </if>
            <if test="params.standardResourceIds != null and params.standardResourceIds != ''">
                and standard_resource_ids = #{params.standardResourceIds,jdbcType=VARCHAR}
            </if>
            <if test="params.physicalResourceIds != null and params.physicalResourceIds != ''">
                and physical_resource_ids = #{params.physicalResourceIds,jdbcType=VARCHAR}
            </if>
            <if test="params.shiftIds != null and params.shiftIds != ''">
                and shift_ids = #{params.shiftIds,jdbcType=VARCHAR}
            </if>
            <if test="params.ruleName != null and params.ruleName != ''">
                and rule_name = #{params.ruleName,jdbcType=VARCHAR}
            </if>
            <if test="params.priority != null">
                and priority = #{params.priority,jdbcType=INTEGER}
            </if>
            <if test="params.repeatFrequency != null and params.repeatFrequency != ''">
                and repeat_frequency = #{params.repeatFrequency,jdbcType=VARCHAR}
            </if>
            <if test="params.frequencyPattern != null and params.frequencyPattern != ''">
                and frequency_pattern = #{params.frequencyPattern,jdbcType=VARCHAR}
            </if>
            <if test="params.workHours != null">
                and work_hours = #{params.workHours,jdbcType=VARCHAR}
            </if>
            <if test="params.overtimeHours != null">
                and overtime_hours = #{params.overtimeHours,jdbcType=VARCHAR}
            </if>
            <if test="params.calendarType != null and params.calendarType != ''">
                and calendar_type = #{params.calendarType,jdbcType=VARCHAR}
            </if>
            <if test="params.calendarRuleType != null and params.calendarRuleType != ''">
                and calendar_rule_type = #{params.calendarRuleType,jdbcType=VARCHAR}
            </if>
            <if test="params.efficiency != null">
                and efficiency = #{params.efficiency,jdbcType=INTEGER}
            </if>
            <if test="params.resourceQuantity != null">
                and resource_quantity = #{params.resourceQuantity,jdbcType=INTEGER}
            </if>
            <if test="params.startDate != null">
                and start_date = #{params.startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.endDate != null">
                and end_date = #{params.endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="params.remark != null and params.remark != ''">
                and remark = #{params.remark,jdbcType=VARCHAR}
            </if>
            <if test="params.enabled != null and params.enabled != ''">
                and enabled = #{params.enabled,jdbcType=VARCHAR}
            </if>
            <if test="params.creator != null and params.creator != ''">
                and creator = #{params.creator,jdbcType=VARCHAR}
            </if>
            <if test="params.createTime != null">
                and create_time = #{params.createTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.modifier != null and params.modifier != ''">
                and modifier = #{params.modifier,jdbcType=VARCHAR}
            </if>
            <if test="params.modifyTime != null">
                and modify_time = #{params.modifyTime,jdbcType=TIMESTAMP}
            </if>
            <if test="params.oemCode != null and params.oemCode != ''">
                and oem_code = #{params.oemCode,jdbcType=VARCHAR}
            </if>
            <if test="params.oemName != null and params.oemName !=''">
                and oem_name = #{params.oemName,jdbcType=VARCHAR}
            </if>
        </where>
    </sql>
    <!-- 详情查询 -->
    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_cal_calendar_rule
        where id = #{id,jdbcType=VARCHAR}
    </select>
    <!-- ID列表查询 -->
    <select id="selectByPrimaryKeys" parameterType="java.util.List" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_cal_calendar_rule
        where id in
        <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>
    <!-- 分页查询 -->
    <select id="selectByCondition" resultMap="VOResultMap">
        select
        <include refid="VO_Column_List" />
        from v_dfp_cal_calendar_rule
        <where>
            <if test="queryCriteriaParam != null and queryCriteriaParam != ''">
                ${queryCriteriaParam}
            </if>
        </where>
        <if test="sortParam != null and sortParam != ''">
            order by ${sortParam}
        </if>
    </select>
    <!-- 条件查询 -->
    <select id="selectByParams" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from dfp_cal_calendar_rule
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 组合查询 -->
    <select id="selectVOByParams" resultMap="VOResultMap">
        <!-- TODO -->
        select
        <include refid="VO_Column_List" />
        from v_dfp_cal_calendar_rule
        <include refid="Base_Where_Condition" />
    </select>
    <!-- 新增 -->
    <insert id="insert" parameterType="com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO">
        <selectKey keyProperty="id" resultType="java.lang.String" order="BEFORE">
            select md5(uuid()) from dual
        </selectKey>
        insert into dfp_cal_calendar_rule(
        id,
        organization_id,
        standard_resource_ids,
        physical_resource_ids,
        shift_ids,
        rule_name,
        priority,
        repeat_frequency,
        frequency_pattern,
        work_hours,
        overtime_hours,
        calendar_type,
        calendar_rule_type,
        efficiency,
        resource_quantity,
        start_date,
        end_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        oem_code)
        values (
        #{id,jdbcType=VARCHAR},
        #{organizationId,jdbcType=VARCHAR},
        #{standardResourceIds,jdbcType=VARCHAR},
        #{physicalResourceIds,jdbcType=VARCHAR},
        #{shiftIds,jdbcType=VARCHAR},
        #{ruleName,jdbcType=VARCHAR},
        #{priority,jdbcType=INTEGER},
        #{repeatFrequency,jdbcType=VARCHAR},
        #{frequencyPattern,jdbcType=VARCHAR},
        #{workHours,jdbcType=VARCHAR},
        #{overtimeHours,jdbcType=VARCHAR},
        #{calendarType,jdbcType=VARCHAR},
        #{calendarRuleType,jdbcType=VARCHAR},
        #{efficiency,jdbcType=INTEGER},
        #{resourceQuantity,jdbcType=INTEGER},
        #{startDate,jdbcType=TIMESTAMP},
        #{endDate,jdbcType=TIMESTAMP},
        #{remark,jdbcType=VARCHAR},
        #{enabled,jdbcType=VARCHAR},
        #{creator,jdbcType=VARCHAR},
        #{createTime,jdbcType=TIMESTAMP},
        #{modifier,jdbcType=VARCHAR},
        #{modifyTime,jdbcType=TIMESTAMP},
        #{oemCode,jdbcType=VARCHAR})
    </insert>
    <!-- 新增（带主键） -->
    <insert id="insertWithPrimaryKey" parameterType="com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO">
        insert into dfp_cal_calendar_rule(
            id,
            organization_id,
            standard_resource_ids,
            physical_resource_ids,
            shift_ids,
            rule_name,
            priority,
            repeat_frequency,
            frequency_pattern,
            work_hours,
            overtime_hours,
            calendar_type,
            calendar_rule_type,
            efficiency,
            resource_quantity,
            start_date,
            end_date,
            remark,
            enabled,
            creator,
            create_time,
            modifier,
            modify_time,
            oem_code)
        values (
                   #{id,jdbcType=VARCHAR},
                   #{organizationId,jdbcType=VARCHAR},
                   #{standardResourceIds,jdbcType=VARCHAR},
                   #{physicalResourceIds,jdbcType=VARCHAR},
                   #{shiftIds,jdbcType=VARCHAR},
                   #{ruleName,jdbcType=VARCHAR},
                   #{priority,jdbcType=INTEGER},
                   #{repeatFrequency,jdbcType=VARCHAR},
                   #{frequencyPattern,jdbcType=VARCHAR},
                   #{workHours,jdbcType=VARCHAR},
                   #{overtimeHours,jdbcType=VARCHAR},
                   #{calendarType,jdbcType=VARCHAR},
                   #{calendarRuleType,jdbcType=VARCHAR},
                   #{efficiency,jdbcType=INTEGER},
                   #{resourceQuantity,jdbcType=INTEGER},
                   #{startDate,jdbcType=TIMESTAMP},
                   #{endDate,jdbcType=TIMESTAMP},
                   #{remark,jdbcType=VARCHAR},
                   #{enabled,jdbcType=VARCHAR},
                   #{creator,jdbcType=VARCHAR},
                   #{createTime,jdbcType=TIMESTAMP},
                   #{modifier,jdbcType=VARCHAR},
                   #{modifyTime,jdbcType=TIMESTAMP},
                   #{oemCode,jdbcType=VARCHAR})
    </insert>
    <!-- 批量新增 -->
    <insert id="insertBatch" parameterType="java.util.List">
        insert into dfp_cal_calendar_rule(
        id,
        organization_id,
        standard_resource_ids,
        physical_resource_ids,
        shift_ids,
        rule_name,
        priority,
        repeat_frequency,
        frequency_pattern,
        work_hours,
        overtime_hours,
        calendar_type,
        calendar_rule_type,
        efficiency,
        resource_quantity,
        start_date,
        end_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        oem_code)
        values
        <foreach collection="list" item="entity" separator=",">
        ((select md5(uuid()) from dual),
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.standardResourceIds,jdbcType=VARCHAR},
        #{entity.physicalResourceIds,jdbcType=VARCHAR},
        #{entity.shiftIds,jdbcType=VARCHAR},
        #{entity.ruleName,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.repeatFrequency,jdbcType=VARCHAR},
        #{entity.frequencyPattern,jdbcType=VARCHAR},
        #{entity.workHours,jdbcType=VARCHAR},
        #{entity.overtimeHours,jdbcType=VARCHAR},
        #{entity.calendarType,jdbcType=VARCHAR},
        #{entity.calendarRuleType,jdbcType=VARCHAR},
        #{entity.efficiency,jdbcType=INTEGER},
        #{entity.resourceQuantity,jdbcType=INTEGER},
        #{entity.startDate,jdbcType=TIMESTAMP},
        #{entity.endDate,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.oemCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 批量新增（带主键） -->
    <insert id="insertBatchWithPrimaryKey" parameterType="java.util.List">
        insert into dfp_cal_calendar_rule(
        id,
        organization_id,
        standard_resource_ids,
        physical_resource_ids,
        shift_ids,
        rule_name,
        priority,
        repeat_frequency,
        frequency_pattern,
        work_hours,
        overtime_hours,
        calendar_type,
        calendar_rule_type,
        efficiency,
        resource_quantity,
        start_date,
        end_date,
        remark,
        enabled,
        creator,
        create_time,
        modifier,
        modify_time,
        oem_code)
        values
        <foreach collection="list" item="entity" separator=",">
        (
        #{entity.id,jdbcType=VARCHAR},
        #{entity.organizationId,jdbcType=VARCHAR},
        #{entity.standardResourceIds,jdbcType=VARCHAR},
        #{entity.physicalResourceIds,jdbcType=VARCHAR},
        #{entity.shiftIds,jdbcType=VARCHAR},
        #{entity.ruleName,jdbcType=VARCHAR},
        #{entity.priority,jdbcType=INTEGER},
        #{entity.repeatFrequency,jdbcType=VARCHAR},
        #{entity.frequencyPattern,jdbcType=VARCHAR},
        #{entity.workHours,jdbcType=VARCHAR},
        #{entity.overtimeHours,jdbcType=VARCHAR},
        #{entity.calendarType,jdbcType=VARCHAR},
        #{entity.calendarRuleType,jdbcType=VARCHAR},
        #{entity.efficiency,jdbcType=INTEGER},
        #{entity.resourceQuantity,jdbcType=INTEGER},
        #{entity.startDate,jdbcType=TIMESTAMP},
        #{entity.endDate,jdbcType=TIMESTAMP},
        #{entity.remark,jdbcType=VARCHAR},
        #{entity.enabled,jdbcType=VARCHAR},
        #{entity.creator,jdbcType=VARCHAR},
        #{entity.createTime,jdbcType=TIMESTAMP},
        #{entity.modifier,jdbcType=VARCHAR},
        #{entity.modifyTime,jdbcType=TIMESTAMP},
        #{entity.oemCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    <!-- 修改 -->
    <update id="update" parameterType="com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO">
        update dfp_cal_calendar_rule set
        organization_id = #{organizationId,jdbcType=VARCHAR},
        standard_resource_ids = #{standardResourceIds,jdbcType=VARCHAR},
        physical_resource_ids = #{physicalResourceIds,jdbcType=VARCHAR},
        shift_ids = #{shiftIds,jdbcType=VARCHAR},
        rule_name = #{ruleName,jdbcType=VARCHAR},
        priority = #{priority,jdbcType=INTEGER},
        repeat_frequency = #{repeatFrequency,jdbcType=VARCHAR},
        frequency_pattern = #{frequencyPattern,jdbcType=VARCHAR},
        work_hours = #{workHours,jdbcType=VARCHAR},
        overtime_hours = #{overtimeHours,jdbcType=VARCHAR},
        calendar_type = #{calendarType,jdbcType=VARCHAR},
        calendar_rule_type = #{calendarRuleType,jdbcType=VARCHAR},
        efficiency = #{efficiency,jdbcType=INTEGER},
        resource_quantity = #{resourceQuantity,jdbcType=INTEGER},
        start_date = #{startDate,jdbcType=TIMESTAMP},
        end_date = #{endDate,jdbcType=TIMESTAMP},
        remark = #{remark,jdbcType=VARCHAR},
        enabled = #{enabled,jdbcType=VARCHAR},
        modifier = #{modifier,jdbcType=VARCHAR},
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        oem_code = #{oemCode,jdbcType=VARCHAR}
        where id = #{id,jdbcType=VARCHAR}
    </update>
    <!-- 选择修改 -->
    <update id="updateSelective" parameterType="com.yhl.scp.dfp.calendar.infrastructure.po.CalendarRulePO">
        update dfp_cal_calendar_rule
        <set>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceIds != null and item.standardResourceIds != ''">
                standard_resource_ids = #{item.standardResourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceIds != null and item.physicalResourceIds != ''">
                physical_resource_ids = #{item.physicalResourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.shiftIds != null and item.shiftIds != ''">
                shift_ids = #{item.shiftIds,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleName != null and item.ruleName != ''">
                rule_name = #{item.ruleName,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.repeatFrequency != null and item.repeatFrequency != ''">
                repeat_frequency = #{item.repeatFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.frequencyPattern != null and item.frequencyPattern != ''">
                frequency_pattern = #{item.frequencyPattern,jdbcType=VARCHAR},
            </if>
            <if test="item.workHours != null">
                work_hours = #{item.workHours,jdbcType=VARCHAR},
            </if>
            <if test="item.overtimeHours != null">
                overtime_hours = #{item.overtimeHours,jdbcType=VARCHAR},
            </if>
            <if test="item.calendarType != null and item.calendarType != ''">
                calendar_type = #{item.calendarType,jdbcType=VARCHAR},
            </if>
            <if test="item.calendarRuleType != null and item.calendarRuleType != ''">
                calendar_rule_type = #{item.calendarRuleType,jdbcType=VARCHAR},
            </if>
            <if test="item.efficiency != null">
                efficiency = #{item.efficiency,jdbcType=INTEGER},
            </if>
            <if test="item.resourceQuantity != null">
                resource_quantity = #{item.resourceQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.startDate != null">
                start_date = #{item.startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endDate != null">
                end_date = #{item.endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>

        </set>
        where id = #{item.id,jdbcType=VARCHAR}
    </update>
    <!-- 批量修改 -->
    <update id="updateBatch" parameterType="java.util.List">
        update dfp_cal_calendar_rule
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="organization_id = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.organizationId,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="standard_resource_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.standardResourceIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="physical_resource_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.physicalResourceIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="shift_ids = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.shiftIds,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="rule_name = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.ruleName,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="priority = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.priority,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="repeat_frequency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.repeatFrequency,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="frequency_pattern = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.frequencyPattern,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="work_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.workHours,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="overtime_hours = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.overtimeHours,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="calendar_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.calendarType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="calendar_rule_type = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.calendarRuleType,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="efficiency = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.efficiency,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="resource_quantity = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.resourceQuantity,jdbcType=INTEGER}
                </foreach>
            </trim>
            <trim prefix="start_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.startDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="end_date = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.endDate,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="remark = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.remark,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="enabled = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.enabled,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modifier = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifier,jdbcType=VARCHAR}
                </foreach>
            </trim>
            <trim prefix="modify_time = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.modifyTime,jdbcType=TIMESTAMP}
                </foreach>
            </trim>
            <trim prefix="oem_code = case" suffix="end,">
                <foreach collection="list" index="index" item="item">
                    when id = #{item.id,jdbcType=VARCHAR} then #{item.oemCode,jdbcType=VARCHAR}
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id,jdbcType=VARCHAR}
        </foreach>
    </update>
    <!-- 批量选择修改 -->
    <update id="updateBatchSelective" parameterType="java.util.List">
       <foreach collection="list" index="index" item="item" separator=";">
        update dfp_cal_calendar_rule
        <set>
            <if test="item.organizationId != null and item.organizationId != ''">
                organization_id = #{item.organizationId,jdbcType=VARCHAR},
            </if>
            <if test="item.standardResourceIds != null and item.standardResourceIds != ''">
                standard_resource_ids = #{item.standardResourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.physicalResourceIds != null and item.physicalResourceIds != ''">
                physical_resource_ids = #{item.physicalResourceIds,jdbcType=VARCHAR},
            </if>
            <if test="item.shiftIds != null and item.shiftIds != ''">
                shift_ids = #{item.shiftIds,jdbcType=VARCHAR},
            </if>
            <if test="item.ruleName != null and item.ruleName != ''">
                rule_name = #{item.ruleName,jdbcType=VARCHAR},
            </if>
            <if test="item.priority != null">
                priority = #{item.priority,jdbcType=INTEGER},
            </if>
            <if test="item.repeatFrequency != null and item.repeatFrequency != ''">
                repeat_frequency = #{item.repeatFrequency,jdbcType=VARCHAR},
            </if>
            <if test="item.frequencyPattern != null and item.frequencyPattern != ''">
                frequency_pattern = #{item.frequencyPattern,jdbcType=VARCHAR},
            </if>
            <if test="item.workHours != null">
                work_hours = #{item.workHours,jdbcType=VARCHAR},
            </if>
            <if test="item.overtimeHours != null">
                overtime_hours = #{item.overtimeHours,jdbcType=VARCHAR},
            </if>
            <if test="item.calendarType != null and item.calendarType != ''">
                calendar_type = #{item.calendarType,jdbcType=VARCHAR},
            </if>
            <if test="item.calendarRuleType != null and item.calendarRuleType != ''">
                calendar_rule_type = #{item.calendarRuleType,jdbcType=VARCHAR},
            </if>
            <if test="item.efficiency != null">
                efficiency = #{item.efficiency,jdbcType=INTEGER},
            </if>
            <if test="item.resourceQuantity != null">
                resource_quantity = #{item.resourceQuantity,jdbcType=INTEGER},
            </if>
            <if test="item.startDate != null">
                start_date = #{item.startDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.endDate != null">
                end_date = #{item.endDate,jdbcType=TIMESTAMP},
            </if>
            <if test="item.remark != null and item.remark != ''">
                remark = #{item.remark,jdbcType=VARCHAR},
            </if>
            <if test="item.enabled != null and item.enabled != ''">
                enabled = #{item.enabled,jdbcType=VARCHAR},
            </if>
            <if test="item.modifier != null and item.modifier != ''">
                modifier = #{item.modifier,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null">
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.oemCode != null and item.oemCode != ''">
                oem_code = #{item.oemCode,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{item.id,jdbcType=VARCHAR}    
        </foreach>
    </update>
    <!-- 删除 -->
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        delete from dfp_cal_calendar_rule where id = #{id,jdbcType=VARCHAR}
    </delete>
    <!-- 批量删除 -->
    <delete id="deleteBatch" parameterType="java.util.List">
        delete from dfp_cal_calendar_rule where id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
