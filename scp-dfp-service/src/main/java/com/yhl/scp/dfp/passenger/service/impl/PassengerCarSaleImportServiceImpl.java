package com.yhl.scp.dfp.passenger.service.impl;

import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.google.common.collect.Lists;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.oem.service.OemVehicleModelMapService;
import com.yhl.scp.dfp.oem.vo.OemVehicleModelMapVO;
import com.yhl.scp.dfp.passenger.convertor.PassengerCarSaleImportConvertor;
import com.yhl.scp.dfp.passenger.domain.entity.PassengerCarSaleImportDO;
import com.yhl.scp.dfp.passenger.domain.service.PassengerCarSaleImportDomainService;
import com.yhl.scp.dfp.passenger.dto.PassengerCarSaleDTO;
import com.yhl.scp.dfp.passenger.dto.PassengerCarSaleImportDTO;
import com.yhl.scp.dfp.passenger.infrastructure.dao.PassengerCarSaleImportDao;
import com.yhl.scp.dfp.passenger.infrastructure.po.PassengerCarSaleImportPO;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleImportService;
import com.yhl.scp.dfp.passenger.service.PassengerCarSaleService;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleImportVO;
import com.yhl.scp.dfp.passenger.vo.PassengerCarSaleVO;
import com.yhl.scp.ips.utils.BasePOUtils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <code>PassengerCarSaleImportServiceImpl</code>
 * <p>
 * 乘用车市场信息导入临时表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-02-25 15:20:15
 */
@Slf4j
@Service
public class PassengerCarSaleImportServiceImpl extends AbstractService implements PassengerCarSaleImportService {

    @Resource
    private PassengerCarSaleImportDao passengerCarSaleImportDao;

    @Resource
    private PassengerCarSaleImportDomainService passengerCarSaleImportDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;
    
    @Resource
    private PassengerCarSaleService passengerCarSaleService;
    
    @Resource
    private OemVehicleModelMapService oemVehicleModelMapService;

    @Override
    public BaseResponse<Void> doCreate(PassengerCarSaleImportDTO passengerCarSaleImportDTO) {
        // 0.数据转换
        PassengerCarSaleImportDO passengerCarSaleImportDO = PassengerCarSaleImportConvertor.INSTANCE.dto2Do(passengerCarSaleImportDTO);
        PassengerCarSaleImportPO passengerCarSaleImportPO = PassengerCarSaleImportConvertor.INSTANCE.dto2Po(passengerCarSaleImportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        passengerCarSaleImportDomainService.validation(passengerCarSaleImportDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(passengerCarSaleImportPO);
        passengerCarSaleImportDao.insertWithPrimaryKey(passengerCarSaleImportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(PassengerCarSaleImportDTO passengerCarSaleImportDTO) {
        // 0.数据转换
        PassengerCarSaleImportDO passengerCarSaleImportDO = PassengerCarSaleImportConvertor.INSTANCE.dto2Do(passengerCarSaleImportDTO);
        PassengerCarSaleImportPO passengerCarSaleImportPO = PassengerCarSaleImportConvertor.INSTANCE.dto2Po(passengerCarSaleImportDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        passengerCarSaleImportDomainService.validation(passengerCarSaleImportDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(passengerCarSaleImportPO);
        passengerCarSaleImportDao.update(passengerCarSaleImportPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<PassengerCarSaleImportDTO> list) {
        List<PassengerCarSaleImportPO> newList = PassengerCarSaleImportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        passengerCarSaleImportDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<PassengerCarSaleImportDTO> list) {
        List<PassengerCarSaleImportPO> newList = PassengerCarSaleImportConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        passengerCarSaleImportDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return passengerCarSaleImportDao.deleteBatch(idList);
        }
        return passengerCarSaleImportDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public PassengerCarSaleImportVO selectByPrimaryKey(String id) {
        PassengerCarSaleImportPO po = passengerCarSaleImportDao.selectByPrimaryKey(id);
        return PassengerCarSaleImportConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_passenger_car_sale_import")
    public List<PassengerCarSaleImportVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_passenger_car_sale_import")
    public List<PassengerCarSaleImportVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<PassengerCarSaleImportVO> dataList = passengerCarSaleImportDao.selectByCondition(sortParam, queryCriteriaParam);
        PassengerCarSaleImportServiceImpl target = springBeanUtils.getBean(PassengerCarSaleImportServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<PassengerCarSaleImportVO> selectByParams(Map<String, Object> params) {
        List<PassengerCarSaleImportPO> list = passengerCarSaleImportDao.selectByParams(params);
        return PassengerCarSaleImportConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<PassengerCarSaleImportVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.PASSENGER_CAR_SALE_IMPORT.getCode();
    }

    @Override
    public List<PassengerCarSaleImportVO> invocation(List<PassengerCarSaleImportVO> dataList, Map<String, Object> params, String invocation) {
        return dataList;
    }

	@Override
	public void deleteByYearMonth(String yearMonth) {
		passengerCarSaleImportDao.deleteByYearMonth(yearMonth);
	}

	@Override
	public void updateSaleData(PassengerCarSaleImportDTO passengerCarSaleImportDTO) {
		//获取导入的数据
		String statrYearMonth = passengerCarSaleImportDTO.getStatrYearMonth();
		String endYearMonth = passengerCarSaleImportDTO.getEndYearMonth();
		Date startTime = DateUtils.getMonthFirstDay(DateUtil.parse(statrYearMonth, "yyyyMM"));
		Date etartTime = DateUtils.getMonthLastDay(DateUtil.parse(endYearMonth, "yyyyMM"));
		List<PassengerCarSaleImportVO> importList = this.selectByParams(ImmutableMap.of(
    			"enabled", YesOrNoEnum.YES.getCode(), 
    			"startTime" , startTime,
    			"etartTime" , etartTime));
		if(CollectionUtils.isEmpty(importList)) {
			throw new BusinessException("请先导入乘用车销量信息数据");
		}
		//获取存量数据
		List<PassengerCarSaleVO> oldImportList = passengerCarSaleService.selectByParams(ImmutableMap.of(
    			"startTime" , startTime,
    			"etartTime" , etartTime));
		//只处理匹配到主机厂车型的数据
		List<String> oldIds = oldImportList.stream()
				.filter(e -> StringUtils.isNotEmpty(e.getVehicleModelCode()) 
            			&& StringUtils.isNotEmpty(e.getVehicleModelName()))
				.map(PassengerCarSaleVO::getId).collect(Collectors.toList());
		Map<String, PassengerCarSaleVO> oldPassengerCarSaleMap = CollectionUtils.isEmpty(oldImportList)
                ? MapUtil.newHashMap() :
                	oldImportList.stream()
                	.filter(e -> StringUtils.isNotEmpty(e.getVehicleModelCode()) 
                			&& StringUtils.isNotEmpty(e.getVehicleModelName()))
                	.collect(Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelName(),
                                        DateUtils.dateToString(x.getSaleTime(), "yyyyMM"), x.getVehicleModelCode()),
                                Function.identity(), (v1, v2) -> v1));
		//未匹配上主机厂车型的乘用车销量数据
		Map<String, PassengerCarSaleVO> oldEnptyVehicleModelCodeMap = CollectionUtils.isEmpty(oldImportList)
                ? MapUtil.newHashMap() :
                	oldImportList.stream()
                	.filter(e -> StringUtils.isEmpty(e.getVehicleModelCode()))
                	.collect(Collectors.toMap(x -> StringUtils.join("_", x.getVehicleModelName(),
                                        DateUtils.dateToString(x.getSaleTime(), "yyyyMM")),
                                Function.identity(), (v1, v2) -> v1));
		List<OemVehicleModelMapVO> oemVehicleModelMapVOS = oemVehicleModelMapService.selectAll();
		Map<String, List<OemVehicleModelMapVO>> vehicleNameMap = oemVehicleModelMapVOS.stream()
                .filter(x -> org.apache.commons.lang3.StringUtils.isNotBlank(x.getVehicleModelName())
                		&& YesOrNoEnum.YES.getCode().equals(x.getEnabled()))
                .collect(Collectors.groupingBy(OemVehicleModelMapVO::getVehicleModelName));
		Set<String> uniqueInsertKeys = new HashSet<>();
        Set<String> uniqueUpdateKeys = new HashSet<>();
        List<PassengerCarSaleDTO> insertPassengerCarSales = Lists.newArrayList();
        List<PassengerCarSaleDTO> updatePassengerCarSales = Lists.newArrayList();
		for (PassengerCarSaleImportVO improtData : importList) {
			String vehicleModelName = improtData.getVehicleModelName();
			// 获取当前车型名称对应的多个车型映射
            List<OemVehicleModelMapVO> oemVehicleModelMapVOSForName = vehicleNameMap
            		.getOrDefault(vehicleModelName, Collections.emptyList());
            for (OemVehicleModelMapVO oemVehicleModelMapVO : oemVehicleModelMapVOSForName) {
            	PassengerCarSaleDTO passengerCarSaleDTO = new PassengerCarSaleDTO();
                passengerCarSaleDTO.setSaleTime(improtData.getSaleTime());
                passengerCarSaleDTO.setSaleQuantity(improtData.getSaleQuantity());
                passengerCarSaleDTO.setVehicleModelName(vehicleModelName);
                passengerCarSaleDTO.setProductQuantity(improtData.getProductQuantity());
                passengerCarSaleDTO.setOemCode(oemVehicleModelMapVO.getOemCode());
                passengerCarSaleDTO.setVehicleModelCode(oemVehicleModelMapVO.getVehicleModelCode());
                String yearMonth = DateUtils.dateToString(passengerCarSaleDTO.getSaleTime(), "yyyyMM");
                String key = StringUtils.join("_", vehicleModelName, yearMonth, oemVehicleModelMapVO.getVehicleModelCode());
                PassengerCarSaleVO oldPassengerCarSaleVO = oldPassengerCarSaleMap.get(key);
                if (Objects.nonNull(oldPassengerCarSaleVO)) {
                    passengerCarSaleDTO.setId(oldPassengerCarSaleVO.getId());
                    if (uniqueUpdateKeys.add(key)) {
                    	passengerCarSaleDTO.setRemark(oldPassengerCarSaleVO.getRemark());
                    	passengerCarSaleDTO.setEnabled(YesOrNoEnum.YES.getCode());
                    	passengerCarSaleDTO.setCreator(oldPassengerCarSaleVO.getCreator());
                    	passengerCarSaleDTO.setCreateTime(oldPassengerCarSaleVO.getCreateTime());
                    	passengerCarSaleDTO.setVersionValue(oldPassengerCarSaleVO.getVersionValue());
                        updatePassengerCarSales.add(passengerCarSaleDTO);
                    }
                } else {
                    if (uniqueInsertKeys.add(key)) {
                        insertPassengerCarSales.add(passengerCarSaleDTO);
                    }
                }
            }
            if(CollectionUtils.isEmpty(oemVehicleModelMapVOSForName)) {
            	//通过车型名称未匹配上主机厂车型映射关系的数据
            	PassengerCarSaleDTO passengerCarSaleDTO = new PassengerCarSaleDTO();
                passengerCarSaleDTO.setSaleTime(improtData.getSaleTime());
                passengerCarSaleDTO.setSaleQuantity(improtData.getSaleQuantity());
                passengerCarSaleDTO.setVehicleModelName(vehicleModelName);
                passengerCarSaleDTO.setProductQuantity(improtData.getProductQuantity());
                String yearMonth = DateUtils.dateToString(passengerCarSaleDTO.getSaleTime(), "yyyyMM");
                String key = StringUtils.join("_", vehicleModelName, yearMonth);
                PassengerCarSaleVO oldPassengerCarSaleVO = oldEnptyVehicleModelCodeMap.get(key);
                if (Objects.nonNull(oldPassengerCarSaleVO)) {
                    passengerCarSaleDTO.setId(oldPassengerCarSaleVO.getId());
                    if (uniqueUpdateKeys.add(key)) {
                    	passengerCarSaleDTO.setRemark(oldPassengerCarSaleVO.getRemark());
                    	passengerCarSaleDTO.setEnabled(YesOrNoEnum.YES.getCode());
                    	passengerCarSaleDTO.setCreator(oldPassengerCarSaleVO.getCreator());
                    	passengerCarSaleDTO.setCreateTime(oldPassengerCarSaleVO.getCreateTime());
                    	passengerCarSaleDTO.setVersionValue(oldPassengerCarSaleVO.getVersionValue());
                        updatePassengerCarSales.add(passengerCarSaleDTO);
                    }
                } else {
                    if (uniqueInsertKeys.add(key)) {
                        insertPassengerCarSales.add(passengerCarSaleDTO);
                    }
                }
            }
		}
		//数据入库
		if (CollectionUtils.isNotEmpty(insertPassengerCarSales)) {
			passengerCarSaleService.doCreateBatch(insertPassengerCarSales);
        }
        if (CollectionUtils.isNotEmpty(updatePassengerCarSales)) {
        	List<String> updateIds = updatePassengerCarSales.stream().map(PassengerCarSaleDTO::getId)
        			.collect(Collectors.toList());
        	oldIds.removeAll(updateIds);
        	passengerCarSaleService.doUpdateBatch(updatePassengerCarSales);
        }
        if(CollectionUtils.isNotEmpty(oldIds)) {
        	//存量数据未跟新的进行删除处理
        	passengerCarSaleService.doUpdateEnableNo(oldIds);
        }
	}

}
