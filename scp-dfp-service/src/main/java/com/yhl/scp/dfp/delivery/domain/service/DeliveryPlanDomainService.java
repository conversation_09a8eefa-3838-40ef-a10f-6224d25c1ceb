package com.yhl.scp.dfp.delivery.domain.service;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.JacksonUtils;
import com.yhl.platform.common.utils.StringUtils;
import com.yhl.scp.dfp.calendar.service.DfpResourceCalendarService;
import com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO;
import com.yhl.scp.dfp.clean.service.CleanDemandDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanDemandDataService;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanDemandDataVO;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.delivery.convertor.DeliveryPlanDateUtils;
import com.yhl.scp.dfp.delivery.domain.entity.DeliveryPlanDO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanLockConfigVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanReplenishConfigVO;
import com.yhl.scp.dfp.delivery.vo.DeliveryPlanVersionVO;
import com.yhl.scp.dfp.oem.service.OemInventorySubmissionService;
import com.yhl.scp.dfp.oem.vo.OemInventorySubmissionVO;
import com.yhl.scp.dfp.stock.infrastructure.dao.InventoryShiftDao;
import com.yhl.scp.dfp.stock.service.InventoryBatchDetailService;
import com.yhl.scp.dfp.stock.vo.InventoryShiftVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.compress.utils.Sets;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <code>DeliveryPlanDomainService</code>
 * <p>
 * 发货计划表领域业务
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-08-21 13:51:24
 */
@Slf4j
@Service
public class DeliveryPlanDomainService {

    @Resource
    InventoryShiftDao inventoryShiftDao;
    @Resource
    InventoryBatchDetailService inventoryBatchDetailService;
    @Resource
    DfpResourceCalendarService dfpResourceCalendarService;
    @Resource
    ConsistenceDemandForecastDataService consistenceDemandForecastDataService;
    @Resource
    ConsistenceDemandForecastDataDetailService consistenceDemandForecastDataDetailService;
    @Resource
    CleanDemandDataService cleanDemandDataService;
    @Resource
    CleanDemandDataDetailService cleanDemandDataDetailService;
    @Resource
    OemInventorySubmissionService oemInventorySubmissionService;

    @SuppressWarnings("unused")
    public Map<String, Integer> getOemInventory() {
        List<OemInventorySubmissionVO> oemInventorySubmissionVOS = oemInventorySubmissionService.selectAll();
        // 转map，key为oemCode&&productCode，value为stockInventoryQuantity,oemInventoryQuantity求和
        return oemInventorySubmissionVOS.stream().collect(Collectors.toMap(
                vo -> vo.getOemCode() + "&&" + vo.getProductCode(),
                vo -> vo.getStockInventoryQuantity().add(vo.getOemInventoryQuantity()).intValue(),
                (t1, t2) -> t2));
    }


    /**
     * 数据校验
     *
     * @param deliveryPlanDO 领域对象
     */
    public void validation(@SuppressWarnings("unused") DeliveryPlanDO deliveryPlanDO) {
        // TODO document why this method is empty
    }

    /**
     *
     * 校验主机厂发货配置是否存在
     * @param mtsOemCodes
     * @param deliveryPlanLockConfigVOMap
     * @param deliveryPlanReplenishConfigVOMap
     */
    @SuppressWarnings("unused")
    public void checkMtsOemDeliveryConfigExist(List<String> mtsOemCodes,
                                               Map<String, DeliveryPlanLockConfigVO> deliveryPlanLockConfigVOMap,
                                               Map<String, DeliveryPlanReplenishConfigVO> deliveryPlanReplenishConfigVOMap) {
        String errorMessage = "";
        List<String> mtsOemDeliveryLockConfigNotExist = mtsOemCodes.stream()
                .filter(oemCode -> !deliveryPlanLockConfigVOMap.containsKey(oemCode))
                .collect(Collectors.toList());
        List<String> mtsOemDeliveryReplenishNotExist = mtsOemCodes.stream()
                .filter(oemCode -> !deliveryPlanReplenishConfigVOMap.containsKey(oemCode))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(mtsOemDeliveryLockConfigNotExist)) {
            // 换行符结尾
            errorMessage += "主机厂锁定配置不存在：" + String.join(",", mtsOemDeliveryLockConfigNotExist) + "\n";
        }
        if (CollectionUtils.isNotEmpty(mtsOemDeliveryReplenishNotExist)) {
            errorMessage += "主机厂补货配置不存在：" + String.join(",", mtsOemDeliveryReplenishNotExist);
        }
        if (StringUtils.isNotEmpty(errorMessage)) {
            throw new BusinessException(errorMessage);
        }
    }

    /**
     * 获取实时库存
     *
     * @param deliveryPlanVersionCode
     * @param oemTransitInventoryMap
     * @param productScope
     * @return java.util.Map<java.lang.String, java.lang.Integer>
     */
    public Map<String, Integer> getRealTimeInventory(String deliveryPlanVersionCode,
                                                     Map<String, Integer> oemTransitInventoryMap,
                                                     List<String> productScope) {
        Map<String, Integer> productInventory = new HashMap<>();
        // if (deliveryPlanVersionCode.endsWith("00001")) {
        //
        //     return productInventory;
        // }
        // key:oemCode+"&&"+productCode
        log.info("productScope====== {}", JacksonUtils.toJson(productScope));
        Map<String, Integer> realTimeInventoryMap = inventoryBatchDetailService.getRealTimeInventory(productScope);
        if (MapUtils.isEmpty(realTimeInventoryMap) && MapUtils.isEmpty(oemTransitInventoryMap)) {
            return productInventory;
        }
        log.info("realTimeInventoryMap====== {}", JacksonUtils.toJson(realTimeInventoryMap));
        Set<String> keys = Sets.newHashSet();
        if (MapUtils.isNotEmpty(oemTransitInventoryMap)) {
            keys.addAll(oemTransitInventoryMap.keySet());
        }
        if (MapUtils.isNotEmpty(realTimeInventoryMap)) {
            keys.addAll(realTimeInventoryMap.keySet());
        }
        keys.forEach(key -> {
            Integer realTimeInventoryQuantity = realTimeInventoryMap.get(key) == null
                    ? 0 : realTimeInventoryMap.get(key);
            Integer oemTransitInventoryQuantity = oemTransitInventoryMap.get(key) == null
                    ? 0 : oemTransitInventoryMap.get(key);
            productInventory.put(key, realTimeInventoryQuantity + oemTransitInventoryQuantity);
        });
        // 当前版本不是首版，则从首版库存推移表中获取
        // StringBuilder sb = new StringBuilder(deliveryPlanVersionCode);
        // sb.replace(sb.length() - 5, sb.length(), "00001");
        // Date planDate =
        //         DateUtils.stringToDate(deliveryPlanVersionCode.substring(deliveryPlanVersionCode.length() - 13,
        //                 deliveryPlanVersionCode.length() - 5), "yyyyMMdd");
        // List<InventoryShiftVO> inventoryShiftVOS = inventoryShiftDao
        //         .selectVOByParams(ImmutableMap.of("plannedDate", planDate, "versionCode", sb.toString()));
        // if (CollectionUtils.isNotEmpty(inventoryShiftVOS)) {
        //     return inventoryShiftVOS.stream().collect(Collectors.toMap(t -> t.getOemCode() + "&&" + t.getProductCode(),
        //             t -> t.getOpeningInventory() == null ? 0 : t.getOpeningInventory(), (t1, t2) -> t2));
        // }
        return productInventory;
    }

    /**
     * 获取未来30天装车日,如果未来三天没有，默认每天都有
     *
     * @param oemCodes
     * @param dateList
     * @return java.util.Map<java.lang.String, java.util.List < com.yhl.scp.dfp.calendar.vo.ResourceCalendarVO>>
     */
    public Map<String, List<ResourceCalendarVO>> getFuture30DaysCalendar(List<String> oemCodes, List<Date> dateList) {
        return dfpResourceCalendarService.getResourceByOem(oemCodes, dateList, false);
    }

    /**
     *
     * @param deliveryPlanVersion 发货计划版本
     * @param oemCodeScope 主机厂范围
     * @param productScope 物料范围
     * @param vehicleModelScope 车型代码范围
     * @param months 计算时间范围
     * @param lastDay 起始时间
     * @param bySearch true表示只作查询用，查不到数据会返回空，false表示计算用，查不到数据会报错
     * @return
     */
    public Map<String,List<CleanDemandDataDetailVO>> mergeCleanDemandAndForecastDemandData(
            DeliveryPlanVersionVO deliveryPlanVersion,
            List<String> oemCodeScope,
            List<String> productScope, List<String> vehicleModelScope,List<String> months,Date lastDay,
            boolean bySearch) {

        List<Date> deliveryPlanDateList =
                DeliveryPlanDateUtils.getDeliveryPlanDateList(deliveryPlanVersion.getVersionCode());
        Date cleanDemandLastDay = deliveryPlanDateList.get(deliveryPlanDateList.size() - 1);


        List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS = cleanDemandDataDetailService
                .selectByParams(ImmutableMap.of(
                        "versionId", deliveryPlanVersion.getDemandVersionId(),
                        "oemCodeList", oemCodeScope,
                        "productCodeList", productScope));
        if (CollectionUtils.isEmpty(cleanDemandDataDetailVOS)) {
            if (bySearch){
                return new HashMap<>();
            }else {
                throw new BusinessException("未找到当前用户管理物料的日需求");
            }
        }
        List<String> oemCodes = cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getOemCode).distinct()
                .collect(Collectors.toList());

        //id和demandCategory映射关系
        Map<String,String> cleanDemandCategory = getCleanDemandCategory(cleanDemandDataDetailVOS);
        //量产需求
        Map<String, List<CleanDemandDataDetailVO>> outputCleanDemandDataDetailByOemCodeAndProductCode =
                getCleanDemandDataByCategory(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),
                        cleanDemandCategory,cleanDemandDataDetailVOS);
        //项目需求
        Map<String, List<CleanDemandDataDetailVO>> projectCleanDemandDataDetailByOemCodeAndProductCode =
                getCleanDemandDataByCategory(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(),
                        cleanDemandCategory,cleanDemandDataDetailVOS);


        // 查询主机厂装车日历

        Map<String, Map<String, List<Date>>> resourceCalendarVOMap = getResourceByOemByProduct(oemCodes,
                months, vehicleModelScope);

        // 查询最近两个月(包含当月)的一致性预测需求数据
        List<ConsistenceDemandForecastDataDetailVO> latestPublishedVersionData =
                consistenceDemandForecastDataDetailService.getLatestPublishedVersionData(oemCodeScope, productScope);
        // 一致性预测需求数据id和demandCategory映射关系
        Map<String,String> consistenceDemandForecastDataIdMap =
                getConsistenceDemandCategory(latestPublishedVersionData);
        //量产需求
        Map<String, CleanDemandDataDetailVO> forecastCleanDemandDataDetailVOMap = new HashMap<>();
        //项目需求
        Map<String, CleanDemandDataDetailVO> projectForecastCleanDemandDataDetailVOMap = new HashMap<>();
        latestPublishedVersionData.stream()
                .filter(t -> months.contains(DateUtils.dateToString(t.getForecastTime(), "yyyy-MM")))
                .forEach(t -> {
                    Map<String, List<Date>> map = resourceCalendarVOMap.computeIfAbsent(t.getOemCode(),
                            key -> new HashMap<>());
                    List<Date> resourceCalendarVOS = map.computeIfAbsent(DateUtils
                            .dateToString(t.getForecastTime(), "yyyy-MM"), k -> Lists.newArrayList());
                    // TODO:resourceMap已经对日期做了判空，此处应该不需要了
                    if (CollectionUtils.isEmpty(resourceCalendarVOS)) {
                        List<Date> list = DateUtils.getIntervalDates(DateUtils.getMonthFirstDay(t.getForecastTime()),
                                DateUtils.getMonthLastDay(t.getForecastTime()));
                        // if (isWeekend(d)) {
                        //     continue;
                        // }
                        resourceCalendarVOS.addAll(list);
                        //map.put(t.getOemCode(), resourceCalendarVOS);
                    }
                    BigDecimal forecastQuantity = t.getForecastQuantity() == null ? BigDecimal.ZERO : t.getForecastQuantity();
                    int forecastQtyAvg = forecastQuantity.intValue() / resourceCalendarVOS.size();
                    int remainder = forecastQuantity.intValue() % resourceCalendarVOS.size();
                    Collections.sort(resourceCalendarVOS);
                    String demandCategory = consistenceDemandForecastDataIdMap.get(t.getConsistenceDemandForecastDataId());
                    for (int i = 0; i < resourceCalendarVOS.size(); i++) {

                        int share = forecastQtyAvg;
                        CleanDemandDataDetailVO cleanDemandDataDetail = new CleanDemandDataDetailVO();
                        cleanDemandDataDetail.setDemandTime(resourceCalendarVOS.get(i));
                        cleanDemandDataDetail.setOemCode(t.getOemCode());
                        cleanDemandDataDetail.setProductCode(t.getProductCode());
                        if (i < remainder) {
                            share++;
                        }
                        cleanDemandDataDetail.setDemandQuantity(BigDecimal.valueOf(share));
                        String key = t.getOemCode() + "&&" + t.getProductCode() + "&&"
                                + DateUtils.dateToString(resourceCalendarVOS.get(i),
                                DateUtils.COMMON_DATE_STR3);
                        if (StringUtils.isEmpty(demandCategory) || demandCategory.equals(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode())){
                            forecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                        }else {
                            projectForecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                        }

                        //forecastCleanDemandDataDetailVOMap.put(key, cleanDemandDataDetail);
                    }
                });

        List<CleanDemandDataDetailVO> outputCleanDemandDataDetailList =
                mergeCleanDemandAndForecastDemandByCategory(outputCleanDemandDataDetailByOemCodeAndProductCode,
                        resourceCalendarVOMap,lastDay,cleanDemandLastDay,forecastCleanDemandDataDetailVOMap);
        List<CleanDemandDataDetailVO> projectCleanDemandDataDetailList =
                mergeCleanDemandAndForecastDemandByCategory(projectCleanDemandDataDetailByOemCodeAndProductCode,
                        resourceCalendarVOMap,lastDay,cleanDemandLastDay,projectForecastCleanDemandDataDetailVOMap);
        Map<String, List<CleanDemandDataDetailVO>> result = new HashMap<>();
        result.put(ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(),outputCleanDemandDataDetailList);
        result.put(ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(),projectCleanDemandDataDetailList);
        return result;
    }

    private Map<String,List<CleanDemandDataDetailVO>> getCleanDemandDataByCategory(
            String demandCategory, Map<String,String> cleanDemandCategory,
            List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS){
        return cleanDemandDataDetailVOS.stream()
                        .filter(t ->
                                StringUtils.isNotEmpty(cleanDemandCategory.get(t.getCleanDemandDataId())) && cleanDemandCategory.get(t.getCleanDemandDataId()).equals(demandCategory))
                        .collect(Collectors.groupingBy(t -> t.getOemCode() + "&&" + t.getProductCode()));
    }

    private List<CleanDemandDataDetailVO> mergeCleanDemandAndForecastDemandByCategory(
            Map<String, List<CleanDemandDataDetailVO>> cleanDemandDataDetailByOemCodeAndProductCode,
            Map<String, Map<String,List<Date>>> resourceCalendarVOMap, Date lastDay,Date cleanDemandLastDay,
            Map<String,CleanDemandDataDetailVO> forecastCleanDemandDataDetailVOMap) {

        List<CleanDemandDataDetailVO> result = new ArrayList<>();
        cleanDemandDataDetailByOemCodeAndProductCode.keySet().forEach(oemCodeProductCode -> {
            String oemCode = oemCodeProductCode.split("&&")[0];
            Map<String, List<Date>> resourceCalendarByMonth =
                    resourceCalendarVOMap.computeIfAbsent(oemCode, key -> new HashMap<>());
            List<Date> resourceCalendarPerOem =
                    resourceCalendarByMonth.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(resourceCalendarByMonth)) {
                Date monthFirstDay = DateUtils.getMonthFirstDay(lastDay);
                Date monthLastDay = DateUtils.getMonthLastDay(DateUtils.moveMonth(lastDay, 3));
                List<Date> list = DateUtils.getIntervalDates(monthFirstDay, monthLastDay);
                for (Date date : list) {
                    if (isWeekend(date)) {
                        continue;
                    }
                    resourceCalendarPerOem.add(date);
                }
            }

            Map<String, CleanDemandDataDetailVO> cleanDemandDataDetailMapDemandTime =
                    cleanDemandDataDetailByOemCodeAndProductCode.get(oemCodeProductCode).stream()
                            .collect(Collectors.toMap(t -> DateUtils.dateToString(t.getDemandTime(),
                                    DateUtils.COMMON_DATE_STR3), Function.identity(), (t1, t2) -> t2));

            resourceCalendarPerOem.stream().distinct().forEach(resourceCalendarVO -> {
                String key = DateUtils.dateToString(resourceCalendarVO, DateUtils.COMMON_DATE_STR3);
                CleanDemandDataDetailVO cleanDemandDataDetailVO = cleanDemandDataDetailMapDemandTime.remove(key);

                if (cleanDemandDataDetailVO != null) {
                    result.add(cleanDemandDataDetailVO);
                    return;
                }
                if (!resourceCalendarVO.after(cleanDemandLastDay)) {
                    return;
                }
                // 当前装车日历没有日需求，用预测数据填充
                String key2 = oemCodeProductCode + "&&" + key;
                CleanDemandDataDetailVO cleanDemandDataDetailVO1 = forecastCleanDemandDataDetailVOMap.get(key2);
                if (cleanDemandDataDetailVO1 != null) {
                    result.add(cleanDemandDataDetailVO1);
                }
            });
            result.addAll(cleanDemandDataDetailMapDemandTime.values());
        });

        return result;
    }

    private Map<String,String> getCleanDemandCategory(List<CleanDemandDataDetailVO> cleanDemandDataDetailVOS){
        List<String> cleanDemandDataIds = cleanDemandDataDetailVOS.stream().map(CleanDemandDataDetailVO::getCleanDemandDataId)
                .distinct().collect(Collectors.toList());
        List<CleanDemandDataVO> cleanDemandDataVOS = cleanDemandDataService
                .selectByPrimaryKeys(cleanDemandDataIds);

        return cleanDemandDataVOS.stream().collect(Collectors.toMap(BaseVO::getId,
                CleanDemandDataVO::getDemandCategory));
    }

    private Map<String,String> getConsistenceDemandCategory(List<ConsistenceDemandForecastDataDetailVO> consistenceDemandForecastDataDetailVOS){
        List<String> consistenceDemandForecastDateIds = consistenceDemandForecastDataDetailVOS.stream()
                .map(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(consistenceDemandForecastDateIds)) {
            return new HashMap<>(2);
        }
        List<ConsistenceDemandForecastDataVO> consistenceDemandForecastDataVOS =
                consistenceDemandForecastDataService.selectByPrimaryKeys(consistenceDemandForecastDateIds);
        return consistenceDemandForecastDataVOS.stream()
                .collect(Collectors.toMap(ConsistenceDemandForecastDataVO::getId,
                        ConsistenceDemandForecastDataVO::getDemandCategory, (v1, v2) -> v2));
    }

    private Map<String, Map<String, List<Date>>> getResourceByOemByProduct(List<String> oemCodes,
                                                                                         List<String> months,
                                                                                         List<String> vehicles) {
        return dfpResourceCalendarService.getMonthMapByOemByVehicle(oemCodes, vehicles, months);
    }

    public List<String> getMonths(Date lastDay) {
        List<String> months = new ArrayList<>();
        months.add(DateUtils.dateToString(lastDay, DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 1), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 2), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 3), DateUtils.YEAR_MONTH));
        months.add(DateUtils.dateToString(DateUtils.moveMonth(lastDay, 4), DateUtils.YEAR_MONTH));
        return months;
    }

    public boolean isWeekend(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        DayOfWeek dayOfWeek = localDate.getDayOfWeek();
        return dayOfWeek == DayOfWeek.SATURDAY || dayOfWeek == DayOfWeek.SUNDAY;

    }

}
