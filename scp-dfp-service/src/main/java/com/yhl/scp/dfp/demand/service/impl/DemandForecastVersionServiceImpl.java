package com.yhl.scp.dfp.demand.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.common.CustomThreadPoolFactory;
import com.yhl.platform.common.LabelValue;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.DateUtils;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.dfp.basic.forecast.enums.VersionStatusEnum;
import com.yhl.scp.dfp.clean.service.CleanAlgorithmDataService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataDetailService;
import com.yhl.scp.dfp.clean.service.CleanForecastDataService;
import com.yhl.scp.dfp.clean.vo.CleanAlgorithmDataVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataDetailVO;
import com.yhl.scp.dfp.clean.vo.CleanForecastDataVO;
import com.yhl.scp.dfp.common.constants.DfpConstants;
import com.yhl.scp.dfp.common.dto.RemoveVersionDTO;
import com.yhl.scp.dfp.common.enums.DateTypeEnum;
import com.yhl.scp.dfp.common.enums.GenerateTypeEnum;
import com.yhl.scp.dfp.common.enums.GranularityEnum;
import com.yhl.scp.dfp.common.enums.ProductionDemandTypeEnum;
import com.yhl.scp.dfp.common.enums.PublishStatusEnum;
import com.yhl.scp.dfp.common.enums.VersionTypeEnum;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataDetailService;
import com.yhl.scp.dfp.consistence.service.ConsistenceDemandForecastDataService;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataDetailVO;
import com.yhl.scp.dfp.consistence.vo.ConsistenceDemandForecastDataVO;
import com.yhl.scp.dfp.demand.convertor.DemandForecastVersionConvertor;
import com.yhl.scp.dfp.demand.domain.entity.DemandForecastVersionDO;
import com.yhl.scp.dfp.demand.domain.service.DemandForecastVersionDomainService;
import com.yhl.scp.dfp.demand.dto.DemandForecastEstablishmentDTO;
import com.yhl.scp.dfp.demand.dto.DemandForecastVersionDTO;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastEstablishmentDao;
import com.yhl.scp.dfp.demand.infrastructure.dao.DemandForecastVersionDao;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastEstablishmentPO;
import com.yhl.scp.dfp.demand.infrastructure.po.DemandForecastVersionPO;
import com.yhl.scp.dfp.demand.service.DemandForecastEstablishmentService;
import com.yhl.scp.dfp.demand.service.DemandForecastVersionService;
import com.yhl.scp.dfp.demand.service.DemandVersionService;
import com.yhl.scp.dfp.demand.vo.DemandForecastVersionVO;
import com.yhl.scp.dfp.demand.vo.DemandVersionVO;
import com.yhl.scp.dfp.enums.ObjectTypeEnum;
import com.yhl.scp.dfp.material.service.PartRiskLevelService;
import com.yhl.scp.dfp.material.vo.PartRiskLevelVO;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemRiskLevelDao;
import com.yhl.scp.dfp.oem.infrastructure.dao.OemVehicleModelDao;
import com.yhl.scp.dfp.oem.infrastructure.po.OemRiskLevelPO;
import com.yhl.scp.dfp.oem.service.OemService;
import com.yhl.scp.dfp.oem.vo.OemVO;
import com.yhl.scp.dfp.utils.DfpDateUtils;
import com.yhl.scp.dfp.warehouse.service.WarehouseReleaseRecordService;
import com.yhl.scp.dfp.warehouse.vo.WarehouseReleaseRecordVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.feign.common.NewMdsFeign;
import com.yhl.scp.mds.newproduct.enums.ProductTransportTypeEnum;
import com.yhl.scp.mds.newproduct.vo.NewProductStockPointVO;

import lombok.extern.slf4j.Slf4j;

/**
 * <code>DemandForecastVersionServiceImpl</code>
 * <p>
 * 业务预测版本应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-07-23 17:46:57
 */
@Slf4j
@Service
public class DemandForecastVersionServiceImpl extends AbstractService implements DemandForecastVersionService {

    private static final String YEAR_MONTH_PATTERN = "yyyy/MM";
    @Resource
    protected OemVehicleModelDao oemVehicleModelDao;
    @Resource
    private DemandForecastVersionDao demandForecastVersionDao;
    @Resource
    private DemandForecastVersionDomainService demandForecastVersionDomainService;
    @Resource
    private SpringBeanUtils springBeanUtils;
    @Resource
    private OemService oemService;
    @Resource
    private DemandVersionService demandVersionService;
    @Resource
    private CleanForecastDataService forecastDataService;
    @Resource
    private CleanForecastDataDetailService forecastDataDetailService;
    @Resource
    private WarehouseReleaseRecordService warehouseService;
    @Resource
    private ConsistenceDemandForecastDataService consistenceService;
    @Resource
    private ConsistenceDemandForecastDataDetailService consistenceDetailService;
    @Resource
    private CleanAlgorithmDataService algorithmDataService;
    @Resource
    private NewMdsFeign mdsFeign;
    @Resource
    private OemRiskLevelDao oemRiskLevelDao;
    @Resource
    private PartRiskLevelService partRiskLevelService;
    @Resource
    private DemandForecastEstablishmentService establishmentService;
    @Resource
    private DemandForecastEstablishmentDao establishmentDao;
    @Resource
    private CleanForecastDataService cleanForecastDataService;
    @Resource
    private CleanForecastDataDetailService cleanForecastDataDetailService;
    @Resource
    private NewMdsFeign newMdsFeign;

    private static String extractDecimalNumbers(String input) {
        StringBuilder numbers = new StringBuilder();
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?");
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            numbers.append(matcher.group()).append(" ");
        }
        return numbers.toString().trim(); // 去除末尾多余的空格
    }

    @Override
    public BaseResponse<Void> doCreate(DemandForecastVersionDTO demandForecastVersionDTO) {
        // 0.数据转换
        DemandForecastVersionDO demandForecastVersionDO =
                DemandForecastVersionConvertor.INSTANCE.dto2Do(demandForecastVersionDTO);
        DemandForecastVersionPO demandForecastVersionPO =
                DemandForecastVersionConvertor.INSTANCE.dto2Po(demandForecastVersionDTO);
        // 1.数据校验
        demandForecastVersionDomainService.validation(demandForecastVersionDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(demandForecastVersionPO);
        demandForecastVersionDao.insertWithPrimaryKey(demandForecastVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public BaseResponse<Void> doUpdate(DemandForecastVersionDTO demandForecastVersionDTO) {
        // 0.数据转换
        DemandForecastVersionDO demandForecastVersionDO =
                DemandForecastVersionConvertor.INSTANCE.dto2Do(demandForecastVersionDTO);
        DemandForecastVersionPO demandForecastVersionPO =
                DemandForecastVersionConvertor.INSTANCE.dto2Po(demandForecastVersionDTO);
        // 1.数据校验
        demandForecastVersionDomainService.validation(demandForecastVersionDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(demandForecastVersionPO);
        demandForecastVersionDao.update(demandForecastVersionPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<DemandForecastVersionDTO> list) {
        List<DemandForecastVersionPO> newList = DemandForecastVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        demandForecastVersionDao.insertBatchWithPrimaryKey(newList);
    }

    @Override
    public void doUpdateBatch(List<DemandForecastVersionDTO> list) {
        List<DemandForecastVersionPO> newList = DemandForecastVersionConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        demandForecastVersionDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return demandForecastVersionDao.deleteBatch(idList);
        }
        return demandForecastVersionDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public DemandForecastVersionVO selectByPrimaryKey(String id) {
        DemandForecastVersionPO po = demandForecastVersionDao.selectByPrimaryKey(id);
        return DemandForecastVersionConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "v_fdp_demand_forecast_version")
    public List<DemandForecastVersionVO> selectByPage(Pagination pagination, String sortParam,
                                                      String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "v_fdp_demand_forecast_version")
    public List<DemandForecastVersionVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<DemandForecastVersionVO> dataList = demandForecastVersionDao.selectByCondition(sortParam,
                queryCriteriaParam);
        DemandForecastVersionServiceImpl target = springBeanUtils.getBean(DemandForecastVersionServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<DemandForecastVersionVO> selectByParams(Map<String, Object> params) {
        List<DemandForecastVersionPO> list = demandForecastVersionDao.selectByParams(params);
        return DemandForecastVersionConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<DemandForecastVersionVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.DEMAND_FORECAST_VERSION.getCode();
    }

    @Override
    public List<DemandForecastVersionVO> invocation(List<DemandForecastVersionVO> dataList,
                                                    Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

    @Override
    public int doDeleteByVersion(List<RemoveVersionDTO> versionDTOList) {
        if (CollectionUtils.isNotEmpty(versionDTOList)) {
            return demandForecastVersionDao.deleteBatchVersion(versionDTOList);
        }
        return 0;
    }

    @Override
    public List<DemandForecastVersionVO> treeQuery() {
        // 获取要返回的需求版本数据

        List<DemandForecastVersionVO> demandVersionVOS = demandForecastVersionDao.selectVOByParams(new HashMap<>());
        // 获取首层原始需求版本集合
        List<DemandForecastVersionVO> firstDemandVersionList =
                demandVersionVOS.stream().filter(k -> StringUtils.isEmpty(k.getParentVersionId())).collect(Collectors.toList());
        // 其他层根据父级版本号分组
        Map<String, List<DemandForecastVersionVO>> childVersionGroup =
                demandVersionVOS.stream().filter(k -> StringUtils.isNotEmpty(k.getParentVersionId())).collect(Collectors
                        .groupingBy(DemandForecastVersionVO::getParentVersionId));
        // 组装为树结构返回前端
        List<DemandForecastVersionVO> versionTreeVOS = new ArrayList<>();
        for (DemandForecastVersionVO forecastVersionVO : firstDemandVersionList) {
            toDemandVersionTreeVO(childVersionGroup, forecastVersionVO);
            versionTreeVOS.add(forecastVersionVO);
        }
        return versionTreeVOS;
    }

    @Override
    public void createDemandForecastVersion(DemandForecastVersionDTO versionDTO) {
        log.info("开始创建版本,入参为：{}", JSON.toJSONString(versionDTO));
        // 获取主机厂编码集合 目标原始需求版本号判断是否为空，取该用户权限下所有，否则取原始需求版本对应的主机厂集合
        List<String> oemCodeList = new ArrayList<>();
        if (StringUtils.isEmpty(versionDTO.getTargetDemandForecastVersionCode())) {
            List<OemVO> oemVOList = oemService.getOemCodeByUserPermission();
            if (CollectionUtils.isNotEmpty(oemVOList)) {
                oemCodeList = oemVOList.stream().map(OemVO::getOemCode).collect(Collectors.toList());
            }
        } else {
            oemCodeList = getOemCodeByVersionCode(versionDTO.getTargetDemandForecastVersionCode());
            // 去除指定主机厂编码
            oemCodeList =
                    oemCodeList.stream().filter(k -> !versionDTO.getOemCodeResource().contains(k)).collect(Collectors.toList());
        }
        // 获取新建版本号
        String newVersionCode = getVersionCodeByPlanPeriod(versionDTO.getTargetDemandForecastVersionCode(),
                versionDTO.getPlanPeriod(), oemCodeList);
        // 层级判断
        Integer level = getLevel(versionDTO, newVersionCode);
        log.info("生成业务预测数据");
        originalDemandCreate(newVersionCode, oemCodeList, versionDTO, level);
        log.info("业务预测映射关系拷贝");
        copyDemandVersionData();
        log.info("业务预测映射关系拷贝完成");
    }

    /**
     * 判断层级 目标版本没有 从第一级开始创建 不存在该版本号 从二级创建 存在该版本号 从三级创建
     *
     * @param versionDTO     业务预测版本
     * @param newVersionCode 版本代码
     * @return java.lang.Integer
     */
    private Integer getLevel(DemandForecastVersionDTO versionDTO, String newVersionCode) {
        int level;
        if (StringUtils.isEmpty(versionDTO.getTargetDemandForecastVersionCode())) {
            level = 1;
        } else {
            List<DemandForecastVersionPO> demandVersionPOS = demandForecastVersionDao.selectByParams(ImmutableMap.of(
                    "versionCode", newVersionCode));
            if (CollectionUtils.isNotEmpty(demandVersionPOS)) {
                level = 3;
            } else {
                level = 2;
            }
        }
        return level;
    }

    /**
     * 复制映射关系
     */
    private void copyDemandVersionData() {

    }

    /**
     * 生成新原始需求
     *
     * @param newVersionCode 版本编码
     * @param oemCodeList    主机厂编码
     * @param versionDTO     新建版本DTO
     * @param level          层级
     */
    private void originalDemandCreate(String newVersionCode, List<String> oemCodeList,
                                      DemandForecastVersionDTO versionDTO, Integer level) {
        if (level <= 1) {
            log.info("一级需求生成");
            DemandForecastVersionPO firstDemandVersionPO = new DemandForecastVersionPO();
            firstDemandVersionPO.setPlanPeriod(versionDTO.getPlanPeriod());
            firstDemandVersionPO.setGenerateType(GenerateTypeEnum.MANUAL.getCode());
            BasePOUtils.insertFiller(firstDemandVersionPO);
            demandForecastVersionDao.insertWithPrimaryKey(firstDemandVersionPO);
        }
        if (level <= 2) {
            log.info("二级需求生成");
            DemandForecastVersionPO versionPO =
                    demandForecastVersionDao.selectOneLevelDetail(versionDTO.getPlanPeriod());
            DemandForecastVersionPO nextDemandVersionPO = new DemandForecastVersionPO();
            nextDemandVersionPO.setPlanPeriod(versionDTO.getPlanPeriod());
            nextDemandVersionPO.setPlanHorizon(12);
            nextDemandVersionPO.setPlanGranularity(GranularityEnum.MONTH.getCode());
            nextDemandVersionPO.setVersionCode(newVersionCode);
            nextDemandVersionPO.setParentVersionId(versionPO.getId());
            nextDemandVersionPO.setGenerateType(GenerateTypeEnum.MANUAL.getCode());
            nextDemandVersionPO.setVersionStatus(PublishStatusEnum.UNPUBLISH.getCode());
            BasePOUtils.insertFiller(nextDemandVersionPO);
            demandForecastVersionDao.insertWithPrimaryKey(nextDemandVersionPO);
        }

        log.info("子级需求生成");
        DemandForecastVersionPO versionPO = demandForecastVersionDao.selectTwoLevelDetail(newVersionCode);
        for (String oemCode : oemCodeList) {
            DemandForecastVersionPO sonDemandVersionPO = new DemandForecastVersionPO();
            sonDemandVersionPO.setPlanPeriod(versionDTO.getPlanPeriod());
            sonDemandVersionPO.setVersionCode(newVersionCode);
            sonDemandVersionPO.setPlanHorizon(12);
            sonDemandVersionPO.setPlanGranularity(GranularityEnum.MONTH.getCode());
            sonDemandVersionPO.setParentVersionId(versionPO.getId());
            sonDemandVersionPO.setRollingVersionId(versionDTO.getCleanForecastVersionCode());
            sonDemandVersionPO.setAlgorithmVersionId(versionDTO.getCleanAlgorithmVersionCode());
            sonDemandVersionPO.setOemCode(oemCode);
            sonDemandVersionPO.setGenerateType(GenerateTypeEnum.MANUAL.getCode());
            BasePOUtils.insertFiller(sonDemandVersionPO);
            demandForecastVersionDao.insertWithPrimaryKey(sonDemandVersionPO);
        }

    }

    @Override
    public void publishVersion(String versionId) {
        // 更新版本数据为已发布
        log.info("发布业务预测版本");
        List<DemandForecastVersionVO> secondVersionVOS = this.selectByParams(ImmutableMap.of("id", versionId));
        List<DemandForecastVersionVO> thirdVersionVOS = this.selectByParams(ImmutableMap.of("parentVersionId",
                versionId));
        secondVersionVOS.addAll(thirdVersionVOS);
        secondVersionVOS.forEach(x -> x.setVersionStatus(VersionStatusEnum.PUBLISHED.getCode()));
        List<DemandForecastVersionDTO> versionDTOList =
                DemandForecastVersionConvertor.INSTANCE.vo2Dtos(secondVersionVOS);
        BasePOUtils.updateBatchFiller(versionDTOList);
        this.doUpdateBatch(versionDTOList);
        if (CollectionUtils.isNotEmpty(thirdVersionVOS)) {
            log.info("创建业务预测编制数据");
            createEstablishment(thirdVersionVOS);
        }
        log.info("业务预测版本发布完成");
    }

    /**
     * 业务预测编制数据创建
     *
     * @param thirdVersionVOS 第三级版本集合
     */
    private void createEstablishment(List<DemandForecastVersionVO> thirdVersionVOS) {
        DemandForecastVersionVO demandForecastVersionVO = thirdVersionVOS.get(0);
        // 获取风险等级信息
        List<String> oemCodeList =
                thirdVersionVOS.stream().map(DemandForecastVersionVO::getOemCode).collect(Collectors.toList());
        Map<String, String> oemRiskLevelMap = oemRiskLevelDao.selectByParams(ImmutableMap.of("oemCodeList",
                oemCodeList)).stream().collect(Collectors.toMap(OemRiskLevelPO::getOemCode,
                OemRiskLevelPO::getRiskLevel,
                (k1, k2) -> k1));
        // 获取滚动预测版本号
        DemandVersionVO rollingVO =
                demandVersionService.selectByPrimaryKey(demandForecastVersionVO.getRollingVersionId());
        DemandVersionVO algorithmVO =
                demandVersionService.selectByPrimaryKey(demandForecastVersionVO.getAlgorithmVersionId());
        // 获取所有历史月份
        List<String> historyMonthList = DfpDateUtils.getPastMonthsIncludingCurrent();
        // 获取所有预测月份
        List<String> nextMonthList = DfpDateUtils.getNextElevenMonths();
        // 上一计划周期中最新版滚动预测
        String preVersionId = demandVersionService.selectPreVersionByType(VersionTypeEnum.CLEAN_FORECAST.getCode(),
                rollingVO.getPlanPeriod());
        List<String> cleanForecastCodeList = new ArrayList<>();
        cleanForecastCodeList.add(preVersionId);
        cleanForecastCodeList.add(rollingVO.getId());
        // 获取滚动预测版本数据
        List<CleanForecastDataVO> cleanForecastDataVOS = forecastDataService.selectByParams(ImmutableMap.of(
                "versionIdList", cleanForecastCodeList));
        // 根据主机厂分组
        Map<String, List<CleanForecastDataVO>> forecastGroup =
                cleanForecastDataVOS.stream().filter(k -> StringUtils.isNotBlank(k.getOemCode())).collect(Collectors
                        .groupingBy(CleanForecastDataVO::getOemCode));
        // 获取滚动预测详情
        List<CleanForecastDataDetailVO> forecastDataDetailVOS =
                forecastDataDetailService.selectByParams(ImmutableMap.of("cleanForecastDataIdList",
                        cleanForecastDataVOS.stream().map(CleanForecastDataVO::getId).collect(Collectors.toList())));
        Map<String, List<CleanForecastDataDetailVO>> forecastDataDetailGroup =
                forecastDataDetailVOS.stream().collect(Collectors.groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));

        // 获取实际发货数据
        List<WarehouseReleaseRecordVO> warehouseVOS =
                warehouseService.actualDelivery(DateUtils.stringToDate(historyMonthList.get(0), YEAR_MONTH_PATTERN),
                        DateUtils.stringToDate(nextMonthList.get(nextMonthList.size() - 1), YEAR_MONTH_PATTERN),
                        ProductTransportTypeEnum.ZZ.getCode());
        // 本厂编码+日期分组
        Map<String, BigDecimal> wareHouseMap = warehouseVOS.stream().collect(Collectors.toMap(k -> k.getItemCode() +
                        Constants.DELIMITER + DateUtils.dateToString(k.getCreationDate(), YEAR_MONTH_PATTERN),
                WarehouseReleaseRecordVO::getSumQty));

        // 获取上一计划周期一致性业务预测结果 用来计算历史业务预测值
        String preDemandForecastVersionCode =
                demandVersionService.selectPreVersionByType(VersionTypeEnum.CONSISTENCE_DEMAND_FORECAST.getCode(),
                        rollingVO.getPlanPeriod());
        // 获取一致性业务预测版本数据
        List<ConsistenceDemandForecastDataVO> consistenceVOS = new ArrayList<>();
        if (StringUtils.isNotBlank(preDemandForecastVersionCode)) {
            consistenceVOS = consistenceService.selectByParams(ImmutableMap.of("versionId",
                    preDemandForecastVersionCode));
        }
        // 根据主机厂编码分组
        Map<String, List<ConsistenceDemandForecastDataVO>> consistenceGroup =
                consistenceVOS.stream().filter(k -> StringUtils.isNotBlank(k.getOemCode())).collect(Collectors
                        .groupingBy(ConsistenceDemandForecastDataVO::getOemCode));

        // 获取一致性业务预测详情
        List<ConsistenceDemandForecastDataDetailVO> consistenceDetailVOS =
                consistenceDetailService.selectByParams(ImmutableMap.of("cleanForecastDataIdList",
                        consistenceVOS.stream().map(ConsistenceDemandForecastDataVO::getId).collect(Collectors.toList())));
        Map<String, List<ConsistenceDemandForecastDataDetailVO>> consistenceDetailGroup =
                consistenceDetailVOS.stream().collect(Collectors.groupingBy(ConsistenceDemandForecastDataDetailVO::getConsistenceDemandForecastDataId));

        // 上一计划周期中最新版算法预测
        String preAlgorithmVersionCode =
                demandVersionService.selectPreVersionByType(VersionTypeEnum.CLEAN_ALGORITHM.getCode(),
                        rollingVO.getPlanPeriod());
        List<String> algorithmCodeList = new ArrayList<>();
        algorithmCodeList.add(preAlgorithmVersionCode);
        algorithmCodeList.add(algorithmVO.getVersionCode());
        List<CleanAlgorithmDataVO> algorithmDataList = algorithmDataService.selectByParams(ImmutableMap.of(
                "versionIdList", algorithmCodeList));
        // 根据主机厂编码分组
        Map<String, List<CleanAlgorithmDataVO>> algorithmGroup =
                algorithmDataList.stream().collect(Collectors.groupingBy(CleanAlgorithmDataVO::getOemCode));
        // 月份汇总
        List<String> allMonth = new ArrayList<>();
        allMonth.addAll(historyMonthList);
        allMonth.addAll(nextMonthList);
        // 预测编制生成
        List<DemandForecastEstablishmentDTO> establishmentDTOList = new ArrayList<>();
        for (String oemCode : oemCodeList) {
            // 获取滚动预测 算法预测 一致性预测 主机厂下车型信息 并根据车型分组
            List<CleanForecastDataVO> gdDataList = forecastGroup.getOrDefault(oemCode, new ArrayList<>());
            Map<String, List<CleanForecastDataVO>> gdGroupByVehicle =
                    gdDataList.stream().collect(Collectors.groupingBy(CleanForecastDataVO::getVehicleModelCode));
            List<ConsistenceDemandForecastDataVO> yzDataList = consistenceGroup.getOrDefault(oemCode,
                    new ArrayList<>());
            Map<String, List<ConsistenceDemandForecastDataVO>> yzGroupByVehicle =
                    yzDataList.stream().collect(Collectors.groupingBy(ConsistenceDemandForecastDataVO::getVehicleModelCode));
            List<CleanAlgorithmDataVO> sfDataList = algorithmGroup.getOrDefault(oemCode, new ArrayList<>());
            Map<String, List<CleanAlgorithmDataVO>> sfGroupByVehicle =
                    sfDataList.stream().collect(Collectors.groupingBy(CleanAlgorithmDataVO::getVehicleModelCode));
            // 获取车型集合
            // fixme 待完善
            Set<String> vehicleModelCodeSet = new HashSet<>()/*getVehicleModelCodeList(gdDataList,yzDataList,
            sfDataList)*/;
            for (String vehicleModelCode : vehicleModelCodeSet) {
                // 获取到零件层
                List<CleanForecastDataVO> gdDataDetailList = gdGroupByVehicle.getOrDefault(vehicleModelCode,
                        new ArrayList<>());
                List<ConsistenceDemandForecastDataVO> yzDataDetailList =
                        yzGroupByVehicle.getOrDefault(vehicleModelCode, new ArrayList<>());
                List<CleanAlgorithmDataVO> sfDataDetailList = sfGroupByVehicle.getOrDefault(vehicleModelCode,
                        new ArrayList<>());
                // 根据零件转为map
                Map<String, BigDecimal> sfMap =
                        sfDataDetailList.stream().collect(Collectors.toMap(k -> k.getProductCode() + Constants.DELIMITER
                                        + DateUtils.dateToString(k.getForecastTime(), YEAR_MONTH_PATTERN),
                                CleanAlgorithmDataVO::getForecastQuantity));
                Map<String, CleanForecastDataVO> gdMap =
                        gdDataDetailList.stream().collect(Collectors.toMap(CleanForecastDataVO::getProductCode,
                                Function.identity(), (v1, v2) -> v1));
                Map<String, ConsistenceDemandForecastDataVO> yzMap =
                        yzDataDetailList.stream().collect(Collectors.toMap(ConsistenceDemandForecastDataVO::getProductCode,
                                Function.identity(), (v1, v2) -> v1));
                // 获取所有零件集合
                // fixme 待完善
                Set<String> productCodeSet = new HashSet<>()/*getProductCodeList(gdDataDetailList,yzDataDetailList,
                sfDataDetailList)*/;
                for (String productCode : productCodeSet) {
                    CleanForecastDataVO forecastDataVO = gdMap.get(productCode);
                    ConsistenceDemandForecastDataVO consistenceDemandForecastDataVO = yzMap.get(productCode);
                    Map<String, BigDecimal> forecastNumMap = getForecastNumMap(forecastDataDetailGroup, forecastDataVO);
                    Map<String, BigDecimal> consistenceNumMap = getConsistenceNumMap(consistenceDetailGroup,
                            consistenceDemandForecastDataVO);
                    for (String date : allMonth) {
                        DemandForecastEstablishmentDTO establishmentDTO = new DemandForecastEstablishmentDTO();
                        establishmentDTO.setForecastVersionId(demandForecastVersionVO.getParentVersionId());
                        establishmentDTO.setOemCode(oemCode);
                        establishmentDTO.setRiskLevel(oemRiskLevelMap.get(oemCode));
                        establishmentDTO.setVehicleModelCode(vehicleModelCode);
                        establishmentDTO.setProductCode(productCode);
                        establishmentDTO.setForecastTime(DateUtils.stringToDate(date, YEAR_MONTH_PATTERN));
                        establishmentDTO.setCustomerForecast(forecastNumMap.get(date));
                        establishmentDTO.setAlgorithmForecast(sfMap.get(productCode + Constants.DELIMITER + date));
                        if (historyMonthList.contains(date)) {
                            establishmentDTO.setDemandForecast(consistenceNumMap.get(date));
                        } else {
                            establishmentDTO.setDemandForecast(establishmentDTO.getCustomerForecast());
                        }
                        establishmentDTO.setDeliveryNum(wareHouseMap.get(productCode + Constants.DELIMITER + date));
                        establishmentDTO.setDateType(historyMonthList.contains(date) ?
                                DateTypeEnum.HISTORY.getCode() : DateTypeEnum.FORECAST.getCode());
                        establishmentDTOList.add(establishmentDTO);
                    }
                }
            }

        }
        // 物品相关字段补充
        List<String> productCodeList =
                establishmentDTOList.stream().map(DemandForecastEstablishmentDTO::getProductCode).collect(Collectors.toList());
        Map<String, NewProductStockPointVO> productStockPointVOMap = new HashMap<>()/*mdsFeign.selectByProductCode
        (productCodeList).stream().collect(Collectors.toMap(k -> k.getProductCode(), v -> v))*/;
        // 零件风险等级
        Map<String, PartRiskLevelVO> riskLevelVOMap = partRiskLevelService.selectByParams(ImmutableMap.of(
                "productCodeList", productCodeList)).stream().collect(Collectors.toMap(PartRiskLevelVO::getProductCode,
                Function.identity(), (v1, v2) -> v1));
        for (DemandForecastEstablishmentDTO establishmentDTO : establishmentDTOList) {
            String productCode = establishmentDTO.getProductCode();
            if (productStockPointVOMap.containsKey(productCode)) {
                establishmentDTO.setPartName(productStockPointVOMap.get(productCode).getProductName());
            }
            if (riskLevelVOMap.containsKey(productCode)) {
                establishmentDTO.setPartName(riskLevelVOMap.get(productCode).getMaterialRiskLevelDetailId());
            }
        }
        // 保存到编制
        establishmentService.doCreateBatch(establishmentDTOList);
    }

    /**
     * 获取一致预测时间数量map
     *
     * @param consistenceDetailGroup          一致预测详情数据分组
     * @param consistenceDemandForecastDataVO 一致性业务预测数据
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getConsistenceNumMap(Map<String, List<ConsistenceDemandForecastDataDetailVO>> consistenceDetailGroup,
                                                         ConsistenceDemandForecastDataVO consistenceDemandForecastDataVO) {
        if (consistenceDemandForecastDataVO == null
                || !consistenceDetailGroup.containsKey(consistenceDemandForecastDataVO.getId())) {
            return new HashMap<>();
        } else {
            return consistenceDetailGroup.get(consistenceDemandForecastDataVO.getId()).stream().collect(Collectors
                    .toMap(k -> DateUtils.dateToString(k.getForecastTime(), YEAR_MONTH_PATTERN),
                            ConsistenceDemandForecastDataDetailVO::getForecastQuantity));
        }
    }

    /**
     * 获取滚动预测时间数量map
     *
     * @param forecastDataDetailGroup 预测详情数据分组
     * @param forecastDataVO          预测数据
     * @return java.util.Map<java.lang.String, java.math.BigDecimal>
     */
    private Map<String, BigDecimal> getForecastNumMap(Map<String, List<CleanForecastDataDetailVO>> forecastDataDetailGroup,
                                                      CleanForecastDataVO forecastDataVO) {
        if (forecastDataVO == null || !forecastDataDetailGroup.containsKey(forecastDataVO.getId())) {
            return new HashMap<>();
        } else {
            return forecastDataDetailGroup.get(forecastDataVO.getId()).stream().collect(Collectors
                    .toMap(k -> DateUtils.dateToString(k.getForecastTime(), YEAR_MONTH_PATTERN),
                            v -> BigDecimal.valueOf(v.getForecastQuantity())));
        }
    }

    @Override
    public DemandForecastVersionVO selectForecastVersion(String versionCode) {
        return demandForecastVersionDao.selectForecastVersion(versionCode);
    }

    @Override
    public List<Map<String, String>> selectDistinctVersionCode(String publishStatus) {
        return demandForecastVersionDao.selectDistinctVersionCodes(publishStatus);
    }

    @Override
    public List<DemandForecastVersionVO> selectPublishVersionDetail() {
        List<DemandForecastVersionPO> demandForecastVersionPOS =
                demandForecastVersionDao.selectPublishVersionDetail(PublishStatusEnum.PUBLISHED.getCode());
        return DemandForecastVersionConvertor.INSTANCE.po2Vos(demandForecastVersionPOS);
    }

    @Override
    public Set<String> selectOemCodeListByVersionId(String versionId) {
        return demandForecastVersionDao.selectOemCodeListByVersionId(versionId);
    }

    @Override
    public List<LabelValue<String>> targetVersion(String planPeriod) {
        Map<String, Object> queryMap = new HashMap<>();
        if (StringUtils.isNotBlank(planPeriod)) {
            queryMap.put("planPeriod", planPeriod);
        }
        List<DemandForecastVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isEmpty(demandVersionVOS)) {
            return Lists.newArrayList();
        }
        return demandVersionVOS.stream().filter(k -> StringUtils.isBlank(k.getOemCode())
                        && StringUtils.isNotBlank(k.getParentVersionId()))
                .sorted(Comparator.comparing(DemandForecastVersionVO::getModifyTime, Comparator.reverseOrder()))
                .map(x -> new LabelValue<>(x.getVersionCode(), x.getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<LabelValue<String>> selectLastVersionCodes(List<String> planPeriods) {
        List<LabelValue<String>> result = Lists.newArrayList();
        List<Map<String, String>> demandVersionCodes = demandForecastVersionDao.selectLastVersionCodes(planPeriods);
        if (CollectionUtils.isEmpty(demandVersionCodes)) {
            return result;
        }
        demandVersionCodes.stream().forEach(versionCode -> {
            LabelValue<String> labelValue = new LabelValue<>();
            labelValue.setValue(versionCode.get("id"));
            labelValue.setLabel(versionCode.get("versionCode"));
            result.add(labelValue);
        });
        return result;
    }

    @Override
    public DemandForecastVersionVO selectVersionsNewByParams(Map<String, Object> extMap2) {
        return demandForecastVersionDao.selectVersionsNewByParams(extMap2);
    }

    /**
     * 获取目标主机厂数据
     *
     * @param versionCode 版本代码
     * @return java.util.List<java.lang.String>
     */
    public List<String> getOemCodeByVersionCode(String versionCode) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("versionCode", versionCode);
        // queryMap.put("creator", SystemHolder.getUserId());
        List<DemandForecastVersionVO> demandVersionVOS = this.selectByParams(queryMap);
        if (CollectionUtils.isNotEmpty(demandVersionVOS))
            return demandVersionVOS.stream().filter(k -> StringUtils.isNotBlank(k.getOemCode()))
                    .map(DemandForecastVersionVO::getVersionCode).collect(Collectors.toList());
        return null;
    }

    /**
     * 获取版本号
     *
     * @param versionCode 版本代码
     * @param planPeriod  计划周期
     * @param oemCodeList 主机厂代码列表
     * @return java.lang.String
     */
    private String getVersionCodeByPlanPeriod(String versionCode, String planPeriod, List<String> oemCodeList) {
        String newVersionCode;
        String prefix = DfpConstants.DEMAND_FORECAST_VERSION_CODE_PRE + planPeriod;
        if (StringUtils.isBlank(versionCode) || versionCode.length() < 5) {
            newVersionCode = DfpConstants.ORIGIN_DEMAND_VERSION_CODE;
        } else {
            Map<String, Object> queryMap = new HashMap<>();
            queryMap.put("planPeriod", planPeriod);
            queryMap.put("oemCodeList", oemCodeList);
            newVersionCode = demandForecastVersionDao.selectNewVersionCode(queryMap);
            if (StringUtils.isBlank(newVersionCode)) {
                newVersionCode = DfpConstants.ORIGIN_DEMAND_VERSION_CODE;
            } else {
                // 获取最新需求版本
                String lastFive = newVersionCode.substring(newVersionCode.length() - 5);
                try {
                    int number = Integer.parseInt(lastFive);
                    // 数字加1
                    number++;
                    // 格式化为五位数，不足五位补零
                    newVersionCode = String.format("%05d", number);
                } catch (NumberFormatException e) {
                    throw new BusinessException(e.getMessage());
                }
            }
        }
        return prefix + newVersionCode;
    }

    /**
     * 添加子节点
     *
     * @param childVersionGroup 子版本分组
     * @param demandVersionVO   需求版本
     */
    private void toDemandVersionTreeVO(Map<String, List<DemandForecastVersionVO>> childVersionGroup,
                                       DemandForecastVersionVO demandVersionVO) {
        if (childVersionGroup.containsKey(demandVersionVO.getId())) {
            List<DemandForecastVersionVO> demandVersionVOList = new ArrayList<>();
            for (DemandForecastVersionVO versionVO : childVersionGroup.get(demandVersionVO.getId())) {
                toDemandVersionTreeVO(childVersionGroup, versionVO);
                demandVersionVOList.add(versionVO);
            }
            demandVersionVOList.sort(Comparator.comparing(DemandForecastVersionVO::getCreateTime));
            demandVersionVO.setDemandVersionVOList(demandVersionVOList);
        }
    }

    @Override
    public String selectByRollingVersionId(String rollingVersionId) {
        List<DemandForecastVersionPO> demandForecastVersions =
                demandForecastVersionDao.selectByRollingVersionId(rollingVersionId);
        if (CollectionUtils.isNotEmpty(demandForecastVersions)) {
            if (demandForecastVersions.size() > 1) {
                throw new BusinessException("关联多版业务预测，无法跳转");
            }
            return demandForecastVersions.get(0).getId();
        } else {
            throw new BusinessException("没有关联到业务预测，无法跳转");
        }
    }

    @Override
    public void doCopyByCleanForecastData(String rollingVersionId) {
        log.info("需求预测数据同步开始");
        DemandVersionVO rollingDemandVersionVO = demandVersionService.selectByPrimaryKey(rollingVersionId);
        if (null == rollingDemandVersionVO) {
            throw new BusinessException("没有滚动需求版本");
        }
        //根据滚动需求版本查询关联业务预测版本
        String versionCode = rollingDemandVersionVO.getVersionCode();
        //根据滚动版本号查询业务预测版本
        List<DemandForecastVersionPO> demandForecastVersionPOS = demandForecastVersionDao.selectByRollingVersionId(rollingVersionId);
        if (CollectionUtils.isEmpty(demandForecastVersionPOS)) {
            log.info("关联业务预测版本流水号:{}", versionCode);
            throw new BusinessException("没有关联的业务预测版本");
        }
        log.info("关联:{}条需求预测版本，版本号为:{}", demandForecastVersionPOS.size(), demandForecastVersionPOS.get(0).getVersionCode());
        //查询用户权限下的产品编码
        List<NewProductStockPointVO> userProducrList = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of("orderPlanner", SystemHolder.getUserId()));
        List<String> productCodes = userProducrList.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productCodes)) {
            log.info("当前计划员无物料权限，无法进行需求预测同步操作");
            throw new BusinessException("当前计划员无物料权限，无法进行需求预测同步操作");
        }
        //获取编制数据
        Map<String, Object> params = new HashMap<>();
        params.put("forecastVersionId", demandForecastVersionPOS.get(0).getId());
        params.put("productCodeList", productCodes);
        List<DemandForecastEstablishmentPO> demandForecastEstablishmentPOS = establishmentDao.selectByParams(params);
        Map<String, DemandForecastEstablishmentPO> demandForecastEstablishmentPOSMapOfUnionKey = new HashMap<>();
        List<String> productCodeList = new ArrayList<>();
        List<String> oemCodeList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(demandForecastEstablishmentPOS)) {
            //按主机厂，产品，时间，需求类型分组
            demandForecastEstablishmentPOSMapOfUnionKey = demandForecastEstablishmentPOS.stream()
                    .collect(Collectors.toMap(item -> String.join("#", item.getOemCode(), item.getProductCode(), DateUtils.dateToString(item.getForecastTime()), item.getDemandCategory()),
                            Function.identity(), (k1, k2) -> k2));
        }
        // 组装滚动预测数量
        Map<String, Integer> cleanForecastQuantityMap = new HashMap<>();
        assembleCleanForecastQuantityMap(rollingVersionId,
                productCodeList, oemCodeList, ProductionDemandTypeEnum.OUTPUT_DEMAND.getCode(), cleanForecastQuantityMap);
        assembleCleanForecastQuantityMap(rollingVersionId,
                productCodeList, oemCodeList, ProductionDemandTypeEnum.PROJECT_DEMAND.getCode(), cleanForecastQuantityMap);

        Map<String, Object> productParams = new HashMap<>();
        productParams.put("productCodeList", productCodeList);
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(), productParams);

        Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode = new HashMap<>();
        if (CollectionUtils.isNotEmpty(newProductStockPointVOS)) {
            productStockPointVOMapOfProductCode = newProductStockPointVOS.stream().collect(Collectors.toMap(NewProductStockPointVO::getProductCode, Function.identity(), (k1, k2) -> k2));
        }

        List<DemandForecastEstablishmentPO> insertList = new ArrayList<>();
        List<DemandForecastEstablishmentPO> updateList = new ArrayList<>();

        //量产需求/项目需求
        doCopyByCleanForecastDataByDemand(cleanForecastQuantityMap, demandForecastEstablishmentPOSMapOfUnionKey,
                demandForecastVersionPOS, productStockPointVOMapOfProductCode, insertList, updateList);

        List<String> deleteList = new ArrayList<>();
        for (Map.Entry<String, DemandForecastEstablishmentPO> entry : demandForecastEstablishmentPOSMapOfUnionKey.entrySet()) {
            if (!cleanForecastQuantityMap.containsKey(entry.getKey())) {
                // 删除预测需求编制数据
                deleteList.add(entry.getValue().getId());
            }
        }
        log.info("需求版本数据同步，新增数据:{}条，修改数据:{}条，删除数据:{}条", insertList.size(), updateList.size(), deleteList);
        ThreadPoolExecutor threadPoolExecutor = CustomThreadPoolFactory.instance();
        List<CompletableFuture<Integer>> completableFutureList = new ArrayList<>();
        try {
			if (CollectionUtils.isNotEmpty(insertList)) {
			    com.google.common.collect.Lists.partition(insertList, 1000).forEach(item -> {
			    	CompletableFuture<Integer> insertCompletableFuture = CompletableFuture.supplyAsync(() -> establishmentDao.insertBatch(item), threadPoolExecutor);
			    	completableFutureList.add(insertCompletableFuture);
			    });
			}
			if (CollectionUtils.isNotEmpty(updateList)) {
			    com.google.common.collect.Lists.partition(updateList, 500).forEach(item -> {
			    	CompletableFuture<Integer> updateCompletableFuture = CompletableFuture.supplyAsync(() -> establishmentDao.updateBatch(item), threadPoolExecutor);
			    	completableFutureList.add(updateCompletableFuture);
			    });
			}
			completableFutureList.forEach( e -> {
				e.join();
			});
		} catch (Exception e) {
			log.error("多线程增量维护业务预测编制异常：", e);
		}finally {
			CustomThreadPoolFactory.closeOrShutdown(threadPoolExecutor);
		}
//        if (CollectionUtils.isNotEmpty(insertList)) {
//            com.google.common.collect.Lists.partition(insertList, 500).forEach(item -> establishmentDao.insertBatch(item));
//        }
//
//        if (CollectionUtils.isNotEmpty(updateList)) {
//            com.google.common.collect.Lists.partition(updateList, 500).forEach(item -> establishmentDao.updateBatch(item));
//        }

        if (CollectionUtils.isNotEmpty(deleteList)) {
            establishmentDao.deleteBatch(deleteList);
        }
    }

    public void doCopyByCleanForecastDataByDemand(Map<String, Integer> cleanForecastQuantityMap,
                                                  Map<String, DemandForecastEstablishmentPO> demandForecastEstablishmentPOSMapOfUnionKey,
                                                  List<DemandForecastVersionPO> demandForecastVersionPOS,
                                                  Map<String, NewProductStockPointVO> productStockPointVOMapOfProductCode,
                                                  List<DemandForecastEstablishmentPO> insertList,
                                                  List<DemandForecastEstablishmentPO> updateList) {
        for (String unionKey : cleanForecastQuantityMap.keySet()) {
            String[] split = unionKey.split("#");
            String oemCode = split[0];
            String productCode = split[1];
            String planPeriod = split[2];
            String demandCategory = split[3];
            DemandForecastEstablishmentPO demandForecastEstablishmentPO = demandForecastEstablishmentPOSMapOfUnionKey.get(unionKey);
            if (null == demandForecastEstablishmentPO) {
                //新增
                Integer cleanForecastQuantity = cleanForecastQuantityMap.get(unionKey);
                DemandForecastEstablishmentPO establishmentPO = new DemandForecastEstablishmentPO();
                establishmentPO.setForecastVersionId(demandForecastVersionPOS.get(0).getId());
                establishmentPO.setDemandCategory(demandCategory);
                establishmentPO.setOemCode(oemCode);
                establishmentPO.setProductCode(productCode);
                establishmentPO.setForecastTime(StringUtils.isBlank(planPeriod) ? null : DateUtils.stringToDate(planPeriod));
                if (productStockPointVOMapOfProductCode.containsKey(productCode)) {
                    establishmentPO.setVehicleModelCode(productStockPointVOMapOfProductCode.get(productCode).getVehicleModelCode());
                    // 取数位置直接获取本厂编码的【装车位置小类】
                    establishmentPO.setAccessPosition(productStockPointVOMapOfProductCode.get(productCode).getLoadingPositionSub());
                }
                BigDecimal customerQty = Objects.nonNull(cleanForecastQuantity) ? BigDecimal.valueOf(cleanForecastQuantity) : BigDecimal.ZERO;
                establishmentPO.setCustomerForecast(customerQty);
                establishmentPO.setAlgorithmForecast(BigDecimal.ZERO);
                establishmentPO.setDemandForecast(customerQty);
                BasePOUtils.insertFiller(establishmentPO);
                insertList.add(establishmentPO);
            } else {
                //更新
                //进行数据复制,滚动预测影响编制页面的这两个字段
                Integer cleanForecastQuantity = cleanForecastQuantityMap.get(unionKey);
                BigDecimal customerQty = Objects.nonNull(cleanForecastQuantity) ? BigDecimal.valueOf(cleanForecastQuantity) : BigDecimal.ZERO;
                demandForecastEstablishmentPO.setCustomerForecast(customerQty);
                demandForecastEstablishmentPO.setDemandCategory(demandCategory);
                demandForecastEstablishmentPO.setDemandForecast(customerQty);
                if (productStockPointVOMapOfProductCode.containsKey(productCode)) {
                    demandForecastEstablishmentPO.setVehicleModelCode(productStockPointVOMapOfProductCode.get(productCode).getVehicleModelCode());
                    // 取数位置直接获取本厂编码的【装车位置小类】
                    demandForecastEstablishmentPO.setAccessPosition(productStockPointVOMapOfProductCode.get(productCode).getLoadingPositionSub());
                }
                BasePOUtils.updateFiller(demandForecastEstablishmentPO);
                updateList.add(demandForecastEstablishmentPO);
            }
        }
    }

    /**
     * 组装滚动预测数量Map
     *
     * @param versionId
     * @param productCodeList
     * @param oemCodeList
     * @param demandCategory
     * @param cleanForecastQuantityMap
     * @return
     */
    private Map<String, Integer> assembleCleanForecastQuantityMap(String versionId, List<String> productCodeList,
                                                                  List<String> oemCodeList, String demandCategory,
                                                                  Map<String, Integer> cleanForecastQuantityMap) {
        List<NewProductStockPointVO> newProductStockPointVOS = newMdsFeign.selectProductStockPointByParams(SystemHolder.getScenario(),
                ImmutableMap.of("orderPlanner", SystemHolder.getUserId()));
        List<String> productCodes = newProductStockPointVOS.stream().map(NewProductStockPointVO::getProductCode).distinct().collect(Collectors.toList());
        Map<String, Object> params = new HashMap<>(2);
        params.put("versionId", versionId);
        params.put("demandCategory", demandCategory);
        params.put("productCodeList", productCodes);
        List<CleanForecastDataVO> dataList = cleanForecastDataService.selectByParams(params).stream()
                .filter(item -> StringUtils.isNotBlank(item.getOemCode())
                        && StringUtils.isNotBlank(item.getProductCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dataList)) {
            return cleanForecastQuantityMap;
        }
        List<String> dataIds = dataList.stream().map(CleanForecastDataVO::getId).collect(Collectors.toList());
        Map<String, List<CleanForecastDataDetailVO>> detailMap =
                cleanForecastDataDetailService.selectByParams(ImmutableMap
                        .of("cleanForecastDataIdList", dataIds)).stream().filter(item -> item.getForecastTime() != null).collect(Collectors
                        .groupingBy(CleanForecastDataDetailVO::getCleanForecastDataId));

        //dataList先根据oemCode分组，再根据productCode分组
        Map<String, Map<String, List<CleanForecastDataVO>>> dataMap = dataList.stream().collect(Collectors
                .groupingBy(CleanForecastDataVO::getOemCode, Collectors.groupingBy(CleanForecastDataVO::getProductCode)));

        for (Map.Entry<String, Map<String, List<CleanForecastDataVO>>> entry : dataMap.entrySet()) {
            oemCodeList.add(entry.getKey());
            for (Map.Entry<String, List<CleanForecastDataVO>> dataEntry : entry.getValue().entrySet()) {
                String productCode = dataEntry.getKey();
                productCodeList.add(productCode);
                List<String> subDataIds =
                        dataEntry.getValue().stream().map(CleanForecastDataVO::getId).collect(Collectors.toList());
                List<String> existList =
                        detailMap.keySet().stream().filter(subDataIds::contains).collect(Collectors.toList());
                if (com.yhl.platform.common.utils.CollectionUtils.isEmpty(existList)) {
                    continue;
                }

                Map<String, Integer> quantityMap =
                        existList.stream()
                                .map(detailMap::get)
                                .flatMap(List::stream)
                                .collect(Collectors.groupingBy(
                                        item -> DateUtils.dateToString(item.getForecastTime()),
                                        Collectors.summingInt(item -> Optional.ofNullable(item.getForecastQuantity()).orElse(0))
                                ));
                for (Map.Entry<String, Integer> quantityEntry : quantityMap.entrySet()) {
                    String planPeriod = quantityEntry.getKey();
                    Integer quantity = quantityEntry.getValue();
                    cleanForecastQuantityMap.put(String.join("#", entry.getKey(), productCode, planPeriod, demandCategory), quantity);
                }
            }
        }
        return cleanForecastQuantityMap;
    }

    @Override
    public DemandForecastVersionVO selectLastVersionByPlanPeriod(String planPeriod) {
        DemandForecastVersionPO demandForecastVersionPO = demandForecastVersionDao.selectLastVersionByPlanPeriod(planPeriod);
        if (null != demandForecastVersionPO) {
            return DemandForecastVersionConvertor.INSTANCE.po2Vo(demandForecastVersionPO);
        }
        return null;
    }

    @Override
    public String selectLatestVersionId() {
        return demandForecastVersionDao.selectLatestVersionId();
    }

}