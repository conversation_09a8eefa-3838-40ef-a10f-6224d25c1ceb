---
apiVersion: v1
data:
  ACCESS_TOKEN: default_token
  MYSQL_URL: ***********************************************************************************************************************************
  MYSQL_USERNAME: bpim
  MYSQL_PASSWORD: BpiqbIm(yy%sr4RV=!
kind: ConfigMap
metadata:
  name: xxl-job-admin-cm
  namespace: bpim-xxx-namespace
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bpim-xxx-service
  namespace: bpim-xxx-namespace
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bpim-xxx-service
  template:
    metadata:
      name: bpim-xxx-service
      labels:
        app: bpim-xxx-service
        tier: backend
      namespace: bpim-xxx-namespace
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values: [ "bpim-xxx-service" ]
                  - key: tier
                    operator: In
                    values: [ "backend" ]
              topologyKey: "kubernetes.io/hostname"
      containers:
        - env:
            - name: MYSQL_URL
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_URL
                  name: xxl-job-admin-cm
                  optional: false
            - name: MYSQL_USERNAME
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_USERNAME
                  name: xxl-job-admin-cm
                  optional: false
            - name: MYSQL_PASSWORD
              valueFrom:
                configMapKeyRef:
                  key: MYSQL_PASSWORD
                  name: xxl-job-admin-cm
                  optional: false
          name: bpim-xxx-service
          image: bpim-xxx-image
          resources:
            requests:
              memory: "2Gi"
              cpu: "1"
            limits:
              memory: "4Gi"
              cpu: "2"
          imagePullPolicy: Always
          ports:
            - name: server-port
              containerPort: 8080
      volumes:
        - name: sw-agent
          emptyDir: { }
        - name: bpim-data-volume
          persistentVolumeClaim:
            claimName: bpim-xxx-pvc
      imagePullSecrets:
        - name: harbor-registry
      terminationGracePeriodSeconds: 240
---
apiVersion: v1
kind: Service
metadata:
  name: bpim-xxx-service
  namespace: bpim-xxx-namespace
  labels:
    app: bpim-xxx-service
spec:
  ports:
    - port: 8080
      name: server
      targetPort: 8080
  selector:
    app: bpim-xxx-service