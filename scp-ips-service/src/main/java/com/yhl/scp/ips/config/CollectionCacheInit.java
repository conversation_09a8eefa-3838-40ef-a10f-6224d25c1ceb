 package com.yhl.scp.ips.config;

import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.enums.TenantCodeEnum;
import com.yhl.scp.ips.collection.service.CollectionManagementService;
import com.yhl.scp.ips.collection.service.CollectionValueService;
import com.yhl.scp.ips.collection.vo.CollectionManagementVO;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.common.constants.IpsConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 初始化字典表信息至缓存
 */
@Component
@Slf4j
public class CollectionCacheInit implements ApplicationRunner {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private CollectionManagementService collectionManagementService;

    @Resource
    private CollectionValueService collectionValueService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("加载字典表信息至缓存");
        // 查询字典表的code信息
        List<CollectionManagementVO> managements = Optional.ofNullable(collectionManagementService.selectByParams(MapUtil.newHashMap())).orElse(Lists.newArrayList());
        // 查询字典表的value信息
        List<CollectionValueVO> values = Optional.ofNullable(collectionValueService.selectByParams(MapUtil.newHashMap())).orElse(Lists.newArrayList());
        Map<String, List<CollectionValueVO>> valueMap = values.stream().collect(Collectors.groupingBy(CollectionValueVO::getCollectionId));
        for (CollectionManagementVO managementVO : managements) {
            String tenantId = managementVO.getTenantId();
            String collectionCode = managementVO.getCollectionCode();
            List<CollectionValueVO> collectionValues = valueMap.get(managementVO.getId());
            if (CollectionUtils.isEmpty(collectionValues)) {
                continue;
            }
            String key = String.format(IpsConstants.COLLECTION_KEY_PREFIX, tenantId, collectionCode);
            redisUtil.set(key, collectionValues);
        }
        log.info("加载字典表信息至缓存完成");
    }

//    /**
//     * 重新加载字典表值到缓存
//     *
//     * @param collectionId
//     * @return
//     */
//    public BaseResponse<Void> loadCollectionValues(String collectionId) {
//        CollectionManagementVO managementVO =
//                collectionManagementService.selectByPrimaryKey(collectionId);
//        if (Objects.isNull(managementVO)) {
//            return BaseResponse.error("字典表不存在");
//        }
//        String key =
//                String.format(
//                        IpsConstants.COLLECTION_KEY_PREFIX,
//                        managementVO.getTenantId(),
//                        managementVO.getCollectionCode());
//        Map<String, Object> valueQueryMap = MapUtil.newHashMap();
//        valueQueryMap.put("collectionId", collectionId);
//        List<CollectionValueVO> values =
//                Optional.ofNullable(collectionValueService.selectByParams(valueQueryMap))
//                        .orElse(Lists.newArrayList());
//        if (CollectionUtils.isEmpty(values)) {
//            redisUtil.delete(key);
//            return BaseResponse.error("字典表值不存在");
//        }
//        redisUtil.set(key, values);
//        return BaseResponse.success();
//    }

    /**
     * 删除字典表缓存
     *
     * @param collectionIds
     */
    public void deleteByCollectionIds(List<String> collectionIds) {
        if (CollectionUtils.isEmpty(collectionIds)) {
            return;
        }
        for (String collectionId : collectionIds) {
            if (StringUtils.isBlank(collectionId)) {
                continue;
            }
            CollectionManagementVO managementVO = collectionManagementService.selectByPrimaryKey(collectionId);
            if (Objects.isNull(managementVO)) {
                continue;
            }
            String key = String.format(IpsConstants.COLLECTION_KEY_PREFIX, managementVO.getTenantId(), managementVO.getCollectionCode());
            redisUtil.delete(key);
        }
    }

    /**
     * 删除字典表缓存
     *
     * @param collectionValueIds
     */
    public void deleteByCollectionValueIds(List<String> collectionValueIds) {
        if (CollectionUtils.isEmpty(collectionValueIds)) {
            return;
        }
        for (String collectionValueId : collectionValueIds) {
            if (StringUtils.isBlank(collectionValueId)) {
                continue;
            }
            CollectionValueVO collectionValueVO = collectionValueService.selectByPrimaryKey(collectionValueId);
            if (Objects.isNull(collectionValueVO)) {
                continue;
            }
            CollectionManagementVO managementVO = collectionManagementService.selectByPrimaryKey(collectionValueVO.getCollectionId());
            if (Objects.isNull(managementVO)) {
                continue;
            }
            String key = String.format(IpsConstants.COLLECTION_KEY_PREFIX, managementVO.getTenantId(), managementVO.getCollectionCode());
            redisUtil.delete(key);
        }
    }

    /**
     * 获取字典表值
     *
     * @param collectionCode
     * @return
     */
    public BaseResponse<List<CollectionValueVO>> getByCollectionCode(String collectionCode) {
        String tenantId = SystemHolder.getTenantId();
        if (StringUtils.isBlank(tenantId)) {
            // 设置默认租户
            tenantId = TenantCodeEnum.FYQB.getCode();
        }
        String key = String.format(IpsConstants.COLLECTION_KEY_PREFIX, tenantId, collectionCode);
        if (redisUtil.hasKey(key)) {
            Object cachedData = redisUtil.get(key);
            if (cachedData instanceof List<?>) {
                List<?> rawData = (List<?>) cachedData;
                if (!rawData.isEmpty() && rawData.get(0) instanceof CollectionValueVO) {
                    // 已经是 CollectionValueVO 列表，可以直接返回
                    return BaseResponse.success((List<CollectionValueVO>) cachedData);
                } else {
                    // 是 LinkedHashMap 列表，需要转换为 CollectionValueVO
                    ObjectMapper objectMapper = new ObjectMapper();
                    List<CollectionValueVO> convertedList = rawData.stream()
                            .map(obj -> objectMapper.convertValue(obj, CollectionValueVO.class))
                            .collect(Collectors.toList());
                    return BaseResponse.success(convertedList);
                }
            }
        }
        Map<String, Object> params = new HashMap<>(4);
        params.put("collectionCode", collectionCode);
        params.put("tenantId", SystemHolder.getTenantId());
        List<CollectionManagementVO> collectionManagements = collectionManagementService.selectByParams(params);
        if (CollectionUtils.isEmpty(collectionManagements)) {
            return BaseResponse.error("字典表不存在");
        }
        String collectionId = collectionManagements.get(0).getId();
        List<CollectionValueVO> collectionValueVOList = collectionValueService.selectByParams(ImmutableMap.of("collectionId", collectionId));
        if (CollectionUtils.isNotEmpty(collectionManagements)) {
            redisUtil.set(key, collectionValueVOList);
        }
        return BaseResponse.success(collectionValueVOList);
    }
}
