package com.yhl.scp.ips.requestLog.service.impl;

import com.yhl.scp.ips.requestLog.dao.RequestLogRepository;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
public class RequestLogServiceImpl implements RequestLogService {

    @Resource
    private RequestLogRepository requestLogRepository;

    @Override
    public void saveData(RequestLogDTO requestLogDTO) {
        requestLogRepository.save(requestLogDTO);
    }
}
