package com.yhl.scp.ips.utils;

import com.wf.captcha.ArithmeticCaptcha;
import com.wf.captcha.ChineseCaptcha;
import com.wf.captcha.GifCaptcha;
import com.wf.captcha.SpecCaptcha;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SnowIdUtils;
import com.yhl.platform.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.IOException;

/**
 * <code>AuthCodeUtils</code>
 * <p>
 * 图片验证码工具类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022-03-31 12:37:00
 */
@Slf4j
@Component
public class AuthCodeUtils {

    @Resource
    private RedisUtil redisUtil;

    /**
     * redis的key值的前缀
     */
    private static final String KEY_PREFIX = "AMS";


    /**
     * 默认缓存时间为60秒
     */
    private static final int CACHE_TIME = 60;

    /**
     * 图片验证码，宽度
     */
    private static final int IMAGE_WIDTH = 120;

    /**
     * 图片验证码，高度
     */
    private static final int IMAGE_HIGH = 40;

    /**
     * 生成英文与数字动态验证码
     */
    public void createPhotoGifCode(HttpServletResponse response, HttpSession session) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        GifCaptcha captcha = new GifCaptcha(IMAGE_WIDTH, IMAGE_HIGH);
        // 生成字符的数量
        captcha.setLen(4);
        // 获取生成结果
        String captchaString = captcha.text();
        // 缓存生成验证码的结果
        cacheResult(captchaString, session);
        captcha.out(outputStream);
    }

    /**
     * 生成英文与数字验证码
     */
    public void createPhotoCode(HttpServletResponse response, HttpSession session) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        SpecCaptcha captcha = new SpecCaptcha(IMAGE_WIDTH, IMAGE_HIGH);
        // 生成字符的数量
        captcha.setLen(4);
        // 获取生成结果
        String captchaString = captcha.text();
        // 缓存生成验证码的结果
        cacheResult(captchaString, session);
        captcha.out(outputStream);
    }

    /**
     * 生成中文验证码
     */
    public void createChineseCode(HttpServletResponse response, HttpSession session) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        ChineseCaptcha captcha = new ChineseCaptcha(IMAGE_WIDTH, IMAGE_HIGH);
        // 生成字符的数量
        captcha.setLen(4);
        // 获取生成结果
        String captchaString = captcha.text();
        // 缓存生成验证码的结果
        cacheResult(captchaString, session);
        captcha.out(outputStream);
    }


    /**
     * 生成算术验证码，数字加减乘除
     */
    public void createArithmeticCode(HttpServletResponse response, HttpSession session) throws IOException {
        ServletOutputStream outputStream = response.getOutputStream();
        // 算术验证码 数字加减乘除. 建议2位运算就行，位数太长计算太复杂
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(IMAGE_WIDTH, IMAGE_HIGH);
        // 生成字符的数量
        captcha.setLen(2);
        // 获取生成结果
        String captchaString = captcha.text();
        // 缓存生成验证码的结果
        cacheResult(captchaString, session);
        captcha.out(outputStream);
    }


    /**
     * 缓存图片运算的结果
     *
     * @param captchaString 图片运算生成的结果
     * @param session       session
     */
    private void cacheResult(String captchaString, HttpSession session) {
        String snowId = null;
        Object snowIdObject = session.getAttribute("snowId");
        if (snowIdObject != null) snowId = snowIdObject.toString();
        if (StringUtils.isEmpty(snowId)) snowId = SnowIdUtils.createId();
        log.info("snowId ================" + snowId);
        String key = KEY_PREFIX + snowId;
        // 将生成的验证码存入redis并设置过期时间（单位为秒）
        redisUtil.set(key, captchaString.toUpperCase(), CACHE_TIME);
        log.info("captchaString =====" + captchaString);
        session.setAttribute("snowId", snowId);
    }

    /**
     * 验证传入的验证码与缓存中的验证码是否一致
     *
     * @param session  session
     * @param authCode 用户输入的验证码
     * @return BaseResponse
     */
    public BaseResponse verificationCode(HttpSession session, String authCode) {
        // 第一次刷新页面登录获取为空
        String snowId = null;
        if (session.getAttribute("snowId") != null) {
            log.info("snowId是：" + session.getAttribute("snowId").toString());
            snowId = session.getAttribute("snowId").toString();
        }

        String key = KEY_PREFIX + snowId;
        log.info("get key is " + key);
        log.info("get snowId is " + snowId);
        Object cacheObject = redisUtil.get(key);

        if (null == cacheObject) {
            session.invalidate();
            return BaseResponse.error("验证码失效，请重新输入！");
        }

        String upperCaseAuthCode = authCode.toUpperCase();
        if (upperCaseAuthCode.equals(cacheObject.toString())) {
            // 检验成功后，将对应的缓存删除，保证只能正确使用一次
            redisUtil.delete(key);
            // 校验成功后，将对应的session信息删除
            session.removeAttribute(snowId);
            return BaseResponse.success("验证码正确");
        }
        session.invalidate();
        return BaseResponse.error("验证码错误，请重新输入！");
    }

}
