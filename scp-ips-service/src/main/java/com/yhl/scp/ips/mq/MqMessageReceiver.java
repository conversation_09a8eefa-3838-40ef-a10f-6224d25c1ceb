package com.yhl.scp.ips.mq;

import com.alibaba.fastjson.JSONObject;
import com.yhl.scp.ips.conf.dto.UrlTableDTO;
import com.yhl.scp.ips.conf.service.UrlTableService;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.log.service.DataChangeRecordService;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import com.yhl.scp.ips.requestLog.service.RequestLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.ExchangeTypes;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */

@Component
@Slf4j
public class MqMessageReceiver {

    @Resource
    private DataChangeRecordService dataChangeRecordService;

    @Resource
    private UrlTableService urlTableService;

    @Resource
    private RequestLogService requestLogService;

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConstants.DATA_RECORD_CHANGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = MqConstants.DATA_RECORD_CHANGE_ROUTING_KEY))
    public void receiveDataRecordChangeMessage(String data) {
        try {
            log.info("接收到RabbitMQ数据（数据变更）消息：{}", data);
            dataChangeRecordService.handle(data);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConstants.URL_TABLE_CHANGE_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = MqConstants.URL_TABLE_ROUTING_KEY))
    public void receiveUrlTableMessage(String data) {
        try {
            log.info("接收到RabbitMQ数据（url-table初始化）消息：{}", data);
            if (StringUtils.isBlank(data)) {
                return;
            }
            List<UrlTableDTO> urlTableDTOS = JSONObject.parseArray(data, UrlTableDTO.class);
            urlTableService.handleUrlTables(urlTableDTOS);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @RabbitListener(bindings = @QueueBinding(value = @Queue(value = MqConstants.REQUEST_LOG_QUEUE, ignoreDeclarationExceptions = "true"), exchange = @Exchange(value = MqConstants.BPIM_EVENT_EXCHANGE, ignoreDeclarationExceptions = "true", type = ExchangeTypes.TOPIC), key = MqConstants.REQUEST_LOG_ROUTING_KEY))
    public void receiveRequestLogMessage(String data) {
        try {
            log.info("接收到RabbitMQ数据（request-log）消息：{}", data);
            if (StringUtils.isBlank(data)) {
                return;
            }
            RequestLogDTO requestLogDTO = JSONObject.parseObject(data, RequestLogDTO.class);
            requestLogService.saveData(requestLogDTO);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}

