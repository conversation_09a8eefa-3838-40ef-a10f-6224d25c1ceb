package com.yhl.scp.ips.api.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.biz.common.enums.ObjectTypeEnum;
import com.yhl.scp.ips.api.convertor.ExtApiSyncCtrlConvertor;
import com.yhl.scp.ips.api.domain.entity.ExtApiSyncCtrlDO;
import com.yhl.scp.ips.api.domain.service.ExtApiSyncCtrlDomainService;
import com.yhl.scp.ips.api.dto.ExtApiSyncCtrlDTO;
import com.yhl.scp.ips.api.infrastructure.dao.ExtApiConfigDao;
import com.yhl.scp.ips.api.infrastructure.dao.ExtApiSyncCtrlDao;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiConfigPO;
import com.yhl.scp.ips.api.infrastructure.po.ExtApiSyncCtrlPO;
import com.yhl.scp.ips.api.service.ExtApiSyncCtrlService;
import com.yhl.scp.ips.api.vo.ExtApiSyncCtrlVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@Service
public class ExtApiSyncCtrlServiceImpl extends AbstractService implements ExtApiSyncCtrlService {

    @Resource
    private ExtApiSyncCtrlDao extApiSyncCtrlDao;

    @Resource
    private ExtApiSyncCtrlDomainService extApiSyncCtrlDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private ExtApiConfigDao extApiConfigDao;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(ExtApiSyncCtrlDTO extApiSyncCtrlDTO) {
        // 0.数据转换
        ExtApiSyncCtrlDO extApiSyncCtrlDO = ExtApiSyncCtrlConvertor.INSTANCE.dto2Do(extApiSyncCtrlDTO);
        ExtApiSyncCtrlPO extApiSyncCtrlPO = ExtApiSyncCtrlConvertor.INSTANCE.dto2Po(extApiSyncCtrlDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiSyncCtrlDomainService.validation(extApiSyncCtrlDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(extApiSyncCtrlPO);
        extApiSyncCtrlDao.insert(extApiSyncCtrlPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(ExtApiSyncCtrlDTO extApiSyncCtrlDTO) {
        // 0.数据转换
        ExtApiSyncCtrlDO extApiSyncCtrlDO = ExtApiSyncCtrlConvertor.INSTANCE.dto2Do(extApiSyncCtrlDTO);
        ExtApiSyncCtrlPO extApiSyncCtrlPO = ExtApiSyncCtrlConvertor.INSTANCE.dto2Po(extApiSyncCtrlDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        extApiSyncCtrlDomainService.validation(extApiSyncCtrlDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(extApiSyncCtrlPO);
        extApiSyncCtrlDao.update(extApiSyncCtrlPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<ExtApiSyncCtrlDTO> list) {
        List<ExtApiSyncCtrlPO> newList = ExtApiSyncCtrlConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        extApiSyncCtrlDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<ExtApiSyncCtrlDTO> list) {
        List<ExtApiSyncCtrlPO> newList = ExtApiSyncCtrlConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        extApiSyncCtrlDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return extApiSyncCtrlDao.deleteBatch(idList);
        }
        return extApiSyncCtrlDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public ExtApiSyncCtrlVO selectByPrimaryKey(String id) {
        ExtApiSyncCtrlPO po = extApiSyncCtrlDao.selectByPrimaryKey(id);
        return ExtApiSyncCtrlConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "EXT_API_SYNC_CTRL")
    public List<ExtApiSyncCtrlVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "EXT_API_SYNC_CTRL")
    public List<ExtApiSyncCtrlVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<ExtApiSyncCtrlVO> dataList = extApiSyncCtrlDao.selectByCondition(sortParam, queryCriteriaParam);
        ExtApiSyncCtrlServiceImpl target = springBeanUtils.getBean(ExtApiSyncCtrlServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<ExtApiSyncCtrlVO> selectByParams(Map<String, Object> params) {
        List<ExtApiSyncCtrlPO> list = extApiSyncCtrlDao.selectByParams(params);
        return ExtApiSyncCtrlConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<ExtApiSyncCtrlVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public List<ExtApiSyncCtrlVO> queryData(Pagination pagination, Map<String, Object> params) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<ExtApiSyncCtrlPO> list = extApiSyncCtrlDao.selectByParams(params);
        List<ExtApiSyncCtrlVO> extApiSyncCtrlVOS = ExtApiSyncCtrlConvertor.INSTANCE.po2Vos(list);
        //获取查询数据的接口id集合
        List<String> ids = extApiSyncCtrlVOS.stream()
                .map(ExtApiSyncCtrlVO::getApiConfigId).distinct().collect(Collectors.toList());
        List<ExtApiConfigPO> extApiConfigPOS = extApiConfigDao.selectByPrimaryKeys(ids);
        //按照id分类
        Map<String, List<ExtApiConfigPO>> collect =
                extApiConfigPOS.stream().collect(Collectors.groupingBy(ExtApiConfigPO::getId));
        extApiSyncCtrlVOS.stream().forEach(item->{
            if (collect.containsKey(item.getApiConfigId())){
                ExtApiConfigPO extApiConfigPO = collect.get(item.getApiConfigId()).get(0);
                item.setApiCategory(extApiConfigPO.getApiCategory());
                item.setApiName(extApiConfigPO.getApiName());
            }
        });
        return extApiSyncCtrlVOS;
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.EXT_API_SYNC_CTRL.getCode();
    }

    @Override
    public List<ExtApiSyncCtrlVO> invocation(List<ExtApiSyncCtrlVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
