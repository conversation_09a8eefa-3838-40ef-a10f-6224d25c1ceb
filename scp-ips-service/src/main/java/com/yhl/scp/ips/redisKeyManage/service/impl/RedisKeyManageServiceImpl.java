package com.yhl.scp.ips.redisKeyManage.service.impl;

import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.ips.common.SystemHolder;
import com.yhl.scp.ips.enums.ObjectTypeEnum;
import com.yhl.scp.ips.redisKeyManage.convertor.RedisKeyManageConvertor;
import com.yhl.scp.ips.redisKeyManage.domain.entity.RedisKeyManageDO;
import com.yhl.scp.ips.redisKeyManage.domain.service.RedisKeyManageDomainService;
import com.yhl.scp.ips.redisKeyManage.dto.RedisKeyManageDTO;
import com.yhl.scp.ips.redisKeyManage.infrastructure.dao.RedisKeyManageDao;
import com.yhl.scp.ips.redisKeyManage.infrastructure.po.RedisKeyManagePO;
import com.yhl.scp.ips.redisKeyManage.service.RedisKeyManageService;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageDetailVO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import com.yhl.scp.ips.utils.BasePOUtils;
import io.seata.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.data.redis.connection.DataType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <code>RedisKeyManageServiceImpl</code>
 * <p>
 * redis key管理应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:12
 */
@Slf4j
@Service
public class RedisKeyManageServiceImpl extends AbstractService implements RedisKeyManageService {

    @Resource
    private RedisKeyManageDao redisKeyManageDao;

    @Resource
    private RedisKeyManageDomainService redisKeyManageDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(RedisKeyManageDTO redisKeyManageDTO) {
        // 0.数据转换
        RedisKeyManageDO redisKeyManageDO = RedisKeyManageConvertor.INSTANCE.dto2Do(redisKeyManageDTO);
        RedisKeyManagePO redisKeyManagePO = RedisKeyManageConvertor.INSTANCE.dto2Po(redisKeyManageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        redisKeyManageDomainService.validation(redisKeyManageDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(redisKeyManagePO);
        redisKeyManageDao.insert(redisKeyManagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(RedisKeyManageDTO redisKeyManageDTO) {
        // 0.数据转换
        RedisKeyManageDO redisKeyManageDO = RedisKeyManageConvertor.INSTANCE.dto2Do(redisKeyManageDTO);
        RedisKeyManagePO redisKeyManagePO = RedisKeyManageConvertor.INSTANCE.dto2Po(redisKeyManageDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        redisKeyManageDomainService.validation(redisKeyManageDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(redisKeyManagePO);
        redisKeyManageDao.update(redisKeyManagePO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<RedisKeyManageDTO> list) {
        List<RedisKeyManagePO> newList = RedisKeyManageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        redisKeyManageDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<RedisKeyManageDTO> list) {
        List<RedisKeyManagePO> newList = RedisKeyManageConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        redisKeyManageDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return redisKeyManageDao.deleteBatch(idList);
        }
        return redisKeyManageDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public RedisKeyManageVO selectByPrimaryKey(String id) {
        RedisKeyManagePO po = redisKeyManageDao.selectByPrimaryKey(id);
        return RedisKeyManageConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "REDIS_KEY_MANAGE")
    public List<RedisKeyManageVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        List<RedisKeyManageVO> redisKeyManages = this.selectByCondition(sortParam, queryCriteriaParam);
        if (CollectionUtils.isEmpty(redisKeyManages)) {
            return redisKeyManages;
        }
        for (RedisKeyManageVO redisKeyManage : redisKeyManages) {
            String globbingKey = redisKeyManage.getGlobbingKey();
            if (!YesOrNoEnum.YES.getCode().equals(globbingKey)) {
                //固定key
                Boolean hasKey = redisTemplate.hasKey(redisKeyManage.getConfigCode());
                redisKeyManage.setExistKey(hasKey ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
                Long expire = redisTemplate.getExpire(redisKeyManage.getConfigCode());
                Object keyResult = getValueByConfigCode(redisKeyManage.getConfigCode());
                redisKeyManage.setKeyResult(keyResult);
                redisKeyManage.setExpireTime(expire);
            }
        }
        return redisKeyManages;
    }

    @Override
    @Expression(value = "REDIS_KEY_MANAGE")
    public List<RedisKeyManageVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<RedisKeyManageVO> dataList = redisKeyManageDao.selectByCondition(sortParam, queryCriteriaParam);
        RedisKeyManageServiceImpl target = springBeanUtils.getBean(RedisKeyManageServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<RedisKeyManageVO> selectByParams(Map<String, Object> params) {
        List<RedisKeyManagePO> list = redisKeyManageDao.selectByParams(params);
        return RedisKeyManageConvertor.INSTANCE.po2Vos(list);
    }

    @Override
    public List<RedisKeyManageVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public BaseResponse<Void> removeKey(String id) {
        RedisKeyManageVO manageVO = this.selectByPrimaryKey(id);
        if (Objects.isNull(manageVO)) {
            return BaseResponse.error("数据不存在");
        }
        String configCode = manageVO.getConfigCode();
        if (StringUtils.isBlank(configCode)) {
            return BaseResponse.error("redis key不存在");
        }
        String userId = SystemHolder.getUserId();
        String responsibleUser = manageVO.getResponsibleUser();
        if (StringUtils.isNotBlank(responsibleUser) && !responsibleUser.contains(userId)) {
            return BaseResponse.error("没有权限进行数据清除");
        }
        String patternKey = getPatternKey(configCode);
        Set<String> keys = redisTemplate.keys(patternKey);
        if (CollectionUtils.isEmpty(keys)) {
            return BaseResponse.error("数据不存在");
        }
        for (String key : keys) {
            if (redisTemplate.hasKey(key)) {
                redisTemplate.delete(key);
            }
        }
        return BaseResponse.success("数据清除成功");
    }

    @Override
    public BaseResponse<Void> removeKeyByConfigCode(String configCode) {
        redisTemplate.delete(configCode);
        return BaseResponse.success("key值删除成功");
    }

    @Override
    public BaseResponse<List<RedisKeyManageDetailVO>> getDetailByConfigCode(String configCode) {
        //通配符key
        String patternKey = getPatternKey(configCode);
        Set<String> keys = redisTemplate.keys(patternKey);
        List<RedisKeyManageDetailVO> details = Lists.newArrayList();
        for (String key : keys) {
            RedisKeyManageDetailVO detailVO = new RedisKeyManageDetailVO();
            detailVO.setConfigCode(key);
            Boolean hasKey = redisTemplate.hasKey(key);
            detailVO.setExistKey(hasKey ? YesOrNoEnum.YES.getCode() : YesOrNoEnum.NO.getCode());
            Long expire = redisTemplate.getExpire(key);
            detailVO.setExpireTime(expire);
            detailVO.setKeyResult(getValueByConfigCode(key));
            details.add(detailVO);
        }
        return BaseResponse.success(details);
    }

    private Object getValueByConfigCode(String key) {
        try {
            DataType type = redisTemplate.type(key);
            switch (type) {
                case STRING:
                    return redisTemplate.opsForValue().get(key);
                case HASH:
                    return redisTemplate.opsForHash().entries(key);
                case LIST:
                    return redisTemplate.opsForList().range(key, 0, -1);
                case SET:
                    return redisTemplate.opsForSet().members(key);
                case ZSET:
                    return redisTemplate.opsForZSet().range(key, 0, -1);
                default:
                    return null;
            }
        } catch (Exception e) {
            log.error("获取Redis键值失败，key: {}, 错误: {}", key, e.getMessage());
            return null;
        }
    }

    private String getPatternKey(String configCode) {
        String patternKey = String.format(configCode.replace("{tenantId}", SystemHolder.getTenantId()).replace("{userId}", SystemHolder.getUserId()), "*");
        if (patternKey.endsWith("*")) {
            return patternKey;
        }
        return patternKey + "*";
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.REDIS_KEY_MANAGE.getCode();
    }

    @Override
    public List<RedisKeyManageVO> invocation(List<RedisKeyManageVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
