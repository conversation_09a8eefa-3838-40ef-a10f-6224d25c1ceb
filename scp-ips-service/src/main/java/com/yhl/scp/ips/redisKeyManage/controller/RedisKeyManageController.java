package com.yhl.scp.ips.redisKeyManage.controller;

import com.github.pagehelper.PageInfo;
import com.yhl.platform.common.controller.BaseController;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.ips.redisKeyManage.dto.RedisKeyManageDTO;
import com.yhl.scp.ips.redisKeyManage.service.RedisKeyManageService;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageDetailVO;
import com.yhl.scp.ips.redisKeyManage.vo.RedisKeyManageVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <code>RedisKeyManageController</code>
 * <p>
 * redis key管理控制器
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-06-10 16:16:10
 */
@Slf4j
@Api(tags = "redis key管理控制器")
@RestController
@RequestMapping("redisKeyManage")
public class RedisKeyManageController extends BaseController {

    @Resource
    private RedisKeyManageService redisKeyManageService;

    @ApiOperation(value = "分页查询")
    @GetMapping(value = "page")
    @SuppressWarnings("unchecked")
    public BaseResponse<PageInfo<RedisKeyManageVO>> page() {
        List<RedisKeyManageVO> redisKeyManageList = redisKeyManageService.selectByPage(getPagination(),
                getSortParam(), getQueryCriteriaParam());
        PageInfo<RedisKeyManageVO> pageInfo = new PageInfo<>(redisKeyManageList);
        return BaseResponse.success(BaseResponse.OP_SUCCESS, pageInfo);
    }

    @ApiOperation(value = "新增")
    @PostMapping(value = "create")
    public BaseResponse<Void> create(@RequestBody RedisKeyManageDTO redisKeyManageDTO) {
        return redisKeyManageService.doCreate(redisKeyManageDTO);
    }

    @ApiOperation(value = "修改")
    @PostMapping(value = "update")
    public BaseResponse<Void> update(@RequestBody RedisKeyManageDTO redisKeyManageDTO) {
        return redisKeyManageService.doUpdate(redisKeyManageDTO);
    }

    @ApiOperation(value = "删除")
    @PostMapping(value = "delete")
    @SuppressWarnings("unchecked")
    public BaseResponse<Void> delete(@RequestBody List<String> ids) {
        redisKeyManageService.doDelete(ids);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @ApiOperation(value = "详情查询")
    @GetMapping(value = "detail/{id}")
    @SuppressWarnings("unchecked")
    public BaseResponse<RedisKeyManageVO> detail(@PathVariable(name = "id") String id) {
        return BaseResponse.success(BaseResponse.OP_SUCCESS, redisKeyManageService.selectByPrimaryKey(id));
    }

    @ApiOperation(value = "删除redisKey")
    @PostMapping(value = "removeKey/{id}")
    public BaseResponse<Void> removeKey(@PathVariable(name = "id") String id) {
        return redisKeyManageService.removeKey(id);
    }

    @ApiOperation(value = "根据configCode删除redisKey")
    @PostMapping(value = "removeKeyByConfigCode")
    public BaseResponse<Void> removeKeyByConfigCode(@RequestParam(name = "configCode") String configCode) {
        return redisKeyManageService.removeKeyByConfigCode(configCode);
    }

    @ApiOperation(value = "根据configCode获取通配符详情")
    @PostMapping(value = "getDetailByConfigCode")
    public BaseResponse<List<RedisKeyManageDetailVO>> getDetailByConfigCode(
            @RequestParam(name = "configCode") String configCode) {
        return redisKeyManageService.getDetailByConfigCode(configCode);
    }
}
