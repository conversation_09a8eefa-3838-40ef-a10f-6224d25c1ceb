package com.yhl.scp.ips.rbac.consumer;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.platform.common.exception.BusinessException;
import com.yhl.platform.common.utils.CollectionUtils;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.biz.common.annotation.OnMessage;
import com.yhl.scp.biz.common.annotation.OnMessageConsumer;
import com.yhl.scp.biz.common.topic.MessageConstants;
import com.yhl.scp.biz.common.util.StreamUtils;
import com.yhl.scp.ips.collection.vo.CollectionValueVO;
import com.yhl.scp.ips.config.CollectionCacheInit;
import com.yhl.scp.ips.enums.MessageTypeEnum;
import com.yhl.scp.ips.rbac.dao.RoleDao;
import com.yhl.scp.ips.rbac.dto.UserMessageDTO;
import com.yhl.scp.ips.rbac.service.UserMessageService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务消息处理器
 */
@Slf4j
@OnMessageConsumer
public class WorkBenchMessageHandler {

    @Resource
    private UserMessageService userMessageService;
    @Resource
    private CollectionCacheInit collectionCacheInit;
    @Resource
    private RoleDao roleDao;

    /**
     * 量产移交审批完成提醒（单人）
     *
     * @param message 数据
     */
    @OnMessage(channel = MessageConstants.APPROVAL_COMPLETED)
    private void handleApprovalCompleted(String message) {
        // 量产移交审批完成提醒:OA流程审批完成后，给该物料的计划员发消息通知“您的量产移交流程已审批完成，请继续后面的移交流程”，跳转链接
        UserMessageDTO result = UserMessageDTO.builder()
                .id(UUIDUtil.getUUID())
                .userId(message)
                .messageSource("系统消息")
                .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                .messageTitle("量产移交审批")
                .messageContent("您的量产移交流程已审批完成，请继续后面的移交流程")
                .messageEmergency("3")
                .readStatus(YesOrNoEnum.NO.getCode())
                .build();
        batchInsertMessage(ListUtil.of(result));
    }

    /**
     * 量产移交物料权限交接提醒（单人）
     *
     * @param message 数据
     */
    @OnMessage(channel = MessageConstants.PERMISSION_TRANSFER)
    private void handlePermissionTransfer(String message) {
        // 量产移交物料权限交接提醒:xxx物料项目计划已发起量产移交权限，移交给您，请及时查看需求信息
        List<UserMessageDTO> userMessageDTOS = JSON.parseArray(message, UserMessageDTO.class);
        for (UserMessageDTO userMessageDTO : userMessageDTOS) {
            userMessageDTO.setId(UUIDUtil.getUUID());
            userMessageDTO.setMessageSource("系统消息");
            userMessageDTO.setMessageType(MessageTypeEnum.MY_MESSAGE.getCode());
            userMessageDTO.setMessageTitle("物料权限交接提醒");
            userMessageDTO.setMessageEmergency("3");
            userMessageDTO.setReadStatus(YesOrNoEnum.NO.getCode());
        }
        batchInsertMessage(userMessageDTOS);
        log.info("PERMISSION_TRANSFER物料权限交接提醒行数：{}", userMessageDTOS.size());
    }

    /**
     * 周产能平衡超负荷告警
     *
     * @param message 数据（多人）
     */
    @OnMessage(channel = MessageConstants.CAPACITY_OVERFLOW)
    private void handleCapacityOverflow(String message) {
        // 周产能平衡超负荷告警:XX产线，x月x日~x月x日产线产能超100%(发送给订单计划科长和生产调度科长、物流经理)
        log.info("周产能平衡超负荷告警");
        List<UserMessageDTO> userMessageDTOS = JSON.parseArray(message, UserMessageDTO.class);
        List<CollectionValueVO> roleTypeValues = collectionCacheInit.getByCollectionCode("ROLE_MANAGER").getData();
        if (CollectionUtils.isEmpty(roleTypeValues)) {
            throw new BusinessException("ROLE_MANAGER值集未配置");
        }
        List<String> managerNames = StreamUtils.columnToList(roleTypeValues, CollectionValueVO::getValueMeaning);
        List<String> managerUserIds = roleDao.selectUserByRoleNames(managerNames)
                .stream().distinct().collect(Collectors.toList());
        List<UserMessageDTO> userMessageDTOList = new ArrayList<>();
        for (String managerUserId : managerUserIds) {
            for (UserMessageDTO userMessageDTO : userMessageDTOS) {
                String messageContent = userMessageDTO.getMessageContent();
                UserMessageDTO result = UserMessageDTO.builder()
                        .id(UUIDUtil.getUUID())
                        .userId(managerUserId)
                        .messageSource("系统消息")
                        .messageType(MessageTypeEnum.MY_MESSAGE.getCode())
                        .messageTitle("产能平衡超负荷告警")
                        .messageContent(messageContent)
                        .messageEmergency("3")
                        .readStatus(YesOrNoEnum.NO.getCode())
                        .build();
                userMessageDTOList.add(result);
            }
        }
        batchInsertMessage(userMessageDTOList);
        log.info("CAPACITY_OVERFLOW主题产能平衡通知行数：{}", userMessageDTOList.size());
    }

    private void batchInsertMessage(List<UserMessageDTO> userMessageDTOList){
        Lists.partition(userMessageDTOList, 2000).forEach(p -> userMessageService.doCreateBatch(p));
    }

}
