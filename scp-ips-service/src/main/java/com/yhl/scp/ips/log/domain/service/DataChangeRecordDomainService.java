package com.yhl.scp.ips.log.domain.service;

import com.yhl.scp.ips.log.domain.entity.DataChangeRecordDO;
import com.yhl.scp.ips.log.infrastructure.dao.DataChangeRecordDao;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DataChangeRecordDomainService {

    @Resource
    private DataChangeRecordDao dataChangeRecordDao;

    /**
     * 数据校验
     *
     * @param dataChangeRecordDO 领域对象
     */
    public void validation(DataChangeRecordDO dataChangeRecordDO) {
        checkNotNull(dataChangeRecordDO);
        checkUniqueCode(dataChangeRecordDO);
        // TODO 补充其他校验逻辑
    }

    /**
     * 非空检验
     *
     * @param dataChangeRecordDO 领域对象
     */
    private void checkNotNull(DataChangeRecordDO dataChangeRecordDO) {
    }

    /**
     * 唯一性校验
     *
     * @param dataChangeRecordDO 领域对象
     */
    private void checkUniqueCode(DataChangeRecordDO dataChangeRecordDO) {
    }

}
