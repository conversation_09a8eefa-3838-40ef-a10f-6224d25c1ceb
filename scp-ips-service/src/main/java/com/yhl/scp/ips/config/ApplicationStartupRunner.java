package com.yhl.scp.ips.config;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.yhl.platform.cache.redis.RedisUtil;
import com.yhl.platform.common.enums.YesOrNoEnum;
import com.yhl.scp.common.constants.Constants;
import com.yhl.scp.ips.common.ObjectUnitUtil;
import com.yhl.scp.ips.object.entity.ObjectUnit;
import com.yhl.scp.ips.object.service.ObjectUnitService;
import com.yhl.scp.ips.system.entity.Scenario;
import com.yhl.scp.ips.system.service.ScenarioService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>ApplicationStartupRunner</code>
 * <p>
 * 应用启动执行类
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-12-04 20:58:37
 */
@Slf4j
@Component
public class ApplicationStartupRunner implements ApplicationRunner {

    @Resource
    private ScenarioService scenarioService;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private ObjectUnitService objectUnitService;

    @Resource
    private RedisUtil redisUtil;

    @Override
    public void run(ApplicationArguments args) {
        List<Scenario> scenarios = scenarioService.selectAll().stream()
                .filter(item -> YesOrNoEnum.YES.getCode().equals(item.getMaster())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(scenarios)) {
            Map<String, List<Scenario>> scenarioGroup = scenarios.stream().collect(Collectors.groupingBy(Scenario::getTenantId));
            scenarioGroup.forEach((key, value) -> {
                List<String> masterScenarios = value.stream().map(Scenario::getDataBaseName).collect(Collectors.toList());
                ListOperations<String, Object> listOps = redisTemplate.opsForList();
                listOps.leftPushAll(Constants.MASTER_SCENARIO + "#" + key, masterScenarios);
            });
        }
        // 加载单位信息到缓存
        loadObjectUnit();
    }

    private void loadObjectUnit() {
        List<ObjectUnit> objectUnits = objectUnitService.selectAll();
        for (ObjectUnit objectUnit : objectUnits) {
            redisUtil.set(ObjectUnitUtil.getRedisKey(objectUnit.getKey()), objectUnit.getUnit());
        }
    }

}