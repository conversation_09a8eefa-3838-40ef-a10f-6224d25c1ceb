package com.yhl.scp.ips.algorithm.service.impl;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.yhl.platform.common.Pagination;
import com.yhl.platform.common.ddd.AbstractService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.platform.common.utils.SpringBeanUtils;
import com.yhl.platform.component.custom.Expression;
import com.yhl.scp.common.enums.AlgorithmLogStatusEnum;
import com.yhl.scp.ips.algorithm.convertor.AlgorithmServerConvertor;
import com.yhl.scp.ips.algorithm.domain.entity.AlgorithmServerDO;
import com.yhl.scp.ips.algorithm.domain.service.AlgorithmServerDomainService;
import com.yhl.scp.ips.algorithm.dto.AlgorithmServerDTO;
import com.yhl.scp.ips.algorithm.infrastructure.dao.AlgorithmServerDao;
import com.yhl.scp.ips.algorithm.infrastructure.po.AlgorithmServerPO;
import com.yhl.scp.ips.algorithm.service.AlgorithmServerService;
import com.yhl.scp.ips.algorithm.vo.AlgorithmServerVO;
import com.yhl.scp.ips.system.entity.AlgorithmLog;
import com.yhl.scp.ips.system.service.AlgorithmLogService;
import com.yhl.scp.ips.utils.BasePOUtils;
import com.yhl.scp.mds.basic.enums.ObjectTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <code>AlgorithmServerServiceImpl</code>
 * <p>
 * 算法服务器表应用实现
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2023-10-30 16:05:10
 */
@Slf4j
@Service
public class AlgorithmServerServiceImpl extends AbstractService implements AlgorithmServerService {

    @Resource
    private AlgorithmServerDao algorithmServerDao;
    @Resource
    private AlgorithmLogService algorithmLogService;

    @Resource
    private AlgorithmServerDomainService algorithmServerDomainService;

    @Resource
    private SpringBeanUtils springBeanUtils;

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doCreate(AlgorithmServerDTO algorithmServerDTO) {
        // 0.数据转换
        AlgorithmServerDO algorithmServerDO = AlgorithmServerConvertor.INSTANCE.dto2Do(algorithmServerDTO);
        AlgorithmServerPO algorithmServerPO = AlgorithmServerConvertor.INSTANCE.dto2Po(algorithmServerDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        algorithmServerDomainService.validation(algorithmServerDO);
        // 2.数据持久化
        BasePOUtils.insertFiller(algorithmServerPO);
        algorithmServerDao.insert(algorithmServerPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    @SuppressWarnings({"unchecked"})
    public BaseResponse<Void> doUpdate(AlgorithmServerDTO algorithmServerDTO) {
        // 0.数据转换
        AlgorithmServerDO algorithmServerDO = AlgorithmServerConvertor.INSTANCE.dto2Do(algorithmServerDTO);
        AlgorithmServerPO algorithmServerPO = AlgorithmServerConvertor.INSTANCE.dto2Po(algorithmServerDTO);
        // 1.数据校验
        // TODO 完善validation()方法
        algorithmServerDomainService.validation(algorithmServerDO);
        // 2.数据持久化
        BasePOUtils.updateFiller(algorithmServerPO);
        algorithmServerDao.update(algorithmServerPO);
        return BaseResponse.success(BaseResponse.OP_SUCCESS);
    }

    @Override
    public void doCreateBatch(List<AlgorithmServerDTO> list) {
        List<AlgorithmServerPO> newList = AlgorithmServerConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.insertBatchFiller(newList);
        algorithmServerDao.insertBatch(newList);
    }

    @Override
    public void doUpdateBatch(List<AlgorithmServerDTO> list) {
        List<AlgorithmServerPO> newList = AlgorithmServerConvertor.INSTANCE.dto2Pos(list);
        BasePOUtils.updateBatchFiller(newList);
        algorithmServerDao.updateBatch(newList);
    }

    @Override
    public int doDelete(List<String> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return 0;
        }
        if (idList.size() > 1) {
            return algorithmServerDao.deleteBatch(idList);
        }
        return algorithmServerDao.deleteByPrimaryKey(idList.get(0));
    }

    @Override
    public AlgorithmServerVO selectByPrimaryKey(String id) {
        AlgorithmServerPO po = algorithmServerDao.selectByPrimaryKey(id);
        return AlgorithmServerConvertor.INSTANCE.po2Vo(po);
    }

    @Override
    @Expression(value = "ALGORITHM_SERVER")
    public List<AlgorithmServerVO> selectByPage(Pagination pagination, String sortParam, String queryCriteriaParam) {
        PageHelper.startPage(pagination.getPageNum(), pagination.getPageSize());
        return this.selectByCondition(sortParam, queryCriteriaParam);
    }

    @Override
    @Expression(value = "ALGORITHM_SERVER")
    public List<AlgorithmServerVO> selectByCondition(String sortParam, String queryCriteriaParam) {
        List<AlgorithmServerVO> dataList = algorithmServerDao.selectByCondition(sortParam, queryCriteriaParam);
        AlgorithmServerServiceImpl target = springBeanUtils.getBean(AlgorithmServerServiceImpl.class);
        return target.invocation(dataList, null, this.getInvocationName());
    }

    @Override
    public List<AlgorithmServerVO> selectByParams(Map<String, Object> params) {
        List<AlgorithmServerPO> list = algorithmServerDao.selectByParams(params);

        List<AlgorithmLog> runningLogs = algorithmLogService.selectByStatus(AlgorithmLogStatusEnum.RUNNING.getCode());
        Map<String, List<AlgorithmLog>> logsByIpAddress = new HashMap<>();
        List<AlgorithmLog> notIpRunningLog = runningLogs.stream().filter(p -> StrUtil.isEmpty(p.getIpAddress())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(runningLogs) && CollectionUtils.isEmpty(notIpRunningLog)) {
            logsByIpAddress.putAll(runningLogs.stream().collect(Collectors.groupingBy(AlgorithmLog::getIpAddress)));
        }

        List<AlgorithmServerVO> result = AlgorithmServerConvertor.INSTANCE.po2Vos(list);
        result.forEach(t -> {
            t.setRunningNumber(CollectionUtils.isEmpty(logsByIpAddress.get(t.getIpAddress())) ? 0 : logsByIpAddress.get(t.getIpAddress()).size());
            t.setUseRate(BigDecimal.valueOf(t.getRunningNumber())
                    .divide(BigDecimal.valueOf(t.getAlgorithmExecuteNumberLimit()), 2, BigDecimal.ROUND_HALF_UP)
                    .doubleValue());
        });
        return result;
    }

    @Override
    public List<AlgorithmServerVO> selectAll() {
        return this.selectByParams(new HashMap<>(2));
    }

    @Override
    public String getObjectType() {
        return ObjectTypeEnum.ALGORITHM_SERVER.getCode();
    }

    @Override
    public List<AlgorithmServerVO> invocation(List<AlgorithmServerVO> dataList, Map<String, Object> params, String invocation) {
        // TODO
        return dataList;
    }

}
