package com.yhl.scp.gateway.config;

import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpHeaders;
import reactor.core.publisher.Mono;

/**
 * <code>GatewayCookieConfig</code>
 * <p>
 * GatewayCookieConfig
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03 19:13:46
 */
@Configuration
public class GatewayCookieConfig {

    @Bean
    public GlobalFilter cookieSecureFilter() {
        return (exchange, chain) -> {
            return chain.filter(exchange).then(Mono.fromRunnable(() -> {
                // 获取响应头中的 Set-Cookie
                HttpHeaders headers = exchange.getResponse().getHeaders();
                headers.compute("Set-Cookie", (key, cookies) -> {
                    if (cookies == null) return null;
                    // 遍历所有 Cookie，添加 Secure 属性
                    return cookies.stream()
                            .map(cookie -> {
                                // 若 Cookie 未包含 Secure，则添加；已包含则不重复添加
                                if (!cookie.contains("; Secure")) {
                                    return cookie + "; Secure";
                                }
                                return cookie;
                            })
                            .collect(java.util.stream.Collectors.toList());
                });
            }));
        };
    }
}
