package com.yhl.scp.gateway.filters;

import cn.hutool.http.ContentType;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@WebFilter(urlPatterns = "/*")
public class HttpServletRequestRepeatFilter implements Filter {

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        ServletRequest servletWrapper = null;
        if (request instanceof HttpServletRequest) {
            servletWrapper = new RequestWrapper((HttpServletRequest) request);
        }
        if (Objects.isNull(servletWrapper) || ContentType.MULTIPART.getValue().equals(request.getContentType())) {
            chain.doFilter(request, response);
        } else {
            try {
                chain.doFilter(servletWrapper, response);
            } catch (IOException | ServletException e) {
                e.printStackTrace();
            }
        }
    }
}
