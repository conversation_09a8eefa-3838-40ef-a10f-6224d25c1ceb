package com.yhl.scp.ips.api.vo;

import com.yhl.platform.common.ddd.BaseVO;
import com.yhl.platform.common.annotation.FieldInterpretation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>ExtApiSyncCtrlVO</code>
 * <p>
 * 外部api同步控制表VO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-05-22 14:46:58
 */
@ApiModel(value = "外部api同步控制表VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExtApiSyncCtrlVO extends BaseVO implements Serializable {

    private static final long serialVersionUID = -76624411230349399L;

    /**
     * 接口配置ID
     */
    @ApiModelProperty(value = "接口配置ID")
    @FieldInterpretation(value = "接口配置ID")
    private String apiConfigId;
    /**
     * 同步引用字段值
     */
    @ApiModelProperty(value = "同步引用字段值")
    @FieldInterpretation(value = "同步引用字段值")
    private String referValue;
    /**
     * 分组字段值(多字段用#隔开)
     */
    @ApiModelProperty(value = "分组字段值(多字段用#隔开)")
    @FieldInterpretation(value = "分组字段值(多字段用#隔开)")
    private String groupValue;
    /**
     * 最后同步时间
     */
    @ApiModelProperty(value = "最后同步时间")
    @FieldInterpretation(value = "最后同步时间")
    private Date syncTime;
    /**
     * 数据表版本
     */
    @ApiModelProperty(value = "数据表版本")
    @FieldInterpretation(value = "数据表版本")
    private Integer versionValue;
    /**
     * 接口分类（鉴权，物料，客户等）
     */
    @ApiModelProperty(value = "接口分类（鉴权，物料，客户等）")
    private String apiCategory;
    /**
     * 接口名称
     */
    @ApiModelProperty(value = "接口名称")
    private String apiName;


    @Override
    public void clean() {

    }

}
