package com.yhl.scp.ips.config;

import com.alibaba.fastjson.JSONObject;
import com.yhl.platform.common.utils.UUIDUtil;
import com.yhl.scp.ips.constant.MqConstants;
import com.yhl.scp.ips.requestLog.dto.RequestLogDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collections;
import java.util.Date;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 请求日志切面
 * </p>
 *
 * <AUTHOR>
 */
@Aspect
@Slf4j
@Component
public class CustomRequestLogAspect {

    @Resource
    private RabbitTemplate rabbitTemplate;

    @Pointcut("execution(* com.yhl..*Controller.*(..))" +
            " && !execution(* com.yhl..*Controller.setReqAndRes(..))" +
            " && !execution(* com.yhl..*Controller.exceptionHandler(..))" +
            " && !execution(* com.yhl..*Controller.initBinder(..))")
    public void pointCut() {
    }

    @Around("pointCut()")
    public Object doAround(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
        log.info("开始记录环绕通知");
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();

        // 获取请求信息
        Date requestTime = new Date();
        String requestURI = request.getRequestURI();
        String requestPayload = "";
        if ("POST".equalsIgnoreCase(request.getMethod()) || "PUT".equalsIgnoreCase(request.getMethod())) {
            requestPayload = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
        }

        long start = System.currentTimeMillis();
        Object result = proceedingJoinPoint.proceed();
        try {
            long costTime = System.currentTimeMillis() - start;
            HttpServletResponse httpServletResponse = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getResponse();
            int responseStatus = httpServletResponse.getStatus();
            String userId = getUserIdByRequest(request);
            String token = getTokenByRequest(request);
            String requestHeaders = getHeadersByRequest(request);
            String userAgent = getUserAgentByRequest(request);
            // 获取响应信息
            String responsePayload = Objects.nonNull(result) ? JSONObject.toJSONString(result) : "";
            RequestLogDTO requestLogDTO = RequestLogDTO.builder().id(UUIDUtil.getUUID()).requestTime(requestTime).userId(userId).token(token).requestUri(requestURI).responseStatus(responseStatus).requestPayLoad(requestPayload).responsePayLoad(responsePayload).costTime(costTime).requestHeaders(requestHeaders).userAgent(userAgent).createTime(new Date()).build();
            // 发送MQ
            rabbitTemplate.convertAndSend(MqConstants.BPIM_EVENT_EXCHANGE, MqConstants.REQUEST_LOG_ROUTING_KEY, JSONObject.toJSONString(requestLogDTO));
        } catch (Exception e) {
            log.error("请求日志发送MQ失败: {}", e.getMessage());
        }
        return result;
    }

    /**
     * 获取userId
     *
     * @param request
     * @return
     */
    private String getUserIdByRequest(HttpServletRequest request) {
        String user = request.getHeader("X-User-Id");
        if (StringUtils.isBlank(user)) {
            user = request.getHeader("userid");
        }
        if (StringUtils.isBlank(user)) {
            user = request.getHeader("Userid");
        }
        return user;
    }

    /**
     * 获取token
     *
     * @param request
     * @return
     */
    private String getTokenByRequest(HttpServletRequest request) {
        String token = request.getHeader("Authorization");
        if (StringUtils.isBlank(token)) {
            token = request.getHeader("token");
        }
        if (StringUtils.isBlank(token)) {
            token = request.getHeader("Token");
        }
        return token;
    }

    /**
     * 获取user-agent
     *
     * @param request
     * @return
     */
    private String getUserAgentByRequest(HttpServletRequest request) {
        return request.getHeader("User-Agent");
    }

    /**
     * 获取所有的请求头
     *
     * @param request
     * @return
     */
    private String getHeadersByRequest(HttpServletRequest request) {
        return Collections.list(request.getHeaderNames()).stream().collect(Collectors.toMap(name -> name, request::getHeader, (v1, v2) -> v1, java.util.LinkedHashMap::new)).toString();
    }
}
