package com.yhl.scp.mrp.MrpFeign;

import com.yhl.platform.common.FeignConfig;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.common.constants.ServletContextConstants;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialDeliveryNote;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialPurchaseStorage;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesReturnedPurchase;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.srm.SrmSupplierPurchase;
import com.yhl.scp.mrp.inventory.vo.InventoryOceanFreightShippedMapVO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDTO;
import com.yhl.scp.mrp.material.plan.dto.MaterialPlanNeedFeedbackDetailsDTO;
import com.yhl.scp.mrp.material.plan.vo.MaterialPlanReplaceVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPoCreate;
import java.util.List;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpResponse;

/**
 * <code>MrpFeign</code>
 * <p>
 * TODO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-09-12 14:20:16
 */
@FeignClient(value = ServletContextConstants.MRP, path = "/", configuration = FeignConfig.class, url = "${mrp.feign.url:}")
public interface MrpFeign {

    @PostMapping(value = "outsourceTransferMaterialDetail/selectMaterialSupplierPurchaseVOByStockPointCode")
    List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseVOByStockPointCode(@RequestHeader("scenario") String scenario, @RequestBody List<String> StockPointCodeList);

    @PostMapping(value = "materialSupplierPurchase/selectByProductCodeList")
    List<MaterialSupplierPurchaseVO> selectMaterialSupplierPurchaseByProductCodeList(@RequestHeader("scenario") String scenario, @RequestBody List<String> productCodeList);

    @PostMapping(value = "materialPlanReplace/selectAllReplacePlan")
    List<MaterialPlanReplaceVO> selectAllReplacePlan(@RequestHeader("scenario") String scenario);

    @ApiOperation(value = "同步采购入库记录")
    @PostMapping("api/materialPurchaseStorage/handleData")
    BaseResponse<Void> syncMaterialPurchaseStorage(@RequestHeader("scenario") String scenario,
                                          @RequestBody List<MesMaterialPurchaseStorage> materialPurchaseStorages);

    @PostMapping(value = "supplierPurchase/handleData")
    BaseResponse<Void> handleSupplierPurchase(@RequestHeader("scenario") String scenario,
                                              @RequestBody List<SrmSupplierPurchase> srmSupplierPurchases);

    @PostMapping(value = "inventoryOceanFreight/handleData")
    BaseResponse<Void> handleInventoryOceanFreight(@RequestHeader("scenario") String scenario,
                                                   @RequestBody List<InventoryOceanFreightShippedMapVO> o);
    @PostMapping(value = "originalFilmFFInventory/handleData")
    BaseResponse<Void> handleOriginalFilmFFInventory(@RequestHeader("scenario") String scenario, @RequestBody List<ErpOriginalFilmFFInventory> o);

    @PostMapping(value = "materialDeliveryNote/handleData")
    BaseResponse<Void> handleMaterialDeliveryNote(@RequestHeader("scenario") String scenario,
                                                  @RequestBody List<MesMaterialDeliveryNote> scssInventoryOceanFreightList);
    @PostMapping(value = "prQuery/handleData")
    BaseResponse<Void> handlePrQuery(@RequestHeader("scenario") String scenario,
                                    @RequestBody List<ErpPrQuery> erpPrQuery);

    @PostMapping(value = "poQuery/handleData")
    BaseResponse<Void> handlePrCreate(@RequestHeader("scenario") String scenario,
                                     @RequestBody List<ErpPrCreate> erpPrQuery);
    @PostMapping(value = "purchaseOrder/handleData")
    BaseResponse<Void> handlePurchaseOrder(@RequestHeader("scenario") String scenario, @RequestBody List<ErpPurchaseOrder> o);

    @PostMapping(value = "originalFilmFFOnway/handleData")
    BaseResponse<Void> handleOriginalFilmFFOnway(@RequestHeader("scenario") String scenario, @RequestBody List<ErpOriginalFilmFFOnway> o);

    @PostMapping(value = "materialPlanNeed/receiveData")
    BaseResponse<Void> receiveData(@RequestParam("scenario") String scenario,@RequestBody List<MaterialPlanNeedFeedbackDTO> list);

    @PostMapping(value = "poCreat/handleData")
    BaseResponse<Void> handlePoCreate(@RequestHeader("scenario") String scenario,
                                      @RequestBody List<ErpPoCreate> erpPoCreates);

    @PostMapping(value = "poCreat/handleDataQuay")
    BaseResponse<Void> handlePoCreateQuay(@RequestHeader("scenario") String scenario,
                                      @RequestBody List<ErpPoCreate> erpPoCreates);

    @PostMapping(value = "returnedPurchase/handleData")
    BaseResponse<Void> handleReturnedPurchase(@RequestHeader("scenario") String scenario,
                                      @RequestBody List<MesReturnedPurchase> erpPrQuery);

    @PostMapping(value = "prCancel/handleData")
    BaseResponse<Void> handlePrCancel(@RequestHeader("scenario") String scenario, @RequestBody ErpResponse response);

    @PostMapping(value = "poClosed/handleData")
    BaseResponse<Void> handlePoClosed(@RequestHeader("scenario") String scenario, @RequestBody ErpResponse response);

    @GetMapping(value = "glassSubstitutionRelationship/syncGlassSubstitutionRelationship")
    void syncGlassSubstitutionRelationship(@RequestHeader("scenario") String scenario);

    @PostMapping(value = "materialTransactions/handleData")
    BaseResponse<Void> handleMaterialTransactions(@RequestHeader("scenario") String scenario,
                                     @RequestBody List<ErpMaterialTransactions> erpMaterialTransactions);

    @PostMapping(value = "glassSubstitutionRelationship/syncGlassSubstitutionRelationship")
    void addMaterialPlanNeedFeedbackDetails(@RequestHeader("scenario") String scenario, @RequestBody List<MaterialPlanNeedFeedbackDetailsDTO> list);
}

