package com.yhl.scp.mrp.material.arrival.service;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.mes.MesMaterialPurchaseStorage;
import com.yhl.scp.mrp.material.arrival.dto.MaterialPurchaseStorageDTO;
import com.yhl.scp.mrp.material.arrival.vo.MaterialPurchaseStorageVO;

import java.util.List;
import java.util.Map;

/**
 * <code>MaterialPurchaseStorageService</code>
 * <p>
 * 材料采购入库记录应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-26 16:12:21
 */
public interface MaterialPurchaseStorageService extends BaseService<MaterialPurchaseStorageDTO, MaterialPurchaseStorageVO> {

    void doUpdateBatchSelective(List<MaterialPurchaseStorageDTO> list);

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseStorageVO}
     */
    List<MaterialPurchaseStorageVO> selectAll();

	void doDisposePurchaseStorageForJob(Integer moveMinute);

    BaseResponse<Void> syncMaterialPurchaseStorage(String scenario, List<MesMaterialPurchaseStorage> materialPurchaseStorages);

    List<MaterialPurchaseStorageVO> selectByProductOrPurchaseOrderOrWarehouseEntry(Map<String, Object> params);

    BaseResponse<MaterialPurchaseStorageVO> syncData(String scenario,String tenantCode);

}
