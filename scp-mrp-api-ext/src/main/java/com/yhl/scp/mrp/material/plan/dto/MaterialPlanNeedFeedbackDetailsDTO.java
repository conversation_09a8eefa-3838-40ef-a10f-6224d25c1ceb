package com.yhl.scp.mrp.material.plan.dto;

import com.yhl.platform.common.ddd.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.util.Date;

/**
 * <code>MaterialPlanNeedFeedbackDetailsDTO</code>
 * <p>
 * 要货计划反馈明细DTO
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-07-03 14:23:53
 */
@ApiModel(value = "要货计划反馈明细DTO")
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class MaterialPlanNeedFeedbackDetailsDTO extends BaseDTO implements Serializable {

    private static final long serialVersionUID = 119203959209760625L;

    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id")
    private String id;
    /**
     * 反馈状态（true,false）
     */
    @ApiModelProperty(value = "反馈状态（true,false）")
    private String status;
    /**
     * 接口参数
     */
    @ApiModelProperty(value = "接口参数")
    private String interfaceParameters;
    /**
     * 异常原因
     */
    @ApiModelProperty(value = "异常原因")
    private String exceptionReason;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    private String enabled;

}
