package com.yhl.scp.mrp.material.purchase.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.yhl.platform.common.ddd.BaseService;
import com.yhl.platform.common.entity.BaseResponse;
import com.yhl.scp.dcp.apiConfig.externalApi.resp.erp.ErpPrCreate;
import com.yhl.scp.mds.extension.supplier.vo.SupplierPurchaseRatioVO;
import com.yhl.scp.mds.extension.supplier.vo.SupplierVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDataVO;
import com.yhl.scp.mrp.material.plan.vo.NoGlassInventoryShiftDetailVO;
import com.yhl.scp.mrp.material.purchase.dto.MaterialPurchaseRequirementDetailDTO;
import com.yhl.scp.mrp.extension.material.vo.MaterialPurchaseRequirementDetailVO;
import com.yhl.scp.mrp.order.vo.PurchaseOrderInfoVO;
import com.yhl.scp.mrp.supplier.vo.MaterialSupplierPurchaseVO;

/**
 * <code>MaterialPurchaseRequirementDetailService</code>
 * <p>
 * 材料采购需求明细应用接口
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2024-11-11 19:00:23
 */
public interface MaterialPurchaseRequirementDetailService extends BaseService<MaterialPurchaseRequirementDetailDTO,
		MaterialPurchaseRequirementDetailVO> {

    List<MaterialPurchaseRequirementDetailVO> selectVOByParams(Map<String, Object> params);

    /**
     * 查询所有
     *
     * @return list {@link MaterialPurchaseRequirementDetailVO}
     */
    List<MaterialPurchaseRequirementDetailVO> selectAll();

    String doBatchIssue(List<String> ids);

    /**
     * 材料MRP计划发布同步数据到材料采购需求汇总
     */
    Set<String> doSyncMaterialPurchase(List<NoGlassInventoryShiftDataVO> shiftDataList, List<NoGlassInventoryShiftDetailVO> materialPlanInventoryShiftVOList,
                                       List<PurchaseOrderInfoVO> purchaseOrderInfoVOList,
                                       Map<String, MaterialSupplierPurchaseVO> supplierPurchaseVOMap,
                                       Map<String, List<SupplierPurchaseRatioVO>> supplierPurchaseRatioVOGroup, Map<String, SupplierVO> supplierVOMapOfId,
                                       String versionCode, List<String> permissionCodeList);

    MaterialPurchaseRequirementDetailVO selectDetailByPrimaryKey(String id);

    /**
     * PR创建返回
     * @param erpPrCreateList erpPrCreateList
     * @return {@link BaseResponse}
     */
    BaseResponse<Void> handleMaterialPurchaseRequirementDetail(List<ErpPrCreate> erpPrCreateList);

    MaterialPurchaseRequirementDetailDTO getMergeMaterialPurchaseRequirementDetailDTO(Date requireDate, BigDecimal requireQuantitySum, BigDecimal purchaseRatio, SupplierVO supplierVO,
                                                                                      int orderPlacementLeadTimeDay);

    BaseResponse<Void> splitTheOrder(List<MaterialPurchaseRequirementDetailDTO> materialPurchaseRequirementDetailDTOList);

    void doUpdateBatchSelective(List<MaterialPurchaseRequirementDetailDTO> list);

}
