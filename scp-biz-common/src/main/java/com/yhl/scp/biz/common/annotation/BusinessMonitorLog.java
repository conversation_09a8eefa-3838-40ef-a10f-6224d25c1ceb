package com.yhl.scp.biz.common.annotation;

import java.lang.annotation.*;

/**
 * <code>BusinessMonitorLog</code>
 * <p>
 * BusinessMonitorLog
 * </p>
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-03-24 11:19:58
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface BusinessMonitorLog {

    /**
     * 业务代码
     *
     * @return java.lang.String
     */
    String businessCode();

    /**
     * 模块代码
     *
     * @return java.lang.String
     */
    String moduleCode();

    /**
     * 执行频次 DAY MONTH
     *
     * @return java.lang.String
     */
    String businessFrequency();

    /**
     * 操作方式 MANUAL AUTO
     *
     * @return java.lang.String
     */
    String operationMethod() default "MANUAL";

}